import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Card, Table, Tag, Button, Space, Avatar, Modal, Form, Input, Select, message } from 'antd';
import { UserOutlined, PlusOutlined } from '@ant-design/icons';

const { t } = useTranslation();

// 模拟团队数据
const mockTeams = [
  {
    id: '1',
    name: '广告投放团队A',
    role: t('pages.key_523'),
    members: 5,
    projects: 8,
    status: 'active',
  },
  {
    id: '2',
    name: t('pages.key_277'),
    role: t('pages.key_942'),
    members: 12,
    projects: 15,
    status: 'active',
  },
  {
    id: '3',
    name: t('pages.key_1091'),
    role: t('pages.key_942'),
    members: 7,
    projects: 6,
    status: 'active',
  },
  {
    id: '4',
    name: t('pages.key_394'),
    role: t('pages.key_523'),
    members: 3,
    projects: 2,
    status: 'inactive',
  },
];

// 模拟团队成员数据
const mockMembers = {
  '1': [
    { id: 'm1', name: t('pages.key_865'), role: t('pages.key_523'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm2', name: t('pages.key_1327'), role: t('pages.key_775'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm3', name: t('pages.key_1612'), role: t('pages.key_775'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm4', name: t('pages.key_2339'), role: t('pages.key_1092'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm5', name: t('pages.key_2490'), role: t('pages.key_278'), avatar: 'https://via.placeholder.com/40' },
  ],
  '2': [
    { id: 'm6', name: t('pages.key_643'), role: t('pages.key_523'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm7', name: t('pages.key_865'), role: t('pages.key_942'), avatar: 'https://via.placeholder.com/40' },
    // 更多成员...
  ],
  '3': [
    { id: 'm13', name: t('pages.key_505'), role: t('pages.key_523'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm14', name: t('pages.key_865'), role: t('pages.key_942'), avatar: 'https://via.placeholder.com/40' },
    // 更多成员...
  ],
  '4': [
    { id: 'm20', name: t('pages.key_865'), role: t('pages.key_523'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm21', name: t('pages.key_500'), role: t('pages.key_942'), avatar: 'https://via.placeholder.com/40' },
    { id: 'm22', name: t('pages.key_2474'), role: t('pages.key_942'), avatar: 'https://via.placeholder.com/40' },
  ],
};

const TeamsTab = () => {
  const [teams, setTeams] = useState(mockTeams);
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  const handleViewTeam = (teamId: string) => {
    setSelectedTeam(teamId);
  };

  const handleCloseTeamView = () => {
    setSelectedTeam(null);
  };

  const showCreateModal = () => {
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleCreateTeam = (values: any) => {
    const newTeam = {
      id: Date.now().toString(),
      name: values.name,
      role: t('pages.key_523'),
      members: 1,
      projects: 0,
      status: 'active',
    };
    setTeams([...teams, newTeam]);
    setIsModalVisible(false);
    message.success(t('pages.key_518'));
  };

  const teamColumns = [
    {
      title: t('pages.key_519'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('pages.key_930'),
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={role === t('pages.key_523') ? 'blue' : 'green'}>{role}</Tag>
      ),
    },
    {
      title: t('pages.key_943'),
      dataIndex: 'members',
      key: 'members',
    },
    {
      title: t('pages.key_2551'),
      dataIndex: 'projects',
      key: 'projects',
    },
    {
      title: t('pages.key_1602'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'gray'}>
          {status === 'active' ? t('pages.key_1507') : t('pages.key_2537')}
        </Tag>
      ),
    },
    {
      title: t('pages.key_1058'),
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleViewTeam(record.id)}>{t('pages.key_1346')}</Button>
        </Space>
      ),
    },
  ];

  const memberColumns = [
    {
      title: t('pages.key_942'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.avatar} icon={<UserOutlined />} style={{ marginRight: 8 }} />
          {text}
        </div>
      ),
    },
    {
      title: t('pages.key_2067'),
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={role === t('pages.key_523') ? 'blue' : 'green'}>{role}</Tag>
      ),
    },
    {
      title: t('pages.key_1058'),
      key: 'action',
      render: (_: any, _record: any) => (
        // 使用下划线前缀表示故意不使用的参数
        <Space size="middle">
          <Button type="link">{t('pages.key_403')}</Button>
        </Space>
      ),
    },
  ];

  const selectedTeamData = selectedTeam ? teams.find(team => team.id === selectedTeam) : null;
  const teamMembers = selectedTeam ? mockMembers[selectedTeam as keyof typeof mockMembers] : [];

  return (
    <div className="teams-tab">
      {selectedTeam ? (
        <Card
          title={selectedTeamData?.name}
          extra={<Button onClick={handleCloseTeamView}>{t('pages.key_2402')}</Button>}
          bordered={false}
        >
          <div className="team-info" style={{ marginBottom: 24 }}>
            <p><strong>您的角色:</strong> {selectedTeamData?.role}</p>
            <p><strong>成员数:</strong> {selectedTeamData?.members}</p>
            <p><strong>项目数:</strong> {selectedTeamData?.projects}</p>
            <p><strong>状态:</strong> {selectedTeamData?.status === 'active' ? t('pages.key_1507') : t('pages.key_2537')}</p>
          </div>
          <h3>{t('pages.key_520')}</h3>
          <Table
            columns={memberColumns}
            dataSource={teamMembers}
            rowKey="id"
            pagination={false}
          />
        </Card>
      ) : (
        <Card
          title={t('pages.key_944')}
          extra={<Button type="primary" icon={<PlusOutlined />} onClick={showCreateModal}>{t('pages.key_244')}</Button>}
          bordered={false}
        >
          <Table
            columns={teamColumns}
            dataSource={teams}
            rowKey="id"
            pagination={{ pageSize: 5 }}
          />
        </Card>
      )}

      <Modal
        title={t('pages.key_247')}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateTeam}
        >
          <Form.Item
            name="name"
            label={t('pages.key_519')}
            rules={[{ required: true, message: t('pages.key_2185') }]}
          >
            <Input placeholder={t('pages.key_2185')} />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('pages.key_521')}
          >
            <Input.TextArea rows={4} placeholder={t('pages.key_2186')} />
          </Form.Item>
          <Form.Item
            name="type"
            label={t('pages.key_522')}
          >
            <Select placeholder={t('pages.key_2254')}>
              <Select.Option value="ad">{t('pages.key_788')}</Select.Option>
              <Select.Option value="creative">{t('pages.key_277')}</Select.Option>
              <Select.Option value="data">{t('pages.key_1091')}</Select.Option>
              <Select.Option value="other">{t('pages.key_193')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>{t('pages.key_236')}</Button>
            <Button onClick={() => setIsModalVisible(false)}>{t('pages.key_407')}</Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TeamsTab;
