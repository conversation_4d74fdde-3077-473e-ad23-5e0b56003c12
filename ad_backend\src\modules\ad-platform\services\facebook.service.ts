// 统一 FacebookService，导出 facebook/facebook.service.ts 的 FacebookService
export { FacebookService as FacebookService2 } from '../facebook/facebook.service';

import { Injectable } from '@nestjs/common';
import { axiosProxyPost } from '../../../utils/axios-proxy';

@Injectable()
export class FacebookService {
    async createCampaignOnFacebook(input: any, accessToken: string, adAccountId: string) {
        const url = `https://graph.facebook.com/v23.0/act_${adAccountId}/campaigns`;
        const payload = {
            name: input.name,
            objective: input.objective,
            status: input.status,
            special_ad_categories: input.specialAdCategories || [],
            access_token: accessToken,
        };
        console.log(1111)
        try {
            const { data } = await axiosProxyPost(url, payload);
            console.log(data, 2222)
            return data; // { id: 'facebook_campaign_id' }
        } catch (e) {
            console.error('Facebook API 创建广告系列失败:', e?.response?.data || e);
            throw e;
        }
    }

    async updateCampaignOnFacebook(
        facebookCampaignId: string,
        update: { name?: string; objective?: string; status?: string; special_ad_categories?: string[] },
        accessToken: string
    ) {
        const url = `https://graph.facebook.com/v23.0/${facebookCampaignId}`;
        const payload = {
            ...update,
            access_token: accessToken,
        };
        try {
            const { data } = await axiosProxyPost(url, payload);
            console.log('Facebook 广告系列已同步:', data);
            return data;
        } catch (e) {
            console.error('Facebook API 更新广告系列失败:', e?.response?.data || e);
            throw e;
        }
    }

    async deleteCampaignOnFacebook(facebookCampaignId: string, accessToken: string) {
        const url = `https://graph.facebook.com/v23.0/${facebookCampaignId}`;
        const payload = {
            status: 'DELETED',
            access_token: accessToken,
        };
        try {
            const { data } = await axiosProxyPost(url, payload);
            console.log('Facebook 广告系列已删除:', data);
            return data;
        } catch (e) {
            console.error('Facebook API 删除广告系列失败:', e?.response?.data || e);
            throw e;
        }
    }
} 