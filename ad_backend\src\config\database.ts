import * as path from 'path';
import { TlsOptions } from 'tls';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { Tenant, Organization } from '../entity';
import { ConnectionManager, getConnectionManager } from 'typeorm';
import { Employee } from '../modules/employee/employee.entity';
import { AntiBlock } from '../modules/anti-block/antiBlock.entity';
import { ipRestriction } from '../modules/ip-restriction/ipRestriction.entity';
import { materialManagement } from '../modules/material-management/materialManagement.entity';
import { materiaCreate } from '../modules/material-create/materiaCreate.entity';
import { Group } from '../modules/group/group.entity';
import { Ad } from '../modules/ad-platform/entities/ad.entity';
import { AdAccount } from '../modules/ad-platform/entities/ad-account.entity';
import { AdCampaign } from '../modules/ad-platform/entities/ad-campaign.entity';
import { AdCreative } from '../modules/ad-platform/entities/ad-creative.entity';
import { AdSet } from '../modules/ad-platform/entities/adset.entity';
import { Audience } from '../modules/ad-platform/entities/audience.entity';
import { Material } from '../modules/ad-platform/entities/material.entity';
import { Analysis } from '../modules/analysis/analysis.entity';
import { Vcc } from '../modules/ad-platform/entities/vcc.entity';
import { VccCard } from '../modules/vcc/entities/vcc-card.entity';
import { VccTransaction } from '../modules/vcc/entities/vcc-transaction.entity';
import { Pixel } from '../modules/ad-platform/entities/pixel.entity';
import { Route } from '../modules/route/route.entity';
import { Role } from '../modules/role/role.entity';
import { User } from '../modules/user/user.entity';
import { LandingPage } from '../modules/landing-page/landing-page.entity';
import { Promotion } from '../modules/ad-platform/entities/promotion.entity';
import { Campaign } from '../modules/ad-platform/entities/campaign.entity';
import { Report } from '../modules/ad-platform/report/report.entity';
import { Domain } from '../entity/domain.entity';

export const Entities = [
	path.join(__dirname, '../**/*.entity.{ts,js}')
];

@Injectable()
export class TypeOrmConfigService implements TypeOrmOptionsFactory {
	constructor(private readonly configService: ConfigService) { }

	async createTypeOrmOptions(): Promise<TypeOrmModuleOptions> {
		const connectionManager: ConnectionManager = getConnectionManager();
		let options: any;

		const dbType = this.configService.get<string>('config.dbType');

		if (connectionManager.has('default')) {
			options = connectionManager.get('default').options;
			await connectionManager.get('default').close();
		} else {
			if (dbType == 'sqljs') {
				options = {
					type: dbType,
					autoSave: true,
					location: 'ever.db',
					logging: 'all',
					logger: 'file', // Removes console logging, instead logs all queries in a file ormlogs.log
					entities: Entities,
					synchronize:
						process.env.DB_SYNCHRONIZE === 'true' ? true : false, // We are using migrations, synchronize should be set to false.
				};
			} else if (dbType == 'sqlite') {
				const dbPath =
					process.env.DB_PATH ||
					path.join(process.cwd(), 'ever.sqlite3');

				options = {
					type: dbType,
					database: dbPath,
					logging: 'all',
					logger: 'file', // Removes console logging, instead logs all queries in a file ormlogs.log
					entities: Entities,
					synchronize:
						process.env.DB_SYNCHRONIZE === 'true' ? true : false, // We are using migrations, synchronize should be set to false.
				};
			} else if (dbType == 'postgres') {
				const ssl =
					process.env.DB_SSL_MODE === 'true' ? true : undefined;

				let sslParams: TlsOptions;

				if (ssl) {
					const base64data = process.env.DB_CA_CERT;
					const buff = Buffer.from(base64data, 'base64');
					const sslCert = buff.toString('ascii');

					sslParams = {
						rejectUnauthorized: true,
						ca: sslCert,
					};
				}

				options = {
					type: dbType,
					host: process.env.DB_HOST || 'localhost',
					username: process.env.DB_USER || 'postgres',
					password: process.env.DB_PASS || 'root',
					ssl: ssl ? sslParams : undefined,
					database: process.env.DB_NAME || 'ever',
					port: process.env.DB_PORT
						? parseInt(process.env.DB_PORT, 10)
						: 5432,
					entities: Entities,
					// autoLoadEntities: true, // Note: possible to use instead: entities: Entities,
					logging: 'all',
					logger: 'file', // Removes console logging, instead logs all queries in a file ormlogs.log
					synchronize: true,//同步，使用迁移文件
					uuidExtension: 'pgcrypto',
				} as TypeOrmModuleOptions;
			} else {
				throw new Error(
					`Critical: DB of type ${dbType} not supported yet`
				);
			}
		}
		return options;
	}
}
