import { gql } from '@apollo/client';

export const TENANT_LIST = gql`
  query Tenants($search: String, $page: Int, $limit: Int) {
    tenants(search: $search, page: $page, limit: $limit) {
      total
      nodes {
        id
        name
        type
        logo
        db_config
        sync_config
      }
    }
  }
`;

export const TENANT_DETAIL = gql`
  query Tenant($id: ID!) {
    tenant(id: $id) {
      id
      name
      type
      logo
      db_config
      sync_config
      plan
      features
    }
  }
`;

export const CREATE_TENANT = gql`
  mutation CreateTenant($input: CreateTenantInput!) {
    createTenant(input: $input) {
      id
      name
    }
  }
`;

export const UPDATE_TENANT = gql`
  mutation UpdateTenant($input: UpdateTenantInput!) {
    updateTenant(input: $input) {
      id
      name
    }
  }
`;

export const DELETE_TENANT = gql`
  mutation DeleteTenant($id: ID!) {
    deleteTenant(id: $id)
  }
`; 