import { MigrationInterface, QueryRunner } from "typeorm";

export class AddRemarkToAdAccount20240530000400 implements MigrationInterface {
    name = 'AddRemarkToAdAccount20240530000400'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "remark" character varying(255);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "remark";`);
    }
} 