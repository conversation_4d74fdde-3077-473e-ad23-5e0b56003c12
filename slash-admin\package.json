{"name": "slash-admin", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://github.com/d3george/slash-admin", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "preinstall": "lefthook install"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^5.6.1", "@apollo/client": "^3.13.8", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fontsource-variable/inter": "^5.2.5", "@fontsource-variable/open-sans": "^5.2.5", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/timeline": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@iconify/react": "^4.1.1", "@iconify/utils": "^2.3.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.76.2", "@tanstack/react-query-devtools": "^5.76.2", "@vanilla-extract/css": "^1.17.2", "@vanilla-extract/vite-plugin": "^4.0.20", "@vercel/analytics": "^1.5.0", "ahooks": "^3.8.5", "antd": "^5.25.2", "apexcharts": "^4.7.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "color": "^4.2.3", "dayjs": "^1.11.13", "graphql": "^16.11.0", "highlight.js": "^11.11.1", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "input-otp": "^1.4.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "motion": "^12.12.2", "numeral": "^2.0.6", "openai": "^5.3.0", "qrcode.react": "^4.2.0", "ramda": "^0.29.1", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-dom": "^18.3.1", "react-error-boundary": "^4.1.2", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-i18next": "^13.5.0", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-router": "^7.6.0", "react-use": "^17.6.0", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "reset-css": "^5.0.2", "screenfull": "^6.0.2", "sonner": "^1.7.4", "styled-components": "^6.1.18", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.25.28", "zustand": "^4.5.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@faker-js/faker": "^8.4.1", "@types/autosuggest-highlight": "^3.2.3", "@types/color": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^22.15.21", "@types/numeral": "^2.0.5", "@types/ramda": "^0.29.12", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/styled-components": "^5.1.34", "@types/xlsx": "^0.0.36", "@vitejs/plugin-react": "^4.5.0", "lefthook": "^1.11.13", "msw": "^2.8.4", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^4.1.7", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": "20.*"}, "packageManager": "pnpm@10.8.0", "msw": {"workerDirectory": "public"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}