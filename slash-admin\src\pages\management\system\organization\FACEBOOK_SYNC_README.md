# 📊 VCC卡片Facebook数据同步功能使用指南

## 🎯 功能概述

VCC卡片可以绑定Facebook广告账户，自动同步获取以下三项关键数据：

- **💰 消费金额** - Facebook广告实际花费
- **🖱️ 交易数** - 基于广告点击数统计
- **🎯 广告号存活** - 活跃账户数量/总账户数

## 🔧 使用步骤

### 1. Facebook广告账户授权

首先需要到 **广告账户管理页面** 进行OAuth授权：

1. 进入 `管理 → 广告账户管理 → OAuth授权` 页面
2. 点击未授权账户的 **"去授权"** 按钮
3. 跳转到Facebook页面完成授权
4. 授权成功后，账户状态变为 **"已授权"**

### 2. VCC卡片绑定Facebook账户

在VCC卡片管理页面绑定已授权的广告账户：

1. 点击VCC卡片的 **"绑定账号"** 按钮
2. 在下拉框中选择已授权的Facebook广告账户
3. 可以选择多个账户进行绑定
4. 点击 **"确定"** 完成绑定

### 3. 同步Facebook数据

绑定完成后即可同步Facebook数据：

#### 单个卡片同步：
- 点击VCC卡片操作列的 **🔄 同步按钮**
- 系统会调用Facebook Marketing API获取实时数据
- 同步完成后弹出详细结果页面

#### 批量同步：
- 点击页面顶部的 **"同步全部Facebook数据"** 按钮
- 系统会自动同步所有绑定了Facebook账户的VCC卡片
- 显示成功和失败的统计结果

## 📊 数据说明

### 同步的数据项：

| 数据项 | 说明 | 来源 |
|--------|------|------|
| **总消费** | 绑定的所有Facebook账户的广告花费总和 | Facebook Insights API `spend` |
| **交易数** | 绑定的所有Facebook账户的广告点击总数 | Facebook Insights API `clicks` |
| **展示次数** | 绑定的所有Facebook账户的广告展示总数 | Facebook Insights API `impressions` |
| **广告号存活** | 状态为ACTIVE的账户数/总账户数 | Facebook API `account_status` |

### 数据更新：

同步成功后，以下VCC卡片字段会被更新：
- `consumption` ← Facebook总消费
- `transactionCount` ← Facebook总点击数
- `adAccountStatus` ← 活跃账户数量
- `fbRealTimeSpend` ← Facebook实时消费
- `fbSyncStatus` ← 同步状态
- `fbLastSyncTime` ← 最后同步时间

## 🖥️ 界面显示

### 表格列显示：

1. **消费列**：
   - 上行：本地消费金额
   - 下行：Facebook实时消费（蓝色显示）
   - 同步状态标签（已同步/同步中/同步失败/待同步）

2. **交易数列**：
   - 上行：交易数数值
   - 下行：最后同步时间

3. **广告号存活列**：
   - 上行：本地数据（活跃数/总数）
   - 下行：Facebook数据（活跃数/总数，蓝色显示）

## ⚠️ 注意事项

### 前置条件：
1. Facebook广告账户必须已完成OAuth授权
2. OAuth令牌必须有 `ads_read` 权限
3. VCC卡片必须已绑定Facebook广告账户

### 常见错误：
1. **"未绑定Facebook广告账户"** - 需要先绑定账户
2. **"没有有效的OAuth授权信息"** - 需要到广告账户管理页面重新授权
3. **"Facebook访问令牌无效"** - OAuth令牌已过期，需要重新授权
4. **"API调用频率过高"** - 请稍后再试，避免频繁同步

### 性能考虑：
- 批量同步时每批处理5个账户，避免API限制
- 单个账户同步间隔1秒，避免频率限制
- 建议定期同步，不要过于频繁

## 🔗 相关页面

- **VCC卡片管理**：`管理 → 系统管理 → 组织管理`
- **广告账户管理**：`管理 → 广告账户管理`
- **OAuth授权**：`管理 → 广告账户管理 → OAuth授权`

## 📞 技术支持

如遇到同步问题，请检查：
1. Facebook账户授权状态
2. OAuth令牌有效性
3. 网络连接状态
4. Facebook API服务状态

详细错误信息会在同步失败时显示，请根据提示进行相应处理。 