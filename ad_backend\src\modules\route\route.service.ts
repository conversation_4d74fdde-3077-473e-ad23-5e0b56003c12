import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TreeRepository } from 'typeorm';
import { Route } from './route.entity';
import { CreateRouteDto } from './dto/create-route.dto';
import { UpdateRouteDto } from './dto/update-route.dto';

@Injectable()
export class RouteService {
    constructor(
        @InjectRepository(Route)
        private routeRepository: TreeRepository<Route>
    ) { }

    async findAll(): Promise<Route[]> {
        return this.routeRepository.findTrees();
    }

    async findOne(id: string): Promise<Route> {
        const route = await this.routeRepository.findOne({ where: { id } });
        if (!route) {
            throw new NotFoundException(`Route with ID "${id}" not found`);
        }
        return route;
    }

    async create(createRouteDto: CreateRouteDto): Promise<Route> {
        // 如果有父节点，检查父节点是否存在且类型是否为menu
        if (createRouteDto.parentId) {
            const parent = await this.findOne(createRouteDto.parentId);
            if (parent.type !== 'menu') {
                throw new BadRequestException('Only menu type routes can have children');
            }
        }

        const route = this.routeRepository.create(createRouteDto);
        return this.routeRepository.save(route);
    }

    async update(id: string, updateRouteDto: UpdateRouteDto): Promise<Route> {
        const route = await this.findOne(id);
        Object.assign(route, updateRouteDto);
        return this.routeRepository.save(route);
    }

    async remove(id: string): Promise<void> {
        const route = await this.findOne(id);
        await this.routeRepository.remove(route);
    }

    // 新增清空所有路由数据的方法
    async clearAll(): Promise<void> {
        await this.routeRepository.clear();
    }
} 