import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { useQuery } from '@apollo/client';
import { GET_AD_PLATFORM_DISTRIBUTION } from '@/api/adPlatformDistribution.graphql';

type AdPlatformDistributionProps = {
  startDate: string;
  endDate: string;
};

const AdPlatformDistribution = ({ startDate, endDate }: AdPlatformDistributionProps) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(GET_AD_PLATFORM_DISTRIBUTION, {
    variables: { startDate, endDate },
    fetchPolicy: 'network-only',
  });

  // 兜底空数据，保证hooks顺序一致
  const platformDistribution = data?.adPlatformDistribution?.platformDistribution ?? [];
  const platformPerformance = data?.adPlatformDistribution?.platformPerformance ?? { platforms: [], ctr: [], cvr: [], cpc: [], roi: [] };

  const distributionChartOptions = useChart({
    labels: platformDistribution.map((item: any) => item.name),
    legend: { position: 'bottom' },
    tooltip: { fillSeriesColor: false, y: { formatter: (value: number) => `${value}%` } },
    stroke: { show: false },
    colors: ['#3b82f6', '#4ade80', '#f59e0b', '#ec4899', '#a855f7'],
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          labels: {
            show: true,
            total: { show: true, label: t('pages.key_916'), formatter: () => '100%' },
          },
        },
      },
    },
  });

  const radarChartOptions = useChart({
    xaxis: { categories: [t('pages.key_1587'), t('pages.key_2359'), t('pages.key_1583'), 'ROI'] },
    yaxis: { show: false },
    markers: { size: 4 },
    stroke: { width: 2 },
    fill: { opacity: 0.2 },
    colors: ['#3b82f6', '#4ade80', '#f59e0b', '#ec4899', '#a855f7'],
  });

  const radarSeries = platformPerformance.platforms.map((platform: string, index: number) => ({
    name: platform,
    data: [
      platformPerformance.ctr[index] * 2.5,
      platformPerformance.cvr[index] * 4,
      10 - platformPerformance.cpc[index] * 2,
      platformPerformance.roi[index] / 40
    ]
  }));

  const roiChartOptions = useChart({
    xaxis: { categories: platformPerformance.platforms },
    yaxis: { labels: { formatter: (value: number) => `${value}%` } },
    tooltip: { y: { formatter: (value: number) => `${value}%` } },
    colors: ['#3b82f6'],
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: '60%',
        dataLabels: { position: 'top' },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val}%`,
      offsetY: -20,
      style: { fontSize: '12px', colors: ['#304758'] },
    },
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;
  if (!data?.adPlatformDistribution) return <div>{t('pages.key_1177')}</div>;

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">{t('pages.key_784')}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="distribution">
          <TabsList className="mb-4">
            <TabsTrigger value="distribution">{t('pages.key_761')}</TabsTrigger>
            <TabsTrigger value="comparison">{t('pages.key_1080')}</TabsTrigger>
            <TabsTrigger value="roi">ROI分析</TabsTrigger>
          </TabsList>

          <TabsContent value="distribution" className="h-[300px]">
            <Chart
              type="donut"
              series={platformDistribution.map((item: any) => item.value)}
              options={distributionChartOptions}
              height={300}
            />
          </TabsContent>

          <TabsContent value="comparison" className="h-[300px]">
            <Chart
              type="radar"
              series={radarSeries}
              options={radarChartOptions}
              height={300}
            />
          </TabsContent>

          <TabsContent value="roi" className="h-[300px]">
            <Chart
              type="bar"
              series={[{ name: 'ROI', data: platformPerformance.roi }]}
              options={roiChartOptions}
              height={300}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AdPlatformDistribution;
