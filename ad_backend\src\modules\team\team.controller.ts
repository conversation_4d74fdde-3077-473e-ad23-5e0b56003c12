import { BadRequestException, Req } from '@nestjs/common';
import { Controller } from '@nestjs/common';
import { TeamService } from './team.service';

@Controller('api/teams')
export class TeamController {
    constructor(private readonly service: TeamService) { }
    async getMany(@Req() req) {
        if (!req.query.tenantId) throw new BadRequestException('tenantId required');
        // TeamService 暂无 find 方法，返回空数组或自定义实现
        return [];
    }
} 