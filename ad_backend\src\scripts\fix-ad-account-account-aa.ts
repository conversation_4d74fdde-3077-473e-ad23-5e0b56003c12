import { DataSource } from 'typeorm';
import { AdAccount } from '../modules/ad-platform/entities/ad-account.entity';
import { AppDataSource } from '../config/data-source';

async function fixAdAccountAccountToAA() {
    const dataSource: DataSource = AppDataSource;
    if (!dataSource.isInitialized) {
        await dataSource.initialize();
    }
    const repo = dataSource.getRepository(AdAccount);
    const result = await repo.createQueryBuilder()
        .update(AdAccount)
        .set({ account: 'aa' })
        .where('account IS NULL')
        .execute();
    console.log(`已修复 ${result.affected} 条 account 字段为 null 的 ad_account 记录，全部改为 'aa'。`);
    await dataSource.destroy();
}

fixAdAccountAccountToAA().catch(e => {
    console.error(e);
    process.exit(1);
}); 