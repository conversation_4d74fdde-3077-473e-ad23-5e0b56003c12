import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ad } from './entities/ad.entity';
import { ObjectType, Field, Float } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@ObjectType()
class PlatformDistributionItem {
    @Field() name: string;
    @Field(() => Float) value: number;
}

@ObjectType()
class PlatformPerformance {
    @Field(() => [String]) platforms: string[];
    @Field(() => [Float]) ctr: number[];
    @Field(() => [Float]) cvr: number[];
    @Field(() => [Float]) cpc: number[];
    @Field(() => [Float]) roi: number[];
}

@ObjectType()
class AdPlatformDistributionDTO {
    @Field(() => [PlatformDistributionItem]) platformDistribution: PlatformDistributionItem[];
    @Field(() => PlatformPerformance) platformPerformance: PlatformPerformance;
}

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => AdPlatformDistributionDTO)
export class AdPlatformDistributionResolver {
    constructor(
        @InjectRepository(Ad)
        private readonly adRepo: Repository<Ad>,
    ) { }

    @Query(() => AdPlatformDistributionDTO)
    async adPlatformDistribution(
        @Args('startDate') startDate: string,
        @Args('endDate') endDate: string,
        @Context() ctx: any,
    ): Promise<AdPlatformDistributionDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? {} : { tenant: { id: tenantId } };
        // 查询所有广告，按平台分组
        const ads = await this.adRepo.find({ where });
        const platformMap = new Map<string, Ad[]>();
        for (const ad of ads) {
            if (!ad.platform) continue;
            if (!platformMap.has(ad.platform)) platformMap.set(ad.platform, []);
            platformMap.get(ad.platform)!.push(ad);
        }
        // 平台分布（按广告数量占比）
        const total = ads.length;
        const platformDistribution: PlatformDistributionItem[] = [];
        for (const [platform, group] of platformMap.entries()) {
            platformDistribution.push({
                name: platform,
                value: total > 0 ? Math.round((group.length / total) * 1000) / 10 : 0, // 百分比
            });
        }
        // 平台效果
        const platforms: string[] = [];
        const ctr: number[] = [];
        const cvr: number[] = [];
        const cpc: number[] = [];
        const roi: number[] = [];
        for (const [platform, group] of platformMap.entries()) {
            let impressions = 0, clicks = 0, conversions = 0, cost = 0, revenue = 0;
            for (const ad of group) {
                impressions += Number(ad.insight?.impressions || 0);
                clicks += Number(ad.insight?.clicks || 0);
                conversions += Number(ad.insight?.conversions || 0);
                cost += Number(ad.insight?.spend || 0);
                revenue += Number(ad.insight?.revenue || 0);
            }
            platforms.push(platform);
            ctr.push(impressions > 0 ? Math.round((clicks / impressions) * 1000) / 10 : 0);
            cvr.push(clicks > 0 ? Math.round((conversions / clicks) * 1000) / 10 : 0);
            cpc.push(clicks > 0 ? Math.round((cost / clicks) * 100) / 100 : 0);
            roi.push(cost > 0 ? Math.round((revenue / cost) * 100) / 1 : 0);
        }
        return {
            platformDistribution,
            platformPerformance: { platforms, ctr, cvr, cpc, roi },
        };
    }
} 