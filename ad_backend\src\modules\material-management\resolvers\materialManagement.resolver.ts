import { QueryService, InjectQueryService } from '@ptc-org/nestjs-query-core';
import { CRUDResolver, InjectPubSub, PagingStrategies } from '@ptc-org/nestjs-query-graphql';
import { Resolver, Query, Args, ObjectType, Field, Int, Context } from '@nestjs/graphql';
import { PubSub } from 'graphql-subscriptions';
import { materialManagementDTO } from '../dto/materialManagement.dto';
import { materialManagement } from '../materialManagement.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/auth/jwt-auth.guard';
import { TenantGuard } from 'src/guards/tenant.guard';
import { CreativeTrendDTO, PieDataDTO } from '../../ad-platform/entities/creative-analysis.dto';

@ObjectType()
export class CreativeAnalysisItem {
	@Field()
	key: string;
	@Field()
	label: string;
	@Field(() => Int)
	impressions: number;
	@Field(() => Int)
	clicks: number;
	@Field(() => Int)
	conversions: number;
	@Field(() => Number)
	ctr: number;
	@Field(() => Number)
	cvr: number;
	@Field(() => Number)
	spend: number;
	@Field({ nullable: true })
	type?: string;
}

@ObjectType()
export class CreativeAnalysisResult {
	@Field(() => [CreativeTrendDTO])
	typeTrends: CreativeTrendDTO[];

	@Field(() => [PieDataDTO])
	sizePerformance: PieDataDTO[];

	@Field(() => [PieDataDTO])
	elementPerformance: PieDataDTO[];
}

@Resolver(() => materialManagementDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class materialManagementResolver extends CRUDResolver(materialManagementDTO, {
	delete: { disabled: true },
	enableAggregate: true,
	enableTotalCount: true,
	pagingStrategy: PagingStrategies.OFFSET
}) {
	constructor(
		@InjectQueryService(materialManagement) readonly service: QueryService<materialManagement>,
		@InjectPubSub() readonly pubSub: PubSub
	) {
		super(service);
	}

	@Query(() => CreativeAnalysisResult)
	async creativeAnalysis(
		@Context() ctx: any,
		@Args('startDate') startDate: string,
		@Args('endDate') endDate: string,
	): Promise<CreativeAnalysisResult> {
		const isSuperAdmin = ctx.isSuperAdmin;
		const tenantId = ctx.user?.tenantId || ctx.tenantId;
		if (!tenantId && !isSuperAdmin) throw new Error('未获取到租户ID');
		let materials;
		if (isSuperAdmin) {
			materials = await this.service.query({});
		} else {
			const normalizedTenantId = String(tenantId).trim();
			materials = await this.service.query({ filter: { tenantId: { eq: normalizedTenantId } } });
		}
		console.log('creativeAnalysis materials:', materials);

		// 按类型统计趋势
		const typeMap: Record<string, number[]> = {};
		const start = new Date(startDate);
		const end = new Date(endDate);
		const months = Math.ceil((end.getTime() - start.getTime()) / (30 * 24 * 60 * 60 * 1000));

		for (let i = 0; i < months; i++) {
			const month = new Date(start.getFullYear(), start.getMonth() + i, 1);
			const monthMaterials = materials.filter((m: any) => {
				const createdAt = new Date(m.createdAt);
				return createdAt.getMonth() === month.getMonth() &&
					createdAt.getFullYear() === month.getFullYear();
			});

			monthMaterials.forEach((m: any) => {
				const type = m.facebookImageId ? '图片' : m.facebookVedioId ? '视频' : '其他';
				if (!typeMap[type]) typeMap[type] = Array(months).fill(0);
				typeMap[type][i]++;
			});
		}

		const typeTrends = Object.entries(typeMap).map(([type, data]) => ({ type, data }));

		// 统计尺寸性能
		const sizeMap: Record<string, number> = {};
		materials.forEach((m: any) => {
			const size = m.raw?.size || '未知尺寸';
			sizeMap[size] = (sizeMap[size] || 0) + 1;
		});
		const sizePerformance = Object.entries(sizeMap).map(([name, value]) => ({ name, value }));

		// 统计元素性能
		const elementMap: Record<string, number> = {};
		materials.forEach((m: any) => {
			const elements: string[] = m.raw?.elements || [];
			elements.forEach(e => {
				elementMap[e] = (elementMap[e] || 0) + 1;
			});
		});
		const elementPerformance = Object.entries(elementMap).map(([name, value]) => ({ name, value }));

		return { typeTrends, sizePerformance, elementPerformance };
	}
}
