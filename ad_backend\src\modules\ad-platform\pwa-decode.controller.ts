import { Controller, Get, Query } from '@nestjs/common';

@Controller('api/pwa')
export class PwaDecodeController {
    @Get('decode')
    decode(@Query('data') data: string) {
        if (!data) {
            return { error: 'Missing data param' };
        }
        try {
            // 兼容 URL 传参空格变 + 问题
            const fixed = data.replace(/ /g, '+');
            const json = Buffer.from(fixed, 'base64').toString('utf-8');
            try {
                const obj = JSON.parse(json);
                return obj;
            } catch (e) {
                return { error: 'Invalid JSON', detail: e.message, raw: json };
            }
        } catch (e) {
            return { error: 'Invalid base64', detail: e.message };
        }
    }
} 