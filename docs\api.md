# 广告管理系统接口文档

> 本文档主要覆盖 GraphQL 接口，REST 接口可通过 `/api` 路径访问（如 `/api/ad-set`、`/api/ad` 等），用于历史兼容和部分同步服务。推荐优先使用 GraphQL。

## 认证与通用说明

- **认证方式**：JWT（Bearer Token），所有接口需在 Header 中携带 `Authorization: Bearer <token>`
- **GraphQL 入口**：`POST /graphql`
- **REST 入口**：如 `/api/ad-set`、`/api/ad` 等
- **Content-Type**：`application/json`
- **Schema 文件**：`ad_backend/generated/schemas/schema.graphql`
- **分页/筛选/排序**：所有 GraphQL 查询均支持 `filter`、`paging`、`sorting` 参数，详见 schema。

## 实体字段一览

### 广告账户

| 字段名       | 类型     | 必填  | 说明     |
| --------- | ------ | --- | ------ |
| id        | ID     | Y   | 广告账户ID |
| platform  | String | Y   | 平台类型   |
| accountId | String | Y   | 平台账户ID |
| name      | String | Y   | 账户名称   |
| tenant    | Tenant | Y   | 所属租户   |
| raw       | JSON   | N   | 原始数据   |
| status    | String | Y   | 状态     |

### 广告系列

| 字段名        | 类型        | 必填  | 说明     |
| ---------- | --------- | --- | ------ |
| id         | ID        | Y   | 广告系列ID |
| platform   | String    | Y   | 平台类型   |
| campaignId | String    | Y   | 平台系列ID |
| name       | String    | Y   | 系列名称   |
| adAccount  | AdAccount | Y   | 所属账户   |
| tenant     | Tenant    | Y   | 所属租户   |
| raw        | JSON      | N   | 原始数据   |
| status     | String    | Y   | 状态     |

### 广告组

| 字段名        | 类型         | 必填  | 说明     |
| ---------- | ---------- | --- | ------ |
| id         | ID         | Y   | 广告组ID  |
| platform   | String     | Y   | 平台类型   |
| adsetId    | String     | Y   | 平台原生ID |
| name       | String     | Y   | 广告组名称  |
| adAccount  | AdAccount  | Y   | 所属账户   |
| adCampaign | AdCampaign | Y   | 所属广告系列 |
| tenant     | Tenant     | Y   | 所属租户   |
| raw        | JSON       | N   | 原始数据   |
| status     | String     | Y   | 状态     |

### 广告

| 字段名        | 类型         | 必填  | 说明     |
| ---------- | ---------- | --- | ------ |
| id         | ID         | Y   | 广告ID   |
| platform   | String     | Y   | 平台     |
| adId       | String     | Y   | 平台广告ID |
| name       | String     | Y   | 广告名称   |
| adAccount  | AdAccount  | Y   | 所属账户   |
| adSet      | AdSet      | Y   | 所属广告组  |
| adCreative | AdCreative | N   | 所属创意   |
| tenant     | Tenant     | Y   | 所属租户   |
| raw        | JSON       | N   | 原始数据   |
| status     | String     | Y   | 状态     |

### 创意

| 字段名     | 类型     | 必填  | 说明    |
| ------- | ------ | --- | ----- |
| id      | ID     | Y   | 创意ID  |
| name    | String | Y   | 创意名称  |
| type    | String | Y   | 创意类型  |
| url     | String | N   | 素材URL |
| content | String | N   | 文本内容  |
| tenant  | Tenant | Y   | 所属租户  |
| raw     | JSON   | N   | 原始数据  |
| status  | String | Y   | 状态    |

### 受众

| 字段名       | 类型        | 必填  | 说明   |
| --------- | --------- | --- | ---- |
| id        | ID        | Y   | 受众ID |
| name      | String    | Y   | 受众名称 |
| adAccount | AdAccount | Y   | 所属账户 |
| tenant    | Tenant    | Y   | 所属租户 |
| raw       | JSON      | N   | 原始数据 |
| status    | String    | Y   | 状态   |

### 素材

| 字段名     | 类型     | 必填  | 说明    |
| ------- | ------ | --- | ----- |
| id      | ID     | Y   | 素材ID  |
| type    | String | Y   | 素材类型  |
| name    | String | Y   | 素材名称  |
| url     | String | N   | 素材URL |
| content | String | N   | 文本内容  |
| tenant  | Tenant | Y   | 所属租户  |
| raw     | JSON   | N   | 原始数据  |
| status  | String | Y   | 状态    |

### VCC

| 字段名         | 类型     | 必填  | 说明     |
| ----------- | ------ | --- | ------ |
| id          | ID     | Y   | VCC卡ID |
| cardNumber  | String | Y   | 卡号     |
| cardHolder  | String | Y   | 持卡人    |
| expiry      | String | Y   | 有效期    |
| cvv         | String | Y   | 安全码    |
| bindAccount | String | N   | 绑定账户   |
| tenant      | Tenant | Y   | 所属租户   |
| raw         | JSON   | N   | 原始数据   |
| status      | String | Y   | 状态     |

### 员工

| 字段名       | 类型       | 必填  | 说明   |
| --------- | -------- | --- | ---- |
| id        | ID       | N   | 员工ID |
| firstName | String   | N   | 名    |
| lastName  | String   | N   | 姓    |
| name      | String   | N   | 姓名   |
| createdAt | DateTime | N   | 创建时间 |
| updatedAt | DateTime | N   | 更新时间 |
| isActive  | Boolean  | Y   | 是否激活 |

### IP限制管理

| 字段名       | 类型       | 必填  | 说明    |
| --------- | -------- | --- | ----- |
| id        | Number   | N   | IP ID |
| ip        | String   | N   | IP地址  |
| status    | String   | N   | 状态    |
| operator  | String   | N   | 操作者   |
| userId    | String   | N   | 操作ID  |
| createdAt | DateTime | N   | 创建时间  |
| updatedAt | DateTime | N   | 更新时间  |

### 防封域名

| 字段名           | 类型       | 必填  | 说明    |
| ------------- | -------- | --- | ----- |
| id            | Number   | N   | 域名ID  |
| url           | String   | N   | 链接    |
| relatedDomain | String   | N   | 归属主域名 |
| name          | String   | N   | 名称    |
| createdAt     | DateTime | N   | 创建时间  |
| updatedAt     | DateTime | N   | 更新时间  |

### 群组

| 字段名         | 类型       | 必填  | 说明        |
| ----------- | -------- | --- | --------- |
| id          | ID       | Y   | 群组ID      |
| name        | String   | Y   | 群组名称      |
| description | String   | N   | 描述        |
| belongTo    | String   | N   | 归属人       |
| contactInfo | String   | N   | 联系方式      |
| memberCount | Int      | N   | 成员数量      |
| permissions | JSON     | N   | 权限配置      |
| status      | String   | Y   | 状态(正常/禁用) |
| createTime  | DateTime | N   | 创建时间      |
| updateTime  | DateTime | N   | 更新时间      |

### 素材管理

| 字段名           | 类型       | 必填  | 说明   |
| ------------- | -------- | --- | ---- |
| id            | Number   | Y   | 素材ID |
| fileName      | String   | N   | 文件名称 |
| tags          | String   | N   | 标签   |
| group         | String   | N   | 群组   |
| adCount       | String   | N   | 广告数  |
| activeAdCount | Int      | N   | 投放中  |
| bannedCount   | Int      | N   | 封禁数  |
| spend         | Int      | N   | 消耗   |
| conversion    | Int      | N   | 转换   |
| commission    | Int      | N   | 状佣金  |
| file          | Int      | N   | 文件   |
| tenantId      | String   | Y   | 客户Id |
| notes         | String   | Y   | 备注   |
| createTime    | DateTime | N   | 创建时间 |
| updateTime    | DateTime | N   | 更新时间 |

### 素材创意

| 字段名           | 类型       | 必填  | 说明   |
| ------------- | -------- | --- | ---- |
| id            | Number   | Y   | 创意ID |
| userId        | String   | N   | 创建人id |
| name          | String   | N   | 创意名称   |
| materials     | Number   | N   | 素材id   |
| pageLading    | String   | N   | 落地页  |
| createId      | Int      | N   | facebook创意id  |
| slogan        | Int      | N   | 文案  |
| account       | Int      | N   | facebook账户   |
| pageId         | Int      | N   | 主页id   |
| createTime    | DateTime | N   | 创建时间 |
| updateTime    | DateTime | N   | 更新时间 |

## GraphQL 接口示例

### 查询广告账户列表

```graphql
query {
  adAccounts(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { 
      id 
      name 
      status 
    }
    totalCount
  }
}
```

### 创建广告组

```graphql
mutation {
  createOneAdSet(input: {
    adSet: {
      name: "广告组A"
      platform: "facebook"
      adsetId: "adset123"
      adAccount: { id: "accountId" }
      adCampaign: { id: "campaignId" }
      tenant: { id: "tenantId" }
      status: "active"
    }
  }) {
    id
    name
  }
}
```

### 删除广告

```graphql
mutation {
  deleteOneAd(input: { id: "adId" }) {
    id
  }
}
```

### 查询素材列表

```graphql
query {
  materials(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { 
      id 
      name 
      status 
    }
    totalCount
  }
}
```

> 其它实体操作与上述类似，参数和返回字段详见 schema。

---

## 广告系列相关

### 字段定义

| 字段名        | 类型        | 必填  | 说明     |
| ---------- | --------- | --- | ------ |
| id         | ID        | Y   | 广告系列ID |
| platform   | String    | Y   | 平台类型   |
| campaignId | String    | Y   | 平台系列ID |
| name       | String    | Y   | 系列名称   |
| adAccount  | AdAccount | Y   | 所属账户   |
| tenant     | Tenant    | Y   | 所属租户   |
| raw        | JSON      | N   | 原始数据   |
| status     | String    | Y   | 状态     |

### 查询广告系列列表

```graphql
query {
  adCampaigns(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { id name status }
    totalCount
  }
}
```

### 创建广告系列

```graphql
mutation {
  createOneAdCampaign(input: {
    adCampaign: {
      name: "系列A"
      platform: "facebook"
      campaignId: "c123"
      adAccount: { id: "accountId" }
      tenant: { id: "tenantId" }
      status: "active"
    }
  }) {
    id
    name
  }
}
```

### 删除广告系列

```graphql
mutation {
  deleteOneAdCampaign(input: { id: "adCampaignId" }) {
    id
  }
}
```

---

## 广告组相关

### 字段定义

| 字段名        | 类型         | 必填  | 说明     |
| ---------- | ---------- | --- | ------ |
| id         | ID         | Y   | 广告组ID  |
| platform   | String     | Y   | 平台类型   |
| adsetId    | String     | Y   | 平台原生ID |
| name       | String     | Y   | 广告组名称  |
| adAccount  | AdAccount  | Y   | 所属账户   |
| adCampaign | AdCampaign | Y   | 所属广告系列 |
| tenant     | Tenant     | Y   | 所属租户   |
| raw        | JSON       | N   | 原始数据   |
| status     | String     | Y   | 状态     |

### 查询广告组列表

```graphql
query {
  adSets(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { id name status }
    totalCount
  }
}
```

### 删除广告组

```graphql
mutation {
  deleteOneAdSet(input: { id: "adsetId" }) {
    id
  }
}
```

---

## 广告创意相关

### 字段定义

| 字段名     | 类型     | 必填  | 说明    |
| ------- | ------ | --- | ----- |
| id      | ID     | Y   | 创意ID  |
| name    | String | Y   | 创意名称  |
| type    | String | Y   | 创意类型  |
| url     | String | N   | 素材URL |
| content | String | N   | 文本内容  |
| tenant  | Tenant | Y   | 所属租户  |
| raw     | JSON   | N   | 原始数据  |
| status  | String | Y   | 状态    |

### 查询创意列表

```graphql
query {
  adCreatives(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { id name status }
    totalCount
  }
}
```

### 创建创意

```graphql
mutation {
  createOneAdCreative(input: {
    adCreative: {
      name: "创意A"
      type: "image"
      url: "https://example.com/a.jpg"
      tenant: { id: "tenantId" }
      status: "active"
    }
  }) {
    id
    name
  }
}
```

### 删除创意

```graphql
mutation {
  deleteOneAdCreative(input: { id: "creativeId" }) {
    id
  }
}
```

---

## 素材（Material）相关

### 字段定义

| 字段名     | 类型     | 必填  | 说明    |
| ------- | ------ | --- | ----- |
| id      | ID     | Y   | 素材ID  |
| type    | String | Y   | 素材类型  |
| name    | String | Y   | 素材名称  |
| url     | String | N   | 素材URL |
| content | String | N   | 文本内容  |
| tenant  | Tenant | Y   | 所属租户  |
| raw     | JSON   | N   | 原始数据  |
| status  | String | Y   | 状态    |

### 查询素材列表

```graphql
query {
  materials(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { id name status }
    totalCount
  }
}
```

### 创建素材

```graphql
mutation {
  createOneMaterial(input: {
    material: {
      name: "素材A"
      type: "image"
      url: "https://example.com/a.jpg"
      tenant: { id: "tenantId" }
      status: "active"
    }
  }) {
    id
    name
  }
}
```

### 删除素材

```graphql
mutation {
  deleteOneMaterial(input: { id: "materialId" }) {
    id
  }
}
```

---

## VCC 相关

### 字段定义

| 字段名         | 类型     | 必填  | 说明     |
| ----------- | ------ | --- | ------ |
| id          | ID     | Y   | VCC卡ID |
| cardNumber  | String | Y   | 卡号     |
| cardHolder  | String | Y   | 持卡人    |
| expiry      | String | Y   | 有效期    |
| cvv         | String | Y   | 安全码    |
| bindAccount | String | N   | 绑定账户   |
| tenant      | Tenant | Y   | 所属租户   |
| raw         | JSON   | N   | 原始数据   |
| status      | String | Y   | 状态     |

### 查询VCC列表

```graphql
query {
  vccs(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { id cardNumber status }
    totalCount
  }
}
```

### 创建VCC

```graphql
mutation {
  createOneVcc(input: {
    vcc: {
      cardNumber: "****************"
      cardHolder: "张三"
      expiry: "12/29"
      cvv: "123"
      tenant: { id: "tenantId" }
      status: "active"
    }
  }) {
    id
    cardNumber
  }
}
```

### 删除VCC

```graphql
mutation {
  deleteOneVcc(input: { id: "vccId" }) {
    id
  }
}
```

---

## 员工（Employee）相关

### 字段定义

| 字段名       | 类型       | 必填  | 说明   |
| --------- | -------- | --- | ---- |
| id        | ID       | N   | 员工ID |
| firstName | String   | N   | 名    |
| lastName  | String   | N   | 姓    |
| name      | String   | N   | 姓名   |
| createdAt | DateTime | N   | 创建时间 |
| updatedAt | DateTime | N   | 更新时间 |
| isActive  | Boolean  | Y   | 是否激活 |

### 查询员工列表

```graphql
query {
  employees(filter: {}, paging: {limit: 10, offset: 0}) {
    nodes { id name isActive }
    totalCount
  }
}
```

### 创建员工

```graphql
mutation {
  createOneEmployee(input: {
    employee: {
      firstName: "李"
      lastName: "四"
      name: "李四"
      isActive: true
    }
  }) {
    id
    name
  }
}
```

> 注意：员工模块删除接口已禁用。

---

## 防封域名（AntiBlock）相关

### 字段定义

| 字段名           | 类型       | 必填  | 说明    |
| ------------- | -------- | --- | ----- |
| id            | number   | N   | 域名Id  |
| url           | String   | N   | 链接    |
| relatedDomain | String   | N   | 归属主域名 |
| name          | String   | N   | 名称    |
| createdAt     | DateTime | N   | 创建时间  |
| updatedAt     | DateTime | N   | 更新时间  |

### 查询列表

```graphql
query GetAntiBlocks(
  $paging: OffsetPaging
  $filter: AntiBlockFilter
  $sorting: [AntiBlockSort!]!
) {
  antiBlocks(
    paging: $paging
    filter: $filter
    sorting: $sorting
  ) {
    nodes {
      id
      url
      status
      relatedDomain
      createdAt
    }
    totalCount  
}
```

### 群组管理

### 字段定义

| 字段名         | 类型       | 必填  | 说明        |
| ----------- | -------- | --- | --------- |
| id          | ID       | Y   | 群组ID      |
| name        | String   | Y   | 群组名称      |
| description | String   | N   | 描述        |
| belongTo    | String   | N   | 归属人       |
| contactInfo | String   | N   | 联系方式      |
| memberCount | Int      | N   | 成员数量      |
| permissions | JSON     | N   | 权限配置      |
| status      | String   | Y   | 状态(正常/禁用) |
| createTime  | DateTime | N   | 创建时间      |
| updateTime  | DateTime | N   | 更新时间      |

### 查询群组列表

```graphql
query {
  groups(
    filter: {}, 
    paging: {limit: 10, offset: 0},
    sorting: [{field: "createTime", direction: DESC}]
  ) {
    nodes {
      id
      name
      description
      belongTo
      contactInfo
      memberCount
      status
      permissions
      createTime
      updateTime
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
  }
}
```

### 批量添加域名

```graphql
  mutation CreateManyAntiBlocks($input: CreateManyAntiBlocksInput!) {
    createManyAntiBlocks(input: $input) {
    id
    url
    status
    relatedDomain
    }
  }
```

### 删除域名

```graphql
  mutation DeleteAntiBlock($input: DeleteOneAntiBlockInput!) {
    deleteOneAntiBlock(input: $input) {
      id
    }
  }
```

---

## ip限制（IpRestriction）相关

### 字段定义

| 字段名       | 类型       | 必填  | 说明   |
| --------- | -------- | --- | ---- |
| id        | number   | N   | ipId |
| ip        | String   | N   | 链接   |
| status    | String   | N   | 状态   |
| operator  | String   | N   | 操作者  |
| userId    | String   | N   | 操作id |
| createdAt | DateTime | N   | 创建时间 |
| updatedAt | DateTime | N   | 更新时间 |

### 查询列表

```graphql
query GetipRestriction(
  $paging: OffsetPaging
  $filter: ipRestrictionFilter
  $sorting: [ipRestrictionSort!]!
) {
  ipRestrictions(
    paging: $paging
    filter: $filter
    sorting: $sorting
  ) {
    nodes {
      id
      ip
      status
      operator
      updatedAt
    }
    totalCount  
}
```

### 根据ID获取单个群组

```graphql
query {
  group(id: "群组ID") {
    id
    name
    description
    belongTo
    contactInfo
    memberCount
    status
    permissions
    createTime
    updateTime
  }
}
```

### 创建群组

```graphql
mutation {
  createOneGroup(
    input: {
      group: {
        name: "技术部"
        description: "负责系统开发和维护"
        belongTo: "张三"
        contactInfo: "***********"
        memberCount: 8
        status: "正常"
        permissions: {
          systems: ["system:user", "system:role", "system:group"],
          ads: ["ad:account", "ad:material"]
        }
      }
    }
  ) {
    id
    name
    description
    status
    createTime
  }
}
```

### 添加iP限制名单

```graphql
mutation CreateOneIpRestriction($input: CreateOneIpRestrictionInput!) {
  createOneIpRestriction(input: $input) {
      id
      ip
      userId
      status
      operator
}
```

### 更新群组

```graphql
mutation {
  updateOneGroup(
    input: {
      id: "群组ID",
      update: {
        description: "更新后的描述"
        memberCount: 15
        status: "禁用"
        permissions: {
          systems: ["system:user"],
          ads: ["ad:material", "ad:campaign"]
        }
      }
    }
  ) {
    id
    name
    description
    memberCount
    status
    permissions
    updateTime
  }
}
```

### 删除iP限制名单

```graphql
  mutation deleteOneIpRestriction($input: DeleteOneIpRestrictionInput!) {
    deleteOneIpRestriction(input: $input) {
      id
    }
  }
}
```

### 更新iP限制名单

```graphql
  mutation UpdateIpRestriction($input: UpdateOneIpRestrictionInput!) {
    updateOneIpRestriction(input: $input) {
      id
      ip
      userId
      operator
      status
      updatedAt
    }
  }
}

### 删除群组
```graphql
mutation {
  deleteOneGroup(
    input: { id: "群组ID" }
  ) {
    id
    name
  }
}
```

### 群组统计

```graphql
query {
  groupsAggregate {
    count {
      total
      status
    }
    sum {
      memberCount
    }
    avg {
      memberCount
    }
  }
}
```

---

## 素材管理相关

### 字段定义

| 字段名           | 类型       | 必填  | 说明                             |
| ------------- | -------- | --- | ------------------------------ |
| id            | Number   | Y   | 素材ID                           |
| fileName      | String   | N   | 文件名称                           |
| tags          | String   | N   | 标签                             |
| group         | String   | N   | 群组                             |
| adCount       | String   | N   | 广告数                            |
| activeAdCount | Int      | N   | 投放中                            |
| bannedCount   | Int      | N   | 封禁数                            |
| spend         | Int      | N   | 消耗                             |
| conversion    | Int      | N   | 转换                             |
| commission    | Int      | N   | 佣金 |
| file          | Int      | N   | 文件                             |
| tenantId      | String   | Y   | 客户Id                           |
| notes         | String   | Y   | 备注                             |
| createTime    | DateTime | N   | 创建时间                           |
| updateTime    | DateTime | N   | 更新时间                           |

### 新增素材

```graphql
mutation CreateOneMaterialManagement($input: CreateOneMaterialManagementInput!) {
  createOneMaterialManagement(input: $input) {
      id
      tenantId
      fileName
      tags
      group
      notes
      file
  }
}
```

### 删除素材

```graphql
  mutation deleteOneMaterialManagement($input: DeleteOneMaterialManagementInput!) {
    deleteOneMaterialManagement(input: $input) {
      id
    }
  }
```

### 查询素材管理列表

```graphql
query GetmaterialManagement(
  $paging: OffsetPaging
  $filter: materialManagementFilter
  $sorting: [materialManagementSort!]!
) {
  materialManagements(
    paging: $paging
    filter: $filter
    sorting: $sorting
  ) {
    nodes {
      id
      fileName
      tags
      group
      adCount
      activeAdCount
      bannedCount
      spend
      conversion
      commission
      file
      notes
      tenantId
      updatedAt
    }
    totalCount  
  }
}
```

## 创意管理相关

### 字段定义


| 字段名           | 类型       | 必填  | 说明   |
| ------------- | -------- | --- | ---- |
| id            | Number   | Y   | 创意ID |
| userId        | String   | N   | 创建人id |
| name          | String   | N   | 创意名称   |
| materials     | Number   | N   | 素材id   |
| pageLading    | String   | N   | 落地页  |
| createId      | Int      | N   | facebook创意id  |
| slogan        | Int      | N   | 文案  |
| account       | Int      | N   | facebook账户   |
| pageId         | Int      | N   | 主页id   |
| createTime    | DateTime | N   | 创建时间 |
| updateTime    | DateTime | N   | 更新时间 |

### 新增创意

```graphql
mutation CreateOneMateriaCreate($input: CreateOneMateriaCreateInput!) {
  createOneMateriaCreate(input: $input) {
        id
        name
        materials
        pageLading
        slogan
        account
        pageId
        createId
  }
}
```

### 删除创意

```graphql
  mutation deleteOneMateriaCreate($input: DeleteOneMateriaCreateInput!) {
    deleteOneMateriaCreate(input: $input) {
      id
    }
  }
```

## 其它说明

- 所有 GraphQL 接口支持分页、筛选、排序。
- 字段类型及参数请参考自动生成的 schema.graphql。
- 推荐使用 Apollo Client、GraphQL Playground 等工具调试。
- 部分模块如风控、报表等尚未完成REST到GraphQL转换，请通过REST接口访问（/api/ad-platform/risk、/api/ad-platform/report 等），后续会补充GraphQL支持。

---

## 其它说明

- 部分模块如风控、报表等尚未完成REST到GraphQL转换，请通过REST接口访问（/api/ad-platform/risk、/api/ad-platform/report 等），后续会补充GraphQL支持。
- 推荐使用 Apollo Client、GraphQL Playground、Postman、Insomnia 等工具调试接口。
- 字段类型及参数请参考自动生成的 schema.graphql。

---

## FAQ与注意事项

- Entity/DTO 字段必须严格一致，否则 CRUDResolver 类型推断会报错
- GraphQL schema 自动生成，变更后需重启后端
- 推荐前端使用 codegen 工具同步生成类型

---

## 常见错误码

| 错误码 | 场景          | 解决建议          |
| --- | ----------- | ------------- |
| 401 | 未认证/Token失效 | 检查Header、重新登录 |
| 400 | 参数格式错误      | 检查字段类型与必填项    |
| 404 | 资源不存在       | 检查ID、资源是否已创建  |
| 500 | 服务器异常       | 查看后端日志、联系开发者  |

> 如需获取完整 schema 字段与类型，请查阅 `ad_backend/generated/schemas/schema.graphql`
