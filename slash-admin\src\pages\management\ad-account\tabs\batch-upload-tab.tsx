import { useTranslation } from 'react-i18next';
import { Upload, Button, message, Table } from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { useState } from "react";
import * as XLSX from "xlsx";
import { gql, useMutation } from "@apollo/client";

const BATCH_UPLOAD = gql`
  mutation BatchUploadAdAccounts($accounts: [AdAccountInput!]!, $platform: String!) {
    batchUploadAdAccounts(accounts: $accounts, platform: $platform) {
      account
      status
    }
  }
`;

export default function BatchUploadTab() {
  const { t } = useTranslation();
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploadResult, setUploadResult] = useState<any[]>([]);
  const [batchUploadAdAccounts, { loading }] = useMutation(BATCH_UPLOAD);

  const handleFileChange = (file: File) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      const data = new Uint8Array(e.target!.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: "array" });
      const sheet = workbook.Sheets[workbook.SheetNames[0]];
      const json = XLSX.utils.sheet_to_json(sheet);
      const accounts = json.map((row: any) => ({
        accountId: row['ID']?.toString() || '',
        account: row[t('pages.key_2320')]?.toString() || '',
        password: row[t('pages.key_674')]?.toString() || '',
        tag: row[t('pages.key_1373')]?.toString() || '',
        channel: row[t('pages.key_1556')]?.toString() || '',
      })).filter(row => row.accountId);
      console.log('上传数据:', accounts);
      try {
        const { data } = await batchUploadAdAccounts({ variables: { accounts, platform: 'facebook' } });
        setUploadResult(data.batchUploadAdAccounts);
        message.success(t('pages.key_5'));
      } catch (e) {
        message.error(t('pages.key_3'));
      }
    };
    reader.readAsArrayBuffer(file);
    return false;
  };

  const props = {
    accept: ".xlsx",
    beforeUpload: (file: any) => {
      setFileList([file]);
      handleFileChange(file);
      return false;
    },
    fileList,
    onRemove: () => setFileList([]),
  };

  return (
    <div style={{ position: 'relative', marginBottom: 16 }}>
      <div style={{ position: 'absolute', right: 0, top: 0 }}>
        <a href="/account-template.xlsx" download>
          <Button type="primary">{t('pages.key_16')}</Button>
        </a>
      </div>
      <Upload {...props}>
        <Button icon={<UploadOutlined />}>{t('pages.key_2446')}</Button>
      </Upload>
    </div>
  );
}
