#!/bin/bash

# 文档同步 - Git Hooks 安装脚本

set -e

echo "🔧 开始安装 Git Hooks..."

# 获取项目根目录
PROJECT_ROOT=$(git rev-parse --show-toplevel)
HOOKS_DIR="$PROJECT_ROOT/.git/hooks"
SCRIPTS_DIR="$PROJECT_ROOT/scripts/doc-sync"

# 确保脚本目录存在
mkdir -p "$SCRIPTS_DIR"

# 创建 pre-commit hook
cat > "$HOOKS_DIR/pre-commit" << 'EOF'
#!/bin/bash

# 文档同步 - Pre-commit Hook
# 在提交前检测代码变更并提示文档更新

echo "🔍 检测代码变更..."

# 运行变更检测脚本
if node scripts/doc-sync/detect-changes.js; then
    echo "✅ 未检测到需要同步的代码变更"
    exit 0
else
    echo "⚠️  检测到代码变更，可能需要更新文档"
    echo "📝 建议运行: npm run doc:sync"
    
    # 询问是否继续提交
    read -p "是否继续提交? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "✅ 继续提交"
        exit 0
    else
        echo "❌ 取消提交"
        exit 1
    fi
fi
EOF

# 创建 post-commit hook
cat > "$HOOKS_DIR/post-commit" << 'EOF'
#!/bin/bash

# 文档同步 - Post-commit Hook
# 在提交后自动更新文档

echo "📝 检查是否需要更新文档..."

# 检查是否有文档相关的变更
if [ -f "scripts/doc-sync/changes.json" ]; then
    echo "🔄 自动更新文档..."
    
    # 运行文档更新脚本
    if node scripts/doc-sync/update-docs.js; then
        echo "✅ 文档更新完成"
        
        # 如果有文档更新，创建新的提交
        if git diff --quiet docs/; then
            echo "📄 无文档变更"
        else
            echo "📝 发现文档变更，创建自动提交..."
            git add docs/
            git commit -m "docs: 自动更新模块文档
            
- 根据代码变更自动同步文档
- 更新时间: $(date)
- 变更检测: scripts/doc-sync/detect-changes.js
- 文档更新: scripts/doc-sync/update-docs.js"
            
            echo "✅ 文档自动提交完成"
        fi
    else
        echo "❌ 文档更新失败"
    fi
else
    echo "📄 无需更新文档"
fi
EOF

# 创建 pre-push hook
cat > "$HOOKS_DIR/pre-push" << 'EOF'
#!/bin/bash

# 文档同步 - Pre-push Hook
# 在推送前最后检查文档一致性

echo "🔍 推送前文档一致性检查..."

# 运行文档验证脚本
if node scripts/doc-sync/validate-docs.js; then
    echo "✅ 文档一致性检查通过"
    exit 0
else
    echo "❌ 文档一致性检查失败"
    echo "📝 请运行 npm run doc:sync 更新文档"
    exit 1
fi
EOF

# 设置执行权限
chmod +x "$HOOKS_DIR/pre-commit"
chmod +x "$HOOKS_DIR/post-commit"
chmod +x "$HOOKS_DIR/pre-push"

echo "✅ Git Hooks 安装完成"
echo ""
echo "📋 已安装的 Hooks:"
echo "  - pre-commit:  提交前检测代码变更"
echo "  - post-commit: 提交后自动更新文档"
echo "  - pre-push:    推送前验证文档一致性"
echo ""
echo "🔧 使用方法:"
echo "  - 正常提交代码，Hooks 会自动运行"
echo "  - 手动同步: npm run doc:sync"
echo "  - 验证文档: npm run doc:validate"
