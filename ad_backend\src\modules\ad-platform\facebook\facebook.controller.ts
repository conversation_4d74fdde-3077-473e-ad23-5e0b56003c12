import { Controller, Get, Query, Post, Body, BadRequestException } from '@nestjs/common';
import { FacebookService } from './facebook.service';
import { FacebookApiService, AdAccountOAuth } from '../services/facebook-api.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Campaign } from '../entities/campaign.entity';

@Controller('ad-platform/facebook')
export class FacebookController {
  constructor(
    private readonly facebookService: FacebookService,
    private readonly facebookApiService: FacebookApiService,
    @InjectRepository(Campaign)
    private readonly campaignRepo: Repository<Campaign>
  ) { }

  @Post('authorize')
  async authorize(@Body() body: any) {
    return this.facebookService.authorize(body);
  }

  @Get('accounts')
  async getAccounts(@Query('accessToken') accessToken: string) {
    if (!accessToken) throw new BadRequestException('accessToken is required');
    return this.facebookService.getAccounts({ accessToken });
  }

  @Get('campaigns')
  async getCampaigns(@Query('accessToken') accessToken: string, @Query('accountId') accountId: string) {
    if (!accessToken || !accountId) throw new BadRequestException('accessToken and accountId are required');
    return this.facebookService.getCampaigns({ accessToken, accountId });
  }

  @Get('adsets')
  async getAdSets(@Query('accessToken') accessToken: string, @Query('accountId') accountId: string) {
    if (!accessToken || !accountId) throw new BadRequestException('accessToken and accountId are required');
    return this.facebookService.getAdSets({ accessToken, accountId });
  }

  @Get('ads')
  async getAds(@Query('accessToken') accessToken: string, @Query('accountId') accountId: string) {
    if (!accessToken || !accountId) throw new BadRequestException('accessToken and accountId are required');
    return this.facebookService.getAds({ accessToken, accountId });
  }

  @Get('adcreatives')
  async getAdCreatives(@Query('accessToken') accessToken: string, @Query('accountId') accountId: string) {
    if (!accessToken || !accountId) throw new BadRequestException('accessToken and accountId are required');
    return this.facebookService.getAdCreatives({ accessToken, accountId });
  }

  @Get('pixels')
  async getPixels(
    @Query('accessToken') accessToken?: string,
    @Query('accountId') accountId?: string,
    @Query('campaignId') campaignId?: string
  ) {
    if (campaignId) {
      const campaign = await this.campaignRepo.findOne({ where: { id: campaignId }, relations: ['adAccount'] });
      if (!campaign) throw new BadRequestException('广告系列不存在');
      accountId = campaign.adAccount?.accountId;
      accessToken = campaign.adAccount?.accessToken;
      if (!accountId || !accessToken) throw new BadRequestException('广告系列未绑定广告账户或accessToken');
    }
    if (!accessToken || !accountId) throw new BadRequestException('accessToken and accountId are required');
    return this.facebookService.getPixels({ accessToken, accountId });
  }

  @Get('audiences')
  async getAudiences(@Query('accessToken') accessToken: string, @Query('accountId') accountId: string) {
    if (!accessToken || !accountId) throw new BadRequestException('accessToken and accountId are required');
    return this.facebookService.getAudiences({ accessToken, accountId });
  }

  @Get('insights')
  async getInsights(
    @Query('accessToken') accessToken: string,
    @Query('accountId') accountId: string,
    @Query('level') level?: string,
    @Query('fields') fields?: string,
    @Query('date_preset') date_preset?: string
  ) {
    if (!accessToken || !accountId) throw new BadRequestException('accessToken and accountId are required');
    const fieldsArr = fields ? fields.split(',') : undefined;
    return this.facebookService.getInsights({ accessToken, accountId, level, fields: fieldsArr, date_preset });
  }

  // VCC Facebook数据同步API
  @Post('vcc/sync-single')
  async syncVccFacebookData(@Body() body: { accountOAuths: AdAccountOAuth[] }) {
    const { accountOAuths } = body;

    if (!accountOAuths || !Array.isArray(accountOAuths) || accountOAuths.length === 0) {
      throw new BadRequestException('accountOAuths array is required');
    }

    try {
      const facebookData = await this.facebookApiService.getBatchAccountData(accountOAuths);

      // 聚合数据
      const totalSpend = facebookData.reduce((sum, account) => sum + account.spend, 0);
      const totalImpressions = facebookData.reduce((sum, account) => sum + account.impressions, 0);
      const totalClicks = facebookData.reduce((sum, account) => sum + account.clicks, 0);
      const activeAccounts = facebookData.filter(account => account.accountStatus === 'ACTIVE').length;
      const totalAccounts = facebookData.length;

      const accountStatuses = facebookData.reduce((acc: { [key: string]: string }, account) => {
        acc[account.accountId] = account.accountStatus;
        return acc;
      }, {} as { [key: string]: string });

      return {
        success: true,
        data: {
          totalSpend,
          totalImpressions,
          totalClicks,
          activeAccounts,
          totalAccounts,
          accountStatuses,
          detailData: facebookData,
          lastUpdated: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('VCC Facebook数据同步失败:', error);
      throw new BadRequestException(`Facebook数据同步失败: ${error.message}`);
    }
  }

  // 批量VCC Facebook数据同步API
  @Post('vcc/sync-batch')
  async syncBatchVccFacebookData(@Body() body: {
    vccCards: Array<{
      vccCardId: string;
      accountOAuths: AdAccountOAuth[];
    }>
  }) {
    const { vccCards } = body;

    if (!vccCards || !Array.isArray(vccCards) || vccCards.length === 0) {
      throw new BadRequestException('vccCards array is required');
    }

    const results = [];

    for (const vccCard of vccCards) {
      try {
        if (!vccCard.accountOAuths || vccCard.accountOAuths.length === 0) {
          results.push({
            vccCardId: vccCard.vccCardId,
            success: false,
            error: '没有绑定Facebook广告账户'
          });
          continue;
        }

        const facebookData = await this.facebookApiService.getBatchAccountData(vccCard.accountOAuths);

        // 聚合数据
        const totalSpend = facebookData.reduce((sum, account) => sum + account.spend, 0);
        const totalImpressions = facebookData.reduce((sum, account) => sum + account.impressions, 0);
        const totalClicks = facebookData.reduce((sum, account) => sum + account.clicks, 0);
        const activeAccounts = facebookData.filter(account => account.accountStatus === 'ACTIVE').length;
        const totalAccounts = facebookData.length;

        const accountStatuses = facebookData.reduce((acc: { [key: string]: string }, account) => {
          acc[account.accountId] = account.accountStatus;
          return acc;
        }, {} as { [key: string]: string });

        results.push({
          vccCardId: vccCard.vccCardId,
          success: true,
          data: {
            totalSpend,
            totalImpressions,
            totalClicks,
            activeAccounts,
            totalAccounts,
            accountStatuses,
            detailData: facebookData,
            lastUpdated: new Date().toISOString()
          }
        });

        // 添加延时避免API限制
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`VCC卡片 ${vccCard.vccCardId} Facebook数据同步失败:`, error);
        results.push({
          vccCardId: vccCard.vccCardId,
          success: false,
          error: error.message
        });
      }
    }

    return {
      success: true,
      results,
      summary: {
        total: vccCards.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    };
  }
}
