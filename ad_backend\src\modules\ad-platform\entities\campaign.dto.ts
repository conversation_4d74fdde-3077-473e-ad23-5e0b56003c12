import { ObjectType, Field, ID } from '@nestjs/graphql';
import { AdAccountDTO } from './ad-account.dto';

@ObjectType('CampaignDTO')
export class CampaignDTO {
    @Field(() => ID)
    id: string;
    @Field()
    name: string;
    @Field()
    objective: string;
    @Field()
    status: string;
    @Field(() => [String])
    specialAdCategories: string[];
    @Field({ nullable: true })
    facebookCampaignId?: string;
    @Field()
    accountId: string;
    @Field()
    createdAt: Date;
    @Field()
    updatedAt: Date;
    @Field({ nullable: true })
    syncStatus?: string;
    @Field({ nullable: true })
    syncError?: string;
    @Field(() => AdAccountDTO, { nullable: true })
    adAccount?: AdAccountDTO;
} 