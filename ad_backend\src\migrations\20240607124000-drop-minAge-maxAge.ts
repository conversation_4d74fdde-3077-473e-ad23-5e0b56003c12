import { MigrationInterface, QueryRunner } from "typeorm";

export class DropMinAgeMaxAge20240607124000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "minAge"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "maxAge"`);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN "minAge" int`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN "maxAge" int`);
    }
} 