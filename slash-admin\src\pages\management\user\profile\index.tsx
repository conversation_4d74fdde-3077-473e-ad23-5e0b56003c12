import { useTranslation } from 'react-i18next';
import { useEffect, useState } from "react";
import { Tabs, TabsProps, Table, Button, Tag, Upload, Space, Modal, Card, Radio, Checkbox, Input, message, Select, Spin } from "antd";
import { UploadOutlined, EyeOutlined, DeleteOutlined } from "@ant-design/icons";
import { TablePaginationConfig } from "antd/lib";

// 导入实际的标签页组件
import ProfileTab from './tabs/ProfileTab';
import TeamsTab from './tabs/TeamsTab';
import ProjectsTab from './tabs/ProjectsTab';
import ConnectionsTab from './tabs/ConnectionsTab';
import { uploadToCloudflare } from "@/utils/upload";
import { useGroups } from "@/api/services/groupService";
import { useTenant } from '@/hooks/useTenant';
import { useAsyncEffect, useSetState } from "ahooks";
import { useLazyQuery, useMutation, useQuery } from "@apollo/client";
import { CREATE_MATERIACREATE_MUTATION, CREATE_MATERIALMANAGEMENT_MUTATION, DELETE_MATERIACREATE, DELETE_MATERIALMANAGEMENT, GET_MATERIALMANAGEMENT } from "@/api/graphql/materialManagement.graphql";
import { toast } from "sonner";
import { creativeComboType, materialManagementType } from "./type";
import ExcelZipUploader from "./batchupload";
import { useLocalStorage, useMount } from "react-use";
import facebookService from "@/api/facebook/index";
import { createReadStream } from "fs";
import { GET_AD_ACCOUNT_LIST } from "@/api/adAccount.graphql";
import { GET_LANDING_PAGES } from "../../landing-page/graphql";
import { LandingPageFormValues } from "../../landing-page/LandingPageModal";
import materialManagementService from "@/api/services/materialManagement";
import _ from "lodash";
// Placeholder for useUserInfo hook
const useUserInfo = () => {
  return {
    avatar: 'https://via.placeholder.com/40',
    username: 'pages.key_865'
  };
};

// Placeholder image for cover
const CoverImage = 'https://via.placeholder.com/600x200?text=Cover+Image';

// Mock Data
const mockMaterials = [
  { id: "1", type: 'pages.key_530', name: "banner1.jpg", url: "https://via.placeholder.com/120x80", tags: ['pages.key_51'], createdAt: "2025-05-01" },
  { id: "2", type: 'pages.key_2062', name: "ad1.mp4", url: "https://www.w3schools.com/html/mov_bbb.mp4", tags: ['pages.key_2062'], createdAt: "2025-05-02" },
  { id: "3", type: 'pages.key_1122', name: "文案A", content: "Buy now!", tags: ['pages.key_129'], createdAt: "2025-05-03" },
];
const mockCopywriting = [
  { id: "c1", lang: 'pages.key_43', content: "立即购买！", sensitive: false },
  { id: "c2", lang: "English", content: "Buy now!", sensitive: false },
  { id: "c3", lang: 'pages.key_43', content: 'pages.key_1082', sensitive: true },
];
const mockLandingPages = [
  { id: "l1", url: "https://main-landing.com", type: 'pages.key_52', tags: ["主"], createdAt: "2025-05-01" },
  { id: "l2", url: "https://safe-landing.com", type: 'pages.key_647', tags: ['pages.key_646'], createdAt: "2025-05-02" },
];
const mockCreatives = [
  { id: "cr1", name: "组合A", materials: ["banner1.jpg", "文案A"], stats: "1000曝光/30点击", createdAt: "2025-05-01" },
];

interface Group {
  id: string;
  name: string;
}

interface TenantUserInfo {
  [key: string]: {
    groups: Group[];
  };
}
interface LandingPageItem extends LandingPageFormValues {
  id: string;
  code: string;
  status: "active" | "inactive";
  createdAt: string;
  creator: string;
  domain: string;
  url: string;
}
function MaterialTab() {
  const { t } = useTranslation();
  const filter: any = {}
  const [fileList, setFileList] = useState<any[]>([]);
  const [preview, setPreview] = useState<any>(null);
  const [data, setData] = useState<materialManagementType[]>([]);
  const [uploading, setUploading] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [createOneMaterialManagement] = useMutation(CREATE_MATERIALMANAGEMENT_MUTATION);
  const [loadData] = useLazyQuery(GET_MATERIALMANAGEMENT, { fetchPolicy: 'network-only' });
  const [tenantUserInfo] = useLocalStorage<TenantUserInfo>('tenant_userInfo')
  const [userInfo] = useLocalStorage<any>('userStore')
  const GroupId = tenantUserInfo && Object.values((tenantUserInfo))[0].groups[0]?.id
  const [deletematerialManagement] = useMutation(DELETE_MATERIALMANAGEMENT, {
    refetchQueries: [{ query: GET_MATERIALMANAGEMENT }],
  });
  const [paginationProps, setPaginationProps] = useSetState({
    total: 0, // 数据总数
    pageSize: 10, // 每页条数
    current: 1, // 当前页码
  })
  const { data: accountData } = useQuery(GET_AD_ACCOUNT_LIST, {
    variables: {
      filter: {},
    }
  });
  const tenantId = userInfo?.state?.userInfo?.id;
  const { data: groupsData } = useGroups({
    paging: { limit: 50, offset: 0 },
    filter,
    sorting: [{ field: "createTime", direction: "DESC" }]
  });
  const [newMaterial, setNewMaterial] = useSetState({
    fileName: '',
    tags: '',
    notes: '',
    file: null,
    account: undefined
  });
  const initData = async () => {
    const data: any = await materialManagementService.getMaterialManagementList({
      paging: {
        limit: paginationProps.pageSize,
        offset: (paginationProps.current - 1) * paginationProps.pageSize,
      },
      filter: {},
      sorting: [
        { "field": "updatedAt", "direction": "DESC" }
      ]
    })

    // const data = await loadData({
    //   variables: {
    //     paging: {
    //       limit: paginationProps.pageSize,
    //       offset: (paginationProps.current - 1) * paginationProps.pageSize,
    //     },
    //     filter: {},
    //     sorting: [
    //       { "field": "updatedAt", "direction": "DESC" }
    //     ]
    //   },
    // })
    let arr = [] as materialManagementType[]
    setPaginationProps({ total: data?.totalCount })
    data?.nodes?.forEach((item: materialManagementType) => {
      arr.push({ ...item, updatedAt: (item.updatedAt as string).slice(0, 19).replace('T', ' ') })
    }) as materialManagementType[]
    setData(arr)
  }
  const handleDelete = async (record: materialManagementType) => {
    await deletematerialManagement({
      variables: {
        input: { id: record.id }
      }
    });
    toast.success(t('pages.key_294'));
    setPaginationProps({ current: 1 })
    initData();
  }
  useEffect(() => {
    initData();
  }, [paginationProps.current, paginationProps.pageSize])

  const columns = [
    { title: "ID", dataIndex: "id", width: 150, key: "id" },
    { title: t('pages.key_1118'), dataIndex: "file", width: 300, key: "file" },
    {
      title: t('pages.key_483'), dataIndex: "fileName", key: "fileName", width: 150
    },
    { title: t('pages.key_1373'), dataIndex: "tags", key: "tags", width: 150 },
    { title: t('pages.key_950'), dataIndex: "group", key: "group", width: 250 },
    { title: t('pages.key_794'), dataIndex: "adCount", width: 200, key: "adCount" },
    { title: t('pages.key_988'), dataIndex: "activeAdCount", width: 150, key: "activeAdCount" },
    { title: t('pages.key_696'), dataIndex: "bannedCount", width: 150, key: "bannedCount" },
    { title: t('pages.key_1519'), dataIndex: "spend", width: 150, key: "spend" },
    { title: t('pages.key_2357'), dataIndex: "conversion", width: 150, key: "conversion" },
    { title: t('pages.key_112'), dataIndex: "commission", width: 150, key: "commission" },
    { title: t('pages.key_588'), dataIndex: "notes", width: 150, key: "notes" },
    { title: t('pages.key_1254'), dataIndex: "updatedAt", width: 300, key: "updatedAt" },
    {
      title: t('pages.key_1058'),
      key: "actions",
      width: 150,
      fixed: 'right',
      render: (_: any, record: any) => (
        <Button type="link" danger icon={<DeleteOutlined />} size="small" onClick={() => handleDelete(record)}>{t('pages.key_287')}</Button>
      )
    }
  ];

  const uploadProps = {
    accept: ".jpg,.jpeg,.png,.mp4",
    beforeUpload: (file: any) => {
      setFileList([...fileList, file]);
      return false;
    },
    fileList,
    onRemove: (file: any) => setFileList(fileList.filter(f => f.uid !== file.uid)),
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
    },
  };

  const handleAddCancel = () => {
    setIsAddModalVisible(false);
    setNewMaterial({
      fileName: '',
      tags: '',
      notes: '',
      file: null,
      account: undefined
    });
  };

  const handleAddSubmit = async () => {
    // const token = 'EAATaTgeuH8cBO1omE0OPXZA2noAxzEXsSiZAoaGZCetp1s9NRinHxxU4Tumjj9ys06VNmPrq9o8KAMLPcT9I4WtLJEkBIiTP50O2a9K4veVDMApjqBd1l4cZAgZBtvfkSmwC1sZBuqYMwqxp0cmxZB4CZBwhhUzQ40ZC3rTA5TcZBAB5uRFnt7UwPlOXlZCFiF55chVLw7tlA4w7PFKoWr3udbRkw2QUptCZBKBEyc3DEoxJ0WVK6FDnoLsE4exkTlgZA'
    // formData.append('access_token', token);
    // https://r2-ad.yeeu.net/3f04876f-6742-49ed-aa6c-05fa9b3fa426/materialManagement/9fad5a2daa8077f773f33f97fe908db8.mp4
    // const data1 = {
    //   access_token: token,
    //   file_url: ' https://r2-ad.yeeu.net/3f04876f-6742-49ed-aa6c-05fa9b3fa426/materialManagement/9fad5a2daa8077f773f33f97fe908db8.mp4',
    // };
    // const res = await facebookService.uploadToFaccebookVedio('act_****************', data1) pageId 122104680320890003
    // console.log(123, res)
    // await facebookService.getMeInfo(token)
    if (!newMaterial.account) {
      message.error(t('pages.key_2247'));
      return;
    }
    if (!newMaterial.fileName || !newMaterial.file) {
      message.error(t('pages.key_2154'));
      return;
    }
    setUploading(true);
    Promise.all([facebookService.uploadToFaccebook(newMaterial.file, newMaterial.fileName, newMaterial.account), uploadToCloudflare(newMaterial.file, tenantId as string, 'materialManagement')]).then(async (res) => {
      const cfPloadData = res[1]
      const fbPloadData = res[0]
      if (cfPloadData?.success && res[1]?.success) {
        let params = {
          ...newMaterial,
          file: cfPloadData?.fileUrl,
          tenantId
        } as any
        if (newMaterial.file) {
          if ((newMaterial.file as any).type.startsWith('video/')) {
            // 是视频文件
            params.facebookVedioId = fbPloadData?.data?.id
          } else {
            // 不是视频文件 或者 type 无法识别（如某些 .mov 文件）
            params.facebookImageId = (Object.values(fbPloadData['data']['images'])[0] as any)?.hash
          }
        }
        delete params.account
        const res = await createOneMaterialManagement({
          variables: {
            input: {
              materialManagement: params
            },
          },
        })
        if (res.data.createOneMaterialManagement) {
          toast.success(t('pages.key_1149'));
          setUploading(false);
          handleAddCancel()
          initData()
        }
        // 调用新增接口
      }
    })
      .finally(() => {
        setUploading(false);
        handleAddCancel()
      })
  };

  // 预览图片
  const handlePreview = async (file: any) => {
    window.open(file.url || file.thumbUrl, '_blank');
  };

  const handleStandardTableChange = (pagination: TablePaginationConfig) => {
    setPaginationProps({
      current: pagination.current as number,
      pageSize: pagination.pageSize as number,
    });
  };

  return (
    <div className="material-tab-container">
      <Card
        bordered={false}
        style={{ marginBottom: '16px' }}
        bodyStyle={{ padding: '16px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Button className="mr-2" type="primary" onClick={() => setIsAddModalVisible(true)} icon={<UploadOutlined />}>{t('pages.key_1152')}</Button>
            <ExcelZipUploader initData={initData}></ExcelZipUploader>
          </div>
          {/* <div>
            <Button style={{ marginLeft: '8px' }}>{t('pages.key_682')}</Button>
          </div> */}
        </div>
      </Card>

      <Card bordered={false}>
        <Table
          scroll={{ x: 1500 }}
          columns={columns as any}
          dataSource={data}
          rowKey="id"
          onChange={handleStandardTableChange}
          pagination={paginationProps}
        />
      </Card>

      <Modal
        destroyOnHidden={true}
        title={t('pages.key_1152')}
        open={isAddModalVisible}
        onOk={handleAddSubmit}
        onCancel={handleAddCancel}
        confirmLoading={uploading}
        okText={t('pages.key_1693')}
        cancelText={t('pages.key_407')}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_483')}</div>
          <Input
            value={newMaterial.fileName}
            onChange={(e) => setNewMaterial({ fileName: e.target.value })}
            placeholder={t('pages.key_2222')}
          />
        </div>
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_1373')}</div>
          <Input
            value={newMaterial.tags}
            onChange={(e) => setNewMaterial({ tags: e.target.value })}
            placeholder={t('pages.key_2212')}
          />
        </div>
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_2447')}</div>
          <Select
            fieldNames={{ label: 'account', value: 'accountId' }}
            style={{ width: '100%' }}
            placeholder={t('pages.key_2248')}
            value={newMaterial.account}
            onChange={(value) => setNewMaterial({ account: value })}
            options={accountData?.adAccounts || []}
          />
        </div>
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_1120')}</div>
          <Upload
            maxCount={1}
            showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
            beforeUpload={(file) => {
              setNewMaterial({ file } as any);
              return false;
            }}
            onRemove={() => {
              setNewMaterial({ file: null });
            }}
          >
            <Button icon={<UploadOutlined />}>{t('pages.key_2449')}</Button>
          </Upload>
        </div>
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_588')}</div>
          <Input.TextArea
            rows={4}
            value={newMaterial.notes}
            onChange={(e) => setNewMaterial({ notes: e.target.value })}
            placeholder={t('pages.key_2191')}
          />
        </div>
      </Modal>

      <Modal
        open={!!preview}
        footer={null}
        onCancel={() => setPreview(null)}
        title={preview?.name}
        width={800}
        centered
        destroyOnClose
      >
        {preview?.type === t('pages.key_530') && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <img src={preview.url} alt={preview.name} style={{ maxWidth: "100%", maxHeight: "600px" }} />
          </div>
        )}
        {preview?.type === t('pages.key_2062') && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <video src={preview.url} controls style={{ maxWidth: "100%", maxHeight: "600px" }} />
          </div>
        )}
      </Modal>
    </div>
  );
}

function CopywritingTab() {
  const { t } = useTranslation();
  const [copyList, _setCopyList] = useState(mockCopywriting);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newCopywriting, setNewCopywriting] = useState({ lang: 'pages.key_43', content: '', sensitive: false });

  const columns = [
    { title: t('pages.key_2142'), dataIndex: "lang", key: "lang", width: 100 },
    {
      title: t('pages.key_1123'), dataIndex: "content", key: "content",
      ellipsis: { showTitle: false },
      render: (content: string) => (
        <div style={{ maxWidth: 400, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
          {content}
        </div>
      )
    },
    {
      title: t('pages.key_1081'), dataIndex: "sensitive", key: "sensitive", width: 100,
      render: (v: boolean) => v ? <Tag color="red">是</Tag> : <Tag color="green">否</Tag>
    },
    {
      title: t('pages.key_1058'), key: "actions", width: 150,
      render: (_: any, _record: materialManagementType) => (
        <Space>
          <Button type="link" style={{ color: '#1677ff' }}>{t('pages.key_1853')}</Button>
          <Button type="link" danger>{t('pages.key_287')}</Button>
        </Space>
      )
    },
  ];

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleAdd = () => {
    // 实际应用中应该添加到数据库
    setIsModalVisible(false);
    message.success(t('pages.key_1124'));
  };

  return (
    <div className="copywriting-tab-container">
      <Card
        bordered={false}
        style={{ marginBottom: '16px' }}
        bodyStyle={{ padding: '16px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Button type="primary" onClick={showModal}>{t('pages.key_1536')}</Button>
            <Button style={{ marginLeft: '8px' }}>{t('pages.key_981')}</Button>
          </div>
          <div>
            <Button type="primary" danger>{t('pages.key_1083')}</Button>
          </div>
        </div>
      </Card>

      <Card bordered={false}>
        <Table
          dataSource={copyList as any}
          columns={columns}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      <Modal
        title={t('pages.key_1536')}
        open={isModalVisible}
        onOk={handleAdd}
        onCancel={handleCancel}
        okText={t('pages.key_1523')}
        cancelText={t('pages.key_407')}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_2142')}</div>
          <Radio.Group
            value={newCopywriting.lang}
            onChange={(e) => setNewCopywriting({ ...newCopywriting, lang: e.target.value })}
          >
            <Radio.Button value={'pages.key_43'}>{t('pages.key_43')}</Radio.Button>
            <Radio.Button value="English">English</Radio.Button>
          </Radio.Group>
        </div>
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_1123')}</div>
          <Input.TextArea
            rows={4}
            value={newCopywriting.content}
            onChange={(e) => setNewCopywriting({ ...newCopywriting, content: e.target.value })}
            placeholder={t('pages.key_2203')}
          />
        </div>
        <div>
          <Checkbox
            checked={newCopywriting.sensitive}
            onChange={(e) => setNewCopywriting({ ...newCopywriting, sensitive: e.target.checked })}
          >{t('pages.key_494')}</Checkbox>
        </div>
      </Modal>
    </div>
  );
}

function LandingPageTab() {
  const { t } = useTranslation();
  const [lpList, _setLpList] = useState(mockLandingPages);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newLandingPage, setNewLandingPage] = useState({ url: '', type: t('pages.key_52'), tags: [] });

  const columns = [
    {
      title: "URL",
      dataIndex: "url",
      key: "url",
      render: (url: string) => (
        <a href={url} target="_blank" rel="noopener noreferrer" style={{ color: '#1677ff' }}>{url}</a>
      )
    },
    { title: t('pages.key_1765'), dataIndex: "type", key: "type" },
    {
      title: t('pages.key_1373'),
      dataIndex: "tags",
      key: "tags",
      render: (tags: string[]) => (
        <div>
          {tags?.map(t => <Tag key={t} color="blue">{t}</Tag>)}
        </div>
      )
    },
    { title: t('pages.key_250'), dataIndex: "createdAt", key: "createdAt" },
    {
      title: t('pages.key_1058'),
      key: "actions",
      width: 150,
      render: (_: any, _record: materialManagementType) => (
        <Space>
          <Button type="link" style={{ color: '#1677ff' }}>{t('pages.key_1853')}</Button>
          <Button type="link" danger>{t('pages.key_287')}</Button>
        </Space>
      )
    },
  ];

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleAdd = () => {
    // 实际应用中应该添加到数据库
    setIsModalVisible(false);
    message.success(t('pages.key_2011'));
  };

  return (
    <div className="landing-page-tab-container">
      <Card
        bordered={false}
        style={{ marginBottom: '16px' }}
        bodyStyle={{ padding: '16px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Button type="primary" onClick={showModal}>添加落地页URL</Button>
            <Button style={{ marginLeft: '8px' }}>{t('pages.key_981')}</Button>
          </div>
          <div>
            <Input.Search
              placeholder="搜索URL"
              style={{ width: 250 }}
              allowClear
            />
          </div>
        </div>
      </Card>
      <Card bordered={false}>
        <Table
          dataSource={lpList as any}
          columns={columns}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>
      <Modal
        title="添加落地页URL"
        open={isModalVisible}
        onOk={handleAdd}
        onCancel={handleCancel}
        okText={t('pages.key_1523')}
        cancelText={t('pages.key_407')}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>URL</div>
          <Input
            value={newLandingPage.url}
            onChange={(e) => setNewLandingPage({ ...newLandingPage, url: e.target.value })}
            placeholder="请输入落地页URL"
          />
        </div>
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_1765')}</div>
          <Radio.Group
            value={newLandingPage.type}
            onChange={(e) => setNewLandingPage({ ...newLandingPage, type: e.target.value })}
          >
            <Radio.Button value={t('pages.key_52')}>{t('pages.key_52')}</Radio.Button>
            <Radio.Button value={t('pages.key_647')}>{t('pages.key_647')}</Radio.Button>
            <Radio.Button value={t('pages.key_193')}>{t('pages.key_193')}</Radio.Button>
          </Radio.Group>
        </div>
        <div>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_1373')}</div>
          <Select
            mode="tags"
            style={{ width: '100%' }}
            placeholder={t('pages.key_2163')}
            value={newLandingPage.tags}
            onChange={(value) => setNewLandingPage({ ...newLandingPage, tags: value })}
            options={[
              { value: '主', label: '主' },
              { value: t('pages.key_646'), label: t('pages.key_646') },
              { value: t('pages.key_1515'), label: t('pages.key_1515') },
            ]}
          />
        </div>
      </Modal>
    </div>
  );
}


function CreativeComboTab() {
  const { t } = useTranslation();
  const [comboList, setComboList] = useState<creativeComboType[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [data, setData] = useState<materialManagementType[]>([]);
  const [createMateriaCreate] = useMutation(CREATE_MATERIACREATE_MUTATION);
  const [userInfo] = useLocalStorage<any>('userStore')
  const [deleteMateriaCreate] = useMutation(DELETE_MATERIACREATE)
  const [newCombo, setNewCombo] = useSetState<{
    name: string; pageLading: undefined | string; materials?: number; slogan: string; account: undefined | string; pageId: undefined | string; facebookImageId: string;
    facebookVedioId: string
  }>({ name: '', pageLading: undefined, materials: undefined, slogan: '', account: undefined, pageId: undefined, facebookImageId: '', facebookVedioId: '' });
  const [loadData] = useLazyQuery(GET_MATERIALMANAGEMENT, { fetchPolicy: 'network-only' });
  const [pageOptions, setPageOptions] = useState([])
  const { data: landingPageData } = useQuery<{ getLandingPages: LandingPageItem[] }>(GET_LANDING_PAGES, { context: { operationName: 'getLandingPages' } });
  const [pageIdLoading, setPageIdLoading] = useState(false)
  const landingPages = landingPageData?.getLandingPages || [];
  const { data: accountData } = useQuery(GET_AD_ACCOUNT_LIST, {
    variables: {
      filter: {},
    }
  });
  const [paginationProps, setPaginationProps] = useSetState({
    total: 0, // 数据总数
    pageSize: 10, // 每页条数
    current: 1, // 当前页码
  })
  const handleDelete = async (record: creativeComboType) => {
    await deleteMateriaCreate({
      variables: {
        input: { id: record.id }
      }
    })
    message.success('创意组合删除成功')
    getDataList()
  }
  // hash 0109414d61c7860d37e29811b50e6641
  // **************** accountId
  const userId = userInfo?.state?.userInfo?.id;
  const columns = [
    { title: t('pages.key_1806'), dataIndex: "name", key: "name" },
    {
      title: t('pages.key_1782'),
      dataIndex: "materialsName",
      key: "materialsName",
    },
    {
      title: t('pages.key_393'), dataIndex: "stats", key: "stats", render: (stats: any) => {
        return <div>
          0
        </div>
      }
    },
    { title: t('pages.key_250'), dataIndex: "createdAt", key: "createdAt" },
    {
      title: t('pages.key_1058'),
      key: "actions",
      width: 180,
      render: (_: any, _record: any) => (
        <Space>
          {/* <Button type="link" style={{ color: '#52c41a' }}>{t('pages.key_1090')}</Button> */}
          <Button onClick={() => handleDelete(_record)} type="link" danger>{t('pages.key_287')}</Button>
        </Space>
      )
    },
  ];
  const initData = async () => {
    const data = await loadData({
      variables: {
        paging: {
          limit: 999,
          offset: 0,
        },
        filter: {},
        sorting: [
          { "field": "updatedAt", "direction": "DESC" }
        ]
      },
    })
    let arr = [] as materialManagementType[]
    data?.data?.materialManagements?.nodes?.forEach((item: materialManagementType) => {
      arr.push({ ...item, updatedAt: (item.updatedAt as string).slice(0, 19).replace('T', ' ') })
    }) as materialManagementType[]
    setData(arr)
  }
  const pageLadingSelect = (url: string) => {
    setNewCombo({ pageLading: url })
    const row = landingPages.find(item => item.url === url)
    setNewCombo({ slogan: row?.slogan as string })
  }
  const accountChange = async (value: string) => {
    setNewCombo({ account: value, pageId: undefined })
    setPageIdLoading(true)
    const data = await facebookService.getfbMainPage(value)
    if (data.success) {
      setPageOptions(data.data)
      setPageIdLoading(false)
    }
  }
  const materialsChange = (value: any) => {
    if (value) {
      const row = data.find(item => item.id === value)
      if (row && row.facebookImageId) {
        setNewCombo({ materials: row.id, facebookImageId: row.facebookImageId })
      } else if (row && row.facebookVedioId) {
        setNewCombo({ materials: row?.id, facebookVedioId: row.facebookVedioId })
      }
    }
  }
  useMount(() => {
    initData()
  })
  const getDataList = async () => {
    const res: any = await materialManagementService.getMateriaCreateList({
      paging: {
        limit: paginationProps.pageSize,
        offset: (paginationProps.current - 1) * paginationProps.pageSize,
      },
      filter: {},
      sorting: [
        { "field": "updatedAt", "direction": "DESC" }
      ]
    })
    if (res?.nodes) {
      let arr = [] as creativeComboType[]
      res?.nodes?.forEach((item: creativeComboType) => {
        arr.push({ ...item, createdAt: (item.createdAt as string).slice(0, 19).replace('T', ' '), materialsName: data.find(subItem => subItem.id === item.materials)?.fileName })
      }) as creativeComboType[]
      setComboList(arr)
      setPaginationProps({ total: res?.totalCount })
    }
  }
  useEffect(() => {
    getDataList()
  }, [paginationProps.current, paginationProps.pageSize])
  const showModal = async () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setNewCombo({ name: '', pageLading: undefined, materials: undefined, slogan: '', account: undefined, pageId: undefined })
    setIsModalVisible(false);
  };

  const handleStandardTableChange = (pagination: TablePaginationConfig) => {
    setPaginationProps({
      current: pagination.current as number,
      pageSize: pagination.pageSize as number,
    });
  };
  const handleAdd = async () => {
    if (!newCombo.account || !newCombo.materials || !newCombo.pageId || !newCombo.name || !newCombo.pageLading) {
      return toast.warning(t('pages.key_2290'))
    }
    const data: any = await facebookService.adcreatives(newCombo)
    const res = await createMateriaCreate({
      variables: {
        input: {
          materiaCreate: { ..._.omit(newCombo, ['facebookVedioId', 'facebookImageId']), createId: data?.id, userId }
        },
      },
    })
    if (res.data.createOneMateriaCreate) {
      message.success(t('pages.key_274'));
      getDataList()
      handleCancel()
    }
    // 实际应用中应该添加到数据库
  };

  return (
    <div className="creative-combo-tab-container">
      <Card
        bordered={false}
        style={{ marginBottom: '16px' }}
        bodyStyle={{ padding: '16px' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Button type="primary" onClick={showModal}>{t('pages.key_248')}</Button>
          </div>
          <div>
            <Input.Search
              placeholder={t('pages.key_1051')}
              style={{ width: 250 }}
              allowClear
            />
          </div>
        </div>
      </Card>
      <Card bordered={false}>
        <Table
          onChange={handleStandardTableChange}
          dataSource={comboList}
          columns={columns}
          rowKey="id"
          pagination={paginationProps}
        />
      </Card>
      <Modal
        title={t('pages.key_248')}
        open={isModalVisible}
        onOk={handleAdd}
        onCancel={handleCancel}
        okText={t('pages.key_236')}
        cancelText={t('pages.key_407')}
        width={600}
        destroyOnHidden
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_1806')}</div>
          <Input
            value={newCombo.name}
            onChange={(e) => setNewCombo({ ...newCombo, name: e.target.value })}
            placeholder={t('pages.key_2224')}
          />
        </div>
        <div>
          <div style={{ marginBottom: '16px' }}>
            <div style={{ marginBottom: '8px' }}>{t('pages.key_2447')}</div>
            <Select
              fieldNames={{ label: 'account', value: 'accountId' }}
              style={{ width: '100%' }}
              placeholder={t('pages.key_2248')}
              value={newCombo.account}
              options={accountData?.adAccounts || []}
              onSelect={accountChange}
            />
          </div>
          <div style={{ marginBottom: '8px' }}>facebook主页选择</div>
          <Spin spinning={pageIdLoading}>
            <Select
              fieldNames={{ value: 'id', label: 'name' }}
              style={{ width: '100%' }}
              placeholder={t('pages.key_2246')}
              value={newCombo.pageId}
              onChange={(value) => setNewCombo({ pageId: value })}
              options={pageOptions}
              optionFilterProp="label"
            />
          </Spin>
        </div>
        <div>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_2456')}</div>
          <Select
            onSelect={pageLadingSelect}
            fieldNames={{ label: 'title', value: 'url' }}
            style={{ width: '100%' }}
            placeholder={t('pages.key_2277')}
            value={newCombo.pageLading}
            onChange={(value) => setNewCombo({ pageLading: value })}
            options={landingPages}
            optionFilterProp="label"
          />
        </div>
        <div>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_1122')}</div>
          <Input disabled value={newCombo.slogan}></Input>
        </div>
        <div>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_2455')}</div>
          <Select
            fieldNames={{ label: 'fileName', value: 'id' }}
            style={{ width: '100%' }}
            placeholder={t('pages.key_2275')}
            value={newCombo.materials}
            onSelect={materialsChange}
            options={data}
          />
        </div>
      </Modal>
    </div>
  );
}
// Removed export default to fix multiple default exports error
function MaterialManagement() {
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState("material");
  const items: TabsProps["items"] = [
    { key: "material", label: t('pages.key_1785'), children: <MaterialTab /> },
    // { key: "copywriting", label: t('pages.key_1125'), children: <CopywritingTab /> },
    // { key: "landing-page", label: t('pages.key_2010'), children: <LandingPageTab /> },
    { key: "creative", label: t('pages.key_275'), children: <CreativeComboTab /> },
  ];
  return (
    <Tabs
      items={items}
      activeKey={activeKey}
      onChange={setActiveKey}
      destroyOnHidden
    />
  );
}

export default function ProfilePage() {
  const { t } = useTranslation();
  const { avatar, username } = useUserInfo();

  const tabItems = [
    // {
    //   key: '0',
    //   label: t('pages.key_28'),
    //   children: <ProfileTab />
    // },
    // {
    //   key: '1',
    //   label: t('pages.key_517'),
    //   children: <TeamsTab />
    // },
    // {
    //   key: '2',
    //   label: t('pages.key_2546'),
    //   children: <ProjectsTab />
    // },
    // {
    //   key: '3',
    //   label: t('pages.key_80'),
    //   children: <ConnectionsTab />
    // },
    {
      key: '4',
      label: t('pages.key_1787'),
      children: <MaterialManagement />
    }
  ];

  return (
    <div >
      {/* 封面图和头像区域 */}
      {/* <div className="cover-container" style={{ position: 'relative', height: '240px' }}>
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '200px',
          backgroundImage: `url(${CoverImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}></div>

        <div style={{
          position: 'absolute',
          bottom: 0,
          left: '20px',
          display: 'flex',
          alignItems: 'flex-end'
        }}>
          <div style={{
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            border: '4px solid white',
            overflow: 'hidden',
            backgroundColor: 'white'
          }}>
            <img
              src={avatar}
              alt=t('pages.key_1632')
              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            />
          </div>
          <div style={{ marginLeft: '20px', marginBottom: '10px' }}>
            <h2 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>{username}</h2>
            <p style={{ margin: '5px 0 0 0', color: '#666' }}>{t('pages.key_787')}</p>
          </div>
        </div>
      </div> */}

      {/* 标签页内容区域 */}
      <h1 className="text-2xl font-bold">{t('pages.key_1787')}</h1>
      {/* <Tabs defaultActiveKey="0" items={tabItems} /> */}
      <MaterialManagement></MaterialManagement>
    </div>
  );
}

// ProfilePage is already exported as default in its function declaration
// No need for a second export statement here
