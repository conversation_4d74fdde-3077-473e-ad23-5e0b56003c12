import { ApolloClient } from '@apollo/client';
import {
    USERS_QUERY,
    USER_QUERY,
    CREATE_USER,
    UPDATE_USER,
    DELETE_USER,
    CHANGE_USER_STATUS
} from '../user.graphql';
import { CreateUserParams, UpdateUserParams, UserQueryParams, UserStatus } from '@/pages/management/system/account/types';

export class UserService {
    constructor(private client: ApolloClient<any>) { }

    /**
     * 获取用户列表
     * @param params 查询参数
     * @returns 用户列表响应
     */
    async getUsers(params: UserQueryParams = {}) {
        try {
            const { data, error } = await this.client.query({
                query: USERS_QUERY,
                variables: {
                    input: {
                        filter: params.filter,
                        page: params.page || 1,
                        limit: params.limit || 10
                    },
                    sorting: params.sorting
                },
                fetchPolicy: 'network-only'
            });

            if (error) {
                throw new Error(`获取用户列表失败：${error.message}`);
            }

            return data.users;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获取单个用户
     * @param id 用户ID
     * @returns 用户信息
     */
    async getUser(id: string) {
        try {
            const { data, error } = await this.client.query({
                query: USER_QUERY,
                variables: { id },
                fetchPolicy: 'network-only'
            });

            if (error) {
                throw new Error(`获取用户详情失败：${error.message}`);
            }

            return data.user;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 创建用户
     * @param userData 用户数据
     * @returns 创建的用户
     */
    async createUser(userData: CreateUserParams) {
        try {
            const { data, errors } = await this.client.mutate({
                mutation: CREATE_USER,
                variables: {
                    input: userData
                },
                refetchQueries: [{
                    query: USERS_QUERY,
                    variables: {
                        input: {
                            page: 1,
                            limit: 10
                        }
                    }
                }]
            });

            if (errors && errors.length > 0) {
                throw new Error(`创建用户失败：${errors[0].message}`);
            }

            return data?.createUser;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 更新用户
     * @param userData 用户更新数据
     * @returns 更新后的用户
     */
    async updateUser(userData: UpdateUserParams) {
        try {
            const { data, errors } = await this.client.mutate({
                mutation: UPDATE_USER,
                variables: {
                    input: userData
                },
                refetchQueries: [{
                    query: USERS_QUERY,
                    variables: {
                        input: {
                            page: 1,
                            limit: 10
                        }
                    }
                }]
            });

            if (errors && errors.length > 0) {
                throw new Error(`更新用户失败：${errors[0].message}`);
            }

            return data?.updateUser;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 删除用户
     * @param id 用户ID
     * @returns 删除的用户
     */
    async deleteUser(id: string) {
        try {
            const { data, errors } = await this.client.mutate({
                mutation: DELETE_USER,
                variables: { id },
                refetchQueries: [{
                    query: USERS_QUERY,
                    variables: {
                        input: {
                            page: 1,
                            limit: 10
                        }
                    }
                }]
            });

            if (errors && errors.length > 0) {
                throw new Error(`删除用户失败：${errors[0].message}`);
            }

            return data?.deleteUser;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 更改用户状态
     * @param id 用户ID
     * @param status 新状态
     * @returns 更新后的用户
     */
    async changeUserStatus(id: string, status: UserStatus) {
        try {
            const { data, errors } = await this.client.mutate({
                mutation: CHANGE_USER_STATUS,
                variables: { id, status },
                refetchQueries: [{
                    query: USERS_QUERY,
                    variables: {
                        input: {
                            page: 1,
                            limit: 10
                        }
                    }
                }]
            });

            if (errors && errors.length > 0) {
                throw new Error(`更改用户状态失败：${errors[0].message}`);
            }

            return data?.changeUserStatus;
        } catch (err) {
            throw err;
        }
    }
} 