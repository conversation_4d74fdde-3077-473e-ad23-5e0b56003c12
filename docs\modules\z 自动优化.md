# 组件路径 /pages/dashboard/auto-optimization/index

## 前端

- **实际路径**：slash-admin/src/pages/dashboard/auto-optimization/index.tsx
- **核心组件**：slash-admin/src/pages/dashboard/workbench/auto-optimization.tsx
- **主要页面/组件**：
  - AutoOptimizationComponent（自动优化主界面，含预算、出价、定向等自动优化Tab）
- **主要依赖API定义**：如有API，通常为 autoOptimization.graphql.ts 或相关服务

### 功能与数据结构

- 支持预算、出价、定向等多维度自动优化
- 支持自动/手动切换、优化进度、优化建议、优化历史等
- 典型数据结构：
  ```ts
  type OptimizationType = "budget" | "bidding" | "targeting";
  interface OptimizationDataBase {
    status: string;
    progress: number;
    lastOptimized: string;
    recommendations: number;
  }
  interface BudgetData extends OptimizationDataBase { potentialSavings: string; }
  interface BiddingData extends OptimizationDataBase { potentialImprovement: string; }
  interface TargetingData extends OptimizationDataBase { potentialReach: string; }
  interface OptimizationData {
    budget: BudgetData;
    bidding: BiddingData;
    targeting: TargetingData;
  }
  ```
- 典型优化历史结构：
  ```ts
  [
    { date: "2025-05-07", type: "budget", result: "节省预算 ¥1,200" },
    { date: "2025-05-06", type: "bidding", result: "提升点击率 8.5%" },
    { date: "2025-05-05", type: "targeting", result: "增加目标受众 120k" },
  ]
  ```

### 前端表单/组件说明

- 支持预算、出价、定向等多Tab自动优化
- 支持自动/手动切换，优化进度展示
- 支持优化建议、优化历史、优化结果展示
- 支持一键优化、进度条、优化状态等

---

## 后端

- **主要接口**：如有API，通常为GraphQL接口 autoOptimization.graphql.ts 或 RESTful /service/autoOptimization
- **数据库表结构**（auto_optimization，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| type        | varchar     | 优化类型（budget/bidding/targeting） | NOT NULL     |
| status      | varchar     | 状态（active/inactive）    | NOT NULL     |
| progress    | int         | 优化进度                   | 可空         |
| lastOptimized | timestamp | 最近优化时间               | 可空         |
| recommendations | int     | 优化建议数                 | 可空         |
| result      | varchar     | 优化结果                   | 可空         |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| updatedAt   | timestamp   | 更新时间                   | 自动生成     |
| ...         | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query autoOptimization / mutation optimizeNow / ...
    后端->>数据库: 操作 auto_optimization 表
    数据库-->>后端: 返回优化数据
    后端-->>前端: 返回优化数据
```

---

## 变更历史/注意事项

- 支持多类型自动优化（预算、出价、定向等）
- 优化建议、进度、历史等需与实际业务流程严格匹配
- 删除/修改优化任务时需校验依赖关系
- 复杂业务字段建议在表结构中用jsonb扩展