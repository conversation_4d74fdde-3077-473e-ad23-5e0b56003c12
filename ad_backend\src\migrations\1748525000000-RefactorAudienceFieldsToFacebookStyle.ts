import { MigrationInterface, QueryRunner } from "typeorm";

export class RefactorAudienceFieldsToFacebookOfficialNames1680000000000 implements MigrationInterface {
    name = 'RefactorAudienceFieldsToFacebookOfficialNames1680000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 新增新字段
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "geo_locations" jsonb`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "excluded_geo_locations" jsonb`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "locales" integer[]`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "genders" integer[]`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "age_min" integer`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "age_max" integer`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "custom_audiences" jsonb`);

        // 数据迁移
        await queryRunner.query(`
            UPDATE "audience"
            SET geo_locations = jsonb_build_object('countries', country),
                excluded_geo_locations = jsonb_build_object('countries', "excludeCountry"),
                locales = NULLIF(language, '{}')::integer[],
                age_min = "ageMin",
                age_max = "ageMax",
                custom_audiences = "customAudiences"
        `);

        // 删除旧字段（保留 platform 字段）
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "country"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "excludeCountry"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "language"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "ageMin"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "ageMax"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "geoLocations"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "excludedGeoLocations"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "customAudiences"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 可选：实现回滚逻辑
    }
} 