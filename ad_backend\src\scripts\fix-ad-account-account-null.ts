import { DataSource } from 'typeorm';
import { AdAccount } from '../modules/ad-platform/entities/ad-account.entity';
import { AppDataSource } from '../config/data-source';

async function fixAdAccountAccountNull() {
    const dataSource: DataSource = AppDataSource;
    if (!dataSource.isInitialized) {
        await dataSource.initialize();
    }
    const repo = dataSource.getRepository(AdAccount);
    const result = await repo.createQueryBuilder()
        .update(AdAccount)
        .set({ account: '' })
        .where('account IS NULL')
        .execute();
    console.log(`已修复 ${result.affected} 条 account 字段为 null 的 ad_account 记录。`);
    await dataSource.destroy();
}

fixAdAccountAccountNull().catch(e => {
    console.error(e);
    process.exit(1);
}); 