import { gql } from '@apollo/client';

export const GET_LANDING_PAGES = gql`
  query getLandingPages {
    getLandingPages {
      id
      code
      title
      url
      slogan
      appIcon
      background
      description
      star
      promoImages
      creator
      createdAt
      tenantId
    }
  }
`;

export const CREATE_LANDING_PAGE = gql`
  mutation createLandingPage(
    $title: String!,
    $url: String!,
    $slogan: String!,
    $appIcon: [String!],
    $background: [String!],
    $description: String,
    $star: Float!,
    $promoImages: [String!],
    $creator: String
  ) {
    createLandingPage(
      title: $title,
      url: $url,
      slogan: $slogan,
      appIcon: $appIcon,
      background: $background,
      description: $description,
      star: $star,
      promoImages: $promoImages,
      creator: $creator
    ) {
      id
      code
      title
      url
      slogan
      appIcon
      background
      description
      star
      promoImages
      creator
      createdAt
      tenantId
    }
  }
`;

export const UPDATE_LANDING_PAGE = gql`
  mutation updateLandingPage($id: String!, $input: UpdateLandingPageInput!) {
    updateLandingPage(id: $id, input: $input) {
      id
      code
      title
      url
      slogan
      appIcon
      background
      description
      star
      promoImages
      creator
      createdAt
      tenantId
    }
  }
`;

export const DELETE_LANDING_PAGE = gql`
  mutation deleteLandingPage($id: String!) {
    deleteLandingPage(id: $id)
  }
`;

// Promotion CRUD
export const GET_PROMOTIONS = gql`
  query getPromotions {
    getPromotions {
      id
      code
      platform
      landingPageCode
      landingPageUrl
      status
      createdAt
      creator
      platformParams
      landingPageName
      tenantId
    }
  }
`;

export const CREATE_PROMOTION = gql`
  mutation createPromotion($input: CreatePromotionDto!) {
    createPromotion(input: $input) {
      id
      code
      platform
      landingPageCode
      landingPageUrl
      status
      createdAt
      creator
      platformParams
      landingPageName
      tenantId
    }
  }
`;

export const UPDATE_PROMOTION = gql`
  mutation updatePromotion($id: String!, $input: UpdatePromotionDto!) {
    updatePromotion(id: $id, input: $input) {
      id
      code
      platform
      landingPageCode
      landingPageUrl
      status
      createdAt
      creator
      platformParams
      landingPageName
      tenantId
    }
  }
`;

export const DELETE_PROMOTION = gql`
  mutation deletePromotion($id: String!) {
    deletePromotion(id: $id)
  }
`;

export const GET_DOMAINS = gql`
  query {
    myDomains {
      id
      name
      cnameTarget
      status
      sslStatus
      createdAt
      error
    }
  }
`;

export const ADD_DOMAIN = gql`
  mutation($name: String!) {
    addDomain(name: $name) {
      id
      name
      cnameTarget
      status
      sslStatus
      createdAt
      error
    }
  }
`;

export const DELETE_DOMAIN = gql`
  mutation($id: String!) {
    deleteDomain(id: $id)
  }
`;

export const REFRESH_DOMAIN = gql`
  mutation($id: String!) {
    refreshDomainStatus(id: $id) {
      id
      status
      sslStatus
      error
    }
  }
`; 