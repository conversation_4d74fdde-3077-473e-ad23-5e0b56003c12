import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAccountToAdAccountAgain20240529003000 implements MigrationInterface {
    name = 'AddAccountToAdAccountAgain20240529003000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "account" character varying(128);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "account";`);
    }
} 