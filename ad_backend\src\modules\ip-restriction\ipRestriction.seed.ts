import { Connection } from 'typeorm';
import { ipRestriction } from './ipRestriction.entity';

export const createipRestriction = async (
	connection: Connection
): Promise<ipRestriction[]> => {
	const employees = new Array<ipRestriction>();

	const employee1 = new ipRestriction();
	employees.push(employee1);

	const employee2 = new ipRestriction();
	employees.push(employee2);

	return await insertipRestriction(connection, employees);
};

const insertipRestriction = async (
	connection: Connection,
	employees: ipRestriction[]
) => {
	const repo = connection.getRepository(ipRestriction);

	// await Promise.all(
	// 	employees.map(async (emp) => {
	// 		const existed = await repo.findOne({
	// 			where: {
	// 				url: emp.url,
	// 			},
	// 		});

	// 		if (existed) {
	// 			emp.id = existed.id;
	// 		}
	// 	})
	// );

	return await repo.save(employees);
};
