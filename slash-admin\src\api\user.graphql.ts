import { gql } from '@apollo/client';

export const USERS_QUERY = gql`
  query Users($input: UserListInput, $sorting: [UserSortInput!]) {
    users(input: $input, sorting: $sorting) {
      nodes {
        id
        username
        email
        avatar
        fullName
        phone
        address
        registerIp
        status
        roles {
          id
          name
        }
        groups {
          id
          name
        }
        createdAt
        updatedAt
        tenantId
        tenantName
      }
      totalCount
      pageInfo {
        hasNextPage
        hasPreviousPage
      }
    }
  }
`;

export const USER_QUERY = gql`
  query User($id: ID!) {
    user(id: $id) {
      id
      username
      email
      avatar
      fullName
      phone
      address
      registerIp
      status
      roles {
        id
        name
      }
      groups {
        id
        name
      }
      createdAt
      updatedAt
      tenantId
      tenantName
    }
  }
`;

export const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      id
      username
      email
      avatar
      fullName
      phone
      address
      registerIp
      status
      roles {
        id
        name
      }
      groups {
        id
        name
      }
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_USER = gql`
  mutation UpdateUser($input: UpdateUserInput!) {
    updateUser(input: $input) {
      id
      username
      email
      avatar
      fullName
      phone
      address
      registerIp
      status
      roles {
        id
        name
      }
      groups {
        id
        name
      }
      createdAt
      updatedAt
    }
  }
`;

export const DELETE_USER = gql`
  mutation DeleteUser($id: ID!) {
    deleteUser(id: $id) {
      id
      username
    }
  }
`;

export const CHANGE_USER_STATUS = gql`
  mutation ChangeUserStatus($id: ID!, $status: UserStatus!) {
    changeUserStatus(id: $id, status: $status) {
      id
      username
      status
    }
  }
`; 