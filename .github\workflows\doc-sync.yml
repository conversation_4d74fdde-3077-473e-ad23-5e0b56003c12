name: 文档同步检查

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'ad_backend/src/modules/**/*.entity.ts'
      - 'ad_backend/src/modules/**/*.resolver.ts'
      - 'slash-admin/src/api/**/*.graphql.ts'
      - 'slash-admin/src/pages/**/*.tsx'
      - 'docs/modules/**/*.md'
  
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'ad_backend/src/modules/**/*.entity.ts'
      - 'ad_backend/src/modules/**/*.resolver.ts'
      - 'slash-admin/src/api/**/*.graphql.ts'
      - 'slash-admin/src/pages/**/*.tsx'
      - 'docs/modules/**/*.md'

jobs:
  doc-sync-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # 获取完整历史，用于比较变更
    
    - name: 设置 Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 安装依赖
      run: |
        npm install
        npm install -g glob
    
    - name: 检测代码变更
      id: detect-changes
      run: |
        echo "🔍 检测代码变更..."
        if node scripts/doc-sync/detect-changes.js; then
          echo "changes=false" >> $GITHUB_OUTPUT
          echo "✅ 未检测到需要同步的代码变更"
        else
          echo "changes=true" >> $GITHUB_OUTPUT
          echo "⚠️  检测到代码变更，需要检查文档同步"
        fi
    
    - name: 验证文档一致性
      if: steps.detect-changes.outputs.changes == 'true'
      run: |
        echo "🔍 验证文档一致性..."
        node scripts/doc-sync/validate-docs.js
    
    - name: 自动更新文档 (仅限推送到主分支)
      if: |
        steps.detect-changes.outputs.changes == 'true' && 
        github.event_name == 'push' && 
        (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
      run: |
        echo "🔄 自动更新文档..."
        node scripts/doc-sync/update-docs.js
        
        # 检查是否有文档变更
        if git diff --quiet docs/; then
          echo "📄 无文档变更"
        else
          echo "📝 发现文档变更，创建自动提交..."
          
          # 配置 Git
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          # 提交文档变更
          git add docs/
          git commit -m "docs: 自动更新模块文档

- 根据代码变更自动同步文档
- 触发分支: ${{ github.ref }}
- 提交哈希: ${{ github.sha }}
- 变更检测: scripts/doc-sync/detect-changes.js
- 文档更新: scripts/doc-sync/update-docs.js"
          
          # 推送变更
          git push
          
          echo "✅ 文档自动更新完成"
        fi
    
    - name: 上传验证报告
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: doc-validation-report
        path: |
          scripts/doc-sync/validation-report.md
          scripts/doc-sync/update-report.md
          scripts/doc-sync/changes.json
        retention-days: 30
    
    - name: 评论 PR (如果是 Pull Request)
      if: |
        github.event_name == 'pull_request' && 
        steps.detect-changes.outputs.changes == 'true'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          // 读取验证报告
          let reportContent = '## 📝 文档同步检查报告\n\n';
          
          try {
            const validationReport = fs.readFileSync('scripts/doc-sync/validation-report.md', 'utf8');
            reportContent += validationReport;
          } catch (error) {
            reportContent += '❌ 无法读取验证报告\n';
          }
          
          // 添加建议
          reportContent += '\n## 🔧 建议操作\n\n';
          reportContent += '如果发现文档不一致，请运行以下命令更新文档：\n\n';
          reportContent += '```bash\n';
          reportContent += 'npm run doc:sync\n';
          reportContent += '```\n\n';
          reportContent += '或者手动运行：\n\n';
          reportContent += '```bash\n';
          reportContent += 'node scripts/doc-sync/detect-changes.js\n';
          reportContent += 'node scripts/doc-sync/update-docs.js\n';
          reportContent += '```\n';
          
          // 创建或更新评论
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });
          
          const botComment = comments.find(comment => 
            comment.user.type === 'Bot' && 
            comment.body.includes('📝 文档同步检查报告')
          );
          
          if (botComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: botComment.id,
              body: reportContent
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: reportContent
            });
          }

  doc-lint:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 设置 Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: 检查 Markdown 格式
      run: |
        echo "🔍 检查 Markdown 格式..."
        
        # 检查文档文件是否存在
        if [ ! -d "docs/modules" ]; then
          echo "❌ docs/modules 目录不存在"
          exit 1
        fi
        
        # 检查 Markdown 文件格式
        find docs/modules -name "*.md" -exec echo "检查文件: {}" \;
        
        # 可以添加更多的格式检查，如：
        # - markdownlint
        # - 链接检查
        # - 图片引用检查等
        
        echo "✅ Markdown 格式检查完成"
    
    - name: 检查文档完整性
      run: |
        echo "🔍 检查文档完整性..."
        
        # 检查必需的文档文件是否存在
        required_docs=(
          "z 广告账户.md"
          "z 卡片管理.md"
          "z 广告系列.md"
          "z 受众管理.md"
          "z 素材管理.md"
        )
        
        missing_docs=()
        for doc in "${required_docs[@]}"; do
          if [ ! -f "docs/modules/$doc" ]; then
            missing_docs+=("$doc")
          fi
        done
        
        if [ ${#missing_docs[@]} -gt 0 ]; then
          echo "❌ 缺少以下必需文档:"
          printf '  - %s\n' "${missing_docs[@]}"
          exit 1
        fi
        
        echo "✅ 文档完整性检查通过"
