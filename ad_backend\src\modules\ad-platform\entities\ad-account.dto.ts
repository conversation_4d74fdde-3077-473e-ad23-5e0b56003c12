import { ObjectType, Field, ID, InputType } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import GraphQLJSON from 'graphql-type-json';
import { TenantDTO } from '../../../entity/tenant.dto';

@ObjectType('AdAccountDTO')
export class AdAccountDTO {
  @Field(() => ID, { description: '内部UUID' })
  id: string;

  @IDField(() => ID)
  accountId: string;

  @FilterableField()
  platform: string;

  @FilterableField()
  account: string;

  @FilterableField({ nullable: true })
  password?: string;

  @FilterableField({ nullable: true })
  accessToken?: string;

  @Field(() => TenantDTO)
  tenant: TenantDTO;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  raw?: any;

  @FilterableField()
  status: string;

  @FilterableField({ nullable: true })
  riskLevel?: string;

  @FilterableField({ nullable: true })
  group?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  oauth?: any;

  @Field({ nullable: true })
  createdAt?: Date;

  @FilterableField({ nullable: true })
  holder?: string;

  @FilterableField({ nullable: true })
  adNumber?: string;

  @FilterableField({ nullable: true })
  remark?: string;

  @Field({ nullable: true })
  updatedAt?: Date;

  @FilterableField({ nullable: true })
  tag?: string;

  @FilterableField({ nullable: true })
  channel?: string;

  @Field({ nullable: true })
  fbStatus?: string;
}

@ObjectType()
export class AdAccountUploadResult {
  @Field()
  account: string;
  @Field()
  status: string;
}

@InputType()
export class AdAccountInput {
  @Field({ nullable: true })
  accountId?: string;
  @Field()
  account: string;
  @Field({ nullable: true })
  password?: string;
  @Field({ nullable: true })
  tag?: string;
  @Field({ nullable: true })
  channel?: string;
  @Field({ nullable: true })
  holder?: string;
  @Field({ nullable: true })
  adNumber?: string;
  @Field({ nullable: true })
  remark?: string;
}
