import { Injectable } from '@nestjs/common';
// 替换 axios 为 axiosProxyPost
import { axiosProxyPost } from '../../../utils/axios-proxy';

@Injectable()
export class PixelService {
    async createAndAssignPixel(
        pixelName: string,
        businessId: string,
        adAccountId: string,
        accessToken: string,
    ) {
        try {
            // 打印入参日志
            console.log('[PixelService.createAndAssignPixel] params:', {
                pixelName,
                businessId,
                adAccountId,
                accessToken: accessToken ? accessToken.slice(0, 6) + '...' : accessToken,
            });
            // 1. 创建 Pixel
            const createUrl = `https://graph.facebook.com/v23.0/${businessId}/adspixels`;
            const createParams = { name: pixelName, access_token: accessToken };
            console.log('[PixelService.createAndAssignPixel] createPixel URL:', createUrl);
            console.log('[PixelService.createAndAssignPixel] createPixel params:', { ...createParams, access_token: accessToken ? accessToken.slice(0, 6) + '...' : accessToken });
            let createRes;
            try {
                createRes = await axiosProxyPost(createUrl, null, { params: createParams });
                console.log('[PixelService.createAndAssignPixel] createPixel response:', createRes?.data);
            } catch (e) {
                const errData = e?.response?.data || e.message || e;
                console.error('[PixelService.createAndAssignPixel] createPixel error:', errData);
                throw new Error('Facebook createPixel error: ' + JSON.stringify(errData));
            }
            const pixelId = createRes.data.id;

            // 2. 授权 Pixel 给广告账户
            const assignUrl = `https://graph.facebook.com/v23.0/${pixelId}/assigned_ad_accounts`;
            const assignParams = { adaccount_id: adAccountId, access_token: accessToken };
            console.log('[PixelService.createAndAssignPixel] assignPixel URL:', assignUrl);
            console.log('[PixelService.createAndAssignPixel] assignPixel params:', { ...assignParams, access_token: accessToken ? accessToken.slice(0, 6) + '...' : accessToken });
            let assignRes;
            try {
                assignRes = await axiosProxyPost(assignUrl, null, { params: assignParams });
                console.log('[PixelService.createAndAssignPixel] assignPixel response:', assignRes?.data);
            } catch (e) {
                const errData = e?.response?.data || e.message || e;
                console.error('[PixelService.createAndAssignPixel] assignPixel error:', errData);
                throw new Error('Facebook assignPixel error: ' + JSON.stringify(errData));
            }

            // 3. 返回新 Pixel 信息
            return { id: pixelId, name: pixelName };
        } catch (e) {
            // 打印 Facebook API 错误详细信息
            console.error('Facebook Pixel API error:', e?.response?.data || e.message || e);
            throw new Error(e?.response?.data?.error?.message || e.message || 'Facebook Pixel API 调用失败');
        }
    }
} 