import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateVccTransactionsTableOnly1749039576000 implements MigrationInterface {
    name = 'CreateVccTransactionsTableOnly1749039576000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建枚举类型
        await queryRunner.query(`CREATE TYPE "public"."vcc_transactions_status_enum" AS ENUM('success', 'pending', 'failed')`);
        await queryRunner.query(`CREATE TYPE "public"."vcc_transactions_type_enum" AS ENUM('payment', 'deposit', 'refund')`);

        // 创建VCC交易表
        await queryRunner.query(`CREATE TABLE "vcc_transactions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "cardId" uuid NOT NULL, "cardNo" character varying(50) NOT NULL, "amount" numeric(12,2) NOT NULL, "merchant" character varying(100) NOT NULL, "transactionTime" TIMESTAMP NOT NULL, "status" "public"."vcc_transactions_status_enum" NOT NULL DEFAULT 'success', "type" "public"."vcc_transactions_type_enum" NOT NULL, "facebookAccountId" character varying(50), "facebookTransactionId" character varying(100), "campaignName" character varying(200), "description" text, "tenantId" uuid NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_7c51d01572400db1b9f5e74152c" PRIMARY KEY ("id")); 
            COMMENT ON COLUMN "vcc_transactions"."cardId" IS 'VCC卡片ID'; 
            COMMENT ON COLUMN "vcc_transactions"."cardNo" IS '卡号（脱敏显示）'; 
            COMMENT ON COLUMN "vcc_transactions"."amount" IS '交易金额'; 
            COMMENT ON COLUMN "vcc_transactions"."merchant" IS '商户名称'; 
            COMMENT ON COLUMN "vcc_transactions"."transactionTime" IS '交易时间'; 
            COMMENT ON COLUMN "vcc_transactions"."status" IS '交易状态'; 
            COMMENT ON COLUMN "vcc_transactions"."type" IS '交易类型'; 
            COMMENT ON COLUMN "vcc_transactions"."facebookAccountId" IS 'Facebook广告账户ID'; 
            COMMENT ON COLUMN "vcc_transactions"."facebookTransactionId" IS 'Facebook交易ID'; 
            COMMENT ON COLUMN "vcc_transactions"."campaignName" IS '广告活动名称'; 
            COMMENT ON COLUMN "vcc_transactions"."description" IS '交易描述'; 
            COMMENT ON COLUMN "vcc_transactions"."tenantId" IS '租户ID'; 
            COMMENT ON COLUMN "vcc_transactions"."createdAt" IS '创建时间'; 
            COMMENT ON COLUMN "vcc_transactions"."updatedAt" IS '更新时间'`);

        // 创建索引
        await queryRunner.query(`CREATE INDEX "IDX_d286427af07aeb521c484e1610" ON "vcc_transactions" ("merchant")`);
        await queryRunner.query(`CREATE INDEX "IDX_30e4a47cdd2551525afe22ce8a" ON "vcc_transactions" ("status", "type")`);
        await queryRunner.query(`CREATE INDEX "IDX_911d900fd5145cc41371fc18ea" ON "vcc_transactions" ("tenantId", "transactionTime")`);
        await queryRunner.query(`CREATE INDEX "IDX_6ecdda6d95823a62035dd3670f" ON "vcc_transactions" ("cardId", "transactionTime")`);

        // 添加外键约束
        await queryRunner.query(`ALTER TABLE "vcc_transactions" ADD CONSTRAINT "FK_cd9fd0946e8f31285bb0b58e378" FOREIGN KEY ("cardId") REFERENCES "vcc_cards"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vcc_transactions" ADD CONSTRAINT "FK_33a612430ca2295484b987547bd" FOREIGN KEY ("tenantId") REFERENCES "tenant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除外键约束
        await queryRunner.query(`ALTER TABLE "vcc_transactions" DROP CONSTRAINT "FK_33a612430ca2295484b987547bd"`);
        await queryRunner.query(`ALTER TABLE "vcc_transactions" DROP CONSTRAINT "FK_cd9fd0946e8f31285bb0b58e378"`);

        // 删除索引
        await queryRunner.query(`DROP INDEX "public"."IDX_6ecdda6d95823a62035dd3670f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_911d900fd5145cc41371fc18ea"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_30e4a47cdd2551525afe22ce8a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d286427af07aeb521c484e1610"`);

        // 删除表
        await queryRunner.query(`DROP TABLE "vcc_transactions"`);

        // 删除枚举类型
        await queryRunner.query(`DROP TYPE "public"."vcc_transactions_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."vcc_transactions_status_enum"`);
    }
} 