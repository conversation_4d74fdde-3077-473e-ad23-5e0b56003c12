import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeAdCampaignFKToCampaign20240614020000 implements MigrationInterface {
    name = 'ChangeAdCampaignFKToCampaign20240614020000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 删除原有外键
        await queryRunner.query(`ALTER TABLE "ad" DROP CONSTRAINT IF EXISTS "FK_9360da70162edc1c52a71ab0933"`);
        // 新增指向 campaign 的外键
        await queryRunner.query(`ALTER TABLE "ad" ADD CONSTRAINT "FK_ad_campaign_to_campaign" FOREIGN KEY ("adCampaignId") REFERENCES "campaign"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回滚时删除新外键，恢复原外键
        await queryRunner.query(`ALTER TABLE "ad" DROP CONSTRAINT IF EXISTS "FK_ad_campaign_to_campaign"`);
        await queryRunner.query(`ALTER TABLE "ad" ADD CONSTRAINT "FK_9360da70162edc1c52a71ab0933" FOREIGN KEY ("adCampaignId") REFERENCES "ad_campaign"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
} 