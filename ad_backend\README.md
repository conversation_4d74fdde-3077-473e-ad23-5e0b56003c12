# 后端项目文档（ad_backend）

## 技术栈
- NestJS（主框架）
- TypeORM（数据库 ORM）
- GraphQL + nestjs-query（自动生成 CRUD）

## 二、依赖安装与环境配置
```bash
pnpm install
cp .env.example .env # 按需配置数据库、端口、JWT等
```

## 三、启动/调试/部署
- 本地开发：`pnpm start:dev`
- 生产部署：`pnpm build && pnpm start:prod`
- GraphQL Playground：默认 http://localhost:3005/graphql
- schema 自动生成路径：`ad_backend/generated/schemas/schema.graphql`
- 推荐使用 VSCode + ESLint + Prettier 插件

## 四、核心目录结构
| 目录/文件                  | 说明                   |
|---------------------------|------------------------|
| src/modules/ad-platform/   | 广告组、广告、受众等核心模块 |
| src/common/                | 通用装饰器、拦截器、工具方法 |
| src/auth/                  | 权限认证模块           |
| generated/schemas/         | 自动生成的 GraphQL schema |

## 五、Entity/DTO 规范
- Entity 与 DTO 字段、类型、可选性务必保持一致
- 变更 Entity/DTO 后需重新启动服务同步 schema
- 推荐每次改动后运行 `npx tsc --noEmit` 检查类型

## 六、GraphQL/REST 集成说明
- GraphQL 入口：`POST /graphql`，详见 docs/api.md
- REST 入口：如 `/api/ad-set`，详见 docs/api.md
- 推荐优先使用 GraphQL，REST 主要用于兼容与同步

## 七、接口变更与同步
- 每次接口/字段变更需同步更新前端类型（codegen）与文档
- 变更 Entity/DTO 后需重启服务，schema 才会同步

## 八、测试与安全建议
- 单元测试：`pnpm test`，建议覆盖核心服务
- e2e 测试：`pnpm test:e2e`，建议覆盖主要接口
- JWT 鉴权，建议定期轮换密钥与权限检查
- 生产环境禁止开启 Playground

## 九、常见问题 FAQ
- CRUDResolver 类型报错：检查 Entity/DTO 字段是否一致
- schema 未同步：确认 autoSchemaFile 配置、重启服务
- 类型冲突：优先以 Entity/DTO 字段为准
- 数据库连接失败：检查 .env 配置与数据库状态

## 十、参考文档与接口
- [接口文档](../docs/api.md)
- [联调说明](../docs/integration.md)

如有疑问请联系后端负责人或查阅 docs 目录补充文档。

# 联调说明文档

## 前后端联调流程

1. **后端启动**  
   - 确保 schema 自动生成
   - 检查 Entity/DTO 字段一致性

2. **前端开发**  
   - 通过 Apollo Client 调用 GraphQL
   - 推荐统一封装 hooks

3. **接口变更同步**  
   - 后端变更 Entity/DTO 后需重启服务以同步 schema
   - 前端根据 schema 自动生成 TypeScript 类型（如使用 codegen）

4. **调试建议**  
   - 推荐使用 GraphQL Playground/Postman 联调接口
   - 前端可直接在 hooks 里调试 GraphQL 查询/变更

---
