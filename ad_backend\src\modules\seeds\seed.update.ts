import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SeedModule } from './seed.module';
import { SeedDataService } from './seed.service';

const log = new Logger('Seed Update');

const runSeedUpdate = async () => {
	const app = await NestFactory.create(SeedModule);
	const seedDataService = app.get(SeedDataService);

	await seedDataService.executeUpdate(true);
	await app.close();
};

runSeedUpdate()
	.then(() => {
		log.debug('Seed update completed!');
	})
	.catch((error) => {
		log.error('Seed update failed!', error);
		throw error;
	});
