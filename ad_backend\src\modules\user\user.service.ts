import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { User, UserStatus } from './user.entity';
import { CreateUserInput } from './dto/create-user.input';
import { UpdateUserInput } from './dto/update-user.input';
import { UserFilterInput, UserSortInput } from './dto/user-filter.input';
import { Role } from '../role/role.entity';
import { Group } from '../group/group.entity';
import { Route } from '../route/route.entity';
import { RouteDTO } from '../route/dto/route.dto';
import { Tenant } from '../../entity/tenant.entity';

@Injectable()
export class UserService {
    constructor(
        @InjectRepository(User)
        private userRepository: Repository<User>,
        @InjectRepository(Role)
        private roleRepository: Repository<Role>,
        @InjectRepository(Group)
        private groupRepository: Repository<Group>,
        @InjectRepository(Tenant)
        private tenantRepository: Repository<Tenant>,
    ) { }

    // 将 Route 实体转换为 RouteDTO
    private convertRouteToDTO(route: Route): RouteDTO {
        return {
            id: route.id,
            parentId: route.parent?.id || null,
            name: route.name,
            path: route.path,
            component: route.component,
            icon: route.icon,
            order: route.order,
            type: route.type,
            status: route.status,
            isHidden: route.isHidden,
            description: route.description,
            createdAt: route.createdAt,
            updatedAt: route.updatedAt,
            children: []
        };
    }

    // 构建路由树的辅助方法（补全父节点，只组装勾选的节点）
    private buildRouteTree(routes: Route[]): RouteDTO[] {
        if (!routes || routes.length === 0) return [];
        // 1. 补全所有父节点
        const allRoutesMap = new Map<string, Route>();
        routes.forEach(route => {
            let current = route;
            while (current) {
                if (!allRoutesMap.has(current.id)) {
                    allRoutesMap.set(current.id, current);
                }
                current = current.parent;
            }
        });
        const allRoutes = Array.from(allRoutesMap.values());
        // 2. 只保留被勾选的节点id
        const checkedIds = new Set(routes.map(r => r.id));
        // 3. 转换为DTO
        const routeMap = new Map<string, RouteDTO>();
        allRoutes.forEach(route => {
            const routeDto: RouteDTO = {
                id: route.id,
                parentId: route.parent?.id || null,
                name: route.name,
                path: route.path,
                component: route.component,
                icon: route.icon,
                order: route.order,
                type: route.type,
                status: route.status,
                isHidden: route.isHidden,
                description: route.description,
                createdAt: route.createdAt,
                updatedAt: route.updatedAt,
                children: []
            };
            routeMap.set(route.id, routeDto);
        });
        // 4. 组装树，只挂载勾选的子节点
        const rootNodes: RouteDTO[] = [];
        allRoutes.forEach(route => {
            const routeDto = routeMap.get(route.id);
            if (route.parent) {
                const parentDto = routeMap.get(route.parent.id);
                if (parentDto && checkedIds.has(route.id)) {
                    parentDto.children = parentDto.children || [];
                    parentDto.children.push(routeDto);
                }
            } else {
                if (checkedIds.has(route.id) || (routeDto.children && routeDto.children.length > 0)) {
                    rootNodes.push(routeDto);
                }
            }
        });
        // 5. 对每个节点的子节点按 order 排序
        const sortChildren = (nodes: RouteDTO[]) => {
            nodes.sort((a, b) => (a.order || 0) - (b.order || 0));
            nodes.forEach(node => {
                if (node.children && node.children.length > 0) {
                    sortChildren(node.children);
                }
            });
        };
        sortChildren(rootNodes);
        return rootNodes;
    }

    async findAll(
        filter?: UserFilterInput,
        page = 1,
        limit = 10,
        sorting?: UserSortInput[],
        currentUser?: User
    ): Promise<{ nodes: User[], totalCount: number }> {
        const skip = (page - 1) * limit;

        // 构建查询条件
        let queryBuilder = this.userRepository.createQueryBuilder('user')
            .leftJoinAndSelect('user.roles', 'roles')
            .leftJoinAndSelect('user.groups', 'groups')
            .leftJoinAndSelect('user.tenant', 'tenant');

        // 应用过滤条件
        if (filter) {
            if (filter.username) {
                queryBuilder = queryBuilder.andWhere('user.username LIKE :username', { username: `%${filter.username}%` });
            }

            if (filter.email) {
                queryBuilder = queryBuilder.andWhere('user.email LIKE :email', { email: `%${filter.email}%` });
            }

            if (filter.fullName) {
                queryBuilder = queryBuilder.andWhere('user.fullName LIKE :fullName', { fullName: `%${filter.fullName}%` });
            }

            if (filter.phone) {
                queryBuilder = queryBuilder.andWhere('user.phone LIKE :phone', { phone: `%${filter.phone}%` });
            }

            if (filter.status) {
                queryBuilder = queryBuilder.andWhere('user.status = :status', { status: filter.status });
            }

            if (filter.roleIds && filter.roleIds.length > 0) {
                queryBuilder = queryBuilder.andWhere('roles.id IN (:...roleIds)', { roleIds: filter.roleIds });
            }

            if (filter.groupIds && filter.groupIds.length > 0) {
                queryBuilder = queryBuilder.andWhere('groups.id IN (:...groupIds)', { groupIds: filter.groupIds });
            }
        }

        // 应用排序
        if (sorting && sorting.length > 0) {
            sorting.forEach(sort => {
                queryBuilder = queryBuilder.addOrderBy(`user.${sort.field}`, sort.direction);
            });
        } else {
            queryBuilder = queryBuilder.orderBy('user.createdAt', 'DESC');
        }

        // 分页
        queryBuilder = queryBuilder.skip(skip).take(limit);

        // 执行查询
        const [nodes, totalCount] = await queryBuilder.getManyAndCount();

        // 判断当前用户是否为超级管理员
        const isSuperAdmin = currentUser?.roles?.some(r => r.name === '超级管理员');

        let filteredNodes = nodes;

        if (!isSuperAdmin) {
            // 1. 过滤掉拥有超级管理员角色的用户
            filteredNodes = filteredNodes.filter(user =>
                user.roles && user.roles.some(role => role.name === '超级管理员') === false
            );
            // 2. 过滤每个用户的roles
            filteredNodes = filteredNodes.map(user => {
                if (user.roles) {
                    user.roles = user.roles
                        .filter(role => role.name !== '超级管理员')
                        .map(role => ({
                            ...role,
                            routeIds: role.routes?.map(route => route.id) || [],
                            routeList: this.buildRouteTree(role.routes || [])
                        }));
                }
                return user;
            });
        } else {
            // 超级管理员看到所有账号和所有角色
            filteredNodes = filteredNodes.map(user => {
                if (user.roles) {
                    user.roles = user.roles.map(role => ({
                        ...role,
                        routeIds: role.routes?.map(route => route.id) || [],
                        routeList: this.buildRouteTree(role.routes || [])
                    }));
                }
                return user;
            });
        }

        return { nodes: filteredNodes, totalCount: filteredNodes.length };
    }

    async findOne(id: string, currentUser?: User, filterSuperAdminRole = true): Promise<User> {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['roles', 'roles.routes', 'roles.routes.parent', 'groups', 'tenant']
        });

        if (!user) {
            throw new NotFoundException(`用户ID ${id} 不存在`);
        }
        // 租户被停用时禁止操作
        if (user.tenant && user.tenant.isActive === false) {
            throw new NotFoundException('所属租户已被停用');
        }

        // 判断当前用户是否为超级管理员
        const isSuperAdmin = currentUser?.roles?.some(r => r.name === '超级管理员');

        // 为每个角色添加 routeIds 和 routeList，并根据权限过滤超级管理员角色
        if (user.roles) {
            user.roles = user.roles
                .filter(role => !filterSuperAdminRole || isSuperAdmin || role.name !== '超级管理员')
                .map(role => ({
                    ...role,
                    routeIds: role.routes?.map(route => route.id) || [],
                    routeList: this.buildRouteTree(role.routes || [])
                }));
        }

        return user;
    }

    async findByUsername(username: string, currentUser?: User, filterSuperAdminRole = true): Promise<User | null> {
        const user = await this.userRepository.findOne({
            where: { username },
            relations: ['roles', 'roles.routes', 'roles.routes.parent', 'groups', 'tenant']
        });
        if (user?.tenant && user.tenant.isActive === false) {
            return null;
        }

        // 判断当前用户是否为超级管理员
        const isSuperAdmin = currentUser?.roles?.some(r => r.name === '超级管理员');

        // 为每个角色添加 routeIds 和 routeList，并根据权限过滤超级管理员角色
        if (user?.roles) {
            user.roles = user.roles
                .filter(role => !filterSuperAdminRole || isSuperAdmin || role.name !== '超级管理员')
                .map(role => ({
                    ...role,
                    routeIds: role.routes?.map(route => route.id) || [],
                    routeList: this.buildRouteTree(role.routes || [])
                }));
        }

        return user;
    }

    async findByEmail(email: string): Promise<User | null> {
        const user = await this.userRepository.findOne({
            where: { email },
            relations: ['roles', 'groups', 'tenant']
        });
        if (user?.tenant && user.tenant.isActive === false) {
            return null;
        }
        return user;
    }

    async create(createUserInput: CreateUserInput, currentUser?: User): Promise<User> {
        // 检查用户名是否已存在
        const usernameExists = await this.findByUsername(createUserInput.username, currentUser);
        if (usernameExists) {
            throw new ConflictException('用户名已存在');
        }

        // 检查邮箱是否已存在
        const emailExists = await this.findByEmail(createUserInput.email);
        if (emailExists) {
            throw new ConflictException('邮箱已存在');
        }

        // 配额校验：用户数
        if (createUserInput.tenantId) {
            const tenant = await this.tenantRepository.findOne({ where: { id: createUserInput.tenantId } });
            if (tenant && tenant.features && tenant.features.maxUsers) {
                const userCount = await this.userRepository.count({ where: { tenant: { id: createUserInput.tenantId } } });
                if (userCount >= tenant.features.maxUsers) {
                    throw new BadRequestException('当前套餐用户数已达上限，请升级套餐');
                }
            }
        }

        // 处理角色和群组关联
        let roles: Role[] = [];
        let tenant = null;
        if (createUserInput.roleIds && createUserInput.roleIds.length > 0) {
            roles = await this.roleRepository.findBy({ id: In(createUserInput.roleIds) });
            // 检查是否包含超级管理员角色
            const hasSuperAdmin = roles.some(role => role.name === '超级管理员');
            // 校验租户逻辑
            if (!hasSuperAdmin) {
                if (!createUserInput.tenantId) {
                    throw new ConflictException('非系统管理员账号必须指定租户');
                }
                tenant = await this.userRepository.manager.findOne(Tenant, { where: { id: createUserInput.tenantId } });
                if (!tenant) {
                    throw new ConflictException('租户不存在');
                }
            }
            // 权限校验
            if (hasSuperAdmin) {
                if (!currentUser || !currentUser.roles?.some(r => r.name === '超级管理员')) {
                    throw new ConflictException('只有超级管理员可以创建超级管理员账号');
                }
            }
        } else {
            // 没有分配任何角色，必须指定租户
            if (!createUserInput.tenantId) {
                throw new ConflictException('必须指定租户');
            }
            tenant = await this.userRepository.manager.findOne(Tenant, { where: { id: createUserInput.tenantId } });
            if (!tenant) {
                throw new ConflictException('租户不存在');
            }
        }

        let groups: Group[] = [];
        if (createUserInput.groupIds && createUserInput.groupIds.length > 0) {
            groups = await this.groupRepository.findBy({ id: In(createUserInput.groupIds) });
        }

        // 创建用户实例
        const user = this.userRepository.create({
            ...createUserInput,
            roles,
            groups,
            tenant,
        });

        // 保存并返回用户
        return this.userRepository.save(user);
    }

    async update(id: string, updateUserInput: UpdateUserInput, currentUser?: User): Promise<User> {
        // 检查用户是否存在
        const user = await this.findOne(id, currentUser);

        // 如果要更新用户名，检查是否已存在
        if (updateUserInput.username && updateUserInput.username !== user.username) {
            const usernameExists = await this.findByUsername(updateUserInput.username, currentUser);
            if (usernameExists) {
                throw new ConflictException('用户名已存在');
            }
        }

        // 如果要更新邮箱，检查是否已存在
        if (updateUserInput.email && updateUserInput.email !== user.email) {
            const emailExists = await this.findByEmail(updateUserInput.email);
            if (emailExists) {
                throw new ConflictException('邮箱已存在');
            }
        }

        // 处理角色和群组关联
        let hasSuperAdmin = false;
        if (updateUserInput.roleIds) {
            const roles = await this.roleRepository.findBy({ id: In(updateUserInput.roleIds) });
            hasSuperAdmin = roles.some(role => role.name === '超级管理员');
            // 校验租户逻辑
            if (!hasSuperAdmin) {
                if (!updateUserInput.tenantId) {
                    throw new ConflictException('非系统管理员账号必须指定租户');
                }
                const tenant = await this.userRepository.manager.findOne(Tenant, { where: { id: updateUserInput.tenantId } });
                if (!tenant) {
                    throw new ConflictException('租户不存在');
                }
                user.tenant = tenant;
                user.tenantId = updateUserInput.tenantId;
            } else {
                user.tenant = null;
                user.tenantId = null;
            }
            // 权限校验
            if (hasSuperAdmin) {
                if (!currentUser || !currentUser.roles?.some(r => r.name === '超级管理员')) {
                    throw new ConflictException('只有超级管理员可以分配超级管理员角色');
                }
            }
            user.roles = roles;
        }

        if (updateUserInput.groupIds) {
            user.groups = await this.groupRepository.findBy({ id: In(updateUserInput.groupIds) });
        }

        // 更新用户信息
        Object.keys(updateUserInput).forEach(key => {
            if (key !== 'id' && key !== 'roleIds' && key !== 'groupIds' && key !== 'tenantId') {
                user[key] = updateUserInput[key];
            }
        });

        // 保存并返回用户
        return this.userRepository.save(user);
    }

    async delete(id: string): Promise<User> {
        const user = await this.findOne(id);
        await this.userRepository.remove(user);
        return user;
    }

    async changeStatus(id: string, status: UserStatus): Promise<User> {
        console.log('转换前的状态值:', status);
        const user = await this.findOne(id);

        // 处理可能的枚举值不匹配问题
        // 如果收到的是GraphQL枚举键名(ACTIVE, INACTIVE, BLOCKED)，转换为数据库枚举值(active, inactive, blocked)
        if (status as unknown as string === 'ACTIVE') {
            user.status = UserStatus.ACTIVE;
        } else if (status as unknown as string === 'INACTIVE') {
            user.status = UserStatus.INACTIVE;
        } else if (status as unknown as string === 'BLOCKED') {
            user.status = UserStatus.BLOCKED;
        } else {
            user.status = status; // 如果已经是小写值，直接使用
        }

        console.log('转换后的状态值:', user.status);
        return this.userRepository.save(user);
    }

    async getUsers(input: any, sorting: any, currentUser: any) {
        const page = input?.page || 1;
        const limit = input?.limit || 10;
        const filter = input?.filter;
        return this.findAll(filter, page, limit, sorting, currentUser);
    }
} 