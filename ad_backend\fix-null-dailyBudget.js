// fix-null-dailyBudget.js
const { DataSource } = require('typeorm');

const AppDataSource = new DataSource({
    type: 'postgres',
    host: "pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com",
    port: 5432,
    username: "yeeu",
    password: "Yy&20240505",
    database: "ad_auto",
    synchronize: false,
    logging: true,
    entities: [],
    migrations: [],
    subscribers: [],
});

async function fixNullDailyBudget() {
    await AppDataSource.initialize();
    const result = await AppDataSource.query('UPDATE ad_set SET "dailyBudget" = 0 WHERE "dailyBudget" IS NULL;');
    console.log('已将所有 dailyBudget 为 null 的数据补成 0');
    await AppDataSource.destroy();
}

fixNullDailyBudget().catch(err => {
    console.error('修复失败:', err);
    process.exit(1);
});