import { DEFAULT_USER } from "@/_mock/assets";
import { useApolloClient } from "@apollo/client";
import { LoginParams, AuthService, type CurrentUserResponse } from "@/api/services/authService";
import { Icon } from "@/components/icon";
import { useUserActions } from "@/store/userStore";
import { Button } from "@/ui/button";
import { Checkbox } from "@/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/ui/form";
import { Input } from "@/ui/input";
import { cn } from "@/utils";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import { toast } from "sonner";
import { LoginStateEnum, useLoginStateContext } from "./providers/login-provider";
import { StorageEnum } from "#/enum";

export function LoginForm({ className, ...props }: React.ComponentPropsWithoutRef<"form">) {
	const { t } = useTranslation();
	const [loading, setLoading] = useState(false);
	const [remember, setRemember] = useState(true);
	const navigate = useNavigate();
	const client = useApolloClient();
	const authService = new AuthService(client);
	const { setUserToken, setUserInfo } = useUserActions();

	const { loginState, setLoginState } = useLoginStateContext();

	const form = useForm<LoginParams>({
		defaultValues: {
			username: DEFAULT_USER.username,
			password: DEFAULT_USER.password,
		},
	});

	if (loginState !== LoginStateEnum.LOGIN) return null;

	const handleFinish = async (values: LoginParams) => {
		setLoading(true);
		try {
			// 1. 调用登录接口获取token
			const response = await authService.login(values);
			console.log('登录成功，获取到的Token:', response.accessToken);

			// 2. 保存token
			const tokenData = {
				accessToken: response.accessToken
			};
			setUserToken(tokenData);
			localStorage.setItem('auth_token', response.accessToken);

			// 3. 获取用户详细信息（包含路由数据）
			try {
				const userResponse = await authService.getCurrentUser();
				if (userResponse?.me?.user) {
					console.log('获取到用户信息:', userResponse.me.user);

					// 4. 保存用户信息到store
					setUserInfo(userResponse.me.user);

					// 5. 确保用户信息中包含路由数据
					if (userResponse.me.user.roles?.[0]?.routeList?.length) {
						console.log(t('pages.key_2347'));

						// 6. 登录成功提示
						toast.success("登录成功！");

						// 7. 跳转到首页
						navigate("/", { replace: true });
					} else {
						throw new Error(t('pages.key_1317'));
					}
				} else {
					throw new Error(t('pages.key_1316'));
				}
			} catch (error) {
				console.error(t('pages.key_1980'), error);
				toast.error(t('pages.key_1981'));
			}
		} catch (error) {
			console.error(t('pages.key_1651'), error);
			toast.error(error.message || t('pages.key_1652'));
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className={cn("flex flex-col gap-6", className)}>
			<Form {...form} {...props}>
				<form onSubmit={form.handleSubmit(handleFinish)} className="space-y-4">
					<div className="flex flex-col items-center gap-2 text-center">
						<h1 className="text-2xl font-bold">{t("sys.login.signInFormTitle")}</h1>
						<p className="text-balance text-sm text-muted-foreground">{t("sys.login.signInFormDescription")}</p>
					</div>

					<FormField
						control={form.control}
						name="username"
						rules={{ required: t("sys.login.accountPlaceholder") }}
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("sys.login.userName")}</FormLabel>
								<FormControl>
									<Input placeholder="admin/test" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="password"
						rules={{ required: t("sys.login.passwordPlaceholder") }}
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("sys.login.password")}</FormLabel>
								<FormControl>
									<Input type="password" placeholder={t("sys.login.password")} {...field} suppressHydrationWarning />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<div className="flex items-center justify-between">
						<div className="flex items-center space-x-2">
							<Checkbox
								id="remember"
								checked={remember}
								onCheckedChange={(checked: boolean) => setRemember(checked)}
							/>
							<label
								htmlFor="remember"
								className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								{t("sys.login.rememberMe")}
							</label>
						</div>
					</div>

					<Button className="w-full" type="submit" disabled={loading}>
						{loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
						{t("sys.login.signInFormTitle")}
					</Button>

					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<span className="w-full border-t" />
						</div>
						<div className="relative flex justify-center text-xs uppercase">
							<span className="bg-background px-2 text-muted-foreground">
								{t("sys.login.orText")}
							</span>
						</div>
					</div>

					<div className="grid grid-cols-2 gap-6">
						<Button
							variant="outline"
							type="button"
							disabled={loading}
							onClick={() => setLoginState(LoginStateEnum.MOBILE)}
						>
							<Icon icon="ic:baseline-phone-android" className="mr-2 h-4 w-4" />
							{t("sys.login.mobileSignInFormTitle")}
						</Button>
						<Button
							variant="outline"
							type="button"
							disabled={loading}
							onClick={() => setLoginState(LoginStateEnum.QR_CODE)}
						>
							<Icon icon="ic:baseline-qr-code" className="mr-2 h-4 w-4" />
							{t("sys.login.qrSignInFormTitle")}
						</Button>
					</div>

					<div className="flex items-center justify-between gap-4">
						<Button
							variant="link"
							type="button"
							className="px-0"
							disabled={loading}
							onClick={() => setLoginState(LoginStateEnum.REGISTER)}
						>
							{t("sys.login.signUpFormTitle")}
						</Button>
						<Button
							variant="link"
							type="button"
							className="px-0"
							disabled={loading}
							onClick={() => setLoginState(LoginStateEnum.RESET_PASSWORD)}
						>
							{t("sys.login.forgetFormTitle")}
						</Button>
					</div>
				</form>
			</Form>
		</div>
	);
}

export default LoginForm;
