import React, { useState } from 'react';
import { Table, Button, Tag, Modal, Space, Input, Form, Select, message, DatePicker, Popconfirm, Tooltip } from 'antd';
import { PlusOutlined, DeleteOutlined, SyncOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table/interface';
import { VCCCard } from './types';
import { mockVCCCards } from './mockData';
import dayjs from 'dayjs';
import { useQuery, useMutation } from '@apollo/client';
import { GET_AD_ACCOUNT_LIST } from '@/api/adAccount.graphql';
import { GET_VCC_CARDS, CREATE_VCC_CARD, BIND_AD_ACCOUNT_TO_VCC_CARD, DELETE_VCC_CARD, UPDATE_VCC_CARD } from '@/api/vccCard.graphql.ts';
import { useTenant } from '@/hooks/useTenant';
import { FacebookApiService, FacebookAccountData, AdAccountOAuth } from '@/services/facebookApi';
import { FacebookDirectApiService, FacebookAccountData as DirectFacebookAccountData, AdAccountOAuth as DirectAdAccountOAuth } from '@/services/facebookDirectApi';

const { Item: FormItem } = Form;
const { TextArea } = Input;

// 定义卡片数据接口（根据截图的列）
interface VccCardData {
  id: string;
  channel: string;        // 渠道
  country: string;        // 国家
  cardHolder: string;     // 持卡人
  countryCode: string;    // 国家代码
  cardNumber: string;     // 卡号
  balance: number;        // 余额
  consumption: number;    // 消费
  boundAdAccount: string; // 已绑定广告号
  boundAdAccountIds?: string[]; // 绑定的广告账户ID数组
  group: string;          // 所属群组
  transactionCount: number; // 交易数
  adAccountStatus: number; // 广告号存活数量
  expiryMonth: string;    // 过期月
  expiryYear: string;     // 过期年
  cvv: string;           // CVV
  zipCode: string;       // 邮编
  usedCount: number;     // 已使用次数
  bindCount: number;      // 绑定次数
  totalAdAccounts: number; // 绑定广告号总数
  limitCount: number;     // 限制次数
  status: string;         // 状态
  remark: string;        // 备注
  // Facebook数据字段
  fbSyncStatus?: 'syncing' | 'success' | 'error' | 'pending'; // 同步状态
  fbLastSyncTime?: string; // 最后同步时间
  fbRealTimeSpend?: number; // Facebook实时消费
  fbAccountStatuses?: { [accountId: string]: 'ACTIVE' | 'DISABLED' | 'PAUSED' }; // 各账户状态
}

// 广告账户接口定义 - 扩展token字段支持
interface AdAccount {
  id: string;           // 内部UUID（用于绑定）
  accountId: string;    // 广告平台账户ID（用于显示）
  account: string;
  status: string;
  riskLevel?: string;
  group?: string;
  oauth?: string;       // Facebook OAuth token（主要字段）
  accessToken?: string; // Facebook访问令牌（备用字段）
  token?: string;       // 通用token字段（备用字段）
  facebookToken?: string; // Facebook专用token字段（备用字段）
  createdAt?: string;
}

// 模拟数据 - 将现有数据适配到新的接口格式
const adaptMockData = (oldData: VCCCard[]): VccCardData[] => {
  return oldData.map((card, index) => {
    const totalAdAccounts = Math.floor(Math.random() * 10) + 1; // 1-10个广告号
    const adAccountStatus = Math.floor(Math.random() * totalAdAccounts) + 1; // 存活数量不超过总数

    return {
      id: card.id,
      channel: 'Visa', // 模拟渠道
      country: '美国', // 模拟国家
      cardHolder: `持卡人${index + 1}`, // 模拟持卡人
      countryCode: 'US', // 模拟国家代码
      cardNumber: card.cardNo,
      balance: card.balance,
      consumption: Math.round(card.balance * 0.1), // 模拟消费金额
      boundAdAccount: card.bindAccount,
      group: card.tags[0] || 'default', // 使用第一个标签作为群组
      transactionCount: Math.floor(Math.random() * 20) + 1, // 模拟交易数
      adAccountStatus, // 广告号存活数量
      expiryMonth: '12', // 模拟过期月
      expiryYear: '2025', // 模拟过期年
      cvv: '123', // 模拟CVV
      zipCode: '10001', // 模拟邮编
      usedCount: Math.floor(Math.random() * 5), // 模拟已使用次数
      bindCount: Math.floor(Math.random() * 5) + 1, // 模拟绑定次数
      totalAdAccounts, // 绑定广告号总数
      limitCount: 10, // 模拟限制次数
      status: card.status === 'active' ? '未使用' : card.status === 'warning' ? '已使用' : '已封禁', // 适配状态
      remark: `备注信息${index + 1}`, // 模拟备注
    };
  });
};

const mockData: VccCardData[] = adaptMockData(mockVCCCards);

const VCCListTab: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isBindModalVisible, setIsBindModalVisible] = useState(false);
  const [isRechargeModalVisible, setIsRechargeModalVisible] = useState(false);
  const [selectedCard, setSelectedCard] = useState<VccCardData | null>(null);
  const [selectKey, setSelectKey] = useState(0); // 用于强制刷新Select组件
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]); // 管理选中的广告账户ID
  const [form] = Form.useForm();
  const [bindForm] = Form.useForm();
  const [rechargeForm] = Form.useForm();
  const tenantId = useTenant();

  // 确保tenantId正确设置 - 使用数据库中存在的tenantId
  React.useEffect(() => {
    const correctTenantId = '3f04876f-6742-49ed-aa6c-05fa9b3fa426';
    if (!tenantId || tenantId !== correctTenantId) {
      localStorage.setItem('current_tenant_id', correctTenantId);
      // 刷新页面以使新的tenantId生效
      window.location.reload();
    }
  }, [tenantId]);

  // 查询广告账户列表
  const { data: adAccountData, loading: adAccountLoading, error: adAccountError, refetch } = useQuery(GET_AD_ACCOUNT_LIST, {
    variables: {
      // 先尝试空的filter，看看是否是tenantId的问题
      filter: {}
    },
    // 先不跳过查询，让它直接执行
    // skip: !tenantId,
    onCompleted: (data) => {
      console.log('广告账户查询成功:', data);
      console.log('广告账户数量:', data?.adAccounts?.length || 0);
      console.log('广告账户详细数据:', data?.adAccounts);
    },
    onError: (error) => {
      console.error('广告账户查询失败:', error);
      console.error('错误详情:', error.message);
      console.error('网络错误:', error.networkError);
      console.error('GraphQL错误:', error.graphQLErrors);
    }
  });

  // 监控广告账户数据变化 - 添加详细的token调试
  React.useEffect(() => {
    console.log('=== 广告账户数据监控 ===');
    console.log('tenantId:', tenantId);
    console.log('adAccountLoading:', adAccountLoading);
    console.log('adAccountError:', adAccountError);
    console.log('adAccountData:', adAccountData);
    console.log('adAccounts数组:', adAccountData?.adAccounts);
    console.log('adAccounts长度:', adAccountData?.adAccounts?.length);

    // 🆕 详细的token调试信息
    if (adAccountData?.adAccounts && adAccountData.adAccounts.length > 0) {
      console.log('=== 详细的广告账户和Token分析 ===');
      adAccountData.adAccounts.forEach((account: AdAccount, index: number) => {
        console.log(`账户[${index}] - ${account.accountId}:`, {
          id: account.id,
          accountId: account.accountId,
          account: account.account,
          status: account.status,
          // 检查所有可能的token字段
          oauth: {
            exists: !!account.oauth,
            length: account.oauth?.length || 0,
            preview: account.oauth ? `${account.oauth.substring(0, 20)}...` : 'N/A',
            isValid: !!(account.oauth && account.oauth.trim() !== '' && account.oauth.length > 10)
          },
          accessToken: {
            exists: !!account.accessToken,
            length: account.accessToken?.length || 0,
            preview: account.accessToken ? `${account.accessToken.substring(0, 20)}...` : 'N/A',
            isValid: !!(account.accessToken && account.accessToken.trim() !== '' && account.accessToken.length > 10)
          },
          token: {
            exists: !!account.token,
            length: account.token?.length || 0,
            preview: account.token ? `${account.token.substring(0, 20)}...` : 'N/A',
            isValid: !!(account.token && account.token.trim() !== '' && account.token.length > 10)
          },
          facebookToken: {
            exists: !!account.facebookToken,
            length: account.facebookToken?.length || 0,
            preview: account.facebookToken ? `${account.facebookToken.substring(0, 20)}...` : 'N/A',
            isValid: !!(account.facebookToken && account.facebookToken.trim() !== '' && account.facebookToken.length > 10)
          },
          // 显示原始数据结构
          rawAccountData: account
        });
      });

      // 统计token情况
      const accountsWithOAuth = adAccountData.adAccounts.filter((acc: AdAccount) => acc.oauth && acc.oauth.trim() !== '');
      const accountsWithAccessToken = adAccountData.adAccounts.filter((acc: AdAccount) => acc.accessToken && acc.accessToken.trim() !== '');
      const accountsWithToken = adAccountData.adAccounts.filter((acc: AdAccount) => acc.token && acc.token.trim() !== '');
      const accountsWithFacebookToken = adAccountData.adAccounts.filter((acc: AdAccount) => acc.facebookToken && acc.facebookToken.trim() !== '');

      console.log('=== Token统计信息 ===');
      console.log('总账户数:', adAccountData.adAccounts.length);
      console.log('有oauth的账户数:', accountsWithOAuth.length);
      console.log('有accessToken的账户数:', accountsWithAccessToken.length);
      console.log('有token的账户数:', accountsWithToken.length);
      console.log('有facebookToken的账户数:', accountsWithFacebookToken.length);

      if (accountsWithOAuth.length === 0 && accountsWithAccessToken.length === 0 &&
        accountsWithToken.length === 0 && accountsWithFacebookToken.length === 0) {
        console.warn('⚠️ 警告：所有广告账户都没有找到有效的Facebook token!');
        console.warn('请检查：');
        console.warn('1. 后端是否正确返回token数据');
        console.warn('2. GraphQL查询是否包含token字段');
        console.warn('3. 广告账户是否已经完成Facebook授权');
      }
    } else {
      console.log('广告账户数据为空或加载中...');
    }

    console.log('=== 监控结束 ===');
  }, [tenantId, adAccountLoading, adAccountError, adAccountData]);

  // 查询VCC卡片列表
  const { data: vccCardsData, loading: vccCardsLoading, error: vccCardsError, refetch: refetchVccCards } = useQuery(GET_VCC_CARDS, {
    variables: {
      filter: { tenantId }
    },
    skip: !tenantId
  });

  // 创建VCC卡片
  const [createVccCard] = useMutation(CREATE_VCC_CARD, {
    onCompleted: () => {
      message.success('VCC卡片创建成功');
      refetchVccCards();
    },
    onError: (error) => {
      message.error('创建失败: ' + error.message);
    }
  });

  // 绑定广告账户
  const [bindAdAccountToVccCard] = useMutation(BIND_AD_ACCOUNT_TO_VCC_CARD);

  // 删除VCC卡片
  const [deleteVccCard] = useMutation(DELETE_VCC_CARD, {
    onCompleted: () => {
      message.success('删除成功');
      refetchVccCards();
    },
    onError: (error) => {
      message.error('删除失败: ' + error.message);
    }
  });

  // 充值VCC卡片
  const [rechargeVccCard] = useMutation(UPDATE_VCC_CARD, {
    onCompleted: () => {
      message.success('充值成功');
      refetchVccCards();
    },
    onError: (error) => {
      message.error('充值失败: ' + error.message);
    }
  });

  // 更新VCC卡片Facebook数据（使用标准更新接口）
  const [updateVccCardFacebookData] = useMutation(UPDATE_VCC_CARD, {
    onCompleted: (data) => {
      console.log('VCC卡片Facebook数据更新成功:', data);
      refetchVccCards();
    },
    onError: (error) => {
      console.error('VCC卡片Facebook数据更新失败:', error);
      message.error('Facebook数据保存失败: ' + error.message);
    }
  });

  // 表格列配置 - 根据截图设计
  const columns: ColumnsType<VccCardData> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 100,
    },
    {
      title: '国家代码',
      dataIndex: 'countryCode',
      key: 'countryCode',
      width: 100,
    },
    {
      title: '持卡人',
      dataIndex: 'cardHolder',
      key: 'cardHolder',
      width: 120,
    },
    {
      title: '卡号',
      dataIndex: 'cardNumber',
      key: 'cardNumber',
      width: 150,
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace' }}>
          {text ? `****${text.slice(-4)}` : '-'}
        </span>
      ),
    },
    {
      title: '余额',
      dataIndex: 'balance',
      key: 'balance',
      width: 100,
      render: (value: number) => `$${value?.toFixed(2) || '0.00'}`,
    },
    {
      title: '消费',
      dataIndex: 'consumption',
      key: 'consumption',
      width: 140,
      render: (value: number, record: VccCardData) => {
        const fbSpend = record.fbRealTimeSpend;
        const hasFbData = fbSpend !== undefined && fbSpend !== null;

        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
            <div style={{ fontSize: '12px', color: '#666' }}>
              本地: ${value?.toFixed(2) || '0.00'}
            </div>
            {hasFbData && (
              <div style={{ fontSize: '12px', color: '#1890ff', fontWeight: 'bold' }}>
                FB: ${fbSpend.toFixed(2)}
              </div>
            )}
            {record.fbSyncStatus && (
              <Tag
                color={
                  record.fbSyncStatus === 'success' ? 'green' :
                    record.fbSyncStatus === 'syncing' ? 'blue' :
                      record.fbSyncStatus === 'error' ? 'red' : 'default'
                }
                style={{ fontSize: '10px', padding: '1px 4px' }}
              >
                {(() => {
                  switch (record.fbSyncStatus) {
                    case 'success': return '已同步';
                    case 'syncing': return '同步中';
                    case 'error': return '同步失败';
                    case 'pending': return '待同步';
                    default: return '未知';
                  }
                })()}
              </Tag>
            )}
          </div>
        );
      },
    },
    {
      title: '已绑定广告号',
      dataIndex: 'boundAdAccount',
      key: 'boundAdAccount',
      width: 120,
    },
    {
      title: '所属群组',
      dataIndex: 'group',
      key: 'group',
      width: 100,
    },
    {
      title: '交易数',
      dataIndex: 'transactionCount',
      key: 'transactionCount',
      width: 100,
      render: (value: number, record: VccCardData) => {
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
            <div style={{ fontSize: '14px' }}>{value || 0}</div>
            {record.fbLastSyncTime && (
              <div style={{ fontSize: '10px', color: '#999' }}>
                {dayjs(record.fbLastSyncTime).format('MM-DD HH:mm')}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '已使用次数',
      dataIndex: 'usedCount',
      key: 'usedCount',
      width: 100,
    },
    {
      title: '绑定广告号总数',
      dataIndex: 'totalAdAccounts',
      key: 'totalAdAccounts',
      width: 120,
    },
    {
      title: '广告号存活',
      dataIndex: 'adAccountStatus',
      key: 'adAccountStatus',
      width: 140,
      render: (count: number, record: VccCardData) => {
        const activeCount = count || 0;
        const totalCount = record.totalAdAccounts || 0;
        const fbStatuses = record.fbAccountStatuses || {};
        const fbActiveCount = Object.values(fbStatuses).filter(status => status === 'ACTIVE').length;

        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
            <span style={{ color: activeCount > 0 ? '#52c41a' : '#ff4d4f' }}>
              本地: {activeCount}/{totalCount}
            </span>
            {Object.keys(fbStatuses).length > 0 && (
              <span style={{ color: fbActiveCount > 0 ? '#52c41a' : '#ff4d4f', fontSize: '12px' }}>
                FB: {fbActiveCount}/{Object.keys(fbStatuses).length}
              </span>
            )}
          </div>
        );
      },
    },
    {
      title: '过期月',
      dataIndex: 'expiryMonth',
      key: 'expiryMonth',
      width: 80,
    },
    {
      title: '过期年',
      dataIndex: 'expiryYear',
      key: 'expiryYear',
      width: 80,
    },
    {
      title: '绑定次数',
      dataIndex: 'bindCount',
      key: 'bindCount',
      width: 80,
    },
    {
      title: '限制次数',
      dataIndex: 'limitCount',
      key: 'limitCount',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colorMap: Record<string, string> = {
          '未使用': 'green',
          '已使用': 'orange',
          '已封禁': 'red',
        };
        return <Tag color={colorMap[status] || 'default'}>{status}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 320,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" wrap>
          <Button
            size="small"
            type="primary"
            onClick={() => handleBindAccount(record)}
          >
            绑定账号
          </Button>
          <Button
            size="small"
            type="default"
            onClick={() => handleRecharge(record)}
          >
            充值
          </Button>
          <Tooltip title="同步Facebook数据">
            <Button
              size="small"
              type="default"
              icon={<SyncOutlined />}
              onClick={() => handleSyncFacebook(record)}
              loading={record.fbSyncStatus === 'syncing'}
            />
          </Tooltip>
          <Popconfirm
            title="删除卡片"
            description={`确定要删除卡号为 ${record.cardNumber} 的卡片吗？此操作不可恢复。`}
            onConfirm={() => handleDelete(record)}
            okText="确定删除"
            cancelText="取消"
            okType="danger"
          >
            <Button
              size="small"
              type="default"
              danger
              icon={<DeleteOutlined />}
              loading={loading}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 自动开卡
  const handleAutoCreate = async () => {
    setLoading(true);
    try {
      // TODO: 实现自动开卡逻辑
      message.success('自动开卡功能开发中...');
    } catch (error) {
      message.error('自动开卡失败');
    } finally {
      setLoading(false);
    }
  };

  // 新增卡片
  const handleAdd = () => {
    form.resetFields();
    setIsModalVisible(true);
  };

  // 绑定账号
  const handleBindAccount = (record: VccCardData) => {
    console.log('=== 绑定回显调试信息 ===');
    console.log('选中的卡片记录:', record);
    console.log('boundAdAccountIds原始值:', record.boundAdAccountIds);
    console.log('boundAdAccountIds类型:', typeof record.boundAdAccountIds);
    console.log('boundAdAccountIds是数组吗:', Array.isArray(record.boundAdAccountIds));

    setSelectedCard(record);

    // 过滤掉null值，确保只回显有效的账户ID
    const validBoundAccountIds = (record.boundAdAccountIds || [])
      .filter((id: string | null) => id != null && id !== '')
      .map((id: string) => String(id));

    console.log('过滤后的有效回显ID:', validBoundAccountIds);

    // 先设置基本表单值（不包括绑定账户）
    const basicFormValues = {
      cardNumber: record.cardNumber,
      balance: `$${record.balance?.toFixed(2) || '0.00'}`,
      remainingBindCount: `${record.limitCount - record.bindCount}`,
      cardHolder: record.cardHolder,
      status: record.status
    };

    console.log('即将设置的基本表单值:', basicFormValues);

    bindForm.setFieldsValue(basicFormValues);

    // 打开弹窗
    setIsBindModalVisible(true);

    // 重置选中的账户ID
    setSelectedAccountIds([]);

    // 等待广告账户数据加载完成后再设置绑定账户值
    const waitForAdAccountData = () => {
      console.log('=== 检查广告账户数据加载状态 ===');
      console.log('adAccountLoading:', adAccountLoading);
      console.log('adAccountData存在:', !!adAccountData);
      console.log('广告账户数量:', adAccountData?.adAccounts?.length || 0);

      if (adAccountLoading) {
        console.log('广告账户数据还在加载中，等待500ms后重试...');
        setTimeout(waitForAdAccountData, 500);
        return;
      }

      if (!adAccountData?.adAccounts || adAccountData.adAccounts.length === 0) {
        console.log('广告账户数据为空，无法设置回显值');
        return;
      }

      console.log('广告账户数据已加载，开始设置回显值');
      console.log('所有广告账户:', adAccountData.adAccounts.map((acc: AdAccount) => ({
        id: acc.id,
        accountId: acc.accountId,
        account: acc.account
      })));

      // 检查哪些绑定的ID在当前广告账户列表中存在
      const availableIds = adAccountData.adAccounts.map((acc: AdAccount) => acc.id);
      const validMatchIds = validBoundAccountIds.filter(id => availableIds.includes(id));
      const invalidMatchIds = validBoundAccountIds.filter(id => !availableIds.includes(id));

      console.log('可用的广告账户ID列表:', availableIds);
      console.log('能匹配的绑定ID:', validMatchIds);
      console.log('无法匹配的绑定ID:', invalidMatchIds);

      if (invalidMatchIds.length > 0) {
        console.warn('警告：以下绑定的账户ID在当前广告账户列表中不存在:', invalidMatchIds);
      }

      // 设置绑定账户的表单值
      bindForm.setFieldValue('bindAccounts', validMatchIds);

      // 同时设置state来控制Select组件的显示
      setSelectedAccountIds(validMatchIds);

      // 强制刷新Select组件
      setSelectKey(prev => prev + 1);

      // 验证设置是否成功
      setTimeout(() => {
        const formValues = bindForm.getFieldsValue();
        console.log('最终表单设置的值:', formValues);
        console.log('bindAccounts字段的值:', formValues.bindAccounts);
        console.log('=== 绑定回显调试信息结束 ===');
      }, 100);
    };

    // 开始等待数据加载
    waitForAdAccountData();
  };

  // 删除卡片
  const handleDelete = async (record: VccCardData) => {
    try {
      setLoading(true);
      await deleteVccCard({
        variables: {
          id: record.id
        }
      });
    } catch (error) {
      console.error('删除失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 充值
  const handleRecharge = (record: VccCardData) => {
    setSelectedCard(record);
    rechargeForm.setFieldsValue({
      cardNumber: record.cardNumber,
      currentBalance: `$${record.balance?.toFixed(2) || '0.00'}`,
      cardHolder: record.cardHolder,
      rechargeAmount: '',
      expectedBalance: `$${record.balance?.toFixed(2) || '0.00'}`
    });
    setIsRechargeModalVisible(true);
  };

  // 同步Facebook数据 - 直接调用Facebook第三方接口
  const handleSyncFacebook = async (record: VccCardData) => {
    if (!record.boundAdAccountIds || record.boundAdAccountIds.length === 0) {
      message.warning('该VCC卡片未绑定任何Facebook广告账户');
      return;
    }

    try {
      setLoading(true);

      // 显示加载提示
      const loadingMessage = message.loading('正在直接从Facebook获取数据...', 0);

      // 获取绑定的广告账户数据，包含Token信息
      const boundAdAccounts = (adAccountData?.adAccounts || []).filter((account: AdAccount) =>
        record.boundAdAccountIds?.includes(account.id)
      );

      if (boundAdAccounts.length === 0) {
        loadingMessage();
        message.error('未找到绑定的广告账户信息');
        return;
      }

      // 检查Token信息，使用新的解析函数
      const accountOAuths: DirectAdAccountOAuth[] = boundAdAccounts
        .map((account: AdAccount) => {
          const tokenResult = getValidToken(account);
          if (tokenResult.token) {
            return {
              accountId: account.accountId,
              accessToken: tokenResult.token
            };
          }
          return null;
        })
        .filter((auth: DirectAdAccountOAuth | null): auth is DirectAdAccountOAuth => auth !== null);

      if (accountOAuths.length === 0) {
        loadingMessage();
        message.error('绑定的广告账户没有有效的Facebook访问令牌，请先到广告账户管理页面进行授权');
        return;
      }

      console.log('准备直接调用Facebook API，账户:', accountOAuths.map(auth => ({
        accountId: auth.accountId,
        tokenLength: auth.accessToken.length
      })));

      // 使用新的直接Facebook API服务
      const facebookDirectApi = new FacebookDirectApiService();
      const aggregatedData = await facebookDirectApi.getAggregatedData(accountOAuths);

      console.log('Facebook API返回的聚合数据:', aggregatedData);

      // 关闭加载提示
      loadingMessage();

      // 显示详细的同步结果
      Modal.success({
        title: '🎉 Facebook数据同步成功！',
        width: 600,
        content: (
          <div style={{ padding: '16px 0' }}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '20px' }}>
              <div style={{ padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
                <div style={{ fontSize: '14px', color: '#389e0d', fontWeight: 'bold', marginBottom: '4px' }}>💰 总消费</div>
                <div style={{ fontSize: '20px', color: '#52c41a', fontWeight: 'bold' }}>${aggregatedData.totalSpend.toFixed(2)}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>来自Facebook实时数据</div>
              </div>

              <div style={{ padding: '12px', backgroundColor: '#f0f5ff', borderRadius: '6px', border: '1px solid #91d5ff' }}>
                <div style={{ fontSize: '14px', color: '#1890ff', fontWeight: 'bold', marginBottom: '4px' }}>🖱️ 总点击</div>
                <div style={{ fontSize: '20px', color: '#1890ff', fontWeight: 'bold' }}>{aggregatedData.totalClicks.toLocaleString()}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>广告点击数统计</div>
              </div>

              <div style={{ padding: '12px', backgroundColor: '#fff7e6', borderRadius: '6px', border: '1px solid #ffd591' }}>
                <div style={{ fontSize: '14px', color: '#fa8c16', fontWeight: 'bold', marginBottom: '4px' }}>📊 总展示</div>
                <div style={{ fontSize: '20px', color: '#fa8c16', fontWeight: 'bold' }}>{aggregatedData.totalImpressions.toLocaleString()}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>广告展示总次数</div>
              </div>

              <div style={{ padding: '12px', backgroundColor: aggregatedData.activeAccounts > 0 ? '#f6ffed' : '#fff2f0', borderRadius: '6px', border: `1px solid ${aggregatedData.activeAccounts > 0 ? '#b7eb8f' : '#ffccc7'}` }}>
                <div style={{ fontSize: '14px', color: aggregatedData.activeAccounts > 0 ? '#389e0d' : '#f5222d', fontWeight: 'bold', marginBottom: '4px' }}>🎯 账户状态</div>
                <div style={{ fontSize: '20px', color: aggregatedData.activeAccounts > 0 ? '#52c41a' : '#f5222d', fontWeight: 'bold' }}>{aggregatedData.activeAccounts}/{aggregatedData.totalAccounts}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>活跃/总数</div>
              </div>
            </div>

            <div style={{ marginTop: '16px' }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>📋 账户详情：</div>
              <div style={{ maxHeight: '120px', overflowY: 'auto', backgroundColor: '#fafafa', padding: '8px', borderRadius: '4px' }}>
                {aggregatedData.detailData.map((account, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '4px 0',
                    borderBottom: index < aggregatedData.detailData.length - 1 ? '1px solid #e8e8e8' : 'none'
                  }}>
                    <span style={{ fontSize: '12px' }}>{account.accountName}</span>
                    <span style={{
                      fontSize: '12px',
                      color: account.accountStatus === 'ACTIVE' ? '#52c41a' : '#f5222d',
                      fontWeight: 'bold'
                    }}>
                      {account.accountStatus === 'ACTIVE' ? '✅ 活跃' :
                        account.accountStatus === 'DISABLED' ? '❌ 禁用' :
                          account.accountStatus === 'PAUSED' ? '⏸️ 暂停' : '❓ 未知'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div style={{
              marginTop: '16px',
              padding: '12px',
              backgroundColor: '#f0f2f5',
              borderRadius: '6px',
              fontSize: '12px',
              color: '#666'
            }}>
              <div><strong>📝 数据来源说明：</strong></div>
              <div>• 数据来源：Facebook Marketing API（直接调用第三方接口）</div>
              <div>• 同步时间：{new Date().toLocaleString()}</div>
              <div>• 时间范围：最近30天的广告数据</div>
              <div>• 已处理账户：{aggregatedData.totalAccounts} 个</div>
            </div>
          </div>
        ),
      });

      // 🚀 调用后端API更新VCC卡片的Facebook数据并更新本地状态
      try {
        const updateInput = {
          // vccCardId移除，将使用id参数
          consumption: aggregatedData.totalSpend,
          transactionCount: aggregatedData.totalClicks,
          adAccountStatus: aggregatedData.activeAccounts,
          totalAdAccounts: aggregatedData.totalAccounts,
          fbRealTimeSpend: aggregatedData.totalSpend,
          fbSyncStatus: 'success',
          fbLastSyncTime: new Date().toISOString(),
          fbAccountStatuses: JSON.stringify(aggregatedData.accountStatuses)
        };

        console.log('正在保存Facebook数据到后端:', updateInput);

        // 保存到后端数据库
        await updateVccCardFacebookData({
          variables: {
            input: updateInput
          }
        });

        console.log('✅ Facebook数据已成功保存到后端数据库');

        // 🆕 立即更新Apollo Client缓存中的数据，用户可以立即看到更新
        refetchVccCards();

        console.log('✅ 本地卡片列表数据将通过refetch更新');

      } catch (saveError: any) {
        console.error('保存Facebook数据到后端失败:', saveError);
        message.error(`数据保存失败: ${saveError.message}`);
      }

      message.success(`✅ 同步完成！消费: $${aggregatedData.totalSpend.toFixed(2)}, 点击: ${aggregatedData.totalClicks}, 存活: ${aggregatedData.activeAccounts}/${aggregatedData.totalAccounts}`);

    } catch (error: any) {
      console.error('Facebook数据同步失败:', error);

      // 详细的错误处理
      let errorMessage = '同步失败';
      let errorDetail = error.message || '未知错误';
      const troubleshootingSteps: string[] = [];

      if (error.message.includes('CORS') || error.message.includes('Cross-Origin')) {
        errorMessage = '浏览器CORS限制';
        errorDetail = '浏览器阻止直接调用Facebook API';
        troubleshootingSteps.push(
          '1. 这是正常现象，浏览器会阻止跨域请求',
          '2. 实际部署时需要通过后端代理调用Facebook API',
          '3. 或者使用Facebook官方SDK'
        );
      } else if (error.message.includes('token') || error.message.includes('401')) {
        errorMessage = 'Facebook Token问题';
        errorDetail = 'Facebook访问令牌无效或已过期';
        troubleshootingSteps.push(
          '1. 到广告账户管理页面重新进行Facebook OAuth授权',
          '2. 确认token具有ads_read权限',
          '3. 检查token是否在有效期内'
        );
      } else if (error.message.includes('permission') || error.message.includes('403')) {
        errorMessage = 'Facebook权限问题';
        errorDetail = 'Facebook账户权限不足';
        troubleshootingSteps.push(
          '1. 确认广告账户有ads_read权限',
          '2. 检查Facebook Business Manager设置',
          '3. 验证用户在广告账户中的角色'
        );
      } else if (error.message.includes('rate limit') || error.message.includes('429')) {
        errorMessage = 'API频率限制';
        errorDetail = 'Facebook API调用频率过高';
        troubleshootingSteps.push(
          '1. 等待几分钟后重试',
          '2. 减少同时同步的账户数量'
        );
      }

      Modal.error({
        title: '❌ Facebook数据同步失败',
        width: 500,
        content: (
          <div>
            <div style={{ marginBottom: '12px' }}>
              <strong>错误类型：</strong>{errorMessage}
            </div>
            <div style={{ marginBottom: '12px' }}>
              <strong>错误详情：</strong>{errorDetail}
            </div>
            <div style={{
              padding: '8px 12px',
              backgroundColor: '#fff7e6',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#fa8c16'
            }}>
              💡 <strong>解决建议：</strong><br />
              {troubleshootingSteps.map((step, index) => (
                <div key={index}>{step}</div>
              ))}
            </div>
          </div>
        ),
      });
    } finally {
      setLoading(false);
    }
  };

  // 批量同步Facebook数据
  // const handleBatchSyncAll = async () => {
  //   try {
  //     setLoading(true);
  //     const allVccCards = vccCardsData?.vccCards || [];

  //     if (allVccCards.length === 0) {
  //       message.warning('没有可同步的VCC卡片');
  //       return;
  //     }

  //     // 过滤有绑定广告账户的VCC卡片
  //     const cardsWithAdAccounts = allVccCards.filter((card: VccCardData) =>
  //       card.boundAdAccountIds && card.boundAdAccountIds.length > 0
  //     );

  //     if (cardsWithAdAccounts.length === 0) {
  //       message.warning('没有绑定Facebook广告账户的VCC卡片');
  //       return;
  //     }

  //     message.info(`开始批量同步 ${cardsWithAdAccounts.length} 张VCC卡片的Facebook数据...`);

  //     // 准备批量数据请求
  //     const vccCardsForSync = cardsWithAdAccounts.map((card: VccCardData) => {
  //       // 获取绑定的广告账户OAuth信息
  //       const boundAdAccounts = (adAccountData?.adAccounts || []).filter((account: AdAccount) =>
  //         card.boundAdAccountIds?.includes(account.id)
  //       );

  //       const accountOAuths: AdAccountOAuth[] = boundAdAccounts
  //         .filter((account: AdAccount) => account.oauth && account.oauth.trim() !== '')
  //         .map((account: AdAccount) => ({
  //           accountId: account.accountId,
  //           accessToken: account.oauth!
  //         }));

  //       return {
  //         vccCardId: card.id,
  //         accountOAuths
  //       };
  //     }).filter((item: { vccCardId: string; accountOAuths: AdAccountOAuth[] }) => item.accountOAuths.length > 0); // 只同步有有效OAuth的卡片

  //     if (vccCardsForSync.length === 0) {
  //       message.error('没有找到有效的OAuth授权信息，请先到广告账户管理页面进行授权');
  //       return;
  //     }

  //     console.log('准备批量同步的VCC卡片:', vccCardsForSync.map((item: { vccCardId: string; accountOAuths: AdAccountOAuth[] }) => ({
  //       vccCardId: item.vccCardId,
  //       accountCount: item.accountOAuths.length
  //     })));

  //     // 调用后端批量同步API
  //     const facebookApiService = new FacebookApiService();
  //     const syncResults = await facebookApiService.getBatchVccData(vccCardsForSync);

  //     console.log('批量同步结果:', syncResults);

  //     // 统计同步结果
  //     const successCount = syncResults.filter(r => r.success).length;
  //     const failCount = syncResults.filter(r => !r.success).length;

  //     // 显示详细的同步结果
  //     Modal.success({
  //       title: '📊 批量Facebook数据同步完成',
  //       width: 700,
  //       content: (
  //         <div style={{ padding: '16px 0' }}>
  //           {/* 统计概览 */}
  //           <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '12px', marginBottom: '20px' }}>
  //             <div style={{ padding: '12px', backgroundColor: '#f0f5ff', borderRadius: '6px', border: '1px solid #91d5ff', textAlign: 'center' }}>
  //               <div style={{ fontSize: '16px', color: '#1890ff', fontWeight: 'bold' }}>{cardsWithAdAccounts.length}</div>
  //               <div style={{ fontSize: '12px', color: '#666' }}>总卡片数</div>
  //             </div>

  //             <div style={{ padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f', textAlign: 'center' }}>
  //               <div style={{ fontSize: '16px', color: '#52c41a', fontWeight: 'bold' }}>{successCount}</div>
  //               <div style={{ fontSize: '12px', color: '#666' }}>成功同步</div>
  //             </div>

  //             <div style={{ padding: '12px', backgroundColor: failCount > 0 ? '#fff2f0' : '#f0f2f5', borderRadius: '6px', border: `1px solid ${failCount > 0 ? '#ffccc7' : '#d9d9d9'}`, textAlign: 'center' }}>
  //               <div style={{ fontSize: '16px', color: failCount > 0 ? '#f5222d' : '#666', fontWeight: 'bold' }}>{failCount}</div>
  //               <div style={{ fontSize: '12px', color: '#666' }}>同步失败</div>
  //             </div>
  //           </div>

  //           {/* 成功的同步详情 */}
  //           {successCount > 0 && (
  //             <div style={{ marginBottom: '20px' }}>
  //               <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>✅ 同步成功的卡片：</div>
  //               <div style={{ maxHeight: '150px', overflowY: 'auto', backgroundColor: '#f6ffed', padding: '8px', borderRadius: '4px', border: '1px solid #b7eb8f' }}>
  //                 {syncResults
  //                   .filter(result => result.success)
  //                   .map((result, index) => (
  //                     <div key={index} style={{
  //                       padding: '4px 0',
  //                       borderBottom: index < successCount - 1 ? '1px solid #e8e8e8' : 'none',
  //                       fontSize: '12px'
  //                     }}>
  //                       <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
  //                         <span style={{ fontWeight: 'bold' }}>卡片 {result.vccCardId.slice(-8)}</span>
  //                         <div style={{ display: 'flex', gap: '8px' }}>
  //                           <span>💰 ${result.data?.totalSpend?.toFixed(2) || '0.00'}</span>
  //                           <span>🖱️ {result.data?.totalClicks || 0}</span>
  //                           <span>🎯 {result.data?.activeAccounts || 0}/{result.data?.totalAccounts || 0}</span>
  //                         </div>
  //                       </div>
  //                     </div>
  //                   ))}
  //               </div>
  //             </div>
  //           )}

  //           {/* 失败的同步详情 */}
  //           {failCount > 0 && (
  //             <div style={{ marginBottom: '20px' }}>
  //               <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px', color: '#f5222d' }}>❌ 同步失败的卡片：</div>
  //               <div style={{ maxHeight: '120px', overflowY: 'auto', backgroundColor: '#fff2f0', padding: '8px', borderRadius: '4px', border: '1px solid #ffccc7' }}>
  //                 {syncResults
  //                   .filter(result => !result.success)
  //                   .map((result, index) => (
  //                     <div key={index} style={{
  //                       padding: '4px 0',
  //                       borderBottom: index < failCount - 1 ? '1px solid #e8e8e8' : 'none',
  //                       fontSize: '12px'
  //                     }}>
  //                       <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
  //                         <span style={{ fontWeight: 'bold' }}>卡片 {result.vccCardId.slice(-8)}</span>
  //                         <span style={{ color: '#f5222d' }}>{result.error || '未知错误'}</span>
  //                       </div>
  //                     </div>
  //                   ))}
  //               </div>
  //             </div>
  //           )}

  //           {/* 总结信息 */}
  //           <div style={{
  //             padding: '12px',
  //             backgroundColor: '#f0f2f5',
  //             borderRadius: '6px',
  //             fontSize: '12px',
  //             color: '#666'
  //           }}>
  //             <div><strong>📋 批量同步总结：</strong></div>
  //             <div>• 处理VCC卡片：{cardsWithAdAccounts.length} 张</div>
  //             <div>• 成功同步：{successCount} 张</div>
  //             <div>• 同步失败：{failCount} 张</div>
  //             <div>• 同步时间：{new Date().toLocaleString()}</div>
  //             <div>• 数据来源：Facebook Marketing API（通过后端服务）</div>
  //           </div>
  //         </div>
  //       ),
  //     });

  //     // TODO: 调用后端API批量更新VCC卡片的Facebook数据
  //     console.log('需要批量更新到后端的数据:', syncResults.filter(r => r.success));

  //     if (successCount > 0) {
  //       message.success(`✅ 批量同步完成！成功: ${successCount}, 失败: ${failCount}`);
  //     } else {
  //       message.error(`❌ 批量同步失败！所有 ${failCount} 张卡片同步都失败了`);
  //     }

  //     // 刷新VCC卡片列表（如果后端支持实时更新）
  //     // await refetchVccCards();

  //   } catch (error: any) {
  //     console.error('批量同步Facebook数据失败:', error);

  //     Modal.error({
  //       title: '❌ 批量同步失败',
  //       width: 500,
  //       content: (
  //         <div>
  //           <div style={{ marginBottom: '12px' }}>
  //             <strong>错误信息：</strong>{error.message || '未知错误'}
  //           </div>
  //           <div style={{
  //             padding: '8px 12px',
  //             backgroundColor: '#fff7e6',
  //             borderRadius: '4px',
  //             fontSize: '12px',
  //             color: '#fa8c16'
  //           }}>
  //             💡 <strong>可能的原因：</strong><br />
  //             1. 后端API服务异常<br />
  //             2. 网络连接问题<br />
  //             3. Facebook API限制<br />
  //             4. OAuth授权信息过期<br />
  //             建议单独同步失败的卡片或重新授权
  //           </div>
  //         </div>
  //       ),
  //     });

  //     message.error(`批量同步失败: ${error.message}`);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // 提交新增卡片
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 格式化数据，确保数据类型正确
      const formattedValues = {
        ...values,
        country: values.countryCode === 'US' ? '美国' : values.countryCode === 'CN' ? '中国' : values.countryCode === 'CA' ? '加拿大' : '其他', // 根据countryCode自动设置country
        expiryMonth: values.expiryMonth ? dayjs(values.expiryMonth).format('MM') : '',
        expiryYear: values.expiryYear ? dayjs(values.expiryYear).format('YYYY') : '',
        balance: parseFloat(values.balance) || 0,
        consumption: parseFloat(values.consumption) || 0,
        usedCount: parseInt(values.usedCount) || 0,
        bindCount: parseInt(values.bindCount) || 0,
        totalAdAccounts: 0, // 初始为0，绑定时自动计算
        adAccountStatus: 0, // 初始为0，绑定时自动计算
        transactionCount: parseInt(values.transactionCount) || 0,
        limitCount: parseInt(values.limitCount) || 10,
        tenantId: tenantId
      };

      await createVccCard({
        variables: {
          input: formattedValues
        }
      });

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('添加卡片失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交绑定账号 - 直接使用当前选中账户的Facebook token
  const handleBindSubmit = async () => {
    let values;
    let validAccountIds: string[] = [];

    try {
      console.log('=== 开始绑定账号流程 ===');

      // 检查表单当前状态
      const currentFormValues = bindForm.getFieldsValue();
      console.log('当前表单值:', currentFormValues);
      console.log('bindAccounts当前值:', currentFormValues.bindAccounts);
      console.log('bindAccounts是否为空:', !currentFormValues.bindAccounts || currentFormValues.bindAccounts.length === 0);

      // 添加表单验证的错误处理
      try {
        values = await bindForm.validateFields();
        console.log('=== 表单验证成功 ===');
      } catch (validateError) {
        console.error('=== 表单验证失败 ===');
        console.error('验证错误详情:', validateError);
        console.error('错误字段:', validateError.errorFields);
        console.error('错误值:', validateError.values);

        // 显示具体的验证错误
        if (validateError.errorFields && validateError.errorFields.length > 0) {
          const firstError = validateError.errorFields[0];
          message.error(`表单验证失败: ${firstError.errors[0]}`);
        } else {
          message.error('表单验证失败，请检查输入');
        }
        return; // 验证失败就直接返回
      }

      console.log('表单原始值:', values);
      console.log('bindAccounts原始值:', values.bindAccounts);
      console.log('bindAccounts类型:', typeof values.bindAccounts);
      console.log('bindAccounts是数组:', Array.isArray(values.bindAccounts));

      setLoading(true);

      console.log('=== 绑定提交开始 ===');
      console.log('selectedCard:', selectedCard);
      console.log('selectedCard?.id:', selectedCard?.id);

      // 过滤掉null、undefined等无效值，确保只传递有效的字符串ID
      validAccountIds = (values.bindAccounts || [])
        .filter((id: any) => id != null && id !== '' && typeof id === 'string')
        .map((id: string) => String(id).trim())
        .filter((id: string) => id.length > 0);

      if (validAccountIds.length === 0) {
        console.error('没有有效的账户ID');
        message.error('请选择至少一个有效的广告账户');
        setLoading(false);
        return;
      }

      console.log('原始选中值:', values.bindAccounts);
      console.log('原始选中值类型:', typeof values.bindAccounts);
      console.log('原始选中值是数组吗:', Array.isArray(values.bindAccounts));
      if (Array.isArray(values.bindAccounts)) {
        console.log('数组每个元素:', values.bindAccounts.map((id: any, index: number) =>
          `[${index}]: ${id} (${typeof id})`
        ));
      }
      console.log('过滤后的有效ID:', validAccountIds);

      // 🆕 在绑定前先分析选中账户的token状态
      console.log('=== 分析选中账户的Facebook token状态 ===');
      console.log('当前adAccountData状态:', {
        exists: !!adAccountData,
        hasAccounts: !!(adAccountData?.adAccounts),
        accountCount: adAccountData?.adAccounts?.length || 0
      });

      // 从当前的广告账户数据中找到选中的账户
      const selectedAccounts = (adAccountData?.adAccounts || []).filter((account: AdAccount) =>
        validAccountIds.includes(account.id)
      );

      console.log('选中的账户详情:', selectedAccounts.map((acc: AdAccount) => ({
        id: acc.id,
        accountId: acc.accountId,
        account: acc.account,
        hasOAuth: !!acc.oauth,
        oauthLength: acc.oauth?.length || 0,
        oauthPreview: acc.oauth ? `${acc.oauth.substring(0, 20)}...` : 'N/A',
        oauthIsValid: !!(acc.oauth && acc.oauth.trim() !== '' && acc.oauth.length > 50)
      })));

      // 检查哪些选中的账户有Facebook token
      const selectedAccountsWithToken = selectedAccounts.filter((account: AdAccount) => {
        const tokenResult = parseAndValidateToken(account);
        console.log(`账户 ${account.accountId} token检查结果:`, {
          hasToken: !!tokenResult.token,
          tokenLength: tokenResult.token?.length || 0,
          tokenPreview: tokenResult.token ? `${tokenResult.token.substring(0, 20)}...` : 'N/A',
          source: tokenResult.source,
          rawValue: tokenResult.rawValue
        });
        return !!tokenResult.token;
      });

      const selectedAccountsWithoutToken = selectedAccounts.filter((account: AdAccount) => {
        const tokenResult = parseAndValidateToken(account);
        return !tokenResult.token;
      });

      console.log('=== 选中账户Facebook token状态分析 ===');
      console.log('选中账户总数:', selectedAccounts.length);
      console.log('有有效token的账户数量:', selectedAccountsWithToken.length);
      console.log('无有效token的账户数量:', selectedAccountsWithoutToken.length);

      // 如果选中的账户有有效token，准备Facebook数据获取
      let facebookDataPromise = null;
      if (selectedAccountsWithToken.length > 0) {
        console.log('=== 准备Facebook数据获取（绑定前预处理）===');

        // 构建OAuth信息 - 使用新的直接API服务类型
        const accountOAuths: DirectAdAccountOAuth[] = selectedAccountsWithToken.map((account: AdAccount) => {
          const tokenResult = parseAndValidateToken(account);
          const token = tokenResult.token!;
          console.log(`构建OAuth信息 - 账户: ${account.accountId}, Token来源: ${tokenResult.source}, Token长度: ${token.length}, Token预览: ${token.substring(0, 30)}...`);
          return {
            accountId: account.accountId,
            accessToken: token
          };
        });

        console.log('即将用于Facebook API调用的OAuth账户:', accountOAuths.map((auth, index) => ({
          index: index + 1,
          accountId: auth.accountId,
          tokenLength: auth.accessToken.length,
          tokenPreview: `${auth.accessToken.substring(0, 30)}...`,
          tokenEnd: `...${auth.accessToken.substring(auth.accessToken.length - 10)}`
        })));

        // 创建Facebook数据获取的Promise，使用新的直接API服务
        const facebookDirectApiService = new FacebookDirectApiService();
        facebookDataPromise = facebookDirectApiService.getAggregatedData(accountOAuths);
      }

      console.log('参数验证通过，开始发送绑定请求...');

      // 验证VCC卡片ID格式（必须是UUID）
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

      if (!selectedCard?.id) {
        throw new Error('VCC卡片ID为空，请重新选择卡片');
      }

      if (!uuidRegex.test(selectedCard.id)) {
        console.error('VCC卡片ID格式错误:', selectedCard.id);
        throw new Error(`VCC卡片ID格式不正确: ${selectedCard.id}，必须是UUID格式`);
      }

      // 现在validAccountIds包含的是内部UUID，应该都是有效的UUID格式
      const invalidAccountIds = validAccountIds.filter(id => !uuidRegex.test(id));
      if (invalidAccountIds.length > 0) {
        console.error('无效的广告账户ID:', invalidAccountIds);
        throw new Error(`以下广告账户ID格式不正确: ${invalidAccountIds.join(', ')}，必须是UUID格式`);
      }

      console.log('=== 数据验证通过 ===');
      console.log('VCC卡片ID:', selectedCard.id);
      console.log('广告账户ID列表:', validAccountIds);
      console.log('所有ID都是有效的UUID格式');

      // 根据后端DTO定义，使用正确的格式
      const bindInput = {
        vccCardId: selectedCard.id,
        adAccountIds: validAccountIds
      };

      console.log('=== 发送绑定请求 ===');
      console.log('请求参数:', JSON.stringify({ input: bindInput }, null, 2));

      const result = await bindAdAccountToVccCard({
        variables: {
          input: bindInput
        },
        errorPolicy: 'all'
      });

      console.log('✅ 绑定成功!');
      console.log('返回结果:', result);

      message.success('绑定成功，正在获取Facebook数据...');

      // 🆕 绑定成功后，如果有token的账户，立即获取Facebook数据并保存到卡片
      if (selectedAccountsWithToken.length > 0) {
        console.log('=== 开始获取Facebook广告账户数据并保存到卡片 ===');

        const fbLoadingMessage = message.loading(`正在获取 ${selectedAccountsWithToken.length} 个账户的Facebook数据...`, 0);

        try {
          console.log('正在调用 FacebookDirectApiService.getAggregatedData...');

          // 使用新的直接Facebook API服务
          const facebookDirectApi = new FacebookDirectApiService();

          // 构建OAuth信息
          const accountOAuths: DirectAdAccountOAuth[] = selectedAccountsWithToken.map((account: AdAccount) => {
            const tokenResult = parseAndValidateToken(account);
            const token = tokenResult.token!;
            console.log(`构建OAuth信息 - 账户: ${account.accountId}, Token来源: ${tokenResult.source}, Token长度: ${token.length}`);
            return {
              accountId: account.accountId,
              accessToken: token
            };
          });

          console.log('即将用于Facebook API调用的OAuth账户:', accountOAuths.map((auth, index) => ({
            index: index + 1,
            accountId: auth.accountId,
            tokenLength: auth.accessToken.length
          })));

          const facebookData = await facebookDirectApi.getAggregatedData(accountOAuths);

          console.log('=== Facebook API调用成功 ===');
          console.log('Facebook聚合数据:', facebookData);

          fbLoadingMessage();

          // 🚀 调用后端API更新VCC卡片的Facebook数据并更新本地状态
          try {
            const updateInput = {
              vccCardId: selectedCard.id,
              consumption: facebookData.totalSpend,
              transactionCount: facebookData.totalClicks,
              adAccountStatus: facebookData.activeAccounts,
              totalAdAccounts: facebookData.totalAccounts,
              fbRealTimeSpend: facebookData.totalSpend,
              fbSyncStatus: 'success',
              fbLastSyncTime: new Date().toISOString(),
              fbAccountStatuses: JSON.stringify(facebookData.accountStatuses)
            };

            console.log('正在保存绑定后的Facebook数据到后端:', updateInput);

            await updateVccCardFacebookData({
              variables: {
                input: updateInput
              }
            });

            console.log('✅ 绑定后的Facebook数据已成功保存到后端数据库');

          } catch (saveError: any) {
            console.error('保存绑定后的Facebook数据到后端失败:', saveError);
            message.warning(`绑定成功，但数据保存失败: ${saveError.message}`);
          }

          // TODO: 调用后端API更新VCC卡片的Facebook数据
          const updateData = {
            vccCardId: selectedCard.id,
            fbRealTimeSpend: facebookData.totalSpend,
            fbSyncStatus: 'success',
            fbLastSyncTime: new Date().toISOString(),
            fbAccountStatuses: facebookData.accountStatuses,
            consumption: facebookData.totalSpend,          // 更新消费字段
            transactionCount: facebookData.totalClicks,    // 更新交易数字段  
            adAccountStatus: facebookData.activeAccounts,  // 更新存活账户数
            totalAdAccounts: facebookData.totalAccounts    // 更新总账户数
          };

          console.log('需要更新到后端的数据:', updateData);

          // 显示绑定和Facebook数据获取结果
          Modal.success({
            title: '🎉 绑定成功并获取Facebook数据！',
            width: 650,
            content: (
              <div style={{ padding: '16px 0' }}>
                <div style={{ marginBottom: '16px', fontSize: '14px' }}>
                  ✅ 已成功绑定 <strong>{selectedAccounts.length}</strong> 个广告账户，其中 <strong>{selectedAccountsWithToken.length}</strong> 个账户已获取到Facebook数据：
                </div>

                {/* Facebook数据展示 */}
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', marginBottom: '16px' }}>
                  <div style={{ padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f', textAlign: 'center' }}>
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>💰 总消费</div>
                    <div style={{ fontSize: '18px', color: '#52c41a', fontWeight: 'bold' }}>${facebookData.totalSpend.toFixed(2)}</div>
                  </div>

                  <div style={{ padding: '12px', backgroundColor: '#f0f5ff', borderRadius: '6px', border: '1px solid #91d5ff', textAlign: 'center' }}>
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>🖱️ 交易数</div>
                    <div style={{ fontSize: '18px', color: '#1890ff', fontWeight: 'bold' }}>{facebookData.totalClicks.toLocaleString()}</div>
                  </div>

                  <div style={{ padding: '12px', backgroundColor: '#fff7e6', borderRadius: '6px', border: '1px solid #ffd591', textAlign: 'center' }}>
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>👁️ 展示数</div>
                    <div style={{ fontSize: '18px', color: '#fa8c16', fontWeight: 'bold' }}>{facebookData.totalImpressions.toLocaleString()}</div>
                  </div>

                  <div style={{ padding: '12px', backgroundColor: facebookData.activeAccounts > 0 ? '#f6ffed' : '#fff2f0', borderRadius: '6px', border: `1px solid ${facebookData.activeAccounts > 0 ? '#b7eb8f' : '#ffccc7'}`, textAlign: 'center' }}>
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>✅ 广告号存活</div>
                    <div style={{ fontSize: '18px', color: facebookData.activeAccounts > 0 ? '#52c41a' : '#f5222d', fontWeight: 'bold' }}>{facebookData.activeAccounts}/{facebookData.totalAccounts}</div>
                  </div>
                </div>

                {/* 详细账户状态 */}
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>📋 账户状态详情：</div>
                  <div style={{ maxHeight: '120px', overflowY: 'auto', backgroundColor: '#fafafa', padding: '8px', borderRadius: '4px' }}>
                    {facebookData.detailData.map((account, index) => (
                      <div key={index} style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: '4px 0',
                        borderBottom: index < facebookData.detailData.length - 1 ? '1px solid #e8e8e8' : 'none'
                      }}>
                        <span style={{ fontSize: '12px' }}>{account.accountName || account.accountId}</span>
                        <span style={{
                          fontSize: '12px',
                          color: account.accountStatus === 'ACTIVE' ? '#52c41a' : '#f5222d',
                          fontWeight: 'bold'
                        }}>
                          {account.accountStatus === 'ACTIVE' ? '✅ 活跃' :
                            account.accountStatus === 'DISABLED' ? '❌ 禁用' :
                              account.accountStatus === 'PAUSED' ? '⏸️ 暂停' : '❓ 未知'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 数据保存说明 */}
                <div style={{
                  padding: '12px',
                  backgroundColor: '#f0f2f5',
                  borderRadius: '6px',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  <div><strong>📝 数据已自动保存到VCC卡片：</strong></div>
                  <div>• 消费金额：${facebookData.totalSpend.toFixed(2)} → 更新到卡片消费字段</div>
                  <div>• 交易数：{facebookData.totalClicks.toLocaleString()} → 更新到卡片交易数字段</div>
                  <div>• 广告号存活：{facebookData.activeAccounts}/{facebookData.totalAccounts} → 更新到存活状态</div>
                  <div>• 数据来源：Facebook Marketing API（实时获取）</div>
                  <div>• 绑定时间：{new Date().toLocaleString()}</div>
                  {selectedAccountsWithoutToken.length > 0 && (
                    <div style={{ color: '#fa8c16', marginTop: '4px' }}>
                      💡 {selectedAccountsWithoutToken.length} 个账户暂无Facebook授权，可稍后手动同步
                    </div>
                  )}
                </div>
              </div>
            ),
          });

          message.success(`🎉 绑定并获取数据完成！消费: $${facebookData.totalSpend.toFixed(2)}, 交易数: ${facebookData.totalClicks}, 存活账户: ${facebookData.activeAccounts}/${facebookData.totalAccounts}`);

        } catch (fbError: any) {
          console.error('Facebook数据获取失败:', fbError);
          fbLoadingMessage();

          // 分析错误类型并提供详细的解决方案
          let errorType = '未知错误';
          let errorDetails = fbError.message || '无详细信息';
          const troubleshootingSteps: string[] = [];

          if (fbError.message.includes('CORS') || fbError.message.includes('Cross-Origin')) {
            errorType = '浏览器CORS限制';
            errorDetails = '浏览器阻止直接调用Facebook API（这是正常现象）';
            troubleshootingSteps.push(
              '1. 这是浏览器安全机制，正常现象',
              '2. 实际部署时需要通过后端代理调用Facebook API',
              '3. 或者配置适当的CORS策略',
              '4. 可以使用Facebook官方SDK避免此问题'
            );
          } else if (fbError.message.includes('token') || fbError.message.includes('401')) {
            errorType = 'Facebook Token问题';
            errorDetails = 'Facebook访问令牌无效或已过期';
            troubleshootingSteps.push(
              '1. 到广告账户管理页面重新进行Facebook OAuth授权',
              '2. 确认token具有ads_read权限',
              '3. 检查token是否在有效期内',
              '4. 验证广告账户ID格式是否正确'
            );
          } else if (fbError.message.includes('permission') || fbError.message.includes('403')) {
            errorType = 'Facebook权限问题';
            errorDetails = 'Facebook账户权限不足';
            troubleshootingSteps.push(
              '1. 确认广告账户有ads_read权限',
              '2. 检查Facebook Business Manager设置',
              '3. 验证用户在广告账户中的角色',
              '4. 重新进行Facebook OAuth授权'
            );
          } else if (fbError.message.includes('rate limit') || fbError.message.includes('429')) {
            errorType = 'API频率限制';
            errorDetails = 'Facebook API调用频率过高';
            troubleshootingSteps.push(
              '1. 等待几分钟后重试',
              '2. 减少同时绑定的账户数量',
              '3. 检查是否有其他地方在大量调用Facebook API'
            );
          }

          // 显示详细的错误诊断信息
          Modal.warning({
            title: '✅ 绑定成功，Facebook数据获取失败',
            width: 600,
            content: (
              <div>
                <div style={{ marginBottom: '16px' }}>
                  <strong>绑定状态：</strong><span style={{ color: '#52c41a' }}>✅ 成功</span><br />
                  <strong>Facebook数据：</strong><span style={{ color: '#f5222d' }}>❌ 失败</span>
                </div>

                <div style={{ marginBottom: '12px' }}>
                  <strong>错误类型：</strong>{errorType}
                </div>
                <div style={{ marginBottom: '16px' }}>
                  <strong>错误详情：</strong>{errorDetails}
                </div>

                <div style={{ marginBottom: '16px' }}>
                  <strong>🔧 解决步骤：</strong>
                  <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                    {troubleshootingSteps.map((step, index) => (
                      <div key={index} style={{ marginBottom: '4px' }}>{step}</div>
                    ))}
                  </div>
                </div>

                <div style={{ marginBottom: '16px' }}>
                  <strong>📊 调试信息：</strong>
                  <div style={{ fontSize: '11px', backgroundColor: '#f5f5f5', padding: '8px', borderRadius: '4px', marginTop: '4px', fontFamily: 'monospace' }}>
                    <div>绑定账户数量: {selectedAccounts.length}</div>
                    <div>有token账户数量: {selectedAccountsWithToken.length}</div>
                    <div>调用接口: Facebook Marketing API (直接调用)</div>
                    <div>传递的token数量: {selectedAccountsWithToken.length}</div>
                    <div>错误消息: {fbError.message}</div>
                  </div>
                </div>

                <div style={{
                  padding: '8px 12px',
                  backgroundColor: '#fff7e6',
                  borderRadius: '4px',
                  fontSize: '12px',
                  color: '#fa8c16'
                }}>
                  💡 <strong>提示：</strong>虽然Facebook数据获取失败，但账户绑定已成功。您可以稍后手动点击"同步Facebook数据"按钮重试，或到广告账户管理页面重新授权。
                </div>
              </div>
            ),
          });
        }
      } else {
        // 没有token的情况
        Modal.success({
          title: '✅ 绑定成功',
          width: 500,
          content: (
            <div>
              <div style={{ marginBottom: '12px' }}>
                已成功绑定 <strong>{selectedAccounts.length}</strong> 个广告账户
              </div>
              <div style={{ marginBottom: '12px', color: '#fa8c16' }}>
                ⚠️ 暂无Facebook授权的账户，无法立即获取数据
              </div>
              <div style={{
                padding: '8px 12px',
                backgroundColor: '#fff7e6',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#fa8c16'
              }}>
                💡 请到广告账户管理页面进行Facebook授权，然后返回手动同步数据
              </div>
            </div>
          ),
        });
      }

      // 刷新VCC卡片数据
      await refetchVccCards();

      setIsBindModalVisible(false);
      bindForm.resetFields();

    } catch (error: any) {
      console.error('=== 绑定失败详细信息 ===');
      console.error('错误对象:', error);
      console.error('错误对象类型:', typeof error);
      console.error('错误对象键:', Object.keys(error || {}));
      console.error('错误消息:', error.message);
      console.error('网络错误:', error.networkError);
      console.error('GraphQL错误:', error.graphQLErrors);
      console.error('错误堆栈:', error.stack);

      // 特别处理网络错误中的响应信息
      if (error.networkError) {
        console.error('网络错误状态码:', error.networkError.statusCode);
        console.error('网络错误响应:', error.networkError.result);
        if (error.networkError.result) {
          console.error('响应错误详情:', JSON.stringify(error.networkError.result, null, 2));
        }

        // 如果是400错误，尝试获取更详细的错误信息
        if (error.networkError.statusCode === 400) {
          console.error('=== 400错误详细分析 ===');
          console.error('请求体可能有问题，以下是我们尝试的所有格式：');

          // 显示所有尝试过的格式
          const allFormats = [
            { name: 'input包装格式', data: { input: { vccCardId: selectedCard?.id, adAccountIds: validAccountIds } } },
            { name: '扁平化格式', data: { vccCardId: selectedCard?.id, adAccountIds: validAccountIds } },
            { name: 'id字段格式', data: { input: { id: selectedCard?.id, adAccountIds: validAccountIds } } },
            { name: 'accountIds字段格式', data: { input: { vccCardId: selectedCard?.id, accountIds: validAccountIds } } }
          ];

          allFormats.forEach(format => {
            console.error(`${format.name}:`, JSON.stringify(format.data, null, 2));
          });

          // 建议检查的事项
          console.error('\n可能的问题：');
          console.error('1. 字段名不匹配 - 后端期望的字段名与前端不同');
          console.error('2. 数据类型错误 - 某些字段期望数字而不是字符串');
          console.error('3. 缺少必需字段 - 后端需要额外的字段');
          console.error('4. 权限问题 - 当前用户没有绑定权限');
          console.error('5. 业务逻辑错误 - 卡片状态、账户状态等问题');
          console.error('\n建议：请检查后端GraphQL schema或API文档');
        }
      }

      // 尝试从不同的属性中获取错误信息
      let errorMessage = '未知错误';

      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        console.error('GraphQL错误详情:', error.graphQLErrors[0]);
        errorMessage = error.graphQLErrors[0].message || 'GraphQL查询错误';

        // 检查是否有扩展信息
        if (error.graphQLErrors[0].extensions) {
          console.error('GraphQL错误扩展信息:', error.graphQLErrors[0].extensions);
        }
      } else if (error.networkError) {
        console.error('网络错误详情:', error.networkError);

        // 从网络错误中提取更详细的信息
        if (error.networkError.result && error.networkError.result.errors) {
          const backendError = error.networkError.result.errors[0];
          errorMessage = backendError?.message || '网络请求错误';

          // 如果有详细的错误信息
          if (backendError.extensions) {
            console.error('后端错误扩展信息:', backendError.extensions);
            if (backendError.extensions.exception) {
              console.error('后端异常详情:', backendError.extensions.exception);
            }
          }
        } else if (error.networkError.result && error.networkError.result.message) {
          errorMessage = error.networkError.result.message;
        } else {
          errorMessage = error.networkError.message || '网络连接错误';
        }
      } else if (error.message) {
        errorMessage = error.message;
      } else if (error.toString && typeof error.toString === 'function') {
        errorMessage = error.toString();
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // 如果还是没有获取到有用的错误信息，尝试JSON序列化
      if (errorMessage === '未知错误' || !errorMessage) {
        try {
          errorMessage = JSON.stringify(error, null, 2);
        } catch (jsonError) {
          console.error('JSON序列化错误对象失败:', jsonError);
          errorMessage = '无法解析的错误';
        }
      }

      console.error('最终错误消息:', errorMessage);
      message.error(`绑定失败: ${errorMessage}`);
    } finally {
      setLoading(false);
      console.log('=== 绑定提交结束 ===');
    }
  };

  // 提交充值
  const handleRechargeSubmit = async () => {
    try {
      const values = await rechargeForm.validateFields();
      setLoading(true);

      const rechargeAmount = parseFloat(values.rechargeAmount);
      if (isNaN(rechargeAmount) || rechargeAmount <= 0) {
        message.error('请输入有效的充值金额');
        return;
      }

      // 计算新的余额
      const currentBalance = selectedCard?.balance || 0;
      const newBalance = currentBalance + rechargeAmount;

      await rechargeVccCard({
        variables: {
          id: selectedCard?.id,
          input: {
            balance: newBalance
          }
        }
      });

      setIsRechargeModalVisible(false);
      rechargeForm.resetFields();
    } catch (error) {
      console.error('充值失败:', error);
      message.error('充值失败');
    } finally {
      setLoading(false);
    }
  };

  // 🆕 增强的token解析和验证函数 - 修复linter错误
  const parseAndValidateToken = (account: AdAccount): { token: string | null; source: string; rawValue?: any } => {
    console.log(`=== 解析账户 ${account.accountId} 的token ===`);

    // 检查所有可能的token字段，按优先级排序
    const tokenFields = [
      { field: 'oauth', value: account.oauth, priority: 1 },
      { field: 'accessToken', value: account.accessToken, priority: 2 },
      { field: 'facebookToken', value: account.facebookToken, priority: 3 },
      { field: 'token', value: account.token, priority: 4 }
    ];

    // 按优先级查找并解析token
    for (const field of tokenFields) {
      if (!field.value) continue;

      let parsedToken: string | null = null;
      const rawValue = field.value;

      try {
        // 情况1: 直接是字符串token
        if (typeof field.value === 'string') {
          const trimmedValue = field.value.trim();

          // 检查是否是JSON字符串格式
          if (trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) {
            try {
              const parsed = JSON.parse(trimmedValue);

              // 尝试从解析的对象中提取token - 修复linter错误
              if (parsed.access_token && typeof parsed.access_token === 'string') {
                parsedToken = parsed.access_token;
                console.log(`从JSON中提取access_token: ${parsedToken?.substring(0, 20)}...`);
              } else if (parsed.token && typeof parsed.token === 'string') {
                parsedToken = parsed.token;
                console.log(`从JSON中提取token: ${parsedToken?.substring(0, 20)}...`);
              } else if (parsed.oauth_token && typeof parsed.oauth_token === 'string') {
                parsedToken = parsed.oauth_token;
                console.log(`从JSON中提取oauth_token: ${parsedToken?.substring(0, 20)}...`);
              } else if (typeof parsed === 'string') {
                parsedToken = parsed;
                console.log(`JSON解析结果本身是字符串token: ${parsedToken?.substring(0, 20)}...`);
              }
            } catch (jsonError) {
              console.log(`${field.field}字段JSON解析失败，当作普通字符串处理:`, jsonError);
              parsedToken = trimmedValue;
            }
          } else {
            // 直接使用字符串值
            parsedToken = trimmedValue;
          }
        }
        // 情况2: 对象类型，尝试提取token
        else if (typeof field.value === 'object' && field.value !== null) {
          const obj = field.value as any;
          if (obj.access_token) {
            parsedToken = obj.access_token;
          } else if (obj.token) {
            parsedToken = obj.token;
          } else if (obj.oauth_token) {
            parsedToken = obj.oauth_token;
          }
        }

        // 验证解析出的token
        if (parsedToken && parsedToken.length > 10 && parsedToken.trim() !== '') {
          console.log(`✅ 找到有效token! 字段: ${field.field}, 长度: ${parsedToken.length}`);
          return {
            token: parsedToken,
            source: field.field,
            rawValue: rawValue
          };
        }
      } catch (error) {
        console.error(`解析${field.field}字段时出错:`, error);
      }
    }

    console.log(`❌ 账户 ${account.accountId} 没有找到有效的token`);
    return { token: null, source: 'none' };
  };

  // 🆕 智能token获取和验证函数 - 简化版本
  const getValidToken = (account: AdAccount): { token: string | null; source: string } => {
    // 检查所有可能的token字段，按优先级排序
    const tokenChecks = [
      { field: 'accessToken', value: account.accessToken, priority: 1 },
      { field: 'oauth', value: account.oauth, priority: 2 },
      { field: 'facebookToken', value: account.facebookToken, priority: 3 },
      { field: 'token', value: account.token, priority: 4 }
    ];

    // 按优先级查找有效token
    for (const check of tokenChecks) {
      if (check.value && check.value.trim() !== '' && check.value.length > 10) {
        return { token: check.value, source: check.field };
      }
    }

    return { token: null, source: 'none' };
  };

  // 🆕 批量token检测函数
  const analyzeAccountTokens = (accounts: AdAccount[]) => {
    console.log('=== 批量Token分析开始 ===');
    console.log(`分析 ${accounts.length} 个账户的token状态`);

    const analysis = accounts.map((account, index) => {
      const tokenResult = getValidToken(account);
      return {
        index,
        account,
        tokenResult,
        summary: {
          id: account.id,
          accountId: account.accountId,
          account: account.account,
          hasValidToken: !!tokenResult.token,
          tokenSource: tokenResult.source,
          tokenLength: tokenResult.token?.length || 0
        }
      };
    });

    const accountsWithToken = analysis.filter(item => item.tokenResult.token);
    const accountsWithoutToken = analysis.filter(item => !item.tokenResult.token);

    console.log('=== 批量Token分析结果 ===');
    console.log('总账户数:', accounts.length);
    console.log('有有效token的账户数:', accountsWithToken.length);
    console.log('无有效token的账户数:', accountsWithoutToken.length);

    if (accountsWithToken.length > 0) {
      console.log('有token的账户详情:', accountsWithToken.map(item => item.summary));
    }

    if (accountsWithoutToken.length > 0) {
      console.log('无token的账户详情:', accountsWithoutToken.map(item => item.summary));
    }

    return analysis;
  };

  return (
    <div style={{ padding: '0' }}>
      {/* 操作按钮区域 */}
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <Space>
          {/* <Button
            type="primary"
            loading={loading}
            onClick={handleAutoCreate}
          >
            自动开卡
          </Button> */}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新增
          </Button>
        </Space>
      </div>

      {/* 卡片列表表格 */}
      <Table
        rowKey="id"
        columns={columns}
        dataSource={vccCardsData?.vccCards || []}
        loading={vccCardsLoading}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
        }}
        scroll={{ x: 1500 }}
        size="middle"
        bordered
      />

      {/* 新增卡片弹窗 */}
      <Modal
        title="新增"
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
        confirmLoading={loading}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{}}
        >
          <Form.Item
            name="channel"
            label="渠道："
            rules={[{ required: true, message: '请输入渠道' }]}
          >
            <Input placeholder="" />
          </Form.Item>

          <Form.Item
            name="countryCode"
            label="国家代码："
            rules={[{ required: true, message: '请输入国家代码' }]}
          >
            <Input placeholder="例如：US, CN, CA" maxLength={10} />
          </Form.Item>

          <Form.Item
            name="cardHolder"
            label="持卡人："
            rules={[{ required: true, message: '请输入持卡人' }]}
          >
            <Input placeholder="" />
          </Form.Item>

          <Form.Item
            name="balance"
            label="余额："
            rules={[{ required: true, message: '请输入余额' }]}
          >
            <Input placeholder="" />
          </Form.Item>

          <Form.Item
            name="cardNumber"
            label="卡号："
            rules={[{ required: true, message: '请输入卡号' }]}
          >
            <Input placeholder="" />
          </Form.Item>

          <Form.Item
            name="expiryMonth"
            label="过期月："
            rules={[{ required: true, message: '请选择过期月' }]}
          >
            <DatePicker
              picker="month"
              placeholder="选择月份"
              style={{ width: '100%' }}
              format="MM"
            />
          </Form.Item>

          <Form.Item
            name="expiryYear"
            label="过期年："
            rules={[{ required: true, message: '请选择过期年' }]}
          >
            <DatePicker
              picker="year"
              placeholder="选择年份"
              style={{ width: '100%' }}
              format="YYYY"
            />
          </Form.Item>

          <Form.Item
            name="cvv"
            label="CVV："
            rules={[{ required: true, message: '请输入CVV' }]}
          >
            <Input placeholder="" maxLength={4} />
          </Form.Item>

          <Form.Item
            name="zipCode"
            label="邮编："
            rules={[{ required: true, message: '请输入邮编' }]}
          >
            <Input placeholder="" />
          </Form.Item>

          <Form.Item
            name="usedCount"
            label="已使用次数："
          >
            <Input type="number" placeholder="" />
          </Form.Item>

          <Form.Item
            name="bindCount"
            label="绑定次数："
          >
            <Input type="number" placeholder="" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态："
          >
            <Select placeholder="选择状态">
              <Select.Option value="未使用">未使用</Select.Option>
              <Select.Option value="已使用">已使用</Select.Option>
              <Select.Option value="已封禁">已封禁</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注："
          >
            <TextArea
              rows={4}
              placeholder=""
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 绑定账号弹窗 */}
      <Modal
        title="绑定账号（替换模式）"
        open={isBindModalVisible}
        onOk={handleBindSubmit}
        onCancel={() => {
          setIsBindModalVisible(false);
          bindForm.resetFields(); // 关闭时重置表单
        }}
        confirmLoading={loading}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16, padding: 8, backgroundColor: '#f0f8ff', borderRadius: 4, fontSize: '12px', color: '#666' }}>
          ⚠️ 注意：绑定操作将完全替换当前绑定的账户，而不是追加
        </div>
        <Form
          form={bindForm}
          layout="vertical"
        >
          <Form.Item
            label="卡号:"
            name="cardNumber"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            label="余额:"
            name="balance"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            label="剩余绑定次数:"
            name="remainingBindCount"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            label="持卡人:"
            name="cardHolder"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            label="状态:"
            name="status"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            name="bindAccounts"
            label="绑定账号（当前选择将完全替换已绑定账户）:"
            rules={[{ required: true, message: '请选择要绑定的账号' }]}
          >
            <Select
              key={selectKey}
              mode="multiple"
              placeholder={adAccountLoading ? "加载中..." : "请选择要绑定的广告账户"}
              style={{ width: '100%' }}
              loading={adAccountLoading}
              showSearch
              allowClear
              value={selectedAccountIds}
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
              notFoundContent={adAccountLoading ? "加载中..." : "暂无数据"}
              onChange={(value) => {
                console.log('=== Select值变化 ===');
                console.log('新选中的值:', value);
                console.log('值的类型:', typeof value);
                console.log('是数组吗:', Array.isArray(value));
                if (Array.isArray(value)) {
                  console.log('数组长度:', value.length);
                  value.forEach((v, i) => console.log(`  [${i}]: ${v} (${typeof v})`));
                }

                // 更新state
                setSelectedAccountIds(value);

                // 同步到表单
                bindForm.setFieldValue('bindAccounts', value);
                bindForm.setFieldsValue({ bindAccounts: value });

                // 验证表单字段是否正确设置
                setTimeout(() => {
                  const currentValue = bindForm.getFieldValue('bindAccounts');
                  console.log('表单字段实际值:', currentValue);
                  console.log('state值:', value);
                  console.log('值是否相等:', JSON.stringify(currentValue) === JSON.stringify(value));
                }, 100);
              }}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  console.log('=== Select下拉打开时的调试信息 ===');
                  console.log('完整的adAccountData:', JSON.stringify(adAccountData, null, 2));
                  console.log('adAccountData?.adAccounts:', adAccountData?.adAccounts);
                  console.log('账户数组长度:', adAccountData?.adAccounts?.length);
                  if (adAccountData?.adAccounts) {
                    adAccountData.adAccounts.forEach((account: AdAccount, index: number) => {
                      console.log(`账户[${index}]:`, {
                        id: account.id,
                        accountId: account.accountId,
                        account: account.account,
                        hasId: !!account.id,
                        hasAccountId: !!account.accountId,
                        hasAccount: !!account.account
                      });
                    });
                  }
                  console.log('=== Select调试信息结束 ===');
                }
              }}
            >
              {adAccountData?.adAccounts?.map((account: AdAccount, index: number) => {
                console.log(`渲染Option[${index}]:`, {
                  internalId: account.id,
                  accountId: account.accountId,
                  account: account.account,
                  hasId: !!account.id,
                  hasAccountId: !!account.accountId,
                  hasAccount: !!account.account
                });
                return (
                  <Select.Option key={account.id || index} value={account.id}>
                    {`${account.accountId} - ${account.account || `账户${index + 1}`}`}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 充值弹窗 */}
      <Modal
        title="VCC卡片充值"
        open={isRechargeModalVisible}
        onOk={handleRechargeSubmit}
        onCancel={() => {
          setIsRechargeModalVisible(false);
          rechargeForm.resetFields();
        }}
        confirmLoading={loading}
        width={500}
        okText="确认充值"
        cancelText="取消"
      >
        <Form
          form={rechargeForm}
          layout="vertical"
        >
          <Form.Item
            label="卡号:"
            name="cardNumber"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            label="持卡人:"
            name="cardHolder"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            label="当前余额:"
            name="currentBalance"
          >
            <Input
              disabled
              placeholder="回显不予修改"
            />
          </Form.Item>

          <Form.Item
            name="rechargeAmount"
            label="充值金额 (USD):"
            rules={[
              { required: true, message: '请输入充值金额' },
              {
                validator: (_, value) => {
                  const amount = parseFloat(value);
                  if (isNaN(amount)) {
                    return Promise.reject(new Error('请输入有效的数字'));
                  }
                  if (amount <= 0) {
                    return Promise.reject(new Error('充值金额必须大于0'));
                  }
                  if (amount > 10000) {
                    return Promise.reject(new Error('单次充值金额不能超过10,000美元'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input
              type="number"
              placeholder="请输入充值金额"
              prefix="$"
              min="0"
              step="0.01"
              onChange={(e) => {
                // 实时计算预期余额
                const rechargeAmount = parseFloat(e.target.value) || 0;
                const currentBalance = selectedCard?.balance || 0;
                const expectedBalance = currentBalance + rechargeAmount;

                // 更新预期余额显示
                rechargeForm.setFieldValue('expectedBalance', `$${expectedBalance.toFixed(2)}`);
              }}
            />
          </Form.Item>

          <Form.Item
            label="充值后余额:"
            name="expectedBalance"
          >
            <Input
              disabled
              placeholder="输入充值金额后自动计算"
              style={{ color: '#52c41a', fontWeight: 'bold' }}
            />
          </Form.Item>

          <div style={{
            padding: '12px',
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '6px',
            marginTop: '16px'
          }}>
            <div style={{ fontSize: '14px', color: '#52c41a', marginBottom: '4px' }}>
              💡 充值说明：
            </div>
            <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.4' }}>
              • 充值金额将直接添加到当前余额<br />
              • 支持美元充值，最低充值1美元<br />
              • 单次最高充值10,000美元<br />
              • 充值后余额将立即更新
            </div>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default VCCListTab;
