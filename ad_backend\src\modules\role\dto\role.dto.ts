import { Field, ID, InputType, Int, ObjectType } from '@nestjs/graphql';
import { IsArray, IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';
import { RolePageInfo, PaginationArgs } from '../../../core/pagination';
import { RouteDTO } from '../../route/dto/route.dto';

@ObjectType('Role')
export class Role {
    @Field(() => ID)
    id: string;
    @Field(() => String)
    name: string;
    @Field(() => String, { nullable: true })
    description: string;
    @Field(() => String)
    status: 'enabled' | 'disabled';
    @Field(() => Int)
    order: number;
    @Field(() => [String], { nullable: true })
    routeIds?: string[];
    @Field(() => [RouteDTO], { nullable: true })
    routeList?: RouteDTO[];
    @Field(() => Date)
    createdAt: Date;
    @Field(() => Date)
    updatedAt: Date;
}

@InputType('RoleInput')
export class RoleInput {
    @Field(() => ID)
    @IsNotEmpty()
    @IsUUID('4')
    id: string;
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    name?: string;
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    description?: string;
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    status?: 'enabled' | 'disabled';
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    order?: number;
    @Field(() => [String], { nullable: true })
    @IsOptional()
    @IsArray()
    routeIds?: string[];
    @Field(() => Date, { nullable: true })
    @IsOptional()
    createdAt?: Date;
    @Field(() => Date, { nullable: true })
    @IsOptional()
    updatedAt?: Date;
}

@ObjectType()
export class RolePagination {
    @Field(() => [Role])
    items: Role[];

    @Field(() => RolePageInfo)
    pageInfo: RolePageInfo;

    @Field(() => Int)
    totalCount: number;
}

@InputType()
export class RoleSearchInput extends PaginationArgs {
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    search?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsEnum(['enabled', 'disabled'])
    status?: 'enabled' | 'disabled';
}

@InputType()
export class CreateRoleInput {
    @Field(() => String)
    @IsNotEmpty()
    @IsString()
    @MaxLength(50)
    name: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    description?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsEnum(['enabled', 'disabled'])
    status?: 'enabled' | 'disabled';

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    order?: number;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID('4', { each: true })
    routeIds?: string[];
}

@InputType()
export class UpdateRoleInput {
    @Field(() => ID)
    @IsNotEmpty()
    @IsUUID('4')
    id: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    name?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    description?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsEnum(['enabled', 'disabled'])
    status?: 'enabled' | 'disabled';

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    order?: number;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID('4', { each: true })
    routeIds?: string[];
}

@InputType()
export class DeleteRoleInput {
    @Field(() => ID)
    @IsNotEmpty()
    @IsUUID('4')
    id: string;
} 