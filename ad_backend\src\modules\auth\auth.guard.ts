// auth.guard.ts
import {
	Injectable,
	CanActivate,
	ExecutionContext,
	ContextType,
	UnauthorizedException,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Reflector } from '@nestjs/core';

@Injectable()
export class AuthGuard implements CanActivate {
	constructor(private reflector: Reflector) { }

	async canActivate(context: ExecutionContext): Promise<boolean> {
		// 检查路由是否已由JwtAuthGuard保护
		const hasJwtGuard = this.reflector.getAllAndOverride<boolean>('hasJwtGuard', [
			context.getHandler(),
			context.getClass(),
		]);

		// 如果路由已由JwtAuthGuard保护，则跳过此AuthGuard
		if (hasJwtGuard) {
			return true;
		}

		// 检查是否要禁用认证
		const IS_DISABLE_AUTH =
			process.env.IS_DISABLE_AUTH === 'true' ? true : false;

		// 如果禁用了认证，则允许所有请求通过
		if (IS_DISABLE_AUTH) {
			return true;
		}

		// 否则执行API密钥验证
		const request = this.getRequest(context);
		const apiKey = request.headers['x-app-id'];
		const apiSecret = request.headers['x-api-key'];

		if (!apiKey || !apiSecret) {
			throw new UnauthorizedException(
				'API key and secret keys are required.',
			);
		}

		// 返回true允许请求继续，返回false拒绝访问
		return !!request.tenant;
	}

	/**
	 * Get the request object from the provided ExecutionContext.
	 * If the context is of type 'graphql', it returns the request object from the GraphQL context.
	 * For HTTP-based contexts, it returns the request object from the HTTP context.
	 * For other types of contexts, it returns undefined.
	 *
	 * @param context The ExecutionContext representing the current request context.
	 * @returns The request object extracted from the context, or undefined if the context type is not supported.
	 */
	getRequest(context: ExecutionContext): any {
		// Check if the context type is 'graphql'
		if (context.getType<ContextType | 'graphql'>() === 'graphql') {
			// Extract the request object from the GraphQL context
			return GqlExecutionContext.create(context).getContext().req;
		}
		// If the context is not 'graphql', assume it is an HTTP-based context
		// Extract the request object from the HTTP context
		return context.switchToHttp().getRequest();
	}
}
