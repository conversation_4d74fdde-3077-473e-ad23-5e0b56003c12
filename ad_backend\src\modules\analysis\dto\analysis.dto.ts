import { IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum TimeRangeEnum {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
}

export class TimeRangeDto {
  @ApiProperty({
    enum: TimeRangeEnum,
    default: TimeRangeEnum.DAY,
    description: '时间范围：day-日，week-周，month-月',
  })
  @IsEnum(TimeRangeEnum)
  @IsOptional()
  timeRange?: TimeRangeEnum = TimeRangeEnum.DAY;
}

export class MetricDataDto {
  @ApiProperty({ description: '数值' })
  value: number;

  @ApiProperty({ description: '百分比变化' })
  percentage: number;
}

export class OverviewCardsResponseDto {
  @ApiProperty({ type: MetricDataDto, description: '总展示量' })
  impressions: MetricDataDto;

  @ApiProperty({ type: MetricDataDto, description: '总点击量' })
  clicks: MetricDataDto;

  @ApiProperty({ type: MetricDataDto, description: '总转化量' })
  conversions: MetricDataDto;

  @ApiProperty({ type: MetricDataDto, description: '总支出' })
  spend: MetricDataDto;
}

export class BarDataDto {
  @ApiProperty({ type: [Number], description: '展示量数据' })
  impressions: number[];

  @ApiProperty({ type: [Number], description: '点击量数据' })
  clicks: number[];

  @ApiProperty({ type: [Number], description: '转化量数据' })
  conversions: number[];
}

export class LineDataDto {
  @ApiProperty({ type: [Number], description: '点击率数据' })
  ctr: number[];

  @ApiProperty({ type: [Number], description: '转化率数据' })
  cvr: number[];
}

export class AdPerformanceAnalysisResponseDto {
  @ApiProperty({ type: BarDataDto, description: '柱状图数据' })
  barData: BarDataDto;

  @ApiProperty({ type: LineDataDto, description: '折线图数据' })
  lineData: LineDataDto;

  @ApiProperty({ type: [String], description: '时间标签' })
  timeLabels: string[];
}

export class AdTrafficAnalysisResponseDto {
  @ApiProperty({ type: [Number], description: '花费数据' })
  spend: number[];

  @ApiProperty({ type: [Number], description: '转化量数据' })
  conversions: number[];

  @ApiProperty({ type: [Number], description: '点击量数据' })
  clicks: number[];

  @ApiProperty({ type: [Number], description: '展示量数据' })
  impressions: number[];

  @ApiProperty({ type: [String], description: '时间标签' })
  timeLabels: string[];
}

export class CreativeItemDto {
  @ApiProperty({ description: '素材ID' })
  id: number;

  @ApiProperty({ description: '素材名称' })
  name: string;

  @ApiProperty({ description: '展示量' })
  impressions: number;

  @ApiProperty({ description: '点击量' })
  clicks: number;

  @ApiProperty({ description: '点击率' })
  ctr: number;

  @ApiProperty({ description: '转化量' })
  conversions: number;

  @ApiProperty({ description: '转化率' })
  cvr: number;
}

export class CreativePerformanceResponseDto {
  @ApiProperty({ type: [CreativeItemDto], description: '图片素材数据' })
  images: CreativeItemDto[];

  @ApiProperty({ type: [CreativeItemDto], description: '视频素材数据' })
  videos: CreativeItemDto[];

  @ApiProperty({ type: [CreativeItemDto], description: '轮播素材数据' })
  carousels: CreativeItemDto[];
}

export enum ChannelTimeRangeEnum {
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
}

export class ChannelMetricDto {
  @ApiProperty({ description: '渠道名称' })
  channel: string;

  @ApiProperty({ description: '展示量' })
  impressions: number;

  @ApiProperty({ description: '点击量' })
  clicks: number;

  @ApiProperty({ description: '转化量' })
  conversions: number;

  @ApiProperty({ description: '花费' })
  cost: number;

  @ApiProperty({ description: '点击率' })
  ctr: number;

  @ApiProperty({ description: '转化率' })
  cvr: number;

  @ApiProperty({ description: '点击成本' })
  cpc: number;

  @ApiProperty({ description: 'ROI投资回报率' })
  roi: number;
}

export class ChannelAnalysisResponseDto {
  @ApiProperty({ type: [ChannelMetricDto], description: '渠道指标数据' })
  channels: ChannelMetricDto[];

  @ApiProperty({ type: [String], description: '渠道名称列表' })
  channelNames: string[];

  @ApiProperty({ type: [Number], description: '各渠道展示量（用于饼图）' })
  impressionsData: number[];

  @ApiProperty({ type: [Number], description: '各渠道ROI数据（用于柱状图）' })
  roiData: number[];
} 