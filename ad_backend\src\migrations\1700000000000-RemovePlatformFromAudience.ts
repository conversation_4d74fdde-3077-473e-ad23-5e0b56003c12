import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemovePlatformFromAudience1700000000000 implements MigrationInterface {
    name = 'RemovePlatformFromAudience1700000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "platform"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN "platform" character varying(32)`);
    }
} 