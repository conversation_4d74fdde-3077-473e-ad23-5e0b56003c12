import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { SeedDataService } from './modules/seeds/seed.service';
import { GraphQLSchemaHost } from '@nestjs/graphql';
import { printSchema } from 'graphql';

async function bootstrap() {
	const app = await NestFactory.create(AppModule);
	app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));
	app.enableCors();

	const options = new DocumentBuilder()
		.setTitle('Ever API Starter Kit')
		.setDescription('The Ever API Starter Kit REST API Description')
		.setVersion('1.0')
		.addTag('ever')
		.build();

	const document = SwaggerModule.createDocument(app, options);
	SwaggerModule.setup('api', app, document);

	// 初始化种子数据
	try {
		const seedService = app.get(SeedDataService);
		await seedService.run();
	} catch (error) {
		console.error('数据初始化失败', error);
	}

	const port = 3005;

	await app.listen(port, async () => {
		console.log(`Listening at Port: ${port}`);
		// 打印 GraphQL schema
		const schemaHost = app.get(GraphQLSchemaHost);
		const schema = schemaHost.schema;
	});
}

bootstrap();
