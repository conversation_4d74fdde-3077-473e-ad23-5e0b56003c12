import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Card, Tabs } from 'antd';
import { CreditCardOutlined } from '@ant-design/icons';
import VCCListTab from './VCCListTab';
import TransactionHistoryTab from './TransactionHistoryTab';
// import WarningSettingsTab from './WarningSettingsTab';

const { TabPane } = Tabs;

// VCC管理主组件
const VCCManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('1');
  const { t } = useTranslation();

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  return (
    <div>
      <Card title={t('pages.key_382')} extra={<CreditCardOutlined />}>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab={t('pages.key_379')} key="1">
            <VCCListTab />
          </TabPane>
          <TabPane tab={t('pages.key_67')} key="2">
            <TransactionHistoryTab />
          </TabPane>
          {/* <TabPane tab={t('pages.key_2566')} key="3">
            <WarningSettingsTab />
          </TabPane> */}
        </Tabs>
      </Card>
    </div>
  );
};

export default VCCManagement;