import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Form, Switch, Input, Checkbox, Card, Row, Col, Button, Alert, Select, message } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { WarningSettings } from './types';
import { defaultWarningSettings } from './mockData';

const { Item: FormItem } = Form;
const { t } = useTranslation();

const WarningSettingsTab: React.FC = () => {
  const [form] = Form.useForm();
  const [settings, setSettings] = useState<WarningSettings>(defaultWarningSettings);

  // 处理表单提交
  const handleSubmit = (values: WarningSettings) => {
    console.log('提交的设置:', values);
    // 这里应该调用API保存设置
    setSettings(values);
    message.success(t('pages.key_2117'));
  };

  return (
    <div>
      <Card>
        <Alert
          message={t('pages.key_107')}
          description="当VCC卡余额低于预警阈值时，系统将自动发送通知。请设置您的预警阈值和通知方式。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          name="warning_settings_form"
          layout="vertical"
          initialValues={settings}
          onFinish={handleSubmit}
        >
          <Row gutter={24}>
            <Col span={12}>
              <FormItem
                name="enabled"
                valuePropName="checked"
                label={t('pages.key_499')}
              >
                <Switch />
              </FormItem>

              <FormItem
                name="threshold"
                label={t('pages.key_2568')}
                rules={[{ required: true, message: t('pages.key_2243') }]}
              >
                <Input
                  type="number"
                  prefix="$"
                  placeholder={t('pages.key_2380')}
                />
              </FormItem>

              <Card title={t('pages.key_2469')} size="small" style={{ marginBottom: 16 }}>
                <FormItem name="notifyEmail" valuePropName="checked">
                  <Checkbox>{t('pages.key_1645')}</Checkbox>
                </FormItem>

                <FormItem name="notifySMS" valuePropName="checked">
                  <Checkbox>SMS短信</Checkbox>
                </FormItem>

                <FormItem name="notifyPush" valuePropName="checked">
                  <Checkbox>{t('pages.key_847')}</Checkbox>
                </FormItem>
              </Card>
            </Col>

            <Col span={12}>
              <FormItem
                name="emails"
                label={t('pages.key_2470')}
                rules={[{ required: true, message: t('pages.key_2229') }]}
              >
                <Select
                  mode="tags"
                  placeholder={t('pages.key_2379')}
                  style={{ width: '100%' }}
                />
              </FormItem>

              <FormItem
                name="phones"
                label={t('pages.key_2468')}
              >
                <Select
                  mode="tags"
                  placeholder={t('pages.key_2377')}
                  style={{ width: '100%' }}
                />
              </FormItem>

              <FormItem>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SettingOutlined />}
                  style={{ marginTop: 16 }}
                >{t('pages.key_142')}</Button>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default WarningSettingsTab;
