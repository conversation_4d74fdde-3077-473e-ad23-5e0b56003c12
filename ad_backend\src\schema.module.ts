import { join } from 'path';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GraphQLModule } from '@nestjs/graphql';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmConfigService } from './config/database';
import { EmployeeModule } from './modules/employee/employee.module';
import { AntiBlockModule } from './modules/anti-block/antiBlock.module';
import { RouteModule } from './modules/route/route.module';
import { GroupModule } from './modules/group/group.module';
import { ipRestrictionModule } from './modules/ip-restriction/ipRestriction.module';
import { RoleModule } from './modules/role/role.module';
import { UserModule } from './modules/user/user.module';
import { VccCardModule } from './modules/vcc/vcc-card.module';
import { SchemaService } from './schema.service';
import { SeedDataService } from './modules/seeds/seed.service';
import { ApolloDriver } from '@nestjs/apollo';
import config, { ConfigurationService } from './config/config';

const autoSchemaFile = process.env.IS_NOT_SLS
	? join(process.cwd(), 'generated/schemas/schema.graphql')
	: // TODO: when we run in Serverless, this should probably be different (for now we use the same path)
	join(process.cwd(), 'generated/schemas/schema.graphql') + '';

// For now let's enable debug, playground, introspection etc in production
// TODO: process.env.NODE_ENV == 'production' ? true : false;
const isProd = false;

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
			load: [config],
		}),
		GraphQLModule.forRoot({
			include: [
				EmployeeModule,
				AntiBlockModule,
				GroupModule,
				RouteModule,
				ipRestrictionModule,
				RoleModule,
				UserModule,
				VccCardModule
			],
			autoSchemaFile,
			// Disable debug in production
			debug: !isProd,
			tracing: !isProd,
			driver: ApolloDriver,
			introspection: !isProd,
			playground: isProd
				? false
				: {
					endpoint: process.env.IS_NOT_SLS
						? '/graphql'
						: `/${process.env.STAGE}/graphql`,
				},
		}),
		TypeOrmModule.forRootAsync({
			inject: [ConfigModule, ConfigurationService],
			useClass: TypeOrmConfigService,
		}),
		EmployeeModule,
		AntiBlockModule,
		GroupModule,
		RouteModule,
		ipRestrictionModule,
		RoleModule,
		UserModule,
		VccCardModule
	],
	providers: [
		ConfigurationService,
		SchemaService,
		TypeOrmConfigService,
		SeedDataService,
	],
})
export class SchemaModule { }
