import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { VccCard } from './entities/vcc-card.entity';
import { Tenant } from '../../entity/tenant.entity';

@Controller('vcc-cards')
@UseGuards(JwtAuthGuard)
@ApiTags('VCC卡片管理')
export class VccCardController {
    constructor(
        @InjectRepository(VccCard)
        private readonly vccCardRepo: Repository<VccCard>,
        @InjectRepository(Tenant)
        private readonly tenantRepo: Repository<Tenant>,
    ) { }

    @Get()
    @ApiOperation({ summary: '获取VCC卡片列表' })
    @ApiResponse({ status: 200, description: '获取成功' })
    async findAll(
        @Query('tenantId') tenantId?: string,
        @Query('page') page = 1,
        @Query('pageSize') pageSize = 20,
        @Query('channel') channel?: string,
        @Query('status') status?: string,
    ) {
        const where: any = {};
        if (tenantId) where.tenant = { id: tenantId };
        if (channel) where.channel = channel;
        if (status) where.status = status;

        const [data, total] = await this.vccCardRepo.findAndCount({
            where,
            relations: ['tenant'],
            skip: (page - 1) * pageSize,
            take: pageSize,
            order: { createdAt: 'DESC' }
        });

        return { data, total, page, pageSize };
    }

    @Get(':id')
    @ApiOperation({ summary: '获取VCC卡片详情' })
    @ApiResponse({ status: 200, description: '获取成功' })
    async findOne(@Param('id') id: string) {
        const card = await this.vccCardRepo.findOne({
            where: { id },
            relations: ['tenant']
        });

        if (!card) {
            throw new Error('VCC卡片不存在');
        }

        return card;
    }

    @Post()
    @ApiOperation({ summary: '创建VCC卡片' })
    @ApiResponse({ status: 201, description: '创建成功' })
    async create(@Body() createDto: any) {
        const { tenantId, ...cardData } = createDto;

        const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
        if (!tenant) {
            throw new Error('租户不存在');
        }

        const card = this.vccCardRepo.create({
            ...cardData,
            tenant,
            tenantId
        });

        return await this.vccCardRepo.save(card);
    }

    @Put(':id')
    @ApiOperation({ summary: '更新VCC卡片' })
    @ApiResponse({ status: 200, description: '更新成功' })
    async update(@Param('id') id: string, @Body() updateDto: any) {
        const card = await this.vccCardRepo.findOne({ where: { id } });

        if (!card) {
            throw new Error('VCC卡片不存在');
        }

        Object.assign(card, updateDto);
        return await this.vccCardRepo.save(card);
    }

    @Delete(':id')
    @ApiOperation({ summary: '删除VCC卡片' })
    @ApiResponse({ status: 200, description: '删除成功' })
    async remove(@Param('id') id: string) {
        const card = await this.vccCardRepo.findOne({ where: { id } });

        if (!card) {
            throw new Error('VCC卡片不存在');
        }

        await this.vccCardRepo.remove(card);
        return { success: true };
    }

    @Post('batch')
    @ApiOperation({ summary: '批量创建VCC卡片' })
    @ApiResponse({ status: 201, description: '批量创建成功' })
    async createBatch(@Body() batchDto: { tenantId: string; cards: any[] }) {
        const { tenantId, cards } = batchDto;

        const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
        if (!tenant) {
            throw new Error('租户不存在');
        }

        // 创建VccCard实体数组
        const entities = cards.map(cardData => {
            const entity = this.vccCardRepo.create();
            Object.assign(entity, cardData, { tenant, tenantId });
            return entity;
        });

        const savedCards = await this.vccCardRepo.save(entities);
        return { inserted: savedCards.length, cards: savedCards };
    }
} 