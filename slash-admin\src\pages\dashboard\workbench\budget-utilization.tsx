import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Progress } from "@/ui/progress";
import { useQuery } from '@apollo/client';
import { GET_BUDGET_UTILIZATION } from '@/api/budgetUtilization.graphql';

type BudgetUtilizationProps = {
  startDate: string;
  endDate: string;
};

const BudgetUtilization = ({ startDate, endDate }: BudgetUtilizationProps) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(GET_BUDGET_UTILIZATION, {
    variables: { startDate, endDate },
    fetchPolicy: 'network-only',
  });

  // 判空处理，保证hooks始终调用
  const platformBudgets = data?.budgetUtilization?.platformBudgets || [];
  const monthlyTrend = data?.budgetUtilization?.monthlyTrend || [];

  const trendChartOptions = useChart({
    xaxis: {
      categories: monthlyTrend.map((item: any) => item.month),
    },
    yaxis: {
      labels: {
        formatter: (value: number) => `¥${value.toLocaleString()}`,
      },
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: (value: number) => `¥${value.toLocaleString()}`,
      },
    },
    stroke: {
      curve: 'smooth',
      width: [3, 3],
      dashArray: [0, 5],
    },
    colors: ['#3b82f6', '#94a3b8'],
    legend: {
      position: 'top',
      horizontalAlign: 'right',
    },
  });

  const trendSeries = [
    {
      name: t('pages.key_665'),
      data: monthlyTrend.map((item: any) => item.spent),
    },
    {
      name: t('pages.key_2563'),
      data: monthlyTrend.map((item: any) => item.allocated),
    },
  ];

  const totalAllocated = platformBudgets.reduce((sum: number, p: any) => sum + p.allocated, 0);
  const totalSpent = platformBudgets.reduce((sum: number, p: any) => sum + p.spent, 0);
  const totalPercentage = Math.round((totalSpent / (totalAllocated || 1)) * 100);

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">{t('pages.key_2560')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-5">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">{t('pages.key_911')}</span>
            <span>¥{totalSpent.toLocaleString()} / ¥{totalAllocated.toLocaleString()}</span>
          </div>
          <Progress value={totalPercentage} className="h-2" />
          <div className="text-xs text-right text-muted-foreground">
            {totalPercentage}% 已使用
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-sm font-medium">{t('pages.key_458')}</h3>
          <div className="space-y-3">
            {platformBudgets.map((platform: any, index: number) => (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>{platform.name}</span>
                  <span>¥{platform.spent.toLocaleString()} / ¥{platform.allocated.toLocaleString()}</span>
                </div>
                <Progress
                  value={platform.percentage}
                  className="h-2"
                  color={platform.percentage > 90 ? "bg-red-500" :
                    platform.percentage > 75 ? "bg-amber-500" :
                      "bg-green-500"}
                />
              </div>
            ))}
          </div>
        </div>

        <div className="h-[250px]">
          <h3 className="text-sm font-medium mb-3">{t('pages.key_1288')}</h3>
          <Chart
            type="line"
            series={trendSeries}
            options={trendChartOptions}
            height={250}
          />
        </div>
      </CardContent>
    </Card>
  );
}

export default BudgetUtilization;
