import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { VccTransaction, TransactionType, TransactionStatus } from '../entities/vcc-transaction.entity';
import { VccCard } from '../entities/vcc-card.entity';
import { AdAccount } from '../../ad-platform/entities/ad-account.entity';
import {
    CreateVccTransactionInputDTO,
    UpdateVccTransactionInputDTO,
    VccTransactionFilterInputDTO,
    VccTransactionPaginationInputDTO,
    VccTransactionStatsDTO,
    VccTransactionDTO,
    PaginatedVccTransactionResult,
    SyncFacebookTransactionsInputDTO
} from '../dto/vcc-transaction.dto';
import { FacebookTransactionProxyService, FacebookTransactionData } from './facebook-transaction-proxy.service';

export interface PaginatedResult<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

@Injectable()
export class VccTransactionService {
    private readonly logger = new Logger(VccTransactionService.name);

    constructor(
        @InjectRepository(VccTransaction)
        private vccTransactionRepository: Repository<VccTransaction>,
        @InjectRepository(VccCard)
        private vccCardRepository: Repository<VccCard>,
        @InjectRepository(AdAccount)
        private adAccountRepository: Repository<AdAccount>,
        private facebookProxyService: FacebookTransactionProxyService
    ) { }

    /**
     * 查询交易记录列表（带分页和过滤）- 与前端接口完全对应
     */
    async findTransactions(
        filter: VccTransactionFilterInputDTO,
        pagination: VccTransactionPaginationInputDTO,
        tenantId: string
    ): Promise<PaginatedResult<VccTransactionDTO>> {
        const queryBuilder = this.createFilteredQuery(filter, tenantId);

        // 分页和排序
        const offset = (pagination.page! - 1) * pagination.limit!;
        queryBuilder
            .orderBy(`transaction.${pagination.sortBy}`, pagination.sortOrder!)
            .skip(offset)
            .take(pagination.limit);

        // 执行查询
        const [transactions, total] = await queryBuilder.getManyAndCount();

        // 转换为DTO格式（与前端TransactionRecord格式对应）
        const data = transactions.map(this.entityToDTO);

        return {
            data,
            total,
            page: pagination.page!,
            limit: pagination.limit!,
            totalPages: Math.ceil(total / pagination.limit!)
        };
    }

    /**
     * 根据ID查询单个交易记录
     */
    async findTransactionById(id: string, tenantId: string): Promise<VccTransactionDTO> {
        const transaction = await this.vccTransactionRepository.findOne({
            where: { id, tenantId },
            relations: ['vccCard']
        });

        if (!transaction) {
            throw new NotFoundException('交易记录不存在');
        }

        return this.entityToDTO(transaction);
    }

    /**
     * 创建交易记录
     */
    async createTransaction(
        input: CreateVccTransactionInputDTO,
        tenantId: string
    ): Promise<VccTransactionDTO> {
        // 验证VCC卡片是否存在且属于当前租户
        const vccCard = await this.vccCardRepository.findOne({
            where: { id: input.cardId, tenantId }
        });

        if (!vccCard) {
            throw new NotFoundException('VCC卡片不存在或无权限访问');
        }

        // 创建交易记录
        const transaction = this.vccTransactionRepository.create({
            ...input,
            tenantId,
            cardNo: vccCard.cardNumber, // 已经是脱敏的
            transactionTime: new Date(input.transactionTime)
        });

        const savedTransaction = await this.vccTransactionRepository.save(transaction);
        return this.entityToDTO(savedTransaction);
    }

    /**
     * 更新交易记录
     */
    async updateTransaction(
        id: string,
        input: UpdateVccTransactionInputDTO,
        tenantId: string
    ): Promise<VccTransactionDTO> {
        const transaction = await this.vccTransactionRepository.findOne({
            where: { id, tenantId }
        });

        if (!transaction) {
            throw new NotFoundException('交易记录不存在');
        }

        // 更新字段
        Object.assign(transaction, input);

        const updatedTransaction = await this.vccTransactionRepository.save(transaction);
        return this.entityToDTO(updatedTransaction);
    }

    /**
     * 删除交易记录
     */
    async deleteTransaction(id: string, tenantId: string): Promise<boolean> {
        const result = await this.vccTransactionRepository.delete({ id, tenantId });

        if (result.affected === 0) {
            throw new NotFoundException('交易记录不存在');
        }

        return true;
    }

    /**
     * 获取交易统计数据
     */
    async getTransactionStats(
        filter: VccTransactionFilterInputDTO,
        tenantId: string
    ): Promise<VccTransactionStatsDTO> {
        const queryBuilder = this.createFilteredQuery(filter, tenantId);
        const [transactions, totalCount] = await queryBuilder.getManyAndCount();

        if (totalCount === 0) {
            return {
                totalCount: 0,
                totalAmount: 0,
                averageAmount: 0,
                successCount: 0,
                pendingCount: 0,
                failedCount: 0,
                successRate: 0
            };
        }

        // 计算统计数据
        const totalAmount = transactions.reduce((sum, t) => sum + Number(t.amount), 0);
        const averageAmount = totalAmount / totalCount;

        const statusCounts = transactions.reduce((acc, t) => {
            acc[t.status] = (acc[t.status] || 0) + 1;
            return acc;
        }, {} as Record<TransactionStatus, number>);

        const successCount = statusCounts[TransactionStatus.SUCCESS] || 0;
        const pendingCount = statusCounts[TransactionStatus.PENDING] || 0;
        const failedCount = statusCounts[TransactionStatus.FAILED] || 0;
        const successRate = totalCount > 0 ? (successCount / totalCount) * 100 : 0;

        return {
            totalCount,
            totalAmount,
            averageAmount,
            successCount,
            pendingCount,
            failedCount,
            successRate
        };
    }

    /**
     * 导出交易记录（CSV格式）
     */
    async exportTransactions(
        filter: VccTransactionFilterInputDTO,
        tenantId: string,
        format: 'csv' | 'json' = 'csv'
    ): Promise<string> {
        const queryBuilder = this.createFilteredQuery(filter, tenantId);
        const transactions = await queryBuilder.getMany();

        if (format === 'json') {
            const data = transactions.map(this.entityToDTO);
            return JSON.stringify(data, null, 2);
        }

        // CSV格式
        const headers = [
            '交易ID', 'VCC卡片ID', '卡号', '交易金额', '商户名称',
            '交易时间', '交易状态', '交易类型', 'Facebook账户ID', 'Facebook交易ID',
            '广告活动名称', '交易描述', '创建时间'
        ];

        const csvRows = [
            headers.join(','),
            ...transactions.map(t => [
                t.id,
                t.cardId,
                t.cardNo,
                t.amount,
                t.merchant,
                t.transactionTime.toISOString(),
                t.status,
                t.type,
                t.facebookAccountId || '',
                t.facebookTransactionId || '',
                t.campaignName || '',
                t.description || '',
                t.createdAt.toISOString()
            ].map(field => `"${field}"`).join(','))
        ];

        return csvRows.join('\n');
    }

    /**
     * 🚀 从Facebook同步VCC卡片的交易记录
     */
    async syncFacebookTransactions(
        input: SyncFacebookTransactionsInputDTO,
        tenantId: string
    ): Promise<VccTransactionDTO[]> {
        this.logger.log(`开始同步VCC卡片 ${input.cardId} 的Facebook交易记录`);

        // 1. 验证VCC卡片并获取绑定的广告账户
        const vccCard = await this.vccCardRepository.findOne({
            where: { id: input.cardId, tenantId }
        });

        if (!vccCard) {
            throw new NotFoundException('VCC卡片不存在或无权限访问');
        }

        if (!vccCard.boundAdAccountIds || vccCard.boundAdAccountIds.length === 0) {
            throw new BadRequestException('该VCC卡片未绑定任何Facebook广告账户');
        }

        // 2. 获取绑定的广告账户信息（包含OAuth token）
        const adAccounts = await this.adAccountRepository.find({
            where: {
                id: In(vccCard.boundAdAccountIds.filter(id => id !== null))
            },
            relations: ['tenant']
        });

        if (adAccounts.length === 0) {
            throw new BadRequestException('未找到有效的绑定广告账户');
        }

        // 验证广告账户是否属于当前租户
        const validAdAccounts = adAccounts.filter(account => {
            return account.tenant && account.tenant.id === tenantId;
        });

        if (validAdAccounts.length === 0) {
            throw new BadRequestException('绑定的广告账户都不属于当前租户');
        }

        // 3. 过滤出有有效OAuth token的账户
        const accountsWithToken = validAdAccounts.filter(account => {
            const token = this.extractValidToken(account);
            return token !== null;
        });

        if (accountsWithToken.length === 0) {
            throw new BadRequestException('绑定的广告账户都没有有效的Facebook访问令牌，请先进行OAuth授权');
        }

        this.logger.log(`找到 ${accountsWithToken.length} 个有效的Facebook广告账户`);

        // 4. 从Facebook API获取交易数据
        const facebookAccounts = accountsWithToken.map(account => ({
            accountId: account.accountId,
            accessToken: this.extractValidToken(account)!
        }));

        const facebookTransactionMap = await this.facebookProxyService.getBatchAccountTransactions(
            facebookAccounts,
            input.startDate,
            input.endDate
        );

        // 5. 合并所有账户的交易数据
        let allFacebookTransactions: FacebookTransactionData[] = [];
        for (const [accountId, transactions] of facebookTransactionMap) {
            allFacebookTransactions = allFacebookTransactions.concat(transactions);
        }

        if (allFacebookTransactions.length === 0) {
            this.logger.warn('未获取到任何Facebook交易数据');
            return [];
        }

        // 6. 转换为VCC交易记录格式
        const vccTransactionData = this.facebookProxyService.convertToVccTransactions(
            allFacebookTransactions,
            input.cardId,
            vccCard.cardNumber,
            tenantId
        );

        // 7. 保存到数据库（处理重复记录）
        const savedTransactions: VccTransaction[] = [];

        for (const transactionData of vccTransactionData) {
            try {
                // 检查是否已存在相同的Facebook交易记录
                const existingTransaction = await this.vccTransactionRepository.findOne({
                    where: {
                        cardId: input.cardId,
                        facebookTransactionId: transactionData.facebookTransactionId,
                        tenantId
                    }
                });

                if (existingTransaction) {
                    if (input.forceOverwrite) {
                        // 强制覆盖模式：更新现有记录
                        Object.assign(existingTransaction, transactionData);
                        const updated = await this.vccTransactionRepository.save(existingTransaction);
                        savedTransactions.push(updated);
                        this.logger.log(`更新已存在的交易记录: ${existingTransaction.id}`);
                    } else {
                        // 跳过已存在的记录
                        this.logger.log(`跳过已存在的交易记录: ${transactionData.facebookTransactionId}`);
                        continue;
                    }
                } else {
                    // 创建新的交易记录
                    const newTransaction = this.vccTransactionRepository.create(transactionData);
                    const saved = await this.vccTransactionRepository.save(newTransaction);
                    savedTransactions.push(saved);
                }
            } catch (error: any) {
                this.logger.error(`保存交易记录失败: ${error.message}`, error.stack);
                // 继续处理其他记录，不中断整个同步过程
            }
        }

        this.logger.log(`成功同步 ${savedTransactions.length} 条Facebook交易记录`);

        // 8. 返回DTO格式的结果
        return savedTransactions.map(this.entityToDTO);
    }

    // ===== 私有辅助方法 =====

    /**
     * 创建过滤查询构建器
     */
    private createFilteredQuery(
        filter: VccTransactionFilterInputDTO,
        tenantId: string
    ): SelectQueryBuilder<VccTransaction> {
        const queryBuilder = this.vccTransactionRepository
            .createQueryBuilder('transaction')
            .leftJoinAndSelect('transaction.vccCard', 'vccCard')
            .where('transaction.tenantId = :tenantId', { tenantId });

        // VCC卡片ID过滤
        if (filter.cardId) {
            queryBuilder.andWhere('transaction.cardId = :cardId', {
                cardId: filter.cardId
            });
        }

        // 卡号模糊搜索
        if (filter.cardNo) {
            queryBuilder.andWhere('transaction.cardNo LIKE :cardNo', {
                cardNo: `%${filter.cardNo}%`
            });
        }

        // 商户名称模糊搜索
        if (filter.merchant) {
            queryBuilder.andWhere('transaction.merchant LIKE :merchant', {
                merchant: `%${filter.merchant}%`
            });
        }

        // 状态过滤
        if (filter.status) {
            queryBuilder.andWhere('transaction.status = :status', {
                status: filter.status
            });
        }

        // 类型过滤
        if (filter.type) {
            queryBuilder.andWhere('transaction.type = :type', {
                type: filter.type
            });
        }

        // 时间范围过滤
        if (filter.startTime && filter.endTime) {
            queryBuilder.andWhere('transaction.transactionTime BETWEEN :startTime AND :endTime', {
                startTime: new Date(filter.startTime),
                endTime: new Date(filter.endTime)
            });
        } else if (filter.startTime) {
            queryBuilder.andWhere('transaction.transactionTime >= :startTime', {
                startTime: new Date(filter.startTime)
            });
        } else if (filter.endTime) {
            queryBuilder.andWhere('transaction.transactionTime <= :endTime', {
                endTime: new Date(filter.endTime)
            });
        }

        // 金额范围过滤
        if (filter.minAmount !== undefined && filter.maxAmount !== undefined) {
            queryBuilder.andWhere('transaction.amount BETWEEN :minAmount AND :maxAmount', {
                minAmount: filter.minAmount,
                maxAmount: filter.maxAmount
            });
        } else if (filter.minAmount !== undefined) {
            queryBuilder.andWhere('transaction.amount >= :minAmount', {
                minAmount: filter.minAmount
            });
        } else if (filter.maxAmount !== undefined) {
            queryBuilder.andWhere('transaction.amount <= :maxAmount', {
                maxAmount: filter.maxAmount
            });
        }

        return queryBuilder;
    }

    /**
     * 实体转DTO（与前端TransactionRecord格式完全对应）
     */
    private entityToDTO = (entity: VccTransaction): VccTransactionDTO => ({
        id: entity.id,
        cardId: entity.cardId,
        cardNo: entity.cardNo,
        amount: Number(entity.amount),
        merchant: entity.merchant,
        transactionTime: entity.transactionTime.toISOString(), // 前端期望字符串格式
        status: entity.status,
        type: entity.type,
        facebookAccountId: entity.facebookAccountId,
        facebookTransactionId: entity.facebookTransactionId,
        campaignName: entity.campaignName,
        description: entity.description
    });

    /**
     * 从广告账户中提取有效的Token
     */
    private extractValidToken(account: AdAccount): string | null {
        // 检查多个可能的token字段
        const tokenFields = [
            account.oauth,
            (account as any).accessToken,
            (account as any).facebookToken,
            (account as any).token
        ];

        for (const token of tokenFields) {
            if (token && typeof token === 'string' && token.trim() !== '' && token.length > 10) {
                return token.trim();
            }
        }

        return null;
    }
} 