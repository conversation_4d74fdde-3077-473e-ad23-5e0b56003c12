import { MigrationInterface, QueryRunner } from "typeorm";

export class AddLocalesToAudience20240611181000 implements MigrationInterface {
    name = 'AddLocalesToAudience20240611181000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "locales" integer[]`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "locales"`);
    }
} 