import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBehaviorsToAudience20240611183000 implements MigrationInterface {
    name = 'AddBehaviorsToAudience20240611183000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "behaviors" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "behaviors"`);
    }
} 