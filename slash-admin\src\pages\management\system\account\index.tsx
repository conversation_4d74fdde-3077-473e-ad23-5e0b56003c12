import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from "react";
import { useApolloClient } from "@apollo/client";
import { Table, Button, Tag, Input, Space, notification, Popconfirm, message, Tooltip } from "antd";
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, LockOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table/interface";
import { UserService } from "@/api/services/user.service";
import { UserItem, UserStatus, CreateUserParams, UpdateUserParams } from "./types";
import UserModal from "./user-modal";
import { formatDate } from "@/utils";
import { useUserInfo } from "@/store/userStore";

export default function AccountManagement() {
  const { t } = useTranslation();
  const client = useApolloClient();
  const userService = new UserService(client);
  const currentUserInfo = useUserInfo(); // 获取当前登录用户信息

  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [users, setUsers] = useState<UserItem[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserItem | undefined>(undefined);
  const [operationLoading, setOperationLoading] = useState(false);
  const [deleteLoadingId, setDeleteLoadingId] = useState<string | null>(null);

  // 加载用户数据
  const loadUsers = async (page = currentPage, limit = pageSize) => {
    try {
      setLoading(true);
      console.log(`正在加载用户数据，第${page}页，每页${limit}条记录...`);

      // 准备查询参数
      const queryParams = {
        page,
        limit,
        filter: searchText ? {
          username: searchText,
        } : undefined
      };

      console.log('查询参数:', queryParams);
      const response = await userService.getUsers(queryParams);

      if (response) {
        console.log(`成功加载用户数据，共${response.totalCount}条记录`);
        setUsers(response.nodes || []);
        setTotal(response.totalCount || 0);
      } else {
        console.warn(t('pages.key_335'));
        notification.warning({ message: t('pages.key_335'), description: t('pages.key_2164') });
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
      notification.error({
        message: t('pages.key_333'),
        description: (error as Error).message || t('pages.key_1312'),
      });
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和分页变化时加载数据
  useEffect(() => {
    loadUsers();
  }, [currentPage, pageSize]);

  // 搜索用户
  const handleSearch = (value: string) => {
    console.log('搜索用户:', value);
    setSearchText(value);
    setCurrentPage(1); // 重置到第一页
    loadUsers(1, pageSize);
  };

  // 显示创建用户模态框
  const showCreateModal = () => {
    console.log(t('pages.key_957'));
    setCurrentUser(undefined);
    setModalVisible(true);
  };

  // 显示编辑用户模态框
  const showEditModal = (user: UserItem) => {
    // 检查是否是当前登录用户
    if (user.id === currentUserInfo.id) {
      message.warning(t('pages.key_19'));
      return;
    }

    console.log('打开编辑用户模态框，用户ID:', user.id);
    setCurrentUser(user);
    setModalVisible(true);
  };

  // 处理用户提交（创建或更新）
  const handleUserSubmit = async (values: CreateUserParams | UpdateUserParams) => {
    setOperationLoading(true);
    try {
      // 确保角色和群组ID是数组
      const userData = {
        ...values,
        roleIds: values.roleIds || [],
        groupIds: values.groupIds || []
      };

      if ('id' in userData) {
        // 更新用户
        console.log('更新用户:', userData);
        await userService.updateUser(userData as UpdateUserParams);
        notification.success({ message: t('pages.key_1634') });
      } else {
        // 创建用户
        console.log('创建用户:', userData);
        await userService.createUser(userData as CreateUserParams);
        notification.success({ message: t('pages.key_1625') });
      }

      setModalVisible(false);
      loadUsers(); // 重新加载用户列表
    } catch (error) {
      console.error('操作用户失败:', error);

      let errorMsg = t('pages.key_1060');
      if (error instanceof Error) {
        errorMsg = error.message;
      } else if (typeof error === 'string') {
        errorMsg = error;
      }

      notification.error({
        message: t('pages.key_1063'),
        description: errorMsg
      });
    } finally {
      setOperationLoading(false);
    }
  };

  // 处理删除用户
  const handleDeleteUser = async (id: string) => {
    // 检查是否是当前登录用户
    if (id === currentUserInfo.id) {
      message.warning(t('pages.key_16'));
      return;
    }
    setDeleteLoadingId(id);
    setOperationLoading(true);
    try {
      console.log('删除用户，ID:', id);
      await userService.deleteUser(id);
      notification.success({ message: t('pages.key_1626') });
      loadUsers();
    } catch (error) {
      console.error('删除用户失败:', error);
      notification.error({
        message: t('pages.key_296'),
        description: (error as Error).message || t('pages.key_1312'),
      });
    } finally {
      setOperationLoading(false);
      setDeleteLoadingId(null);
    }
  };

  // 处理状态变更
  const handleStatusChange = async (user: UserItem) => {
    // 检查是否是当前登录用户
    if (user.id === currentUserInfo.id) {
      message.warning(t('pages.key_18'));
      return;
    }

    setOperationLoading(true);
    try {
      const newStatus = user.status === UserStatus.ACTIVE ? UserStatus.BLOCKED : UserStatus.ACTIVE;
      console.log(`更改用户状态，ID:${user.id}，从:${user.status} 改为:${newStatus}`);
      await userService.changeUserStatus(user.id, newStatus);
      notification.success({ message: t('pages.key_1638') });
      loadUsers();
    } catch (error) {
      console.error('更改用户状态失败:', error);
      notification.error({
        message: t('pages.key_1239'),
        description: (error as Error).message || t('pages.key_1312'),
      });
    } finally {
      setOperationLoading(false);
    }
  };

  // 表格列定义
  const columns: ColumnsType<UserItem> = [
    // {
    //   title: "ID",
    //   dataIndex: "id",
    //   key: "id",
    //   width: 80,
    //   ellipsis: true,
    // },
    {
      title: t('pages.key_2320'),
      dataIndex: "username",
      key: "username",
      width: 120,
    },
    {
      title: t('pages.key_2471'),
      dataIndex: "email",
      key: "email",
      width: 180,
    },
    {
      title: t('pages.key_622'),
      dataIndex: "fullName",
      key: "fullName",
      width: 100,
    },
    {
      title: t('pages.key_1723'),
      dataIndex: "tenantName",
      key: "tenantName",
      width: 120,
      render: (name: string) => name || '—',
    },
    {
      title: t('pages.key_2067'),
      key: "roles",
      width: 100,
      render: (_, record) => (
        <Space size={[0, 8]} wrap>
          {record.roles?.map(role => (
            <Tag key={role.id} color="blue">{role.name}</Tag>
          )) || t('pages.key_1185')}
        </Space>
      )
    },
    {
      title: t('pages.key_1883'),
      key: "groups",
      width: 100,
      render: (_, record) => (
        <Space size={[0, 8]} wrap>
          {record.groups?.map(group => (
            <Tag key={group.id} color="green">{group.name}</Tag>
          )) || t('pages.key_1184')}
        </Space>
      )
    },
    {
      title: t('pages.key_1501'),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 180,
      render: (date) => formatDate(date)
    },
    {
      title: t('pages.key_1602'),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        let color;
        let text;

        switch (status) {
          case UserStatus.ACTIVE:
            color = 'green';
            text = t('pages.key_1465');
            break;
          case UserStatus.INACTIVE:
            color = 'orange';
            text = t('pages.key_1310');
            break;
          case UserStatus.BLOCKED:
            color = 'red';
            text = t('pages.key_1714');
            break;
          default:
            color = 'default';
            text = t('pages.key_1311');
        }

        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: t('pages.key_1058'),
      key: "actions",
      width: 180,
      render: (_, record) => {
        // 判断是否是当前登录用户
        const isCurrentUser = record.id === currentUserInfo.id;

        return (
          <Space>
            {isCurrentUser ? (
              <Tooltip title={t('pages.key_19')}>
                <Button
                  type="link"
                  icon={<LockOutlined />}
                  size="small"
                  disabled
                >{t('pages.key_1853')}</Button>
              </Tooltip>
            ) : (
              <Button
                type="link"
                icon={<EditOutlined />}
                size="small"
                onClick={() => showEditModal(record)}
                disabled={operationLoading}
              >{t('pages.key_1853')}</Button>
            )}

            {isCurrentUser ? (
              <Tooltip title={t('pages.key_17')}>
                <Button
                  type="link"
                  icon={<LockOutlined />}
                  size="small"
                  disabled
                >
                  {record.status === UserStatus.ACTIVE ? t('pages.key_1714') : t('pages.key_496')}
                </Button>
              </Tooltip>
            ) : (
              <Button
                type="link"
                size="small"
                onClick={() => handleStatusChange(record)}
                disabled={operationLoading}
              >
                {record.status === UserStatus.ACTIVE ? t('pages.key_1714') : t('pages.key_496')}
              </Button>
            )}

            {isCurrentUser ? (
              <Tooltip title={t('pages.key_16')}>
                <Button
                  type="link"
                  danger
                  icon={<LockOutlined />}
                  size="small"
                  disabled
                >{t('pages.key_287')}</Button>
              </Tooltip>
            ) : (
              <Popconfirm
                title="确定删除此用户吗？"
                description="删除后无法恢复，请谨慎操作！"
                onConfirm={() => handleDeleteUser(record.id)}
                okText="是"
                cancelText="否"
                disabled={operationLoading}
              >
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                  loading={deleteLoadingId === record.id}
                  disabled={operationLoading || deleteLoadingId === record.id}
                >{t('pages.key_287')}</Button>
              </Popconfirm>
            )}
          </Space>
        );
      }
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{t('pages.key_2333')}</h1>
        <Space>
          <Button
            onClick={() => loadUsers()}
            icon={<ReloadOutlined />}
            disabled={loading}
          >{t('pages.key_310')}</Button>
          <Button type="primary" onClick={showCreateModal} icon={<PlusOutlined />}>{t('pages.key_1136')}</Button>
        </Space>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <div className="mb-4">
          <Space>
            <Input
              placeholder={t('pages.key_1049')}
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={() => handleSearch(searchText)}
              style={{ width: 200 }}
            />
            <Button type="primary" onClick={() => handleSearch(searchText)}>{t('pages.key_1044')}</Button>
            <Button onClick={() => {
              setSearchText('');
              handleSearch('');
            }}>{t('pages.key_2483')}</Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </div>

      <UserModal
        visible={modalVisible}
        user={currentUser}
        onCancel={() => setModalVisible(false)}
        onSubmit={handleUserSubmit}
        confirmLoading={operationLoading}
      />
    </div>
  );
}
