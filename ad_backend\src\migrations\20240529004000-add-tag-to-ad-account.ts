import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTagToAdAccount20240529004000 implements MigrationInterface {
    name = 'AddTagToAdAccount20240529004000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "tag" character varying(128);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "tag";`);
    }
} 