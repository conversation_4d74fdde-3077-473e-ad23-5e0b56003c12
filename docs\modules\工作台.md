# 组件路径 	/pages/dashboard/workbench/index

## 前端

- **实际路径**：slash-admin/src/pages/dashboard/workbench/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，工作台汇总）
  - ad-performance-metrics.tsx（广告性能指标卡片）
  - campaign-overview.tsx（广告活动概览）
  - auto-optimization.tsx（自动优化）
  - creative-analysis.tsx（创意分析）
  - budget-utilization.tsx（预算使用）
  - ad-groups-overview.tsx（广告组概览）
  - audience-insights.tsx（受众洞察）
  - ad-platform-distribution.tsx（广告平台分布）
- **主要依赖API定义**：详见各子组件实现

### API 调用列表与详细说明

| API名称         | 方法     | 路径/GraphQL名           | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|--------------------------|--------------|----------|-----------|
| 获取广告性能指标 | query    | /api/workbench/ad-metrics | 时间范围、广告ID等 | 展示量、点击量、转化量、支出等 | 广告性能卡片 |
| 获取广告活动概览 | query    | /api/workbench/campaign-overview | 时间范围、活动ID等 | 活动数、预算、消耗等 | 活动概览图表 |
| 获取自动优化建议 | query    | /api/workbench/auto-optimization | 类型、广告ID等 | 优化进度、建议、历史等 | 自动优化卡片 |
| 获取创意分析     | query    | /api/workbench/creative-analysis | 时间范围、创意ID等 | 创意点击率、转化率等 | 创意分析图表 |
| 获取预算使用     | query    | /api/workbench/budget-utilization | 时间范围、广告ID等 | 预算使用率、剩余预算等 | 预算使用卡片 |
| 获取广告组概览   | query    | /api/workbench/ad-groups-overview | 时间范围、组ID等 | 广告组数、消耗等 | 广告组概览 |
| 获取受众洞察     | query    | /api/workbench/audience-insights | 时间范围、受众ID等 | 受众分布、兴趣等 | 受众洞察卡片 |
| 获取平台分布     | query    | /api/workbench/ad-platform-distribution | 时间范围、平台ID等 | 平台分布、消耗等 | 平台分布图表 |

#### 主要参数结构说明

- **自动优化数据结构**（auto-optimization.tsx）  
  ```ts
  type OptimizationType = "budget" | "bidding" | "targeting";
  interface OptimizationDataBase {
    status: string;
    progress: number;
    lastOptimized: string;
    recommendations: number;
  }
  interface BudgetData extends OptimizationDataBase {
    potentialSavings: string;
  }
  interface BiddingData extends OptimizationDataBase {
    potentialImprovement: string;
  }
  interface TargetingData extends OptimizationDataBase {
    potentialReach: string;
  }
  interface OptimizationData {
    budget: BudgetData;
    bidding: BiddingData;
    targeting: TargetingData;
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | impressions | number    | 展示量                     |
  | clicks      | number    | 点击量                     |
  | conversions | number    | 转化量                     |
  | cost        | number    | 支出                       |
  | ...         | ...       | 其他分析指标               |

### 前端表单/组件说明

- 支持多维度数据卡片、趋势图、分布图、对比图等可视化
- 支持自动优化（预算、出价、定向），可切换自动/手动
- 支持优化建议、优化历史、进度展示
- 支持按时间、广告、活动、平台、受众等多条件筛选
- 组件化拆分，便于扩展

---

## 后端

- **主要接口**：RESTful或GraphQL接口，详见上表
- **数据库表结构**（以workbench_metrics为例，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| adId        | uuid        | 广告ID                     | 索引         |
| campaignId  | uuid        | 活动ID                     | 索引         |
| date        | date        | 统计日期                   | 索引         |
| impressions | int         | 展示量                     |              |
| clicks      | int         | 点击量                     |              |
| conversions | int         | 转化量                     |              |
| cost        | numeric     | 支出                       |              |
| ...         | ...         | 其他分析指标               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query /api/workbench/ad-metrics?startDate=...&endDate=...
    后端->>数据库: 查询 workbench_metrics 表
    数据库-->>后端: 返回工作台数据
    后端-->>前端: 返回工作台数据
```

---

## 变更历史/注意事项

- 工作台数据需定时汇总，建议用ETL或定时任务
- 支持多维度、多粒度分析，表结构建议宽表设计
- 大数据量建议分区表、索引优化
- 自动优化建议需结合业务规则和AI算法
- 复杂业务字段建议在表结构中用jsonb扩展