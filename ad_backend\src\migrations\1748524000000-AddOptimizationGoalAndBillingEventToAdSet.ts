import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOptimizationGoalAndBillingEventToAdSet1748524000000 implements MigrationInterface {
    name = 'AddOptimizationGoalAndBillingEventToAdSet1748524000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" ADD COLUMN "optimization_goal" varchar(64) NOT NULL DEFAULT 'CONVERSIONS';`);
        await queryRunner.query(`ALTER TABLE "ad_set" ADD COLUMN "billing_event" varchar(64) NOT NULL DEFAULT 'IMPRESSIONS';`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN "billing_event";
        `);
        await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN "optimization_goal";
        `);
    }
} 