import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { RouteSeedService } from '../modules/route/route.seed';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);
    const routeSeedService = app.get(RouteSeedService);

    try {
        await routeSeedService.seed();
        console.log('路由数据已成功恢复');
    } catch (error) {
        console.error('恢复路由数据时发生错误:', error);
    } finally {
        await app.close();
    }
}

bootstrap(); 