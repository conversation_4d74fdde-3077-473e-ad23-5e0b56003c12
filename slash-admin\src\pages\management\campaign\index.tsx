import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, message, Tag, Form, Input, Select, Tabs, Divider, Popover, Radio, TimePicker } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, InfoCircleOutlined, MoreOutlined, MinusCircleOutlined, SyncOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useLazyQuery } from '@apollo/client';
import { GET_AUDIENCE_LIST } from '@/api/audience.graphql';
import { GET_MATERIAL_LIST } from '@/api/material.graphql';
import { GET_AD_ACCOUNT_LIST } from '@/api/adAccount.graphql';
import { ROUTES_QUERY } from '@/api/route.graphql';
import { GET_AD_LIST, CREATE_AD, UPDATE_AD, DELETE_AD, REFRESH_AD } from '@/api/ad.graphql';
import { GET_AD_SET_LIST } from '@/api/adSet.graphql';
import { gql } from '@apollo/client';
import { useQuery as useApolloQuery, useMutation as useApolloMutation } from '@apollo/client';
import dayjs from 'dayjs';
import { REFRESH_CAMPAIGN } from '@/api/campaign.graphql';
// TODO: 替换为实际 GraphQL 查询和 mutation
// import { GET_CAMPAIGN_LIST, CREATE_CAMPAIGN, UPDATE_CAMPAIGN_STATUS, DELETE_CAMPAIGN } from '@/api/campaign.graphql';

// 示例类型，后续可根据实际接口调整
interface CampaignItem {
    id: string;
    name: string;
    autoOptimize: boolean;
    adCount: number;
    status: 'pages.key_667' | 'pages.key_988' | 'pages.key_1222';
    arrears: number;
    rejected: number;
    cost: number;
    register: number;
    download: number;
    paid: number;
    commission: number;
    remark?: string;
}

function getMockData(t: any): CampaignItem[] {
    return [
        {
            id: '1',
            name: t('pages.key_2623'),
            autoOptimize: true,
            adCount: 12,
            status: 'pages.key_988',
            arrears: 0,
            rejected: 1,
            cost: 1200,
            register: 30,
            download: 20,
            paid: 5,
            commission: 300,
            remark: 'pages.key_2614',
        },
        {
            id: '2',
            name: t('pages.key_2624'),
            autoOptimize: false,
            adCount: 8,
            status: 'pages.key_1222',
            arrears: 200,
            rejected: 0,
            cost: 800,
            register: 10,
            download: 5,
            paid: 2,
            commission: 100,
            remark: '',
        },
    ];
}

// 广告创意 GraphQL 查询
const GET_AD_CREATIVES = gql`
  query GetAdCreatives($paging: CursorPaging, $filter: AdCreativeFilter) {
    adCreatives(paging: $paging, filter: $filter) {
      edges {
        node {
          id
          name
          creativeId
        }
      }
    }
  }
`;

// 自动规则相关常量
const METRIC_OPTIONS = [
    { label: 'pages.key_363', value: 'cpa', unit: '$' },
    { label: 'pages.key_1519', value: 'spend', unit: '$' },
    { label: 'pages.key_722', value: 'impressions', unit: '' },
    { label: 'pages.key_1581', value: 'clicks', unit: '' },
];
const OPERATOR_OPTIONS = [
    { label: 'pages.key_595', value: 'GREATER_THAN' },
    { label: 'pages.key_705', value: 'LESS_THAN' },
    { label: 'pages.key_1754', value: 'EQUAL' },
];
const ACTION_OPTIONS = [
    { label: 'pages.key_1224', value: 'PAUSE' },
    { label: 'pages.key_497', value: 'TURN_ON' },
    { label: 'pages.key_405', value: 'SEND_NOTIFICATION' },
];
const OBJECT_OPTIONS = [
    { label: 'pages.key_774', value: 'ad' },
    { label: 'pages.key_811', value: 'adset' },
    { label: 'pages.key_806', value: 'campaign' },
];
const SCHEDULE_OPTIONS = [
    { label: 'pages.key_2513', value: 'long' },
    { label: 'pages.key_1467', value: 'daily' },
    { label: 'pages.key_1915', value: 'custom' },
];

// 自动规则类型
interface RuleItem {
    id: string;
    name: string;
    status: 'ENABLED' | 'DISABLED';
    conditions: Array<{ field: string; operator: string; value: string; unit?: string }>;
    scheduleType: string;
    timeRange: string;
    action: string;
    object: string;
}

const initialRules: RuleItem[] = [
    {
        id: '1',
        name: 'pages.key_1520',
        status: 'ENABLED',
        conditions: [
            { field: 'spend', operator: 'GREATER_THAN', value: '100' },
        ],
        scheduleType: 'long',
        timeRange: '37',
        action: 'PAUSE',
        object: 'ad',
    },
];

// 类型定义
interface Condition {
    field: string;
    operator: string;
    value: string;
    unit?: string;
}
interface RuleModalProps {
    open: boolean;
    onOk: (rule: any) => void;
    onCancel: () => void;
    initialValues?: any;
}

function getCampaignFields(t: any) {
    return [
        { label: t('pages.key_2625'), value: 'clicks' },
        { label: t('pages.key_2626'), value: 'impressions' },
        { label: t('pages.key_2627'), value: 'reach' },
        { label: t('pages.key_2628'), value: 'frequency' },
        { label: t('pages.key_2629'), value: 'cpm' },
        { label: t('pages.key_2630'), value: 'ctr' }
    ];
}
function getAdsetFields(t: any) {
    return [
        ...getCampaignFields(t),
        { label: t('pages.key_2631'), value: 'spend' }
    ];
}
function getAdFields(t: any) {
    return getAdsetFields(t);
}

function RuleModal({ open, onOk, onCancel, initialValues }: RuleModalProps) {
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const [conditions, setConditions] = useState<Condition[]>(initialValues?.conditions || [{ field: '', operator: '', value: '', unit: '$' }]);
    const [scheduleType, setScheduleType] = useState<string>(initialValues?.scheduleType || 'long');

    // 条件增删改
    const addCondition = () => setConditions([...conditions, { field: '', operator: '', value: '', unit: '$' }]);
    const removeCondition = (idx: number) => setConditions(conditions.filter((_, i) => i !== idx));
    const updateCondition = (idx: number, key: keyof Condition, value: string) => {
        const newConds = conditions.map((c, i) => i === idx ? { ...c, [key]: value } : c);
        setConditions(newConds);
        form.setFieldsValue({ conditions: newConds });
    };

    useEffect(() => {
        if (open) {
            form.resetFields();
            setConditions(initialValues?.conditions || [{ field: '', operator: '', value: '', unit: '$' }]);
            setScheduleType(initialValues?.scheduleType || 'long');
            form.setFieldsValue({
                ...initialValues,
                conditions: initialValues?.conditions || [{ field: '', operator: '', value: '', unit: '$' }],
                scheduleType: initialValues?.scheduleType || 'long',
            });
        }
    }, [open, initialValues]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            // 组装参数，兼容后端和 Facebook API
            const params = {
                ...values,
                conditions,
                scheduleType,
                custom_schedule: values.custom_schedule,
                timeRange: values.timeRange,
                status: values.status || 'ENABLED',
            };
            onOk(params);
        } catch { }
    };

    return (
        <Modal
            open={open}
            title={t('pages.key_259')}
            onOk={handleOk}
            onCancel={onCancel}
            width={600}
            okText={t('pages.key_236')}
            cancelText={t('pages.key_407')}
            destroyOnHidden
        >
            <div style={{ marginBottom: 16, color: '#666' }}>
                {t('pages.key_2599')}
                <a href="https://www.facebook.com/business/help/1765231090278228" target="_blank" rel="noopener noreferrer" style={{ marginLeft: 8 }}>{t('pages.key_2137')}</a>
            </div>
            <Form form={form} layout="vertical" initialValues={initialValues}>
                <Form.Item name="name" label={t('pages.key_2049')} rules={[{ required: true, message: t('pages.key_2232') }]}>
                    <Input placeholder={t('pages.key_2049')} />
                </Form.Item>
                <Divider />
                <div style={{ display: 'flex', gap: 8 }}>
                    <Form.Item
                        name="object"
                        label={t('pages.key_2052')}
                        rules={[{ required: true, message: t('pages.key_2283') }]}
                    >
                        <Select
                            placeholder={t('pages.key_2261')}
                            onChange={v => setScheduleType(v)}
                        >
                            <Select.Option value="ad">{t('pages.key_774')}</Select.Option>
                            <Select.Option value="adset">{t('pages.key_811')}</Select.Option>
                            <Select.Option value="campaign">{t('pages.key_806')}</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="action"
                        label={t('pages.key_349')}
                        rules={[{ required: true, message: t('pages.key_2250') }]}
                    >
                        <Select placeholder={t('pages.key_2250')} style={{ width: 200 }}>
                            <Select.Option value="PAUSE">{t('pages.key_2634')}</Select.Option>
                            <Select.Option value="UNPAUSE">{t('pages.key_2635')}</Select.Option>
                            <Select.Option value="NOTIFICATION">{t('pages.key_2636')}</Select.Option>
                            <Select.Option value="CHANGE_BUDGET">{t('pages.key_2637')}</Select.Option>
                            <Select.Option value="CHANGE_BID">{t('pages.key_2638')}</Select.Option>
                        </Select >
                    </Form.Item >
                </div >
                <div style={{ color: '#888', marginBottom: 8 }}>
                    <InfoCircleOutlined style={{ marginRight: 4 }} />
                    {t('pages.key_2600')}
                </div>
                <Divider />
                <div style={{ fontWeight: 500, marginBottom: 8 }}>{t('pages.key_1329')}</div>
                {conditions.map((cond, idx) => (
                    <Space key={idx} style={{ marginBottom: 8 }}>
                        <Select
                            value={cond.field}
                            options={METRIC_OPTIONS}
                            placeholder={t('pages.key_997')}
                            style={{ width: 140 }}
                            onChange={v => updateCondition(idx, 'field', v)}
                        />
                        <Select
                            value={cond.operator}
                            options={OPERATOR_OPTIONS}
                            placeholder={t('pages.key_2397')}
                            style={{ width: 80 }}
                            onChange={v => updateCondition(idx, 'operator', v)}
                        />
                        <Input
                            value={cond.value}
                            placeholder={t('pages.key_1085')}
                            style={{ width: 80 }}
                            onChange={e => updateCondition(idx, 'value', e.target.value)}
                            suffix={cond.unit}
                        />
                        <Popover content={t('pages.key_2632')} trigger="click">
                            <Button icon={<MoreOutlined />} />
                        </Popover>
                        {conditions.length > 1 && (
                            <Button icon={<DeleteOutlined />} onClick={() => removeCondition(idx)} />
                        )}
                        {idx === conditions.length - 1 && (
                            <Button icon={<PlusOutlined />} onClick={addCondition} />
                        )}
                    </Space>
                ))}
                <Divider />
                <Form.Item
                    label={<span>{t('pages.key_1196')} <span style={{ color: '#888', marginLeft: 8 }}>（最大范围）</span></span>}
                    name="timeRange"
                    rules={[{ required: true, message: t('pages.key_2205') }]}
                >
                    <Input suffix={t('pages.key_33')} placeholder={t('pages.key_621')} style={{ width: 160 }} />
                </Form.Item>
                <Divider />
                <div style={{ fontWeight: 500, marginBottom: 8 }}>{t('pages.key_1015')}</div>
                <Form.Item name="scheduleType" initialValue={scheduleType} label={t('pages.key_1016')}>
                    <Radio.Group onChange={e => setScheduleType(e.target.value)} style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                        <Radio value="daily">{t('pages.key_1467')} <span style={{ color: '#888', marginLeft: 8 }}>{t('pages.key_2639')}</span> </Radio>
                        <Radio value="hourly">{t('pages.key_1469')} <span style={{ color: '#888', marginLeft: 8 }}>{t('pages.key_2640')}</span> </Radio>
                        <Radio value="semi_hourly">{t('pages.key_1466')} <span style={{ color: '#888', marginLeft: 8 }}>{t('pages.key_2641')}</span> </Radio>
                        <Radio value="custom">{t('pages.key_1915')} <span style={{ color: '#888', marginLeft: 8 }}>（每天可选多个时间点，所有时间为美国西部太平洋时间）</span> </Radio>
                    </Radio.Group>
                </Form.Item >
                {scheduleType === 'custom' && (
                    <Form.List name="custom_schedule" rules={[
                        { validator: (_, value) => (value && value.length > 0 ? Promise.resolve() : Promise.reject(new Error(t('pages.key_2165')))) }
                    ]}>
                        {(fields, { add, remove }) => (
                            <>
                                <Form.Item label={t('pages.key_2633')}>
                                    <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />}>{t('pages.key_1537')}</Button>
                                </Form.Item>
                                {fields.map(({ key, name, ...restField }) => (
                                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                                        <Form.Item
                                            {...restField}
                                            name={name}
                                            rules={[{ required: true, message: t('pages.key_2271') }]}
                                        >
                                            <TimePicker format="HH:mm" minuteStep={5} />
                                        </Form.Item>
                                        <MinusCircleOutlined onClick={() => remove(name)} />
                                    </Space>
                                ))}
                            </>
                        )}
                    </Form.List>
                )}
            </Form >
        </Modal >
    );
}

const GET_FACEBOOK_CREATIVES_BY_ADSET = gql`
  query FacebookCreativesByAdSet($adSetId: ID!) {
    facebookCreativesByAdSet(adSetId: $adSetId) {
      id
      name
      creativeId
      status
      raw
    }
  }
`;

const GET_RULES = gql`
  query {
    rules {
      id
      name
      status
      conditions {
        field
        operator
        value
        unit
      }
      scheduleType
      timeRange
      action
      object
    }
  }
`;

const CREATE_RULE = gql`
  mutation CreateRule(
    $name: String!
    $action: String!
    $object: String!
    $scheduleType: String
    $custom_schedule: [String!]
    $timeRange: String
    $status: String
    $conditions: [RuleConditionInput!]!
  ) {
    createRule(
      name: $name
      action: $action
      object: $object
      scheduleType: $scheduleType
      custom_schedule: $custom_schedule
      timeRange: $timeRange
      status: $status
      conditions: $conditions
    ) {
      id
      name
      status
      custom_schedule
      conditions {
        field
        operator
        value
        unit
      }
      scheduleType
      timeRange
      action
      object
    }
  }
`;

const UPDATE_RULE = gql`
  mutation UpdateRule(
    $id: String!
    $name: String!
    $action: String!
    $object: String!
    $scheduleType: String
    $custom_schedule: [String!]
    $timeRange: String
    $status: String
    $conditions: [RuleConditionInput!]!
  ) {
    updateRule(
      id: $id
      name: $name
      action: $action
      object: $object
      scheduleType: $scheduleType
      custom_schedule: $custom_schedule
      timeRange: $timeRange
      status: $status
      conditions: $conditions
    ) {
      id
      name
      status
      custom_schedule
      conditions {
        field
        operator
        value
        unit
      }
      scheduleType
      timeRange
      action
      object
    }
  }
`;

const DELETE_RULE = gql`
  mutation DeleteRule($id: String!) {
    deleteRule(id: $id)
  }
`;

export default function CampaignManagement() {
    const { t } = useTranslation();
    const [data, setData] = useState<CampaignItem[]>(getMockData(t));
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editing, setEditing] = useState<any>(null);
    const [form] = Form.useForm();
    const [confirmLoading, setConfirmLoading] = useState(false);

    // 规则管理相关
    const { data: ruleData, refetch: refetchRules } = useApolloQuery(GET_RULES);
    const ruleList: RuleItem[] = ruleData?.rules || [];
    const [ruleModalVisible, setRuleModalVisible] = useState(false);
    const [editingRule, setEditingRule] = useState<RuleItem | null>(null);
    const [ruleForm] = Form.useForm();

    // 条件动态表单
    const [conditionFields, setConditionFields] = useState([{ key: Date.now() }]);

    // TODO: 替换为实际 GraphQL 查询
    // const { data, loading, refetch } = useQuery(GET_CAMPAIGN_LIST);
    // const [createCampaign] = useMutation(CREATE_CAMPAIGN);
    // const [updateCampaignStatus] = useMutation(UPDATE_CAMPAIGN_STATUS);
    // const [deleteCampaign] = useMutation(DELETE_CAMPAIGN);

    // 下拉数据
    const { data: audienceData } = useQuery(GET_AUDIENCE_LIST, { variables: { paging: { first: 50 } } });
    const { data: materialData } = useQuery(GET_MATERIAL_LIST, { variables: { paging: { first: 50 } } });
    const materials = materialData?.materials?.edges?.map((edge: any) => edge.node) || [];
    const { data: adAccountData } = useQuery(GET_AD_ACCOUNT_LIST, { variables: { filter: {} } });
    const { data: routeData } = useQuery(ROUTES_QUERY);

    // 查询广告列表
    const { data: adData, loading: adLoading, refetch: adRefetch } = useQuery(GET_AD_LIST, {
        variables: {
            limit: 10,
            offset: 0,
        },
    });
    const ads = adData?.ads || [];

    // 新建/编辑/删除
    const [createAd] = useMutation(CREATE_AD);
    const [updateAd] = useMutation(UPDATE_AD);
    const [deleteAd] = useMutation(DELETE_AD);

    // 广告组下拉数据
    const { data: adSetData } = useQuery(GET_AD_SET_LIST, { variables: { filter: {} } });
    const adSetOptions = (adSetData?.adSetsCustom || []).map((adset: any) => ({ label: adset.name, value: adset.id }));

    // 广告创意下拉数据
    const { data: creativeData } = useQuery(GET_AD_CREATIVES, { variables: { paging: { first: 50 } } });
    const audiences = (audienceData?.audienceDTOS?.edges || []).map((edge: any) => edge.node);
    const adAccounts = adAccountData?.adAccounts || [];
    const domains = routeData?.getRoutes || [];

    // Facebook 状态映射
    const fbStatusMap: Record<string, string> = {
        ACTIVE: 'pages.key_988',
        PAUSED: 'pages.key_745',
        DELETED: 'pages.key_733',
        ARCHIVED: 'pages.key_742',
        IN_PROCESS: 'pages.key_568',
        WITH_ISSUES: 'pages.key_1299',
        PENDING_REVIEW: 'pages.key_893',
        DISAPPROVED: 'pages.key_2040',
        PREAPPROVED: 'pages.key_2554',
        PENDING_BILLING_INFO: 'pages.key_897',
        CAMPAIGN_PAUSED: 'pages.key_1771',
        ADSET_PAUSED: 'pages.key_815',
        ADSET_PENDING_REVIEW: 'pages.key_1807',
        CAMPAIGN_PENDING_REVIEW: 'pages.key_1770',
        ADSET_WITH_ISSUES: 'pages.key_1808',
        CAMPAIGN_WITH_ISSUES: 'pages.key_1772',
        DISABLED: 'pages.key_747',
        CLOSED: 'pages.key_732',
    };

    // 新增：广告组-创意联动
    const [selectedAdSetId, setSelectedAdSetId] = useState<string | undefined>();
    const [creativeOptions, setCreativeOptions] = useState<any[]>([]);
    const [fetchCreatives, { data: creativeDataByAdSet, loading: creativeLoading }] = useLazyQuery(GET_FACEBOOK_CREATIVES_BY_ADSET);

    // 监听广告组选择变化，自动请求创意
    const handleAdSetChange = (adSetId: string) => {
        setSelectedAdSetId(adSetId);
        form.setFieldsValue({ creative: undefined }); // 清空素材选择
        if (adSetId) {
            fetchCreatives({ variables: { adSetId } });
        } else {
            setCreativeOptions([]);
        }
    };
    // 监听GraphQL返回，更新素材下拉
    React.useEffect(() => {
        if (creativeDataByAdSet?.facebookCreativesByAdSet) {
            setCreativeOptions(
                creativeDataByAdSet.facebookCreativesByAdSet.map((item: any) => ({
                    label: item.name,
                    value: item.creativeId,
                }))
            );
        }
    }, [creativeDataByAdSet]);

    const openModal = (record?: any) => {
        setEditing(record || null);
        setModalVisible(true);
        form.resetFields();
        if (record) {
            // 先触发广告组change，拉取素材
            handleAdSetChange(record.adset_id);
            // 再铺平 creative 字段
            form.setFieldsValue({
                ...record,
                creative: record.creative?.id
            });
        }
    };

    const handleModalOk = async () => {
        setConfirmLoading(true);
        try {
            const values = await form.validateFields();
            if (editing) {
                await updateAd({ variables: { adId: editing.adId, ...values, adset_id: values.adset_id } });
                message.success(t('pages.key_145'));
            } else {
                await createAd({
                    variables: {
                        adset_id: values.adset_id,
                        name: values.name,
                        status: values.status,
                        creative: values.creative,
                        ruleId: values.ruleId,
                        bid_amount: values.bid_amount,
                        remark: values.remark,
                    }
                });
                message.success(t('pages.key_1149'));
            }
            setModalVisible(false);
            setEditing(null);
            adRefetch();
        } catch { }
        setConfirmLoading(false);
    };

    const [deleteLoadingId, setDeleteLoadingId] = useState<string | null>(null);

    const handleDelete = async (record: any) => {
        setDeleteLoadingId(record.id);
        try {
            await deleteAd({ variables: { adId: record.adId } });
            message.success(t('pages.key_294'));
            adRefetch();
        } finally {
            setDeleteLoadingId(null);
        }
    };

    const [refreshAd] = useMutation(REFRESH_AD);
    const [refreshingId, setRefreshingId] = useState<string | null>(null);

    const handleRefresh = async (record: any) => {
        const adId = record.adId || record.id;
        setRefreshingId(record.id);
        try {
            const { data } = await refreshAd({ variables: { adId } });
            if (data && data.refreshAdFromFacebook) {
                await adRefetch();
                message.success(t('pages.key_315'));
            } else {
                message.error(t('pages.key_1315'));
            }
        } catch (e: any) {
            message.error(t('pages.key_2643') + (e.message || ''));
        }
        setRefreshingId(null);
    };

    // 批量刷新全部广告
    const handleRefreshAll = async () => {
        if (!ads || ads.length === 0) {
            message.info(t('pages.key_1226'));
            return;
        }
        let success = 0, fail = 0;
        for (const ad of ads) {
            try {
                const adId = ad.adId || ad.id;
                await refreshAd({ variables: { adId } });
                success++;
            } catch {
                fail++;
            }
        }
        await adRefetch();
        message.success(t('pages.key_2642', { success, fail }));
    };

    const CAMPAIGN_FIELDS = getCampaignFields(t);
    const ADSET_FIELDS = getAdsetFields(t);
    const AD_FIELDS = getAdFields(t);

    const columns = [
        { title: t('pages.key_483'), dataIndex: 'name', key: 'name', width: 120 },
        { title: t('pages.key_811'), dataIndex: 'adset_name', key: 'adset_name', width: 120 },
        { title: t('pages.key_806'), dataIndex: 'campaign_name', key: 'campaign_name', width: 120 },
        { title: t('pages.key_2325'), dataIndex: 'account_name', key: 'account_name', width: 120 },
        { title: t('pages.key_1923'), dataIndex: 'autoOptimize', key: 'autoOptimize', width: 100, render: (v: boolean) => v ? t('是') : t('否') },
        { title: t('pages.key_763'), dataIndex: 'effective_status', key: 'effective_status', width: 120, render: (v: string) => t(fbStatusMap[v] || v) },
        { title: t('pages.key_989'), dataIndex: 'status', key: 'status', width: 100, render: (v: string) => t(fbStatusMap[v] || v) },
        { title: t('pages.key_1454'), dataIndex: 'arrears', key: 'arrears', width: 100 },
        { title: t('pages.key_1519'), dataIndex: 'cost', key: 'cost', width: 100 },
        { title: t('pages.key_1495'), dataIndex: 'register', key: 'register', width: 100 },
        { title: t('pages.key_14'), dataIndex: 'download', key: 'download', width: 100 },
        { title: t('pages.key_90'), dataIndex: 'paid', key: 'paid', width: 100 },
        { title: t('pages.key_112'), dataIndex: 'commission', key: 'commission', width: 100 },
        { title: t('pages.key_588'), dataIndex: 'remark', key: 'remark', width: 120 },
        { title: t('pages.key_250'), dataIndex: 'created_time', key: 'created_time', width: 160 },
        { title: t('pages.key_1254'), dataIndex: 'updated_time', key: 'updated_time', width: 160 },
        {
            title: t('pages.key_1058'),
            key: 'actions',
            width: 180,
            fixed: 'right' as const,
            render: (_: any, record: any) => (
                <Space>
                    <Button size="small" icon={<SyncOutlined />} onClick={() => handleRefresh(record)} loading={refreshingId === record.id}>{t('pages.key_310')}</Button>
                    <Button size="small" icon={<EditOutlined />} onClick={() => openModal(record)}>{t('pages.key_1853')}</Button>
                    <Button size="small" icon={<DeleteOutlined />} danger onClick={() => handleDelete(record)} loading={deleteLoadingId === record.id} disabled={deleteLoadingId === record.id}>{t('pages.key_287')}</Button>
                </Space>
            ),
        },
    ];

    const openRuleModal = (record?: any) => {
        setEditingRule(record || null);
        setRuleModalVisible(true);
    };
    const [createRule] = useApolloMutation(CREATE_RULE);
    const [updateRule] = useApolloMutation(UPDATE_RULE);
    const [deleteRule] = useApolloMutation(DELETE_RULE);
    const handleRuleModalOk = async (rule: any) => {
        if (!rule.name) {
            message.error(t('pages.key_2050'));
            return;
        }
        if (!rule.conditions || !Array.isArray(rule.conditions) || rule.conditions.length === 0) {
            message.error(t('pages.key_2166'));
            return;
        }
        if (editingRule && editingRule.id) {
            await updateRule({
                variables: {
                    id: editingRule.id,
                    name: rule.name,
                    action: rule.action,
                    object: rule.object,
                    scheduleType: rule.scheduleType,
                    custom_schedule: rule.custom_schedule,
                    timeRange: rule.timeRange,
                    status: rule.status,
                    conditions: rule.conditions,
                }
            });
            message.success(t('pages.key_2048'));
        } else {
            await createRule({
                variables: {
                    name: rule.name,
                    action: rule.action,
                    object: rule.object,
                    scheduleType: rule.scheduleType,
                    custom_schedule: rule.custom_schedule,
                    timeRange: rule.timeRange,
                    status: rule.status,
                    conditions: rule.conditions,
                }
            });
            message.success(t('pages.key_2057'));
        }
        await refetchRules();
        setRuleModalVisible(false);
        setEditingRule(null);
    };
    const handleRuleDelete = async (record: any) => {
        await deleteRule({ variables: { id: record.id } });
        await refetchRules();
        message.success(t('pages.key_2051'));
    };
    const ruleColumns = [
        { title: t('pages.key_2049'), dataIndex: 'name', key: 'name' },
        { title: t('pages.key_1329'), dataIndex: 'conditions', key: 'conditions', render: (conds: Condition[] = []) => conds.map((c: Condition) => `${t(METRIC_OPTIONS.find(m => m.value === c.field)?.label || c.field)} ${t(OPERATOR_OPTIONS.find(o => o.value === c.operator)?.label || c.operator)} ${c.value}${c.unit || ''}`).join(' 且 ') },
        { title: t('pages.key_1058'), dataIndex: 'action', key: 'action', render: (v: string) => t(ACTION_OPTIONS.find(a => a.value === v)?.label || v) },
        { title: t('pages.key_1015'), dataIndex: 'scheduleType', key: 'scheduleType', render: (v: string) => t(SCHEDULE_OPTIONS.find(s => s.value === v)?.label || v) },
        { title: t('pages.key_1602'), dataIndex: 'status', key: 'status', render: (v: string) => v === 'ENABLED' ? <Tag color='green'>{t('pages.key_496')}</Tag> : <Tag>{t('pages.key_1714')}</Tag> },
        {
            title: t('pages.key_1058'),
            key: 'actions',
            render: (_: any, record: any) => (
                <Space>
                    <Button size="small" icon={<EditOutlined />} onClick={() => openRuleModal(record)}>{t('pages.key_1853')}</Button>
                    <Button size="small" icon={<DeleteOutlined />} danger onClick={() => handleRuleDelete(record)}>{t('pages.key_287')}</Button>
                </Space>
            ),
        },
    ];

    return (
        <div>
            <Tabs
                defaultActiveKey="campaign"
                style={{ marginBottom: 16 }}
                items={[
                    {
                        key: 'campaign',
                        label: t('pages.key_1021'),
                        children: (
                            <div>
                                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
                                    <Button icon={<SyncOutlined />} style={{ marginRight: 8 }} onClick={handleRefreshAll}>{t('pages.key_182')}</Button>
                                    <Button type="primary" icon={<PlusOutlined />} onClick={() => openModal()}>{t('pages.key_1146')}</Button>
                                </div>
                                <Table
                                    rowKey="id"
                                    columns={columns}
                                    dataSource={ads}
                                    loading={adLoading}
                                    pagination={{ pageSize: 10 }}
                                    scroll={{ x: 'max-content' }}
                                />
                            </div>
                        ),
                    },
                    {
                        key: 'rule',
                        label: t('pages.key_2058'),
                        children: (
                            <div>
                                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
                                    <Button type="primary" icon={<PlusOutlined />} onClick={() => openRuleModal()}>{t('pages.key_1155')}</Button>
                                </div>
                                <Table
                                    rowKey="id"
                                    columns={ruleColumns}
                                    dataSource={ruleList}
                                    pagination={false}
                                />
                                <RuleModal
                                    open={ruleModalVisible}
                                    onOk={handleRuleModalOk}
                                    onCancel={() => setRuleModalVisible(false)}
                                    initialValues={editingRule}
                                />
                            </div>
                        ),
                    },
                ]}
            />
            <Modal
                open={modalVisible}
                title={editing ? t('pages.key_1855') : t('pages.key_1146')}
                onOk={handleModalOk}
                onCancel={() => setModalVisible(false)}
                okText={t('pages.key_1693')}
                cancelText={t('pages.key_407')}
                destroyOnHidden
                confirmLoading={confirmLoading}
            >
                <Form form={form} layout="vertical" preserve={false}>
                    <Form.Item name="name" label={t('pages.key_483')} rules={[{ required: true, message: t('pages.key_2194') }]}>
                        <Input placeholder={t('pages.key_2181')} />
                    </Form.Item>
                    <Form.Item name="adset_id" label={t('pages.key_811')} rules={[{ required: true, message: t('pages.key_2260') }]}>
                        <Select placeholder={t('pages.key_2260')} options={adSetOptions} onChange={handleAdSetChange} />
                    </Form.Item >
                    <Form.Item name="creative" label={t('pages.key_2455')} rules={[{ required: true, message: t('pages.key_2275') }]}>
                        <Select placeholder={t('pages.key_2275')} options={creativeOptions} loading={creativeLoading} disabled={!form.getFieldValue('adset_id')} />
                    </Form.Item >
                    <Form.Item name="ruleId" label={t('pages.key_2046')}>
                        <Select placeholder={t('pages.key_2282')} allowClear options={ruleList.map(rule => ({ label: rule.name, value: rule.id }))} />
                    </Form.Item >
                    <Form.Item name="status" label={t('pages.key_1602')} rules={[{ required: true, message: t('pages.key_2215') }]}>
                        <Select placeholder={t('pages.key_2273')} options={[
                            { label: t('pages.key_2634'), value: 'PAUSED' },
                            { label: t('pages.key_2635'), value: 'UNPAUSED' },

                        ]} />
                    </Form.Item >
                    <Form.Item name="remark" label={t('pages.key_588')}>
                        <Input.TextArea placeholder={t('pages.key_2190')} rows={3} allowClear />
                    </Form.Item >
                </Form >
            </Modal >
        </div >
    );
}