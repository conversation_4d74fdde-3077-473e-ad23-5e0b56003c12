import { gql } from '@apollo/client';

export const ROLES_QUERY = gql`
  query GetRoles($input: RoleSearchInput!) {
    roles(input: $input) {
      items {
        id
        name
        description
        status
        order
        routeIds
        createdAt
        updatedAt
      }
      pageInfo {
        page
        limit
        totalPages
        hasNextPage
        hasPreviousPage
      }
      totalCount
    }
  }
`;

export const ROLE_QUERY = gql`
  query GetRole($id: ID!) {
    role(id: $id) {
      id
      name
      description
      status
      order
      routeIds
      createdAt
      updatedAt
    }
  }
`;

export const CREATE_ROLE = gql`
  mutation CreateRole($input: CreateRoleInput!) {
    createRole(input: $input) {
      id
      name
      description
      status
      order
      routeIds
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_ROLE = gql`
  mutation UpdateRole($input: UpdateRoleInput!) {
    updateRole(input: $input) {
      id
      name
      description
      status
      order
      routeIds
      createdAt
      updatedAt
    }
  }
`;

export const DELETE_ROLE = gql`
  mutation DeleteRole($input: DeleteRoleInput!) {
    deleteRole(input: $input)
  }
`; 