import { useTranslation } from 'react-i18next';
import { Table, Button, Tag, Space, Modal, message, Form, Input, Select, Switch } from "antd";
import { useState } from "react";
import { useQuery, useMutation } from '@apollo/client';
import { GET_AD_ACCOUNT_LIST, CREATE_AD_ACCOUNT, DELETE_AD_ACCOUNT } from '@/api/adAccount.graphql';
import { useTenant } from '@/hooks/useTenant';
import { syncAdAccounts } from '@/api/adAccount';
import type { AdAccountSyncResponse } from '@/types/adAccount';

export default function AccountListTab() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [syncResult, setSyncResult] = useState<AdAccountSyncResponse | null>(null);
  const tenantId = useTenant();
  const { data, loading: queryLoading, refetch } = useQuery(GET_AD_ACCOUNT_LIST, {
    variables: {
      filter: {},
    }
  });
  const [deleteAdAccount, { loading: deleting }] = useMutation(DELETE_AD_ACCOUNT);
  const [deleteModal, setDeleteModal] = useState<{ visible: boolean, record: any | null }>({ visible: false, record: null });

  const fbStatusMap: Record<string, string> = {
    active: t('pages.key_1465'),
    disabled: t('pages.key_695'),
    pending_risk_review: t('pages.key_2573'),
    pending_settlement: t('pages.key_896'),
    in_grace_period: t('pages.key_673'),
    pending_closure: t('pages.key_891'),
    closed: t('pages.key_732'),
    unsettled: t('pages.key_1314'),
    any_active: t('pages.key_1507'),
    any_closed: t('pages.key_186'),
    unknown: t('pages.key_1311')
  };

  const handleSync = async (record: any) => {
    setLoading(true);
    try {
      const accessToken = "TODO";
      const accountId = record.accountId;
      const res = await syncAdAccounts({ tenantId: tenantId || '', accessToken, accountId });
      const syncData = res.data as AdAccountSyncResponse;
      setSyncResult(syncData);
      Modal.info({
        title: t('pages.key_480'),
        width: 600,
        content: (
          <div>
            <div>账户：插入{syncData.accounts?.inserted}，更新{syncData.accounts?.updated}，跳过{syncData.accounts?.skipped}</div>
            <div>系列：插入{syncData.campaigns?.inserted}，更新{syncData.campaigns?.updated}，跳过{syncData.campaigns?.skipped}</div>
            <div>组：插入{syncData.adsets?.inserted}，更新{syncData.adsets?.updated}，跳过{syncData.adsets?.skipped}</div>
            <div>广告：插入{syncData.ads?.inserted}，更新{syncData.ads?.updated}，跳过{syncData.ads?.skipped}</div>
            <div>创意：插入{syncData.adcreatives?.inserted}，更新{syncData.adcreatives?.updated}，跳过{syncData.adcreatives?.skipped}</div>
            <div>像素：插入{syncData.pixels?.inserted}，更新{syncData.pixels?.updated}，跳过{syncData.pixels?.skipped}</div>
            <div>受众：插入{syncData.audiences?.inserted}，更新{syncData.audiences?.updated}，跳过{syncData.audiences?.skipped}</div>
            {syncData.logs && syncData.logs.length > 0 && (
              <div style={{ marginTop: 12 }}>
                <b>错误日志：</b>
                <pre style={{ maxHeight: 200, overflow: 'auto', background: '#f8f8f8' }}>{JSON.stringify(syncData.logs, null, 2)}</pre>
              </div>
            )}
          </div>
        ),
      });
      message.success(t('pages.key_474'));
      refetch();
    } catch (e: any) {
      message.error("同步失败：" + (e?.message || t('pages.key_1312')));
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (record: any) => {
    setDeleteModal({ visible: true, record });
  };

  const handleDeleteOk = async () => {
    const record = deleteModal.record;
    if (!record) return;
    try {
      await deleteAdAccount({ variables: { accountId: record.accountId } });
      message.success(t('pages.key_294'));
      refetch();
    } catch (e: any) {
      message.error(e.message || t('pages.key_293'));
    } finally {
      setDeleteModal({ visible: false, record: null });
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({ visible: false, record: null });
  };

  const columns = [
    { title: "账户ID", dataIndex: "accountId", key: "accountId" },
    { title: t('pages.key_2325'), dataIndex: "account", key: "account" },
    { title: t('pages.key_996'), dataIndex: "holder", key: "holder" },
    { title: t('pages.key_1602'), dataIndex: "status", key: "status", render: (text: string) => text === 'pending' ? <span style={{ color: 'red' }}>{t('pages.key_1309')}</span> : <span style={{ color: 'green' }}>{t('pages.key_744')}</span> },
    { title: "Facebook状态", dataIndex: "fbStatus", key: "fbStatus", render: (v: string) => <span>{fbStatusMap[v] || v}</span> },
    { title: t('pages.key_950'), dataIndex: "group", key: "group" },
    { title: t('pages.key_779'), dataIndex: "adNumber", key: "adNumber" },
    { title: t('pages.key_1373'), dataIndex: "tag", key: "tag" },
    { title: t('pages.key_1556'), dataIndex: "channel", key: "channel" },
    { title: t('pages.key_588'), dataIndex: "remark", key: "remark" },
    { title: t('pages.key_250'), dataIndex: "createdAt", key: "createdAt" },
    { title: t('pages.key_1254'), dataIndex: "updatedAt", key: "updatedAt" },
    {
      title: t('pages.key_1058'),
      key: "actions",
      render: (_: any, record: any) => (
        <Space>
          <Button size="small" loading={loading} onClick={() => handleSync(record)}>{t('pages.key_464')}</Button>
          <Button size="small">{t('pages.key_222')}</Button>
          <Button size="small" danger loading={deleting} onClick={() => handleDelete(record)}>{t('pages.key_287')}</Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Table
        rowKey="accountId"
        columns={columns}
        dataSource={data?.adAccounts || []}
        loading={queryLoading || loading}
        pagination={{ pageSize: 10 }}
      />
      <Modal
        open={deleteModal.visible}
        title="确认删除该广告账户？"
        onOk={handleDeleteOk}
        onCancel={handleDeleteCancel}
        okText={t('pages.key_287')}
        okType="danger"
        cancelText={t('pages.key_407')}
        destroyOnHidden
      >
        <div>账户ID：{deleteModal.record?.accountId}</div>
      </Modal >
    </>
  );
}

export async function handleSync({ tenantId, accessToken, accountId }: { tenantId: string; accessToken: string; accountId: string }) {
  const res = await syncAdAccounts({ tenantId: tenantId || '', accessToken, accountId });
  const data = res.data as AdAccountSyncResponse;
  return {
    accounts: {
      total: data.accounts.total || 0,
      success: data.accounts.success || 0,
      failed: data.accounts.failed || 0,
    },
    campaigns: {
      total: data.campaigns.total || 0,
      success: data.campaigns.success || 0,
      failed: data.campaigns.failed || 0,
    },
    adsets: {
      total: data.adsets.total || 0,
      success: data.adsets.success || 0,
      failed: data.adsets.failed || 0,
    },
    ads: {
      total: data.ads.total || 0,
      success: data.ads.success || 0,
      failed: data.ads.failed || 0,
    },
  };
}
