import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';

@Entity('material')
export class Material {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '物料类型（image/video/text等）' })
  @Column({ length: 32 })
  type: string;

  @ApiProperty({ description: '物料名称' })
  @Column({ length: 128 })
  name: string;

  @ApiProperty({ description: '物料URL', required: false })
  @Column({ length: 512, nullable: true })
  url?: string;

  @ApiProperty({ description: '物料内容', required: false })
  @Column({ type: 'text', nullable: true })
  content?: string;

  @ApiProperty({ description: '状态' })
  @Column({ length: 32, default: 'active' })
  status: string;

  @ApiProperty({ description: '租户' })
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始数据', required: false })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;
}
