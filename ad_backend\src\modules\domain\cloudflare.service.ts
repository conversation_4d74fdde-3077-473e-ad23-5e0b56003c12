import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { axiosProxyPost } from '../../utils/axios-proxy';

@Injectable()
export class CloudflareService {
    private readonly zoneId = process.env.CF_ZONE_ID;
    private readonly apiToken = process.env.CF_API_TOKEN;
    private readonly email = process.env.CF_EMAIL;

    async createCustomHostname(domain: string): Promise<{ id: string }> {
        console.log('[Cloudflare] createCustomHostname 请求:', {
            url: `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/custom_hostnames`,
            domain,
            zoneId: this.zoneId,
            apiToken: this.apiToken ? `${this.apiToken.slice(0, 6)}...${this.apiToken.slice(-4)}` : undefined,
        });
        try {
            const res = await axiosProxyPost(
                `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/custom_hostnames`,
                {
                    hostname: domain,
                    ssl: { method: 'http', type: 'dv', settings: { http2: 'on' } }
                },
                { headers: { 'X-Auth-Key': this.apiToken, 'X-Auth-Email': this.email } }
            );
            console.log('[Cloudflare] createCustomHostname 返回:', res.data);
            if (!res.data.success) throw new Error(res.data.errors?.[0]?.message || 'Cloudflare创建失败');
            return { id: res.data.result.id };
        } catch (error) {
            console.error('[Cloudflare] createCustomHostname 异常:', error?.response?.data || error);
            throw error;
        }
    }

    async getCustomHostnameStatus(domain: string): Promise<any> {
        console.log('[Cloudflare] getCustomHostnameStatus 请求:', {
            url: `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/custom_hostnames?hostname=${domain}`,
            domain,
            zoneId: this.zoneId,
            apiToken: this.apiToken ? `${this.apiToken.slice(0, 6)}...${this.apiToken.slice(-4)}` : undefined,
        });
        try {
            const res = await axios.get(
                `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/custom_hostnames?hostname=${domain}`,
                { headers: { 'X-Auth-Key': this.apiToken, 'X-Auth-Email': this.email } }
            );
            console.log('[Cloudflare] getCustomHostnameStatus 返回:', res.data);
            return res.data.result[0] || {};
        } catch (error) {
            console.error('[Cloudflare] getCustomHostnameStatus 异常:', error?.response?.data || error);
            throw error;
        }
    }

    async deleteCustomHostname(cfId: string): Promise<void> {
        console.log('[Cloudflare] deleteCustomHostname 请求:', {
            url: `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/custom_hostnames/${cfId}`,
            cfId,
            zoneId: this.zoneId,
            apiToken: this.apiToken ? `${this.apiToken.slice(0, 6)}...${this.apiToken.slice(-4)}` : undefined,
        });
        try {
            const res = await axios.delete(
                `https://api.cloudflare.com/client/v4/zones/${this.zoneId}/custom_hostnames/${cfId}`,
                { headers: { 'X-Auth-Key': this.apiToken, 'X-Auth-Email': this.email } }
            );
            console.log('[Cloudflare] deleteCustomHostname 返回:', res.data);
        } catch (error) {
            console.error('[Cloudflare] deleteCustomHostname 异常:', error?.response?.data || error);
            throw error;
        }
    }
} 