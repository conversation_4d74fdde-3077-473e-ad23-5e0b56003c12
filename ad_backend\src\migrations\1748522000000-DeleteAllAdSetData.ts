import { MigrationInterface, QueryRunner } from "typeorm";

export class DeleteAllAdSetData1748522000000 implements MigrationInterface {
    name = 'DeleteAllAdSetData1748522000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DELETE FROM ad_set;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 不做回滚，历史数据无法还原
    }
} 