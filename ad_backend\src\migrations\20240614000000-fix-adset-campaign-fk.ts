import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAdSetCampaignFk20240614000000 implements MigrationInterface {
    name = 'FixAdSetCampaignFk20240614000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 删除原有外键（如果存在）
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "FK_dcb3ef81acf5eae82b9cdb2358f"`);
        // 2. 添加新外键，指向 campaign 表
        await queryRunner.query(`ALTER TABLE "ad_set" ADD CONSTRAINT "FK_adset_campaign_id" FOREIGN KEY ("adCampaignId") REFERENCES "campaign"("id") ON DELETE RESTRICT`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回滚：删除新外键，恢复原外键（如有需要）
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "FK_adset_campaign_id"`);
        await queryRunner.query(`ALTER TABLE "ad_set" ADD CONSTRAINT "FK_dcb3ef81acf5eae82b9cdb2358f" FOREIGN KEY ("adCampaignId") REFERENCES "ad_campaign"("id") ON DELETE RESTRICT`);
    }
} 