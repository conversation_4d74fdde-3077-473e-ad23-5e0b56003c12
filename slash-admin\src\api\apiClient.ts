import axios, { type AxiosRequestConfig, type AxiosError, type AxiosResponse } from "axios";

import { t } from "@/locales/i18n";

import { toast } from "sonner";
import type { Result } from "#/api";
import { ResultEnum } from "#/enum";

const userStore = window.localStorage.getItem('userStore')
let groups = [] as string[]
let userId = ''
if (userStore) {
	const userInfo: any = JSON.parse(userStore)
	userInfo?.state?.userInfo?.groups?.map((item: any) => {
		groups.push(item.id)
	})
	userId = userInfo?.state?.userInfo?.id
}

// 创建 axios 实例
export const axiosInstance = axios.create({
	baseURL: import.meta.env.VITE_APP_BASE_API,
	timeout: 50000,
	headers: {
		'Content-Type': 'application/json;charset=utf-8'
	}
});

// 请求拦截
axiosInstance.interceptors.request.use(
	(config) => {
		// 在请求被发送之前做些什么
		// 重新获取最新的用户信息
		const currentUserStore = window.localStorage.getItem('userStore')
		if (currentUserStore) {
			try {
				const userInfo = JSON.parse(currentUserStore)
				const currentUserId = userInfo?.state?.userInfo?.id
				const currentGroups = userInfo?.state?.userInfo?.groups?.map((item: any) => item.id) || []

				// 更新请求头
				if (currentUserId) {
					config.headers.userid = currentUserId
				}
				if (currentGroups.length > 0) {
					config.headers.groups = currentGroups.join(',')
				}
			} catch (error) {
				console.error('解析用户信息失败:', error)
			}
		}
		// 自动加 Authorization 头
		const token = localStorage.getItem('auth_token') || (currentUserStore ? (() => {
			try {
				const userInfo = JSON.parse(currentUserStore)
				return userInfo?.accessToken || userInfo?.userToken?.accessToken || ''
			} catch { return '' }
		})() : '');
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		return config;
	},
	(error) => {
		// 请求错误时做些什么
		return Promise.reject(error);
	},
);

// 响应拦截
axiosInstance.interceptors.response.use(
	(res: AxiosResponse<Result>) => {
		// if (!res.data) throw new Error(t("sys.api.apiRequestFailed"));

		const { status, data, message } = res.data;
		// 业务请求成功
		const hasSuccess = data && Reflect.has(res.data, "status") && status === ResultEnum.SUCCESS;
		if (hasSuccess) {
			return data;
		}

		// 业务请求错误
		throw new Error(message || t("sys.api.apiRequestFailed"));
	},
	(error: AxiosError<Result>) => {
		const { response, message } = error || {};

		const errMsg = response?.data?.message || message || t("sys.api.errorMessage");
		toast.error(errMsg, {
			position: "top-center",
		});

		// 处理401错误，清除用户信息
		const status = response?.status;
		if (status === 401) {
			try {
				const userStoreData = window.localStorage.getItem('userStore');
				if (userStoreData) {
					const userStore = JSON.parse(userStoreData);
					if (userStore?.getState && typeof userStore.getState === 'function') {
						userStore.getState().actions.clearUserInfoAndToken();
					}
				}
			} catch (e) {
				console.error('清除用户信息失败:', e);
				// 如果解析失败，可以尝试直接清除localStorage
				window.localStorage.removeItem('userStore');
			}
		}
		return Promise.reject(error);
	},
);



class APIClient {
	get<T = any>(config: AxiosRequestConfig): Promise<T> {
		return this.request({ ...config, method: "GET" });
	}

	post<T = any>(config: AxiosRequestConfig): Promise<T> {
		return this.request({ ...config, method: "POST" });
	}

	put<T = any>(config: AxiosRequestConfig): Promise<T> {
		return this.request({ ...config, method: "PUT" });
	}

	delete<T = any>(config: AxiosRequestConfig): Promise<T> {
		return this.request({ ...config, method: "DELETE" });
	}

	request<T = any>(config: AxiosRequestConfig): Promise<T> {
		return new Promise((resolve, reject) => {
			axiosInstance
				.request<any, AxiosResponse<Result>>(config)
				.then((res: AxiosResponse<Result>) => {
					resolve(res as unknown as Promise<T>);
				})
				.catch((e: Error | AxiosError) => {
					reject(e);
				});
		});
	}
}
export default new APIClient();
