import { useTranslation } from 'react-i18next';
import { Table, Button, Input, Space, message } from "antd";
import { useState } from "react";

export default function GroupManageTab() {
  const { t } = useTranslation();
  const mockGroups = [
    { id: "g1", name: t('pages.key_2618'), count: 2 },
    { id: "g2", name: t('pages.key_223'), count: 1 },
  ];
  const [groups, setGroups] = useState(mockGroups);
  const [newGroup, setNewGroup] = useState("");

  const handleAdd = () => {
    if (!newGroup.trim()) return message.warning(t('pages.key_2177'));
    setGroups([...groups, { id: Date.now().toString(), name: newGroup, count: 0 }]);
    setNewGroup("");
    message.success(t('pages.key_1534'));
  };

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder={t('pages.key_1133')}
          value={newGroup}
          onChange={e => setNewGroup(e.target.value)}
          style={{ width: 200 }}
        />
        <Button type="primary" onClick={handleAdd}>{t('pages.key_1526')}</Button>
      </Space>
      <Table
        columns={[
          { title: t('pages.key_224'), dataIndex: "name", key: "name" },
          { title: t('pages.key_2328'), dataIndex: "count", key: "count" },
        ]}
        dataSource={groups}
        rowKey="id"
        pagination={false}
      />
    </div>
  );
}
