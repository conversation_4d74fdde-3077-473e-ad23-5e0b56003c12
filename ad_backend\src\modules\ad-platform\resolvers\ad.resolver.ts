import { Resolver, Mutation, Args, Query, Int, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AdDTO } from '../entities/ad.dto';
import { CreateAdInput, UpdateAdInput } from '../entities/ad.input';
import { FacebookAdService } from '../services/facebook-ad.service';
import { AdAccountService } from '../services/ad-account.service';
import { Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { AdSet } from '../entities/adset.entity';
import { plainToInstance } from 'class-transformer';
import { Ad } from '../entities/ad.entity';
import { GraphQLJSON } from 'graphql-type-json';
import { FacebookSyncService } from '../facebook/facebook-sync.service';
import { Tenant } from '../../../entity/tenant.entity';
import { JwtAuthGuard } from '../../../modules/auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';
import { RuleService } from '../../domain/rule.service';
import { FacebookApiService } from '../services/facebook-api.service';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => AdDTO)
export class AdResolver {
    constructor(
        @Inject(FacebookAdService)
        private readonly facebookAdService: FacebookAdService,
        @Inject(AdAccountService)
        private readonly adAccountService: AdAccountService,
        @InjectRepository(AdSet)
        private readonly adSetRepo: Repository<AdSet>,
        @InjectRepository(Ad)
        private readonly adRepo: Repository<Ad>,
        @Inject(FacebookSyncService)
        private readonly facebookSyncService: FacebookSyncService,
        @InjectRepository(Tenant)
        private readonly tenantRepo: Repository<Tenant>,
        @Inject(RuleService)
        private readonly ruleService: RuleService,
        @Inject(FacebookApiService)
        private readonly facebookApiService: FacebookApiService,
    ) { }

    @Mutation(() => AdDTO)
    async createAd(
        @Args('adset_id') adset_id: string,
        @Args('name') name: string,
        @Args('status') status: string,
        @Args('creative', { type: () => GraphQLJSON, nullable: true }) creative?: any,
        @Args('ruleId', { nullable: true }) ruleId?: string,
        @Args('bid_amount', { nullable: true }) bid_amount?: number,
        @Args('remark', { nullable: true }) remark?: string,
        @Context() ctx?: any,
    ): Promise<AdDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        // 自动推导链路：只需 adset_id，自动查找 campaign_id、account_id、accessToken
        const adset = isSuperAdmin
            ? await this.adSetRepo.findOne({ where: { id: adset_id }, relations: ['adAccount', 'adCampaign', 'tenant'] })
            : await this.adSetRepo.findOne({ where: { id: adset_id, tenant: { id: tenantId } }, relations: ['adAccount', 'adCampaign', 'tenant'] });
        if (!adset) throw new Error('广告组(adset)不存在');
        if (!adset.adAccount) throw new Error('广告账户不存在');
        if (!adset.adCampaign) throw new Error('广告系列不存在');
        if (!adset.adAccount.accessToken) throw new Error('广告账户未配置 accessToken');
        const campaign_id = adset.adCampaign.facebookCampaignId || adset.adCampaign.id;
        const account_id = adset.adAccount.accountId;
        const realAccessToken = adset.adAccount.accessToken;
        const payload = {
            adset_id: adset.adsetId,
            name,
            status: typeof status === 'string' ? status.toUpperCase() : status,
            creative: creative && typeof creative === 'string' ? { creative_id: creative } : creative,
            bid_amount,
            remark,
            campaign_id,
            account_id,
        };
        // 1. 先请求 Facebook API 创建广告
        let fbResult;
        try {
            fbResult = await this.facebookAdService.createAd({ ...payload, access_token: realAccessToken });
        } catch (e) {
            // 只返回Facebook error_user_msg
            const fbError = e?.response?.data?.error;
            if (fbError && fbError.error_user_msg) {
                throw new Error(fbError.error_user_msg);
            }
            throw new Error('Facebook 广告创建失败: ' + (e?.message || e));
        }
        // 2. Facebook 成功后再查详情
        console.log('Facebook createAd 返回:', fbResult);
        const adId = fbResult.id || fbResult.adId || fbResult.ad_id || '';
        if (!adId) {
            throw new Error('Facebook 返回广告ID为空，禁止写入广告！');
        }
        // 查询 Facebook 广告详情，补全所有字段
        let fbAdDetail;
        try {
            fbAdDetail = await this.facebookAdService.getAd(adId, realAccessToken);
            console.log('Facebook getAd 返回:', fbAdDetail);
        } catch (e) {
            console.error('Facebook 查询广告详情失败:', e);
            throw new Error('Facebook 查询广告详情失败: ' + (e?.message || e));
        }
        // 3. 如果有ruleId，查本地规则并同步到Facebook
        if (ruleId) {
            const rule = await this.ruleService.findById(ruleId);
            if (rule) {
                let filters = [];
                if (rule.object === 'ad') {
                    filters = [{
                        field: 'ad.id',
                        operator: 'IN',
                        value: [String(fbAdDetail.id)]
                    }];
                } else if (rule.object === 'adset') {
                    filters = [{
                        field: 'adset.id',
                        operator: 'IN',
                        value: [String(adset.adsetId)]
                    }];
                } else if (rule.object === 'campaign') {
                    const campaignBizId = String((adset as any).adCampaign?.facebookCampaignId);
                    filters = [{
                        field: 'campaign.id',
                        operator: 'IN',
                        value: [campaignBizId]
                    }];
                }
                const fieldMap: Record<string, string> = {
                    clicks: 'CLICKS',
                    impressions: 'IMPRESSIONS',
                    reach: 'REACH',
                    spend: 'SPEND',
                    cpm: 'CPM',
                    cpc: 'CPC',
                    ctr: 'CTR',
                    frequency: 'FREQUENCY'
                };
                const firstCond: any = rule.conditions?.[0] || {};
                const triggerField = fieldMap[(firstCond.field || '').toLowerCase()] || 'CLICKS';
                const evaluationType = 'SCHEDULE';
                const triggerType = 'STATS_CHANGE';
                const evaluationSpec: any = {
                    evaluation_type: evaluationType,
                    trigger: {
                        type: triggerType,
                        field: triggerField,
                        operator: firstCond.operator || 'GREATER_THAN',
                        value: Number(firstCond.value)
                    }
                };
                // 只有trigger.type不是'STATS_CHANGE'时才传filters
                if (triggerType !== 'STATS_CHANGE' && filters && filters.length > 0) {
                    evaluationSpec.filters = filters;
                }
                const fbRulePayload = {
                    name: rule.name,
                    evaluation_spec: evaluationSpec,
                    execution_spec: {
                        execution_type: rule.action
                    },
                    schedule_spec: {
                        schedule_type: rule.scheduleType?.toUpperCase() || 'LONG',
                        custom_schedule: rule.custom_schedule || undefined
                    },
                    status: rule.status,
                    account_id: account_id,
                    access_token: realAccessToken,
                    ad_id: rule.object === 'ad' ? String(fbAdDetail.id) : undefined,
                    adset_id: rule.object === 'adset' ? String(adset.adsetId) : undefined,
                    campaign_id: rule.object === 'campaign' ? String(adset.adCampaign?.facebookCampaignId) : undefined,
                };
                console.log('同步Facebook自动规则 fbRulePayload:', JSON.stringify(fbRulePayload, null, 2));
                try {
                    await this.facebookApiService.createAdRule(fbRulePayload, account_id, realAccessToken);
                } catch (e) {
                    console.error('同步Facebook自动规则失败:', e);
                }
            }
        }
        const adEntity = this.adRepo.create({
            adId,
            adsetId: fbAdDetail.adset_id,
            name: fbAdDetail.name || name,
            platform: 'facebook',
            status: fbAdDetail.status || status,
            raw: fbAdDetail,
            adAccount: adset.adAccount,
            adCampaign: adset.adCampaign,
            tenant: adset.tenant,
            remark,
        });
        const saved = await this.adRepo.save(adEntity);
        return plainToInstance(AdDTO, {
            ...saved,
            adset_id: fbAdDetail.adset_id || adset.adsetId,
            campaign_id: fbAdDetail.campaign_id || adset.adCampaign.facebookCampaignId || adset.adCampaign.id,
            account_id: fbAdDetail.account_id || adset.adAccount.accountId,
            raw: fbAdDetail,
        });
    }

    @Mutation(() => AdDTO)
    async updateAd(
        @Args('adId') adId: string,
        @Args('name', { nullable: true }) name?: string,
        @Args('status', { nullable: true }) status?: string,
        @Args('creative', { type: () => GraphQLJSON, nullable: true }) creative?: any,
        @Args('tracking_specs', { type: () => GraphQLJSON, nullable: true }) tracking_specs?: any,
        @Args('bid_amount', { nullable: true }) bid_amount?: number,
        @Args('adset_id', { nullable: true }) adset_id?: string,
        @Context() ctx?: any,
    ): Promise<AdDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { adId } : { adId, tenant: { id: tenantId } };
        const adEntity = await this.adRepo.findOne({ where, relations: ['adAccount', 'adCampaign', 'tenant'] });
        if (!adEntity) throw new Error('广告不存在');
        const adAccount = adEntity.adAccount;
        const adCampaign = adEntity.adCampaign;
        if (!adAccount) throw new Error('广告账户不存在');
        if (!adCampaign) throw new Error('广告系列不存在');
        if (!adAccount.accessToken) throw new Error('广告账户未配置 accessToken');
        const realAccessToken = adAccount.accessToken;
        // 1. 先请求 Facebook API 更新广告
        const input: any = {};
        if (name !== undefined) input.name = name;
        if (status !== undefined) input.status = status;
        if (creative !== undefined) input.creative = typeof creative === 'string' ? { creative_id: creative } : creative;
        if (tracking_specs !== undefined) input.tracking_specs = tracking_specs;
        if (bid_amount !== undefined) input.bid_amount = bid_amount;
        if (adset_id !== undefined) input.adset_id = adset_id;
        let fbResult;
        try {
            fbResult = await this.facebookAdService.updateAd(adId, { ...input, access_token: realAccessToken });
        } catch (e) {
            throw new Error('Facebook 广告更新失败: ' + (e?.message || e));
        }
        // 2. Facebook 成功后再查详情
        let fbAdDetail;
        try {
            fbAdDetail = await this.facebookAdService.getAd(adId, realAccessToken);
            console.log('Facebook getAd 返回:', fbAdDetail);
        } catch (e) {
            console.error('Facebook 查询广告详情失败:', e);
            throw new Error('Facebook 查询广告详情失败: ' + (e?.message || e));
        }
        Object.assign(adEntity, input, { raw: fbAdDetail, name: fbAdDetail.name || adEntity.name, status: fbAdDetail.status || adEntity.status });
        const saved = await this.adRepo.save(adEntity);
        return plainToInstance(AdDTO, {
            ...saved,
            campaign_id: fbAdDetail.campaign_id || adCampaign.facebookCampaignId || adCampaign.id,
            account_id: fbAdDetail.account_id || adAccount.accountId,
            raw: fbAdDetail,
        });
    }

    @Mutation(() => Boolean)
    async deleteAd(
        @Args('adId') adId: string,
        @Context() ctx?: any,
    ): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { adId } : { adId, tenant: { id: tenantId } };
        const adEntity = await this.adRepo.findOne({ where, relations: ['adAccount', 'tenant'] });
        if (!adEntity) throw new Error('广告不存在');
        const adAccount = adEntity.adAccount;
        if (!adAccount) throw new Error('广告账户不存在');
        if (!adAccount.accessToken) throw new Error('广告账户未配置 accessToken');
        const realAccessToken = adAccount.accessToken;
        // 1. 先请求 Facebook API 删除广告
        try {
            await this.facebookAdService.deleteAd(adId, realAccessToken);
        } catch (e) {
            throw new Error('Facebook 广告删除失败: ' + (e?.message || e));
        }
        // 2. Facebook 成功后再删除本地
        await this.adRepo.delete({ adId });
        return true;
    }

    @Query(() => AdDTO, { nullable: true })
    async getAd(
        @Args('adId') adId: string,
        @Args('accessToken') accessToken: string,
    ): Promise<AdDTO | null> {
        const result = await this.facebookAdService.getAd(adId, accessToken);
        return result;
    }

    @Query(() => [AdDTO])
    async ads(
        @Args({ name: 'limit', type: () => Int, nullable: true }) limit?: number,
        @Args({ name: 'offset', type: () => Int, nullable: true }) offset?: number,
        @Context() ctx?: any,
    ): Promise<AdDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? {} : { tenant: { id: tenantId } };
        const ads = await this.adRepo.find({ where, relations: ['adAccount', 'adCampaign', 'tenant', 'adCreative'], take: limit, skip: offset });
        return ads.map(ad => {
            const raw = ad.raw || {};
            return plainToInstance(AdDTO, {
                ...ad,
                adset_id: raw.adset_id || ad.adsetId,
                adset_name: raw.adset_name || undefined,
                campaign_id: raw.campaign_id || ad.adCampaign?.facebookCampaignId || ad.adCampaign?.id,
                campaign_name: raw.campaign_name || ad.adCampaign?.name,
                account_id: raw.account_id || ad.adAccount?.accountId,
                account_name: raw.account_name || ad.adAccount?.account,
                status: raw.status || ad.status,
                bid_amount: raw.bid_amount || ad.bid_amount,
                effective_status: raw.effective_status || ad.effective_status,
                configured_status: raw.configured_status || ad.configured_status,
                created_time: raw.created_time || undefined,
                updated_time: raw.updated_time || (ad.updated_time ? ad.updated_time.toISOString() : undefined),
                creative: raw.creative || ad.adCreative?.raw,
                tracking_specs: raw.tracking_specs || undefined,
                adId: ad.adId,
                adAccount: ad.adAccount,
                adCampaign: ad.adCampaign,
                adCreative: ad.adCreative,
                tenant: ad.tenant,
                raw: ad.raw,
                insight: ad.insight,
            });
        });
    }

    @Mutation(() => Boolean, { description: '刷新并同步单条广告的 Facebook 状态' })
    async refreshAdFromFacebook(
        @Args('adId', { type: () => String }) adId: string,
        @Context() ctx?: any,
    ): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { adId } : { adId, tenant: { id: tenantId } };
        const entity = await this.adRepo.findOne({ where, relations: ['adAccount', 'adCampaign', 'tenant'] });
        if (!entity) throw new Error('广告不存在');
        if (!entity.adId || !entity.adAccount?.accessToken) throw new Error('缺少 Facebook 相关信息');
        const fbAd = await this.facebookAdService.getAd(entity.adId, entity.adAccount.accessToken);
        console.log('[refreshAdFromFacebook] Facebook 返回广告详情:', fbAd);
        if (!fbAd) throw new Error('Facebook 上未找到该广告');
        entity.status = fbAd.status;
        entity.name = fbAd.name;
        entity.raw = fbAd;
        entity.bid_amount = fbAd.bid_amount;
        entity.effective_status = fbAd.effective_status;
        entity.configured_status = fbAd.configured_status;
        entity.updated_time = fbAd.updated_time;
        await this.adRepo.save(entity);
        return true;
    }
} 