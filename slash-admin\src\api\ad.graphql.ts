import { gql, useQuery, useMutation } from '@apollo/client';

export const GET_AD_LIST = gql`
  query GetAds($limit: Int, $offset: Int) {
    ads(limit: $limit, offset: $offset) {
      id
      adId
      name
      adset_id
      adset_name
      campaign_id
      campaign_name
      account_id
      account_name
      status
      bid_amount
      effective_status
      configured_status
      created_time
      updated_time
      creative
      tracking_specs
    }
  }
`;

export const CREATE_AD = gql`
  mutation CreateAd(
    $adset_id: String!
    $name: String!
    $status: String!
    $creative: JSON
    $ruleId: String
    $bid_amount: Float
    $remark: String
  ) {
    createAd(
      adset_id: $adset_id
      name: $name
      status: $status
      creative: $creative
      ruleId: $ruleId
      bid_amount: $bid_amount
      remark: $remark
    ) {
      id
      adId
      name
      adset_id
      adset_name
      campaign_id
      campaign_name
      account_id
      status
      creative
      tracking_specs
      bid_amount
      effective_status
      configured_status
      created_time
      updated_time
    }
  }
`;

export const UPDATE_AD = gql`
  mutation UpdateAd(
    $adId: String!
    $name: String
    $status: String
    $creative: JSON
    $tracking_specs: JSON
    $bid_amount: Float
    $adset_id: String
  ) {
    updateAd(
      adId: $adId
      name: $name
      status: $status
      creative: $creative
      tracking_specs: $tracking_specs
      bid_amount: $bid_amount
      adset_id: $adset_id
    ) {
      id
      adId
      name
      adset_id
      adset_name
      campaign_id
      campaign_name
      status
      creative
      tracking_specs
      bid_amount
      effective_status
      configured_status
      updated_time
    }
  }
`;

export const DELETE_AD = gql`
  mutation DeleteAd($adId: String!) {
    deleteAd(adId: $adId)
  }
`;

export const GET_AD = gql`
  query GetAd($adId: String!, $accessToken: String!) {
    getAd(adId: $adId, accessToken: $accessToken) {
      id
      adId
      name
      adset_id
      adset_name
      campaign_id
      campaign_name
      account_id
      status
      creative
      tracking_specs
      bid_amount
      effective_status
      configured_status
      created_time
      updated_time
    }
  }
`;

export const REFRESH_AD = gql`
  mutation RefreshAd($adId: String!) {
    refreshAdFromFacebook(adId: $adId)
  }
`;

export function useUpdateAd() {
  return useMutation(UPDATE_AD);
}

export function useDeleteAd() {
  return useMutation(DELETE_AD);
}
