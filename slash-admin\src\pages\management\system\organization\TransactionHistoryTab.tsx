import { useTranslation } from 'react-i18next';
/**
 * VCC交易历史页面
 * 
 * 🚀 当前状态：真实API联调模式 
 * 
 * ✅ 已修复的问题：
 * 1. useExportVccTransactions从useMutation改为useLazyQuery
 * 2. 添加了导出按钮的loading状态
 * 3. 配置正确的GraphQL端点（localhost:3005）
 * 4. 恢复所有真实API调用
 * 
 * 🔧 当前配置：
 * - 后端服务：http://localhost:3005/graphql ✅ 运行中
 * - 前端服务：http://localhost:3002 ✅ 运行中
 * - API认证：JWT Bearer Token（需要登录）
 * 
 * 📋 功能状态：
 * - ✅ GraphQL连接正常
 * - ✅ 组件渲染无错误  
 * - 🔄 等待用户登录后测试功能
 */

import React, { useState, useEffect } from 'react';
import { Table, Button, Tag, Modal, Space, Input, Form, Select, Card, message, DatePicker } from 'antd';
import { EyeOutlined, ExportOutlined, SyncOutlined, SearchOutlined, CreditCardOutlined, PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table/interface';
import dayjs from 'dayjs';

// 类型定义
import { TransactionRecord, TransactionFilter, TransactionPagination } from './types';

// API服务
import {
  useVccTransactions,
  useVccTransactionStats,
  useExportVccTransactions,
  useSyncVccTransactionsFromFacebook,
  useAddVccRechargeTransaction,
  formatTransactionStatus,
  formatTransactionType,
  formatAmount,
  formatTransactionTime,
  downloadExportedData
} from '@/api/services/vccTransactionService';

const { RangePicker } = DatePicker;
const { Item: FormItem } = Form;

const TransactionHistoryTab: React.FC = () => {
  const { t } = useTranslation();
  const [searchForm] = Form.useForm();

  // 状态管理
  const [filter, setFilter] = useState<TransactionFilter>({});
  const [pagination, setPagination] = useState<TransactionPagination>({
    page: 1,
    limit: 20,
    sortBy: 'transactionTime',
    sortOrder: 'DESC'
  });
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionRecord | null>(null);
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  // API Hooks
  const { data: transactionData, loading, refetch } = useVccTransactions(filter, pagination);
  const { data: statsData } = useVccTransactionStats(filter);
  const [exportTransactions, { loading: exportLoading }] = useExportVccTransactions();
  const [syncFromFacebook, { loading: syncLoading }] = useSyncVccTransactionsFromFacebook();
  const [addRecharge, { loading: rechargeLoading }] = useAddVccRechargeTransaction();

  // 提取数据
  const transactions = transactionData?.vccTransactions?.data || [];
  const total = transactionData?.vccTransactions?.total || 0;
  const stats = statsData?.vccTransactionStats;

  // 表格列定义
  const columns: ColumnsType<TransactionRecord> = [
    {
      title: t('pages.key_367'),
      dataIndex: 'cardNo',
      key: 'cardNo',
      render: (text) => {
        // 格式化卡号显示：每4位用空格分隔
        const formattedCardNo = text.replace(/(\d{4})(?=\d)/g, '$1 ');
        return (
          <Space>
            <CreditCardOutlined />
            <span style={{ fontFamily: 'monospace', letterSpacing: '1px' }}>
              {formattedCardNo}
            </span>
          </Space>
        );
      }
    },
    {
      title: t('pages.key_2488'),
      dataIndex: 'amount',
      key: 'amount',
      sorter: true,
      render: (amount) => (
        <span style={{ color: amount > 0 ? '#52c41a' : '#ff4d4f' }}>
          {formatAmount(amount)}
        </span>
      )
    },
    {
      title: t('pages.key_514'),
      dataIndex: 'merchant',
      key: 'merchant'
    },
    {
      title: t('pages.key_70'),
      dataIndex: 'transactionTime',
      key: 'transactionTime',
      sorter: true,
      render: (time) => formatTransactionTime(time)
    },
    {
      title: t('pages.key_1602'),
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: t('pages.key_936'), value: 'success' },
        { text: t('pages.key_568'), value: 'pending' },
        { text: t('pages.key_598'), value: 'failed' }
      ],
      render: (status) => {
        const { text, color } = formatTransactionStatus(status);
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: t('pages.key_1765'),
      dataIndex: 'type',
      key: 'type',
      filters: [
        { text: t('pages.key_1066'), value: 'payment' },
        { text: t('pages.key_157'), value: 'deposit' },
        { text: t('pages.key_2434'), value: 'refund' }
      ],
      render: (type) => {
        const { text, color } = formatTransactionType(type);
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: t('pages.key_1058'),
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          />
        </Space>
      )
    }
  ];

  // 处理搜索表单提交
  const handleSearch = (values: any) => {
    const searchFilter: TransactionFilter = {
      cardNo: values.cardNo,
      merchant: values.merchant,
      status: values.status,
      type: values.type
    };

    // 处理时间范围
    if (values.dateRange && values.dateRange.length === 2) {
      searchFilter.startTime = values.dateRange[0].startOf('day').toISOString();
      searchFilter.endTime = values.dateRange[1].endOf('day').toISOString();
    }

    setFilter(searchFilter);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // 处理表格变化（分页、排序、过滤）
  const handleTableChange = (paginationInfo: any, filters: any, sorter: any) => {
    // 更新分页
    setPagination(prev => ({
      ...prev,
      page: paginationInfo.current,
      limit: paginationInfo.pageSize
    }));

    // 更新排序
    if (sorter.field) {
      setPagination(prev => ({
        ...prev,
        sortBy: sorter.field,
        sortOrder: sorter.order === 'ascend' ? 'ASC' : 'DESC'
      }));
    }

    // 更新过滤
    const newFilter = { ...filter };
    if (filters.status && filters.status.length > 0) {
      newFilter.status = filters.status[0];
    } else {
      delete newFilter.status;
    }
    if (filters.type && filters.type.length > 0) {
      newFilter.type = filters.type[0];
    } else {
      delete newFilter.type;
    }
    setFilter(newFilter);
  };

  // 查看交易详情
  const handleViewDetail = (record: TransactionRecord) => {
    setSelectedTransaction(record);
    setDetailModalOpen(true);
  };

  // 刷新数据
  const handleRefresh = () => {
    refetch();
    message.success(t('pages.key_1100'));
  };

  // 导出数据
  const handleExport = async (format: 'csv' | 'json' = 'csv') => {
    try {
      const { data } = await exportTransactions({
        variables: { filter, format }
      });

      if (data?.exportVccTransactions) {
        const filename = `vcc-transactions-${dayjs().format('YYYY-MM-DD')}.${format}`;
        downloadExportedData(data.exportVccTransactions, filename, format);
        message.success(t('pages.key_687'));
      }
    } catch (error) {
      console.error('导出失败:', error);
      message.error(t('pages.key_686'));
    }
  };

  // Facebook同步
  const handleFacebookSync = async () => {
    // 这里需要选择卡片ID，暂时使用第一个卡片
    if (transactions.length === 0) {
      message.warning(t('pages.key_1478'));
      return;
    }

    try {
      const { data } = await syncFromFacebook({
        variables: {
          input: {
            cardId: transactions[0].cardId,
            startDate: dayjs().subtract(30, 'day').toISOString(),
            endDate: dayjs().toISOString(),
            forceOverwrite: false
          }
        }
      });

      if (data?.syncVccTransactionsFromFacebook) {
        message.success(`成功同步 ${data.syncVccTransactionsFromFacebook.length} 条记录`);
        refetch(); // 刷新数据
      }
    } catch (error) {
      console.error('同步失败:', error);
      message.error('Facebook同步失败');
    }
  };

  return (
    <div>
      {/* 统计卡片 */}
      {stats && (
        <Card style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-around' }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {stats.totalCount}
              </div>
              <div>{t('pages.key_908')}</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {formatAmount(stats.totalAmount)}
              </div>
              <div>{t('pages.key_909')}</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                {stats.successRate.toFixed(1)}%
              </div>
              <div>{t('pages.key_940')}</div>
            </div>
          </div>
        </Card>
      )}

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          name="transaction_search_form"
          layout="inline"
          onFinish={handleSearch}
        >
          <FormItem name="cardNo" label={t('pages.key_367')}>
            <Input placeholder={t('pages.key_2375')} allowClear />
          </FormItem>
          <FormItem name="merchant" label={t('pages.key_514')}>
            <Input placeholder={t('pages.key_2376')} allowClear />
          </FormItem>
          <FormItem name="status" label={t('pages.key_1602')}>
            <Select placeholder={t('pages.key_2453')} allowClear style={{ width: 120 }}>
              <Select.Option value="success">{t('pages.key_936')}</Select.Option>
              <Select.Option value="pending">{t('pages.key_568')}</Select.Option>
              <Select.Option value="failed">{t('pages.key_598')}</Select.Option>
            </Select>
          </FormItem>
          <FormItem name="type" label={t('pages.key_1765')}>
            <Select placeholder={t('pages.key_2454')} allowClear style={{ width: 120 }}>
              <Select.Option value="payment">{t('pages.key_1066')}</Select.Option>
              <Select.Option value="deposit">{t('pages.key_157')}</Select.Option>
              <Select.Option value="refund">{t('pages.key_2434')}</Select.Option>
            </Select>
          </FormItem>
          <FormItem name="dateRange" label={t('pages.key_1196')}>
            <RangePicker />
          </FormItem>
          <FormItem>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>{t('pages.key_1044')}</Button>
          </FormItem>
          <FormItem>
            <Button onClick={() => {
              searchForm.resetFields();
              setFilter({});
              setPagination(prev => ({ ...prev, page: 1 }));
            }}>{t('pages.key_2483')}</Button>
          </FormItem>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            icon={<SyncOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >{t('pages.key_310')}</Button>
          <Button
            icon={<ExportOutlined />}
            onClick={() => handleExport('csv')}
            loading={exportLoading}
          >
            导出CSV
          </Button>
          <Button
            icon={<ExportOutlined />}
            onClick={() => handleExport('json')}
            loading={exportLoading}
          >
            导出JSON
          </Button>
          <Button
            icon={<SyncOutlined />}
            onClick={handleFacebookSync}
            loading={syncLoading}
            type="primary"
          >
            Facebook同步
          </Button>
        </Space>
      </div>

      {/* 交易记录表格 */}
      <Table
        columns={columns}
        dataSource={transactions}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pagination.page,
          pageSize: pagination.limit,
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        onChange={handleTableChange}
      />

      {/* 交易详情Modal */}
      <Modal
        title={t('pages.key_74')}
        open={detailModalOpen}
        onCancel={() => setDetailModalOpen(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalOpen(false)}>{t('pages.key_186')}</Button>
        ]}
        width={600}
      >
        {selectedTransaction && (
          <div>
            <p><strong>交易ID：</strong> {selectedTransaction.id}</p>
            <p><strong>卡号：</strong>
              <span style={{ fontFamily: 'monospace', letterSpacing: '1px', marginLeft: '8px' }}>
                {selectedTransaction.cardNo.replace(/(\d{4})(?=\d)/g, '$1 ')}
              </span>
            </p>
            <p><strong>金额：</strong> {formatAmount(selectedTransaction.amount)}</p>
            <p><strong>商户：</strong> {selectedTransaction.merchant}</p>
            <p><strong>交易时间：</strong> {formatTransactionTime(selectedTransaction.transactionTime)}</p>
            <p><strong>状态：</strong>
              <Tag color={formatTransactionStatus(selectedTransaction.status).color}>
                {formatTransactionStatus(selectedTransaction.status).text}
              </Tag>
            </p>
            <p><strong>类型：</strong>
              <Tag color={formatTransactionType(selectedTransaction.type).color}>
                {formatTransactionType(selectedTransaction.type).text}
              </Tag>
            </p>
            {selectedTransaction.facebookAccountId && (
              <p><strong>Facebook账户ID：</strong> {selectedTransaction.facebookAccountId}</p>
            )}
            {selectedTransaction.campaignName && (
              <p><strong>广告活动：</strong> {selectedTransaction.campaignName}</p>
            )}
            {selectedTransaction.description && (
              <p><strong>描述：</strong> {selectedTransaction.description}</p>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TransactionHistoryTab;
