# 组件路径 /pages/dashboard/data-analysis/index

## 前端

- **实际路径**：slash-admin/src/pages/dashboard/data-analysis/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，数据分析页）
  - ../analysis/data-analysis.tsx（核心数据分析组件，含多维度图表）
- **主要依赖API定义**：详见data-analysis.tsx实现

### API 调用列表与详细说明

| API名称         | 方法     | 路径/GraphQL名           | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|--------------------------|--------------|----------|-----------|
| 获取广告数据分析 | query    | /api/data-analysis/performance | 时间范围、广告ID等 | 展示量、点击量、转化量、支出等 | 多维度趋势图 |
| 获取渠道分布分析 | query    | /api/data-analysis/channel-distribution | 时间范围、渠道ID等 | 渠道分布、各渠道效果等 | 渠道分布饼图、表格 |

#### 主要参数结构说明

- **分析查询参数**（以广告数据为例）  
  ```ts
  {
    startDate: string; // 开始日期
    endDate: string;   // 结束日期
    adId?: string;     // 广告ID（可选）
    channelId?: string; // 渠道ID（可选）
    ... // 其他筛选条件
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | impressions | number    | 展示量                     |
  | clicks      | number    | 点击量                     |
  | conversions | number    | 转化量                     |
  | spend       | number    | 支出                       |
  | month       | string    | 月份（如有）               |
  | ...         | ...       | 其他分析指标               |

### 前端表单/组件说明

- 支持多维度数据趋势图（折线、柱状、面积等可切换）
- 支持渠道分布饼图、渠道效果表格
- 支持按时间、广告、渠道等多条件筛选
- 组件化拆分，便于扩展

---

## 后端

- **主要接口**：RESTful或GraphQL接口，详见上表
- **数据库表结构**（以data_analysis为例，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| adId        | uuid        | 广告ID                     | 索引         |
| channelId   | uuid        | 渠道ID                     | 索引         |
| date        | date        | 统计日期                   | 索引         |
| impressions | int         | 展示量                     |              |
| clicks      | int         | 点击量                     |              |
| conversions | int         | 转化量                     |              |
| spend       | numeric     | 支出                       |              |
| ...         | ...         | 其他分析指标               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query /api/data-analysis/performance?startDate=...&endDate=...
    后端->>数据库: 查询 data_analysis 表
    数据库-->>后端: 返回分析数据
    后端-->>前端: 返回分析数据
```

---

## 变更历史/注意事项

- 分析数据需定时汇总，建议用ETL或定时任务
- 支持多维度、多粒度分析，表结构建议宽表设计
- 大数据量建议分区表、索引优化
- 复杂业务字段建议在表结构中用jsonb扩展