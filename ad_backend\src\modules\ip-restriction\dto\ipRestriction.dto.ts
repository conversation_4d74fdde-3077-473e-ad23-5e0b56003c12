import { IsOptional, IsString } from 'class-validator';
import {
	Field,
	ID,
	ObjectType,
	GraphQLISODateTime,
	InputType,
	Int
} from '@nestjs/graphql';
import { FilterableField } from '@ptc-org/nestjs-query-graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@ObjectType('ipRestriction')
export class ipRestrictionDTO {
	@FilterableField(() => Int, { nullable: true })
	id?: number;
	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	ip?: string;
	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	userId?: string;
	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	status?: string;
	@Field({ nullable: true })
	operator?: string;
	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	tenantId?: string;
	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	createdAt?: Date;
	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	updatedAt?: Date;
}

@InputType('ipRestrictionInput')
export class ipRestrictionInput {
	@Field(() => Int, { nullable: true })
	id?: number;
	@Field({ nullable: true })
	ip?: string;
	@Field({ nullable: true })
	userId?: string;
	@Field({ nullable: true })
	status?: string;
	@Field({ nullable: true })
	operator?: string;
	@Field({ nullable: true })
	createdAt?: Date;
	@Field({ nullable: true })
	updatedAt?: Date;
	@Field({ nullable: true })
	tenantId?: string;
}

@InputType()
export class CreateIpRestrictionInput {
	@Field({ nullable: true })
	ip?: string;
	@Field({ nullable: true })
	userId?: string;
	@Field({ nullable: true })
	status?: string;
	@Field({ nullable: true })
	operator?: string;
}

