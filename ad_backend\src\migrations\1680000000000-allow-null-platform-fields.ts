import { MigrationInterface, QueryRunner } from "typeorm";

export class AllowNullPlatformFields1680000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE ad_account ALTER COLUMN platform DROP NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad_campaign ALTER COLUMN platform DROP NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad_creative ALTER COLUMN platform DROP NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad ALTER COLUMN platform DROP NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad_set ALTER COLUMN platform DROP NOT NULL;`);
        await queryRunner.query(`ALTER TABLE audience ALTER COLUMN platform DROP NOT NULL;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE ad_account ALTER COLUMN platform SET NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad_campaign ALTER COLUMN platform SET NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad_creative ALTER COLUMN platform SET NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad ALTER COLUMN platform SET NOT NULL;`);
        await queryRunner.query(`ALTER TABLE ad_set ALTER COLUMN platform SET NOT NULL;`);
        await queryRunner.query(`ALTER TABLE audience ALTER COLUMN platform SET NOT NULL;`);
    }
} 