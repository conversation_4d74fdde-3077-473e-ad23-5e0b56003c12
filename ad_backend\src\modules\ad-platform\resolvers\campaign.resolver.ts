import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Campaign } from '../entities/campaign.entity';
import { CreateCampaignInputX } from '../entities/campaign.input';
import { FacebookService2 } from '../services/facebook.service';
import { AdAccount } from '../entities/ad-account.entity';
import { FacebookApp } from '../entities/facebook-app.entity';
import { FacebookSyncService } from '../facebook/facebook-sync.service';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';
@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => Campaign)
export class CampaignResolver {
    constructor(
        @InjectRepository(Campaign)
        private readonly campaignRepo: Repository<Campaign>,
        @InjectRepository(AdAccount)
        private readonly adAccountRepo: Repository<AdAccount>,
        private readonly facebookService: FacebookService2,
        private readonly facebookSyncService: FacebookSyncService,
    ) { }

    @Query(() => [Campaign])
    async campaigns(@Context() ctx: any): Promise<Campaign[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? {} : { adAccount: { tenant: { id: tenantId } } };
        return this.campaignRepo.find({ where, relations: ['adAccount'] });
    }

    @Mutation(() => Campaign)
    async createCampaign(@Args('input') input: CreateCampaignInputX, @Context() ctx: any): Promise<Campaign> {
        const { tenantId, isSuperAdmin } = ctx;
        // 1. 查找广告账户及token
        const adAccount = isSuperAdmin
            ? await this.adAccountRepo.findOneBy({ accountId: input.accountId })
            : await this.adAccountRepo.findOne({ where: { accountId: input.accountId, tenant: { id: tenantId } } });
        if (!adAccount) throw new Error('广告账户不存在');
        const accessToken = adAccount.accessToken;
        const fbAccountId = adAccount.accountId;

        // 2. 调用 Facebook API，确保字段同步
        let fbResult;
        try {
            fbResult = await this.facebookService.createCampaignOnFacebook({
                name: input.name,
                objective: input.objective,
                status: input.status,
                specialAdCategories: input.specialAdCategories,
            }, accessToken, fbAccountId);
        } catch (e) {
            // 直接抛出异常，不再本地落库失败数据
            throw new Error('Facebook API 创建广告系列失败: ' + (e?.response?.data?.error?.message || e.message || e));
        }

        // 3. 本地落库
        const entity = this.campaignRepo.create({
            ...input,
            adAccount,
            facebookCampaignId: fbResult.id,
            syncStatus: 'success',
        });
        const saved = await this.campaignRepo.save(entity);

        // 4. 自动同步Facebook Campaign到本地
        try {
            // 拉取Facebook所有campaign
            const { campaigns } = await this.facebookService.getCampaigns({ accessToken, accountId: fbAccountId });
            // 获取当前租户
            const tenant = adAccount.tenant;
            await this.facebookSyncService.syncCampaigns({ campaigns, tenant, adAccount });
        } catch (e) {
            console.error('自动同步Facebook Campaign到本地失败:', e);
        }

        return saved;
    }

    @Mutation(() => Boolean)
    async deleteCampaign(@Args('id', { type: () => ID }) id: string, @Context() ctx: any): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx;
        // 1. 查本地 campaign
        const where = isSuperAdmin ? { id } : { id, adAccount: { tenant: { id: tenantId } } };
        const entity = await this.campaignRepo.findOne({ where, relations: ['adAccount'] });
        if (!entity) throw new Error('广告系列不存在');
        // 2. 同步 Facebook 删除
        if (entity.facebookCampaignId && entity.adAccount?.accessToken) {
            await this.facebookService.deleteCampaignOnFacebook(
                entity.facebookCampaignId,
                entity.adAccount.accessToken
            );
        }
        // 3. 本地删除
        const res = await this.campaignRepo.delete(id);
        return res.affected > 0;
    }

    @Mutation(() => Campaign)
    async updateCampaign(
        @Args('id', { type: () => ID }) id: string,
        @Args('input') input: CreateCampaignInputX,
        @Context() ctx: any,
    ): Promise<Campaign> {
        const { tenantId, isSuperAdmin } = ctx;
        // 1. 查找广告账户
        const adAccount = isSuperAdmin
            ? await this.adAccountRepo.findOneBy({ accountId: input.accountId })
            : await this.adAccountRepo.findOne({ where: { accountId: input.accountId, tenant: { id: tenantId } } });
        if (!adAccount) throw new Error('广告账户不存在');
        // 2. 查找原始 entity
        const where = isSuperAdmin ? { id } : { id, adAccount: { tenant: { id: tenantId } } };
        const entity = await this.campaignRepo.findOne({ where });
        if (!entity) throw new Error('广告系列不存在');
        // 3. 检查需同步的字段
        const updateToFb: any = {};
        if (input.name && input.name !== entity.name) updateToFb.name = input.name;
        if (input.objective && input.objective !== entity.objective) updateToFb.objective = input.objective;
        if (input.status && input.status !== entity.status) updateToFb.status = input.status;
        if (
            input.specialAdCategories &&
            JSON.stringify(input.specialAdCategories) !== JSON.stringify(entity.specialAdCategories)
        ) {
            updateToFb.special_ad_categories = input.specialAdCategories;
        }
        if (
            Object.keys(updateToFb).length > 0 &&
            entity.facebookCampaignId &&
            adAccount.accessToken
        ) {
            await this.facebookService.updateCampaignOnFacebook(
                entity.facebookCampaignId,
                updateToFb,
                adAccount.accessToken
            );
        }
        // 4. 合并字段（排除 accountId）
        const { accountId, ...rest } = input;
        Object.assign(entity, rest);
        entity.adAccount = adAccount;
        // 5. 保存
        return this.campaignRepo.save(entity);
    }
    @Query(() => [FacebookApp], { description: '通过广告系列ID获取Facebook用户管理的App对象列表' })
    async facebookUserAppsByCampaign(
        @Args('campaignId', { type: () => ID }) campaignId: string,
        @Context() ctx: any,
    ): Promise<any[]> {
        const { tenantId, isSuperAdmin } = ctx;
        // 1. 查 campaign 及 adAccount
        const where = isSuperAdmin ? { id: campaignId } : { id: campaignId, adAccount: { tenant: { id: tenantId } } };
        const campaign = await this.campaignRepo.findOne({
            where,
            relations: ['adAccount'],
        });
        if (!campaign) throw new Error('广告系列不存在');
        const accessToken = campaign.adAccount?.accessToken;
        if (!accessToken) throw new Error('广告账户未配置 accessToken');
        // 2. 查 Facebook 用户 apps
        return this.facebookService.getUserApps(accessToken);
    }

    @Query(() => [FacebookApp], { description: '通过广告账户ID获取投放过的App Promotion应用对象列表' })
    async facebookUserAppsByAdAccount(
        @Args('adAccountId', { type: () => String }) adAccountId: string,
        @Args('accessToken', { type: () => String }) accessToken: string,
        @Context() ctx: any,
    ): Promise<FacebookApp[]> {
        const { tenantId, isSuperAdmin } = ctx;
        if (!isSuperAdmin) {
            // 校验该 adAccount 是否属于当前租户
            const adAccount = await this.adAccountRepo.findOne({ where: { accountId: adAccountId, tenant: { id: tenantId } } });
            if (!adAccount) throw new Error('无权访问该广告账户');
        }
        return this.facebookService.getPromotedAppsByAdAccount(adAccountId, accessToken);
    }

    @Query(() => [FacebookApp], { description: '通过广告系列ID获取投放过的App Promotion应用对象列表' })
    async facebookUserAppsByAdAccountByCampaignId(
        @Args('campaignId', { type: () => ID }) campaignId: string,
        @Context() ctx: any,
    ): Promise<FacebookApp[]> {
        const { tenantId, isSuperAdmin } = ctx;
        // 1. 查 campaign 及 adAccount
        const where = isSuperAdmin ? { id: campaignId } : { id: campaignId, adAccount: { tenant: { id: tenantId } } };
        const campaign = await this.campaignRepo.findOne({
            where,
            relations: ['adAccount'],
        });
        if (!campaign) throw new Error('广告系列不存在');
        const adAccountId = campaign.adAccount?.accountId;
        const accessToken = campaign.adAccount?.accessToken;
        if (!adAccountId || !accessToken) throw new Error('广告账户未配置 accountId 或 accessToken');
        // 2. 查广告账户下投放过的 App Promotion 应用
        return this.facebookService.getPromotedAppsByAdAccount(adAccountId, accessToken);
    }

    @Mutation(() => Campaign, { description: '刷新并同步单条广告系列的 Facebook 状态' })
    async refreshCampaignFromFacebook(@Args('id', { type: () => ID }) id: string, @Context() ctx: any): Promise<Campaign> {
        const { tenantId, isSuperAdmin } = ctx;
        // 1. 查本地
        const where = isSuperAdmin ? { id } : { id, adAccount: { tenant: { id: tenantId } } };
        const entity = await this.campaignRepo.findOne({ where, relations: ['adAccount'] });
        if (!entity) throw new Error('广告系列不存在');
        if (!entity.facebookCampaignId || !entity.adAccount?.accessToken) throw new Error('缺少 Facebook 相关信息');
        // 2. 拉取 Facebook 最新数据
        const { campaigns } = await this.facebookService.getCampaigns({
            accessToken: entity.adAccount.accessToken,
            accountId: entity.adAccount.accountId,
        });
        const fb = campaigns.find((c: any) => c.id === entity.facebookCampaignId);
        if (!fb) throw new Error('Facebook 上未找到该广告系列');
        // 3. 更新本地
        entity.status = fb.status;
        entity.name = fb.name;
        entity.objective = fb.objective;
        // 可选：同步更多字段
        (entity as any).raw = fb;
        await this.campaignRepo.save(entity);
        return entity;
    }
} 