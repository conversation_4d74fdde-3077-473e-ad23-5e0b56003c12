interface SyncStats {
  total: number;
  success: number;
  failed: number;
  inserted?: number;
  updated?: number;
  skipped?: number;
}

export interface AdAccountSyncResponse {
  accounts: SyncStats;
  campaigns: SyncStats;
  adsets: SyncStats;
  ads: SyncStats;
  adcreatives: SyncStats;
  pixels: SyncStats;
  audiences: SyncStats;
  logs?: string[];
}

export interface AdAccount {
  accountId: string;
  account: string;
  status: string;
  platform: string;
  accessToken: string;
  // ... other properties
} 