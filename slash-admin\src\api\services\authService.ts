import { ApolloClient } from '@apollo/client';
import { LOGIN_MUTATION, REGISTER_MUTATION, GET_CURRENT_USER } from '../auth.graphql';
import { UserStatus } from '@/pages/management/system/account/types';

// 登录请求参数
export interface LoginParams {
    username: string;
    password: string;
}

// 注册请求参数
export interface RegisterParams {
    username: string;
    email: string;
    password: string;
}

// 登录响应
export interface LoginResponse {
    userId: string;
    accessToken: string;
    username: string;
    roles?: string[];
}

// 注册响应
export interface RegisterResponse {
    userId: string;
    accessToken: string;
    username: string;
}

// 当前用户响应
export interface CurrentUserResponse {
    me: {
        user: {
            id: string;
            username: string;
            email: string;
            avatar: string;
            fullName: string;
            phone: string;
            address: string;
            status: UserStatus;
            roles: {
                id: string;
                name: string;
                routeIds: string[];
                routeList: {
                    id: string;
                    parentId: string | null;
                    name: string;
                    path: string;
                    component: string | null;
                    icon: string | null;
                    order: number;
                    type: 'menu' | 'page';
                    status: 'enabled' | 'disabled';
                    isHidden: boolean;
                    description: string;
                    children?: Array<{
                        id: string;
                        parentId: string;
                        name: string;
                        path: string;
                        component: string | null;
                        icon: string | null;
                        order: number;
                        type: 'menu' | 'page';
                        status: 'enabled' | 'disabled';
                        isHidden: boolean;
                        description: string;
                    }>;
                }[];
            }[];
            groups: { id: string; name: string }[];
            createdAt: string;
            updatedAt: string;
        };
        accessToken: string;
    };
}

export class AuthService {
    constructor(private client: ApolloClient<any>) { }

    /**
     * 用户登录
     * @param params 登录参数
     * @returns 登录响应
     */
    async login(params: LoginParams): Promise<LoginResponse> {
        try {
            const { data, errors } = await this.client.mutate({
                mutation: LOGIN_MUTATION,
                variables: {
                    input: params
                }
            });

            if (errors) {
                throw new Error(`登录失败：${errors[0].message}`);
            }

            return data.login;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 用户注册
     * @param params 注册参数
     * @returns 注册响应
     */
    async register(params: RegisterParams): Promise<RegisterResponse> {
        try {
            const { data, errors } = await this.client.mutate({
                mutation: REGISTER_MUTATION,
                variables: {
                    input: params
                }
            });

            if (errors) {
                throw new Error(`注册失败：${errors[0].message}`);
            }

            return data.register;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获取当前登录用户信息
     * @returns 当前用户信息
     */
    async getCurrentUser(): Promise<CurrentUserResponse> {
        try {
            const { data, error } = await this.client.query({
                query: GET_CURRENT_USER,
                fetchPolicy: 'network-only'
            });

            if (error) {
                throw new Error(`获取当前用户信息失败：${error.message}`);
            }

            return data;
        } catch (err) {
            throw err;
        }
    }
} 