#!/usr/bin/env node

/**
 * 文档同步 - 文档验证脚本
 * 验证文档与代码的一致性
 */

const fs = require('fs');
const path = require('path');
const ChangeDetector = require('./detect-changes');

class DocumentValidator {
  constructor() {
    this.projectRoot = process.cwd();
    this.docsPath = path.join(this.projectRoot, 'docs/modules');
    this.detector = new ChangeDetector();
    this.errors = [];
    this.warnings = [];
  }

  /**
   * 验证实体与文档的一致性
   */
  async validateEntities() {
    console.log('🔍 验证实体与文档一致性...');
    
    // 获取所有实体变更
    await this.detector.detect();
    const entities = this.detector.changes.entities;
    
    for (const entity of entities) {
      const docFiles = this.getDocumentFiles(entity.moduleName);
      
      for (const docFile of docFiles) {
        const docPath = path.join(this.docsPath, docFile);
        if (fs.existsSync(docPath)) {
          this.validateEntityInDocument(docPath, entity, docFile);
        }
      }
    }
  }

  /**
   * 验证文档中的实体信息
   */
  validateEntityInDocument(docPath, entity, docFile) {
    const content = fs.readFileSync(docPath, 'utf8');
    const { tableName, fields } = entity;
    
    // 检查是否包含表结构
    const tableHeaderRegex = new RegExp(`#### ${tableName} 表`, 'i');
    if (!tableHeaderRegex.test(content)) {
      this.warnings.push({
        type: 'missing_table',
        file: docFile,
        message: `缺少 ${tableName} 表结构说明`
      });
      return;
    }
    
    // 验证字段是否在文档中
    fields.forEach(field => {
      const fieldRegex = new RegExp(`\\|\\s*${field.name}\\s*\\|`, 'i');
      if (!fieldRegex.test(content)) {
        this.errors.push({
          type: 'missing_field',
          file: docFile,
          table: tableName,
          field: field.name,
          message: `${tableName} 表缺少字段 ${field.name} 的文档说明`
        });
      }
    });
    
    // 验证字段类型是否正确
    this.validateFieldTypes(content, entity, docFile);
  }

  /**
   * 验证字段类型
   */
  validateFieldTypes(content, entity, docFile) {
    const { tableName, fields } = entity;
    
    fields.forEach(field => {
      const fieldRowRegex = new RegExp(`\\|\\s*${field.name}\\s*\\|\\s*([^|]+)\\s*\\|`, 'i');
      const match = content.match(fieldRowRegex);
      
      if (match) {
        const docType = match[1].trim();
        const expectedType = this.normalizeType(field.type, field.options);
        
        if (!this.isTypeCompatible(docType, expectedType)) {
          this.warnings.push({
            type: 'type_mismatch',
            file: docFile,
            table: tableName,
            field: field.name,
            expected: expectedType,
            actual: docType,
            message: `${tableName}.${field.name} 类型不匹配: 文档中为 ${docType}, 代码中为 ${expectedType}`
          });
        }
      }
    });
  }

  /**
   * 标准化类型名称
   */
  normalizeType(codeType, options) {
    // 处理 TypeORM 类型到文档类型的映射
    const typeMapping = {
      'string': 'varchar',
      'number': options.type === 'decimal' ? 'decimal' : 'int',
      'Date': 'timestamp with time zone',
      'boolean': 'boolean',
      'any': 'jsonb'
    };
    
    // 如果有明确的数据库类型，使用数据库类型
    if (options.type) {
      return options.type;
    }
    
    return typeMapping[codeType] || codeType;
  }

  /**
   * 检查类型兼容性
   */
  isTypeCompatible(docType, expectedType) {
    // 类型别名映射
    const aliases = {
      'varchar': ['string', 'text'],
      'int': ['number', 'integer'],
      'decimal': ['number', 'numeric'],
      'timestamp': ['Date', 'timestamp with time zone'],
      'jsonb': ['any', 'json', 'object']
    };
    
    // 直接匹配
    if (docType.toLowerCase() === expectedType.toLowerCase()) {
      return true;
    }
    
    // 检查别名
    for (const [canonical, aliasList] of Object.entries(aliases)) {
      if (canonical === expectedType.toLowerCase() && 
          aliasList.some(alias => docType.toLowerCase().includes(alias))) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 验证 GraphQL API 文档
   */
  async validateGraphQLAPIs() {
    console.log('🔍 验证 GraphQL API 文档...');
    
    const schemas = this.detector.changes.graphqlSchemas;
    
    for (const schema of schemas) {
      const docFiles = this.getDocumentFilesByGraphQL(schema.fileName);
      
      for (const docFile of docFiles) {
        const docPath = path.join(this.docsPath, docFile);
        if (fs.existsSync(docPath)) {
          this.validateGraphQLInDocument(docPath, schema, docFile);
        }
      }
    }
  }

  /**
   * 验证文档中的 GraphQL API
   */
  validateGraphQLInDocument(docPath, schema, docFile) {
    const content = fs.readFileSync(docPath, 'utf8');
    
    // 验证查询操作
    schema.queries.forEach(query => {
      const queryRegex = new RegExp(`\\|[^|]*${query.operationName}[^|]*\\|`, 'i');
      if (!queryRegex.test(content)) {
        this.warnings.push({
          type: 'missing_api',
          file: docFile,
          operation: query.operationName,
          type: 'query',
          message: `缺少查询操作 ${query.operationName} 的文档说明`
        });
      }
    });
    
    // 验证变更操作
    schema.mutations.forEach(mutation => {
      const mutationRegex = new RegExp(`\\|[^|]*${mutation.operationName}[^|]*\\|`, 'i');
      if (!mutationRegex.test(content)) {
        this.warnings.push({
          type: 'missing_api',
          file: docFile,
          operation: mutation.operationName,
          type: 'mutation',
          message: `缺少变更操作 ${mutation.operationName} 的文档说明`
        });
      }
    });
  }

  /**
   * 验证文档结构完整性
   */
  validateDocumentStructure() {
    console.log('🔍 验证文档结构完整性...');
    
    const docFiles = fs.readdirSync(this.docsPath).filter(file => file.endsWith('.md'));
    
    docFiles.forEach(docFile => {
      const docPath = path.join(this.docsPath, docFile);
      const content = fs.readFileSync(docPath, 'utf8');
      
      // 检查必需的章节
      const requiredSections = [
        '## 前端',
        '### API 调用列表与详细说明',
        '## 后端',
        '### 数据库表结构',
        '## 变更历史/注意事项'
      ];
      
      requiredSections.forEach(section => {
        if (!content.includes(section)) {
          this.warnings.push({
            type: 'missing_section',
            file: docFile,
            section,
            message: `文档缺少必需章节: ${section}`
          });
        }
      });
      
      // 检查表格格式
      this.validateTableFormat(content, docFile);
    });
  }

  /**
   * 验证表格格式
   */
  validateTableFormat(content, docFile) {
    const tableRegex = /\|[^|\n]+\|[^|\n]+\|/g;
    const tables = content.match(tableRegex);
    
    if (tables) {
      tables.forEach((table, index) => {
        const rows = table.split('\n').filter(row => row.trim());
        
        if (rows.length >= 2) {
          const headerCols = (rows[0].match(/\|/g) || []).length;
          const separatorCols = (rows[1].match(/\|/g) || []).length;
          
          if (headerCols !== separatorCols) {
            this.warnings.push({
              type: 'table_format',
              file: docFile,
              table: index + 1,
              message: `表格 ${index + 1} 格式不正确: 表头和分隔符列数不匹配`
            });
          }
        }
      });
    }
  }

  /**
   * 根据模块名获取对应的文档文件
   */
  getDocumentFiles(moduleName) {
    const mapping = {
      'ad-platform': ['z 广告账户.md', 'z 广告系列.md', 'z 受众管理.md'],
      'vcc': ['z 卡片管理.md'],
      'material-management': ['z 素材管理.md'],
      'material-create': ['z 素材管理.md'],
      'user': ['z 账户管理.md'],
      'group': ['z 群组管理.md'],
      'tenant': ['z 租户管理.md']
    };
    
    return mapping[moduleName] || [];
  }

  /**
   * 根据 GraphQL 文件名获取对应的文档文件
   */
  getDocumentFilesByGraphQL(fileName) {
    const mapping = {
      'adAccount': ['z 广告账户.md'],
      'campaign': ['z 广告系列.md'],
      'audience': ['z 受众管理.md'],
      'vccCard': ['z 卡片管理.md'],
      'materialManagement': ['z 素材管理.md'],
      'material': ['z 素材管理.md'],
      'user': ['z 账户管理.md'],
      'group': ['z 群组管理.md'],
      'tenant': ['z 租户管理.md']
    };
    
    return mapping[fileName] || [];
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    const reportPath = path.join(this.projectRoot, 'scripts/doc-sync/validation-report.md');
    
    let report = '# 文档验证报告\n\n';
    report += `验证时间: ${new Date().toLocaleString()}\n\n`;
    
    // 统计信息
    report += '## 验证统计\n\n';
    report += `- 错误: ${this.errors.length}\n`;
    report += `- 警告: ${this.warnings.length}\n\n`;
    
    // 错误详情
    if (this.errors.length > 0) {
      report += '## 错误详情\n\n';
      this.errors.forEach((error, index) => {
        report += `### 错误 ${index + 1}\n\n`;
        report += `- **类型**: ${error.type}\n`;
        report += `- **文件**: ${error.file}\n`;
        report += `- **消息**: ${error.message}\n\n`;
      });
    }
    
    // 警告详情
    if (this.warnings.length > 0) {
      report += '## 警告详情\n\n';
      this.warnings.forEach((warning, index) => {
        report += `### 警告 ${index + 1}\n\n`;
        report += `- **类型**: ${warning.type}\n`;
        report += `- **文件**: ${warning.file}\n`;
        report += `- **消息**: ${warning.message}\n\n`;
      });
    }
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      report += '## 验证结果\n\n✅ 所有文档验证通过，无错误或警告。\n';
    }
    
    fs.writeFileSync(reportPath, report);
    console.log(`📊 验证报告已生成: ${reportPath}`);
  }

  /**
   * 执行验证
   */
  async validate() {
    console.log('🔍 开始文档验证...');
    
    // 验证实体
    await this.validateEntities();
    
    // 验证 GraphQL API
    await this.validateGraphQLAPIs();
    
    // 验证文档结构
    this.validateDocumentStructure();
    
    // 生成报告
    this.generateReport();
    
    // 输出结果
    console.log('\n📊 验证结果:');
    console.log(`❌ 错误: ${this.errors.length}`);
    console.log(`⚠️  警告: ${this.warnings.length}`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ 发现错误:');
      this.errors.forEach(error => {
        console.log(`  - ${error.file}: ${error.message}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️  发现警告:');
      this.warnings.forEach(warning => {
        console.log(`  - ${warning.file}: ${warning.message}`);
      });
    }
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('✅ 所有文档验证通过');
    }
    
    // 返回验证结果
    return {
      success: this.errors.length === 0,
      errors: this.errors,
      warnings: this.warnings
    };
  }
}

// 主函数
async function main() {
  const validator = new DocumentValidator();
  const result = await validator.validate();
  
  // 如果有错误，退出码为 1
  process.exit(result.success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = DocumentValidator;
