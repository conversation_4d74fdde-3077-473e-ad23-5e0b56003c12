import { ObjectType, Field, ID, InputType, PartialType, Int } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import Graph<PERSON>JSON from 'graphql-type-json';
import { TenantDTO } from '../../../entity/tenant.dto';
import { AdAccountDTO } from './ad-account.dto';
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsDefined, IsArray } from 'class-validator';
import { ObjectType as GQLObjectType, Field as GQLField } from '@nestjs/graphql';

@ObjectType('AudienceRegion')
@InputType('AudienceRegionInput')
export class AudienceRegion {
  @Field({ nullable: true })
  id?: string;
  @Field({ nullable: true })
  name?: string;
}

@ObjectType('AudienceCity')
@InputType('AudienceCityInput')
export class AudienceCity {
  @Field({ nullable: true })
  id?: string;
  @Field({ nullable: true })
  name?: string;
}

@ObjectType('AudienceGeoLocation')
@InputType('AudienceGeoLocationInput')
export class AudienceGeoLocation {
  @Field(() => [String], { nullable: true })
  countries?: string[];
  @Field(() => [AudienceRegion], { nullable: true })
  regions?: AudienceRegion[];
  @Field(() => [AudienceCity], { nullable: true })
  cities?: AudienceCity[];
}

@ObjectType('AudienceInterest')
@InputType('AudienceInterestInput')
export class AudienceInterestInput {
  @Field()
  id: string;
  @Field({ nullable: true })
  name?: string;
}

@ObjectType('AudienceBehavior')
@InputType('AudienceBehaviorInput')
export class AudienceBehaviorInput {
  @Field()
  id: string;
  @Field({ nullable: true })
  name?: string;
}

@ObjectType('AudienceDTO')
export class AudienceDTO {
  @IDField(() => ID)
  id: string;

  @FilterableField()
  audienceId: string;

  @FilterableField({ nullable: true })
  @Field({ nullable: true })
  name?: string;

  @Field(() => AudienceGeoLocation, { nullable: true })
  geo_locations?: AudienceGeoLocation;

  @Field(() => AudienceGeoLocation, { nullable: true })
  excluded_geo_locations?: AudienceGeoLocation;

  @Field(() => [Number], { nullable: true })
  locales?: number[];

  @Field({ nullable: true })
  gender?: number;

  @Field({ nullable: true })
  age_min?: number;

  @Field({ nullable: true })
  age_max?: number;

  @Field(() => [AudienceInterestInput], { nullable: true })
  interests?: AudienceInterestInput[];

  @Field(() => [AudienceBehaviorInput], { nullable: true })
  behaviors?: AudienceBehaviorInput[];

  @Field(() => GraphQLJSON, { nullable: true })
  custom_audiences?: any;

  @FilterableField({ nullable: true })
  notes?: string;

  @FilterableField({ nullable: true })
  approximateCount?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  raw?: any;

  @Field(() => [String], { nullable: true })
  platform?: string[];

  @FilterableField()
  get tenantId(): string {
    // 兼容entity为对象或字符串
    // @ts-ignore
    return this.tenant && typeof this.tenant === 'object' ? this.tenant.id : (this.tenant || '');
  }

  @FilterableField({ nullable: true })
  get tenantName(): string {
    // @ts-ignore
    return this.tenant && typeof this.tenant === 'object' ? this.tenant.name : '';
  }

  // @Field(() => AdAccountDTO, { nullable: true })
  // adAccount?: AdAccountDTO;

  // @Field(() => TenantDTO, { nullable: true })
  // tenant?: TenantDTO;
}

@InputType('CreateAudience')
export class CreateAudience {
  @Field(() => ID)
  @IsString()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  audienceId?: string;

  @Field()
  @IsString()
  @IsNotEmpty({ message: '名称必填' })
  @IsDefined({ message: '名称必填' })
  name: string;

  @Field(() => AudienceGeoLocation)
  @IsNotEmpty({ message: '地理位置必填' })
  geo_locations: AudienceGeoLocation;

  @Field(() => AudienceGeoLocation, { nullable: true })
  @IsOptional()
  excluded_geo_locations?: AudienceGeoLocation;

  @Field(() => [Number], { nullable: true })
  @IsNotEmpty({ message: '语言必填' })
  locales: number[];

  @Field({ nullable: true })
  @IsOptional()
  gender?: number;

  @Field({ nullable: true })
  @IsOptional()
  age_min?: number;

  @Field({ nullable: true })
  @IsOptional()
  age_max?: number;

  @Field(() => [AudienceInterestInput], { nullable: true })
  @IsOptional()
  interests?: AudienceInterestInput[];

  @Field(() => [AudienceBehaviorInput], { nullable: true })
  @IsOptional()
  behaviors?: AudienceBehaviorInput[];

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  custom_audiences?: any;

  @Field({ nullable: true })
  @IsOptional()
  notes?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  raw?: any;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  platform?: string[];
}

@InputType('CreateOneAudienceInput')
export class CreateOneAudienceInput {
  @Field(() => CreateAudience)
  @IsNotEmpty()
  audience: CreateAudience;
}

@InputType('DeleteOneAudienceInput')
export class DeleteOneAudienceInput {
  @Field(() => ID)
  @IsString()
  id: string;
}

@ObjectType('AudienceDeleteResult')
export class AudienceDeleteResponse {
  @Field(() => ID, { nullable: true })
  id?: string;
  @Field({ nullable: true })
  name?: string;
}

@InputType('UpdateAudienceInput')
export class UpdateAudienceInput {
  @Field(() => ID)
  id: string;

  @Field(() => ID)
  @IsString()
  audienceId: string;

  @Field(() => ID)
  @IsString()
  @IsNotEmpty({ message: '名称必填' })
  @IsDefined({ message: '名称必填' })
  name: string;

  @Field(() => ID)
  @IsOptional()
  @IsNumber()
  approximateCount?: number;

  @Field(() => ID)
  @IsString()
  adAccountId: string;

  @Field(() => ID)
  @IsString()
  gender: string;

  @Field(() => ID)
  @IsOptional()
  @IsNumber()
  minAge: number;

  @Field(() => ID)
  @IsOptional()
  @IsNumber()
  maxAge: number;

  @Field(() => [AudienceInterestInput], { nullable: true })
  @IsOptional()
  interests?: AudienceInterestInput[];

  @Field(() => ID)
  @IsOptional()
  @IsString()
  notes?: string;

  @Field(() => ID)
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '平台必填' })
  @IsDefined({ message: '平台必填' })
  platform?: string[];

  @Field(() => ID)
  @IsOptional()
  raw?: any;

  @Field(() => [AudienceBehaviorInput], { nullable: true })
  @IsOptional()
  behaviors?: AudienceBehaviorInput[];
}

@ObjectType('AudiencePageInfo')
export class AudiencePageInfo {
  @Field()
  hasNextPage: boolean;
  @Field()
  hasPreviousPage: boolean;
  @Field({ nullable: true })
  startCursor?: string;
  @Field({ nullable: true })
  endCursor?: string;
}

@ObjectType('AudienceCustomEdge')
export class AudienceCustomEdge {
  @Field(() => AudienceDTO)
  node: AudienceDTO;
  @Field()
  cursor: string;
}

@ObjectType()
export class AudienceCustomConnection {
  @Field(() => [AudienceCustomEdge])
  edges: AudienceCustomEdge[];
  @Field(() => AudiencePageInfo)
  pageInfo: AudiencePageInfo;
  @Field(() => Int)
  totalCount: number;
}

@InputType()
export class AudienceCustomFilter {
  @Field({ nullable: true })
  id?: string;

  @Field({ nullable: true })
  name?: string;
}

@InputType()
export class AudienceCursorPaging {
  @Field({ nullable: true })
  first?: number;
  @Field({ nullable: true })
  after?: string;
}

@InputType()
export class AudienceCustomSort {
  @Field({ nullable: true })
  field?: string;
  @Field({ nullable: true })
  direction?: string;
}

@ObjectType('Option')
export class OptionDTO {
  @Field(() => ID)
  id: string;
  @Field()
  name: string;
}
