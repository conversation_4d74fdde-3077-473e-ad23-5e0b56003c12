import { Resolver, Query, Args, Mutation, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LandingPageDTO, CreateLandingPageInput, UpdateLandingPageInput } from '../dto/landing-page.dto';
import { LandingPage } from '../landing-page.entity';
import { LandingPageService } from '../landing-page.service';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../../modules/auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => LandingPageDTO)
export class LandingPageResolver {
    constructor(
        private readonly landingPageService: LandingPageService,
        @InjectRepository(LandingPage)
        private readonly landingPageRepository: Repository<LandingPage>
    ) { }

    @Query(() => [LandingPageDTO], { name: 'getLandingPages' })
    async getLandingPages(@Context() ctx): Promise<LandingPageDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        if (isSuperAdmin) {
            return this.landingPageRepository.find({ order: { createdAt: 'DESC' } });
        }
        return this.landingPageService.findAll(tenantId);
    }

    @Query(() => LandingPageDTO, { nullable: true })
    async getLandingPage(@Args('code') code: string, @Context() ctx): Promise<LandingPageDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        if (isSuperAdmin) {
            return this.landingPageRepository.findOne({ where: { code } });
        }
        return this.landingPageRepository.findOne({ where: { code, tenantId } });
    }

    @Mutation(() => LandingPageDTO)
    async createLandingPage(
        @Args('title') title: string,
        @Args('url') url: string,
        @Args('slogan') slogan: string,
        @Args({ name: 'appIcon', type: () => [String], nullable: true }) appIcon: string[],
        @Args({ name: 'background', type: () => [String], nullable: true }) background: string[],
        @Args({ name: 'description', type: () => String, nullable: true }) description: string,
        @Args('star') star: number,
        @Args({ name: 'promoImages', type: () => [String], nullable: true }) promoImages: string[],
        @Args({ name: 'creator', type: () => String, nullable: true }) creator: string,
        @Context() ctx
    ): Promise<LandingPageDTO> {
        return this.landingPageService.create({ title, url, slogan, appIcon, background, description, star, promoImages, creator }, ctx);
    }

    @Mutation(() => LandingPageDTO)
    async updateLandingPage(
        @Args('id') id: string,
        @Args('input') input: UpdateLandingPageInput,
        @Context() ctx
    ): Promise<LandingPageDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const entity = isSuperAdmin
            ? await this.landingPageRepository.findOne({ where: { id } })
            : await this.landingPageRepository.findOne({ where: { id, tenantId } });
        if (!entity) throw new Error('未找到该落地页');
        Object.assign(entity, input);
        return this.landingPageRepository.save(entity);
    }

    @Mutation(() => Boolean)
    async deleteLandingPage(@Args('id') id: string, @Context() ctx): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx;
        const entity = isSuperAdmin
            ? await this.landingPageRepository.findOne({ where: { id } })
            : await this.landingPageRepository.findOne({ where: { id, tenantId } });
        if (!entity) throw new Error('未找到该落地页');
        await this.landingPageRepository.delete({ id });
        return true;
    }
} 