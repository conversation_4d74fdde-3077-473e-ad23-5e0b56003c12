# 组件路径  /pages/management/system/group/index

## 前端

- **实际路径**：slash-admin/src/pages/management/system/group/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，群组管理表单与列表，含增删改查、分页、搜索、弹窗、权限树等）
- **主要依赖API定义**：@/api/services/groupService

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取群组列表    | query    | groups                 | filter, paging, sorting | id, name, description, belongTo, contactInfo, memberCount, createTime, status, permissions | 支持分页、搜索、过滤 |
| 新增群组        | mutation | createGroup            | input: GroupInput | id, name, ... | 新建群组，id为UUID |
| 编辑群组        | mutation | updateGroup            | input: GroupInput | id, name, ... | 编辑指定群组 |
| 删除群组        | mutation | deleteGroup            | id: string    | id       | 删除指定群组 |

#### 主要参数结构说明

- **GroupInput**（用于新增/编辑）  
  ```ts
  {
    id?: string; // 群组ID，编辑时必填
    name: string; // 群组名称
    description?: string; // 描述
    belongTo?: string; // 归属人
    contactInfo?: string; // 联系方式
    permissions?: any; // 权限树结构
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | id          | string    | 主键，UUID                 |
  | name        | string    | 群组名称                   |
  | description | string    | 描述                       |
  | belongTo    | string    | 归属人                     |
  | contactInfo | string    | 联系方式                   |
  | memberCount | number    | 成员数                     |
  | createTime  | Date      | 创建时间                   |
  | status      | string    | 状态（正常/禁用）          |
  | permissions | object    | 权限树结构                 |

### 前端表单/组件说明

- 支持群组的增删改查，表单字段与上方GroupInput结构一致
- 支持权限树配置（如系统管理、广告管理等）
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）
- 支持群组归属人、联系方式、描述等字段的录入

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（group，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，群组唯一标识         | PRIMARY KEY  |
| name        | varchar     | 群组名称                   | NOT NULL     |
| description | varchar     | 描述                       | 可空         |
| belongTo    | varchar     | 归属人                     | 可空         |
| contactInfo | varchar     | 联系方式                   | 可空         |
| memberCount | int         | 成员数                     | 可空         |
| createTime  | timestamp   | 创建时间                   | 默认now()    |
| status      | varchar     | 状态（正常/禁用）          | 默认正常     |
| permissions | jsonb       | 权限树结构                 | 可空         |
| ...         | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query groups / mutation createGroup(input) / ...
    后端->>数据库: 操作 group 表
    数据库-->>后端: 返回群组数据
    后端-->>前端: 返回群组数据
```

---

## 变更历史/注意事项

- 群组名称需唯一，所有操作需校验唯一性
- 支持权限树结构扩展，建议用jsonb存储
- 删除群组时需校验是否被用户、资源等引用
- 复杂业务字段建议在表结构中用jsonb扩展