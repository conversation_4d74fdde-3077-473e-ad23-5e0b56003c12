import { Controller, Get, Post, Put, Delete, Body, Param, Query, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Material } from './entities/material.entity';
import { Tenant } from '../../entity/tenant.entity';

@Controller('ad-platform/material')
export class MaterialController {
  constructor(
    @InjectRepository(Material) private readonly materialRepo: Repository<Material>,
    @InjectRepository(Tenant) private readonly tenantRepo: Repository<Tenant>,
  ) { }

  @Get()
  async list(@Query('tenantId') tenantId: string, @Query('page') page = 1, @Query('pageSize') pageSize = 20) {
    if (!tenantId) throw new BadRequestException('tenantId required');
    const [data, total] = await this.materialRepo.findAndCount({
      where: { tenant: { id: tenantId } },
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { name: 'ASC' },
    });
    return { data, total, page, pageSize };
  }

  @Post()
  async create(@Body() body: any) {
    if (!body.tenantId || !body.type || !body.name) throw new BadRequestException('tenantId, type, name required');
    const tenant = await this.tenantRepo.findOne({ where: { id: body.tenantId } });
    if (!tenant) throw new BadRequestException('tenant not found');
    if (tenant.features && tenant.features.maxMaterials) {
      const materialCount = await this.materialRepo.count({ where: { tenant } });
      if (materialCount >= tenant.features.maxMaterials) {
        throw new BadRequestException('当前套餐素材数已达上限，请升级套餐');
      }
    }
    const material = this.materialRepo.create({ ...body, tenant });
    await this.materialRepo.save(material);
    return material;
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() body: any) {
    const material = await this.materialRepo.findOne({ where: { id } });
    if (!material) throw new BadRequestException('Material not found');
    Object.assign(material, body);
    await this.materialRepo.save(material);
    return material;
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    const material = await this.materialRepo.findOne({ where: { id } });
    if (!material) throw new BadRequestException('Material not found');
    await this.materialRepo.remove(material);
    return { success: true };
  }

  @Post('batch')
  async batchCreate(@Body() body: { tenantId: string; materialList: any[] }) {
    if (!body.tenantId || !Array.isArray(body.materialList)) throw new BadRequestException('tenantId, materialList required');
    const tenant = await this.tenantRepo.findOne({ where: { id: body.tenantId } });
    if (!tenant) throw new BadRequestException('tenant not found');
    const materials = body.materialList.map(item => this.materialRepo.create({ ...item, tenant }));
    await this.materialRepo.save((Array.isArray(materials[0]) ? materials.flat(1) : materials) as any[]);
    return { inserted: materials.length };
  }
}
