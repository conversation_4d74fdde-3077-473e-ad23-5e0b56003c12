# 组件路径 /pages/management/ad-account/index

## 前端

- **实际路径**：slash-admin/src/pages/management/ad-account/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，Tab页）
  - tabs/account-list-tab.tsx（账户列表）
  - tabs/batch-upload-tab.tsx（批量上传）
  - tabs/oauth-tab.tsx（OAuth授权）
  - tabs/group-manage-tab.tsx（分组管理）
- **主要依赖API定义**：slash-admin/src/api/adAccount.graphql.ts

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取账户列表    | query    | adAccounts             | filter: AdAccountFilterInput | id, accountId, account, status, fbStatus, riskLevel, group, oauth, accessToken, createdAt, tag, channel | 支持分页、搜索、过滤 |
| 新增账户        | mutation | createOneAdAccount     | input: CreateOneAdAccountInput | id, accountId, account, status, riskLevel, group, oauth, createdAt, tag, channel | 新建账户，id为UUID |
| 删除账户        | mutation | deleteOneAdAccount     | accountId: String | id, accountId | 删除指定账户 |
| 批量上传        | mutation | batchUploadAdAccounts  | accounts: [AdAccountInput!], platform: String | account, status | 批量上传Excel，校验并导入 |
| 获取未授权账户  | query    | unauthorizedAdAccounts | 无           | accountId, status | 获取待OAuth授权账户 |
| 获取OAuth链接   | query    | getAdAccountOAuth2Url  | account: String | getAdAccountOAuth2Url | 获取Facebook OAuth授权链接 |

#### 主要参数结构说明

- **AdAccountInput**（用于批量上传/新增）  
  ```ts
  {
    accountId?: string; // 广告平台账户ID
    account: string;    // 账户名称
    password?: string;  // 密码
    tag?: string;       // 标签
    channel?: string;   // 渠道
    holder?: string;    // 持有人
    adNumber?: string;  // 广告号
    remark?: string;    // 备注
  }
  ```
- **AdAccountFilterInput**（用于查询过滤）
  ```ts
  {
    name?: string;
    platform?: string;
    tenantId?: string;
    fbStatus?: string;
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | id          | string    | 主键，UUID                 |
  | platform    | string    | 平台类型                   |
  | accountId   | string    | 广告平台账户ID             |
  | account     | string    | 账户名称                   |
  | login       | string    | 登录账号（如邮箱）         |
  | password    | string    | 登录密码                   |
  | accessToken | string    | 平台鉴权token              |
  | status      | string    | 账户状态                   |
  | riskLevel   | string    | 风险等级                   |
  | group       | string    | 分组                       |
  | oauth       | object    | OAuth信息                  |
  | createdAt   | Date      | 创建时间                   |
  | tag         | string    | 标签                       |
  | channel     | string    | 渠道                       |
  | holder      | string    | 持有人                     |
  | adNumber    | string    | 广告号                     |
  | remark      | string    | 备注                       |
  | updatedAt   | Date      | 更新时间                   |
  | fbStatus    | string    | Facebook账户状态           |

### 前端表单/组件说明

- 支持账户的增删改查，表单字段与上方AdAccountInput结构一致
- 支持批量Excel上传，自动校验字段
- 支持OAuth授权流程，自动获取授权链接
- 支持账户分组管理
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（ad_account，详见 ad_backend/src/modules/ad-platform/entities/ad-account.entity.ts）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，账户唯一标识         | PRIMARY KEY  |
| platform    | varchar(32) | 平台类型                   | NOT NULL     |
| accountId   | varchar(64) | 广告平台账户ID             | UNIQUE, NOT NULL |
| account     | varchar(128)| 账户名称                   | NOT NULL     |
| login       | varchar(128)| 登录账号                   | 可空         |
| password    | varchar(128)| 登录密码                   | 可空         |
| accessToken | text        | 平台鉴权token              | 可空         |
| tenant      | uuid        | 租户ID（外键）             | NOT NULL     |
| raw         | jsonb       | 原始账户数据               | 可空         |
| status      | varchar(32) | 账户状态                   | 默认active   |
| riskLevel   | varchar(32) | 风险等级                   | 可空         |
| group       | varchar(64) | 分组                       | 可空         |
| oauth       | jsonb       | OAuth信息                  | 可空         |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| tag         | varchar(128)| 标签                       | 可空         |
| channel     | varchar(128)| 渠道                       | 可空         |
| holder      | varchar(128)| 持有人                     | 可空         |
| adNumber    | varchar(128)| 广告号                     | 可空         |
| remark      | varchar(255)| 备注                       | 可空         |
| updatedAt   | timestamp   | 更新时间                   | 自动生成     |
| fbStatus    | varchar(32) | Facebook账户状态           | 可空         |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query adAccounts / mutation createOneAdAccount(input) / ...
    后端->>数据库: 操作 ad_account 表
    数据库-->>后端: 返回账户数据
    后端-->>前端: 返回账户数据
```

---

## 变更历史/注意事项

- accountId 字段唯一，所有操作需校验唯一性
- 支持多平台扩展（platform 字段）
- OAuth 授权需配合 Facebook App 配置
- 批量上传需严格校验字段格式
- 删除账户时需校验是否被广告系列等引用
- 账户状态（status）和 Facebook 状态（fbStatus）需同步平台实际状态
- 账户分组、标签、渠道等字段支持灵活扩展