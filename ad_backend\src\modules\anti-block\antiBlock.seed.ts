import { Connection } from 'typeorm';
import { AntiBlock } from './antiBlock.entity';

export const createAntiBlock = async (
	connection: Connection
): Promise<AntiBlock[]> => {
	const employees = new Array<AntiBlock>();

	const employee1 = new AntiBlock();
	employee1.url = 'Ruslan';
	employees.push(employee1);

	const employee2 = new AntiBlock();
	employee2.url = 'Rahul';
	employees.push(employee2);

	return await insertAntiBlock(connection, employees);
};

const insertAntiBlock = async (
	connection: Connection,
	employees: AntiBlock[]
) => {
	const repo = connection.getRepository(AntiBlock);

	await Promise.all(
		employees.map(async (emp) => {
			const existed = await repo.findOne({
				where: {
					url: emp.url,
				},
			});

			if (existed) {
				emp.id = existed.id;
			}
		})
	);

	return await repo.save(employees);
};
