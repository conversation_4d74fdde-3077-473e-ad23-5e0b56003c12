import { gql } from '@apollo/client';

export const ROUTES_QUERY = gql`
  query GetRoutes {
    getRoutes {
      id
      parentId
      name
      path
      component
      icon
      order
      type
      status
      isHidden
      description
      createdAt
      updatedAt
    }
  }
`;

export const CREATE_ROUTE = gql`
  mutation CreateRoute($input: CreateRouteInput!) {
    createOneRoute(input: { route: $input }) {
      id
      parentId
      name
      path
      component
      icon
      order
      type
      status
      isHidden
      description
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_ROUTE = gql`
  mutation UpdateRoute($input: UpdateOneRouteInput!) {
    updateOneRoute(input: $input) {
      id
      parentId
      name
      path
      component
      icon
      order
      type
      status
      isHidden
      description
      createdAt
      updatedAt
    }
  }
`;

export const DELETE_ROUTE = gql`
  mutation DeleteRoute($id: ID!) {
    deleteOneRoute(input: { id: $id }) {
      id
      parentId
      name
      path
      component
      icon
      order
      type
      status
      isHidden
      description
      createdAt
      updatedAt
    }
  }
`; 