import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { useQuery } from '@apollo/client';
import { GET_AUDIENCE_INSIGHTS } from '@/api/audienceInsights.graphql';

type AudienceInsightsProps = {
  startDate: string;
  endDate: string;
};

const AudienceInsights = ({ startDate, endDate }: AudienceInsightsProps) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(GET_AUDIENCE_INSIGHTS, {
    variables: { startDate, endDate },
    fetchPolicy: 'network-only',
  });
  // 兜底空数据，保证hooks顺序一致
  const ageDistribution = data?.audienceInsights?.ageDistribution ?? [];
  const genderDistribution = data?.audienceInsights?.genderDistribution ?? [];
  const locationDistribution = data?.audienceInsights?.locationDistribution ?? [];
  const interestDistribution = data?.audienceInsights?.interestDistribution ?? [];

  const ageChartOptions = useChart({
    labels: ageDistribution.map((item: any) => item.name),
    legend: { position: 'bottom' },
    tooltip: { fillSeriesColor: false },
    stroke: { show: false },
    colors: ['#4ade80', '#3b82f6', '#f59e0b', '#a855f7', '#ec4899'],
    plotOptions: { pie: { donut: { size: '70%' } } },
  });
  const genderChartOptions = useChart({
    labels: genderDistribution.map((item: any) => item.name),
    legend: { position: 'bottom' },
    tooltip: { fillSeriesColor: false },
    stroke: { show: false },
    colors: ['#ec4899', '#3b82f6'],
    plotOptions: { pie: { donut: { size: '70%' } } },
  });
  const locationChartOptions = useChart({
    xaxis: { categories: locationDistribution.map((item: any) => item.name) },
    yaxis: { labels: { formatter: (value: number) => `${value}%` } },
    tooltip: { y: { formatter: (value: number) => `${value}%` } },
    colors: ['#3b82f6'],
    plotOptions: { bar: { borderRadius: 4, columnWidth: '60%' } },
  });
  const interestChartOptions = useChart({
    xaxis: { categories: interestDistribution.map((item: any) => item.name) },
    yaxis: { labels: { formatter: (value: number) => `${value}%` } },
    tooltip: { y: { formatter: (value: number) => `${value}%` } },
    colors: ['#4ade80'],
    plotOptions: { bar: { borderRadius: 4, columnWidth: '60%' } },
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;
  if (!data?.audienceInsights) return <div>{t('pages.key_1177')}</div>;

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">{t('pages.key_418')}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="demographics">
          <TabsList className="mb-4">
            <TabsTrigger value="demographics">{t('pages.key_78')}</TabsTrigger>
            <TabsTrigger value="interests">{t('pages.key_191')}</TabsTrigger>
          </TabsList>

          <TabsContent value="demographics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="h-[250px]">
                <h3 className="text-sm font-medium mb-2 text-center">{t('pages.key_771')}</h3>
                <Chart
                  type="donut"
                  series={ageDistribution.map((item: any) => item.value)}
                  options={ageChartOptions}
                  height={220}
                />
              </div>

              <div className="h-[250px]">
                <h3 className="text-sm font-medium mb-2 text-center">{t('pages.key_904')}</h3>
                <Chart
                  type="donut"
                  series={genderDistribution.map((item: any) => item.value)}
                  options={genderChartOptions}
                  height={220}
                />
              </div>
            </div>

            <div className="h-[250px]">
              <h3 className="text-sm font-medium mb-2 text-center">{t('pages.key_545')}</h3>
              <Chart
                type="bar"
                series={[{ data: locationDistribution.map((item: any) => item.value) }]}
                options={locationChartOptions}
                height={220}
              />
            </div>
          </TabsContent>

          <TabsContent value="interests" className="h-[300px]">
            <h3 className="text-sm font-medium mb-2 text-center">{t('pages.key_190')}</h3>
            <Chart
              type="bar"
              series={[{ data: interestDistribution.map((item: any) => item.value) }]}
              options={interestChartOptions}
              height={280}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AudienceInsights;
