import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { VccCard } from './entities/vcc-card.entity';
import { VccTransaction } from './entities/vcc-transaction.entity';
import { AdAccount } from '../ad-platform/entities/ad-account.entity';
import { Tenant } from '../../entity/tenant.entity';
import { AuthModule } from '../auth/auth.module';
import { VccCardResolver } from './vcc-card.resolver';
import { VccCardController } from './vcc-card.controller';
import { VccCardService } from './vcc-card.service';
import { VccCardSeed } from './vcc-card.seed';
import { VccTransactionResolver } from './resolvers/vcc-transaction.resolver';
import { VccTransactionController } from './controllers/vcc-transaction.controller';
import { VccTransactionService } from './services/vcc-transaction.service';
import { FacebookTransactionProxyService } from './services/facebook-transaction-proxy.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            VccCard,
            VccTransaction,
            AdAccount,
            Tenant
        ]),
        HttpModule,
        AuthModule
    ],
    providers: [
        VccCardResolver,
        VccCardService,
        VccCardSeed,
        VccTransactionResolver,
        VccTransactionService,
        FacebookTransactionProxyService
    ],
    controllers: [
        VccCardController,
        VccTransactionController
    ],
    exports: [
        VccCardService,
        VccCardSeed,
        VccTransactionService,
        FacebookTransactionProxyService
    ]
})
export class VccCardModule { } 