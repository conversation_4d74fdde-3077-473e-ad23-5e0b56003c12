import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';

@Entity('vcc')
export class Vcc {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '卡号' })
  @Index({ unique: true })
  @Column({ length: 64 })
  cardNumber: string;

  @ApiProperty({ description: '持卡人' })
  @Column({ length: 64 })
  cardHolder: string;

  @ApiProperty({ description: '有效期' })
  @Column({ length: 8 })
  expiry: string;

  @ApiProperty({ description: 'CVV' })
  @Column({ length: 8 })
  cvv: string;

  @ApiProperty({ description: '绑定账户', required: false })
  @Column({ length: 128, nullable: true })
  bindAccount?: string;

  @ApiProperty({ description: '状态' })
  @Column({ length: 32, default: 'active' })
  status: string;

  @ApiProperty({ description: '租户' })
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始数据', required: false })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;
}
