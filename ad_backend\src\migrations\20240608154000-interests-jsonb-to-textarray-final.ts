import { MigrationInterface, QueryRunner } from "typeorm";

export class InterestsJsonbToTextarrayFinal20240608154000 implements MigrationInterface {
    name = 'InterestsJsonbToTextarrayFinal20240608154000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 只对 jsonb 类型的 interests 做迁移
        const jsonbRows: { id: string }[] = await queryRunner.query(`
            SELECT id FROM "audience" WHERE pg_typeof("interests")::text = 'jsonb'
        `);
        for (const row of jsonbRows) {
            await queryRunner.query(`
                UPDATE "audience"
                SET "interests" = (
                    SELECT array_agg(elem->>'id')
                    FROM jsonb_array_elements("interests") elem
                    WHERE elem ? 'id'
                )
                WHERE id = $1
            `, [row.id]);
        }
        // 如果还有 jsonb 类型的 interests，全部置空
        await queryRunner.query(`
            UPDATE "audience"
            SET "interests" = '{}'
            WHERE pg_typeof("interests")::text = 'jsonb'
        `);
        // 确保字段类型为 text[]
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "interests" TYPE text[] USING "interests"::text[];`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 不做回滚
    }
} 