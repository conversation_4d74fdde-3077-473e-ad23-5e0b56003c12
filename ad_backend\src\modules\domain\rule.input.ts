import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class RuleConditionInput {
    @Field()
    field: string;

    @Field()
    operator: string;

    @Field()
    value: string;

    @Field({ nullable: true })
    unit?: string;
}

@InputType()
export class RuleInput {
    @Field()
    name: string;

    @Field()
    action: string;

    @Field()
    object: string;

    @Field({ nullable: true })
    scheduleType?: string;

    @Field({ nullable: true })
    timeRange?: string;

    @Field({ nullable: true })
    status?: string;

    @Field(() => [RuleConditionInput])
    conditions: RuleConditionInput[];

    @Field(() => [String], { nullable: true })
    custom_schedule?: string[];
} 