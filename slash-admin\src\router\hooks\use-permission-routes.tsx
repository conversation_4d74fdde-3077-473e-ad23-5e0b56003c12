import { Icon } from "@/components/icon";
import { LineLoading } from "@/components/loading";
import { useUserInfo } from "@/store/userStore";
import { Badge } from "@/ui/badge";
import { Suspense, lazy, useMemo } from "react";
import { Navigate, Outlet } from "react-router";
import type { AppRouteObject } from "#/router";

// 获取所有页面组件
const PAGES = import.meta.glob([
	"/src/pages/**/*.tsx",
	"!/src/pages/**/components/**"
]);

// 调试：打印所有可用的组件路径
console.log('Available component paths:', Object.keys(PAGES));

interface RouteItem {
	id: string;
	parentId: string | null;
	name: string;
	path: string;
	component: string | null;
	icon: string | null;
	order: number;
	type: 'menu' | 'page';
	status: 'enabled' | 'disabled';
	isHidden: boolean;
	description: string;
	children?: RouteItem[];
}

// Components
function NewFeatureTag() {
	return (
		<Badge variant="info">
			<Icon icon="solar:bell-bing-bold-duotone" size={14} />
			New
		</Badge>
	);
}

const loadComponentFromPath = (path: string) => {
	if (!path) return undefined;

	// 1. 标准化路径
	let normalizedPath = path
		// 确保路径以 .tsx 结尾
		.replace(/(?<!\.tsx)$/, '.tsx')
		// 添加 /src 前缀
		.replace(/^\/pages/, '/src/pages')
		.replace(/^(?!\/src)/, '/src');

	// 2. 尝试不同的路径变体
	const pathVariants = [
		normalizedPath,
		// 尝试添加 /index.tsx
		normalizedPath.replace(/\.tsx$/, '/index.tsx'),
		// 尝试移除 /index
		normalizedPath.replace(/\/index\.tsx$/, '.tsx')
	];

	console.log('Trying to load component with paths:', pathVariants);

	// 3. 查找匹配的组件路径
	const componentPath = Object.keys(PAGES).find(
		(key) => pathVariants.some(variant =>
			key.toLowerCase() === variant.toLowerCase()
		)
	);

	if (!componentPath) {
		console.warn(`Component not found for path: ${path}`);
		console.warn('Attempted paths:', pathVariants);
		return undefined;
	}

	console.log('Found component path:', componentPath);
	return PAGES[componentPath];
};

// 处理路由路径，移除父路由路径部分
const processRoutePath = (path: string, parentPath?: string): string => {
	// 移除开头的斜杠
	let processedPath = path.replace(/^\//, '');

	// 如果有父路由路径，移除父路由路径部分
	if (parentPath) {
		const parentPathWithoutSlash = parentPath.replace(/^\//, '');
		processedPath = processedPath.replace(new RegExp(`^${parentPathWithoutSlash}/?`), '');
	}

	return processedPath;
};

// Route Transformers
const createBaseRoute = (route: RouteItem, parentPath?: string): AppRouteObject => {
	const path = processRoutePath(route.path, parentPath);

	console.log(`Creating route for path: ${path}, original path: ${route.path}, parent path: ${parentPath}`);

	const baseRoute: AppRouteObject = {
		path,
		meta: {
			key: route.path,
			label: route.name,
			hideMenu: route.isHidden,
			disabled: route.status === 'disabled',
		},
		order: route.order,
	};

	if (route.icon) {
		baseRoute.meta!.icon = route.icon;
	}

	return baseRoute;
};

const createMenuRoute = (route: RouteItem, parentPath?: string): AppRouteObject => {
	const baseRoute = createBaseRoute(route, parentPath);

	if (route.type === 'menu') {
		baseRoute.element = (
			<Suspense fallback={<LineLoading />}>
				<Outlet />
			</Suspense>
		);

		if (route.children && route.children.length > 0) {
			baseRoute.children = transformRoutesToAppRoutes(route.children, route.path);
			// 添加默认重定向到第一个子路由
			const firstChild = route.children[0];
			const redirectPath = processRoutePath(firstChild.path, route.path);
			baseRoute.children.unshift({
				index: true,
				element: <Navigate to={redirectPath} replace />,
			});
		}
	} else if (route.component) {
		const componentLoader = loadComponentFromPath(route.component);
		if (componentLoader) {
			const Element = lazy(componentLoader as any);
			baseRoute.element = (
				<Suspense fallback={<LineLoading />}>
					<Element />
				</Suspense>
			);
		} else {
			console.error(`Failed to load component for route: ${route.path}, component path: ${route.component}`);
			baseRoute.element = (
				<div className="p-4 text-red-500">
					Component not found: {route.component}
				</div>
			);
		}
	}

	return baseRoute;
};

function transformRoutesToAppRoutes(routes: RouteItem[], parentPath?: string): AppRouteObject[] {
	return routes
		.filter(route => route.status === 'enabled')
		.sort((a, b) => a.order - b.order)
		.map(route => createMenuRoute(route, parentPath));
}

export function usePermissionRoutes() {
	const userInfo = useUserInfo();

	return useMemo(() => {
		if (!userInfo?.roles?.[0]?.routeList) {
			console.warn('No route list found in user info:', userInfo);
			return [];
		}

		const routes = userInfo.roles[0].routeList;
		console.log('Original routes from backend:', routes);

		const transformedRoutes = transformRoutesToAppRoutes(routes);
		console.log('Transformed routes:', transformedRoutes);

		return transformedRoutes;
	}, [userInfo]);
}
