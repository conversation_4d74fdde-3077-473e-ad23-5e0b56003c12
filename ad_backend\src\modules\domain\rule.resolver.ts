import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { RuleService } from './rule.service';
import { Rule } from './rule.entity';
import { RuleConditionInput } from './rule.input';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => Rule)
export class RuleResolver {
    constructor(private readonly ruleService: RuleService) { }

    @Query(() => [Rule])
    rules(@Context() ctx: any) {
        return this.ruleService.findAll();
    }

    @Mutation(() => Rule)
    createRule(
        @Args('name') name: string,
        @Args('action') action: string,
        @Args('object') object: string,
        @Args('scheduleType', { nullable: true }) scheduleType: string,
        @Args('custom_schedule', { type: () => [String], nullable: true }) custom_schedule: string[],
        @Args('timeRange', { nullable: true }) timeRange: string,
        @Args('status', { nullable: true }) status: string,
        @Args('conditions', { type: () => [RuleConditionInput] }) conditions: RuleConditionInput[],
        @Context() ctx: any
    ) {
        return this.ruleService.create({ name, action, object, scheduleType, custom_schedule, timeRange, status, conditions });
    }

    @Mutation(() => Rule)
    updateRule(
        @Args('id') id: string,
        @Args('name') name: string,
        @Args('action') action: string,
        @Args('object') object: string,
        @Args('scheduleType', { nullable: true }) scheduleType: string,
        @Args('custom_schedule', { type: () => [String], nullable: true }) custom_schedule: string[],
        @Args('timeRange', { nullable: true }) timeRange: string,
        @Args('status', { nullable: true }) status: string,
        @Args('conditions', { type: () => [RuleConditionInput] }) conditions: RuleConditionInput[],
        @Context() ctx: any
    ) {
        return this.ruleService.update({ id, name, action, object, scheduleType, custom_schedule, timeRange, status, conditions });
    }

    @Mutation(() => Boolean)
    deleteRule(
        @Args('id') id: string,
        @Context() ctx: any
    ) {
        return this.ruleService.delete(id);
    }
} 