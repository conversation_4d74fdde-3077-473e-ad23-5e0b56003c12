import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAudienceArrayLiteralSafe20240609193000 implements MigrationInterface {
    name = 'FixAudienceArrayLiteralSafe20240609193000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // interests
        await queryRunner.query(`UPDATE "audience" SET "interests" = NULL WHERE "interests"::text = '"[]"'`);
        await queryRunner.query(`UPDATE "audience" SET "interests" = '{}' WHERE "interests" IS NULL`);
        // behaviors
        await queryRunner.query(`UPDATE "audience" SET "behaviors" = NULL WHERE "behaviors"::text = '"[]"'`);
        await queryRunner.query(`UPDATE "audience" SET "behaviors" = '{}' WHERE "behaviors" IS NULL`);
        // platform
        await queryRunner.query(`UPDATE "audience" SET "platform" = NULL WHERE "platform"::text = '"[]"'`);
        await queryRunner.query(`UPDATE "audience" SET "platform" = '{}' WHERE "platform" IS NULL`);
        // locales
        await queryRunner.query(`UPDATE "audience" SET "locales" = NULL WHERE "locales"::text = '"[]"'`);
        await queryRunner.query(`UPDATE "audience" SET "locales" = '{}' WHERE "locales" IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // no revert
    }
} 