import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAdNumberToAdAccount20240530000300 implements MigrationInterface {
    name = 'AddAdNumberToAdAccount20240530000300'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "adNumber" character varying(128);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "adNumber";`);
    }
} 