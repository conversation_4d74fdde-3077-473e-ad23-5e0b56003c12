import { Injectable } from '@nestjs/common';
import { TypeOrmCrudService } from '@dataui/crud-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AntiBlock } from './antiBlock.entity';
import { Repository } from 'typeorm';

// Service responsible to register (and decommission) employees
@Injectable()
export class AntiBlockService extends TypeOrmCrudService<AntiBlock> {
	constructor(
		@InjectRepository(AntiBlock) readonly repo: Repository<AntiBlock>
	) {
		super(repo);
	}

	public async getActiveWithJobSearchCriteria(): Promise<AntiBlock[]> {
		return await this.repo.find({
			relations: [],
		});
	}

	async createWithTenant(data: Partial<AntiBlock>, tenantId: string) {
		return this.repo.save({ ...data, tenantId });
	}

	async updateWithTenant(id: number, data: Partial<AntiBlock>, tenantId: string) {
		return this.repo.update({ id, tenantId }, data);
	}
}
