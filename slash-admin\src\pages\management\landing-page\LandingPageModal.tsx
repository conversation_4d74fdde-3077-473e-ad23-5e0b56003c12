import { useTranslation } from 'react-i18next';
import React, { useEffect } from "react";
import { Modal, Form, Input, Upload, Button, message, InputNumber, Select } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { uploadToCloudflare } from '@/utils/upload';

export interface LandingPageFormValues {
    id?: string;
    title: string;
    url: string;
    slogan: string;
    appIcon: any[];
    background: any[];
    description: string;
    star: number;
    promoImages: any[];
}

interface LandingPageModalProps {
    open: boolean;
    loading?: boolean;
    initialValues?: Partial<LandingPageFormValues>;
    onCancel: () => void;
    onOk: (values: LandingPageFormValues) => void;
    activeDomains?: { id: string; name: string }[];
}

const normFile = (e: any) => Array.isArray(e) ? e : e?.fileList;

const uploadButton = (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <PlusOutlined style={{ fontSize: 24 }} />
    </div>
);

function toFileList(arr?: string[] | any[]): any[] {
    if (!arr) return [];
    if (arr.length && arr[0]?.uid) return arr;
    return arr.map((url, idx) => ({
        uid: idx + '',
        name: typeof url === 'string' ? url.split('/').pop() || `图片${idx + 1}` : url.name || `图片${idx + 1}`,
        status: 'done',
        url: typeof url === 'string' ? url : url.url,
    }));
}

const LandingPageModal: React.FC<LandingPageModalProps> = ({ open, loading, initialValues, onCancel, onOk, activeDomains }) => {
    const { t } = useTranslation();
    const [form] = Form.useForm<LandingPageFormValues>();
    const [uploading, setUploading] = React.useState(false);

    useEffect(() => {
        if (open) {
            form.resetFields();
            if (initialValues) {
                const patch = { ...initialValues };
                patch.appIcon = toFileList(initialValues.appIcon);
                patch.background = toFileList(initialValues.background);
                patch.promoImages = toFileList(initialValues.promoImages);
                form.setFieldsValue(patch);
            }
        }
    }, [open, initialValues, form]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            if ((values.promoImages?.length || 0) < 3) {
                message.error(t('pages.key_1023'));
                return;
            }
            onOk(values);
        } catch { }
    };

    // 预览图片
    const handlePreview = async (file: any) => {
        window.open(file.url || file.thumbUrl, '_blank');
    };

    // 通用自定义上传
    const customUpload = async ({ file, onSuccess, onError }: any) => {
        setUploading(true);
        try {
            // 这里 userId 可根据实际登录用户获取，这里用 "test-user" 占位
            const res = await uploadToCloudflare(file, 'test-user');
            onSuccess({ url: res.fileUrl || res.url || res.Key || res.data || res }, file);
        } catch (e) {
            onError?.(e);
        } finally {
            setUploading(false);
        }
    };

    return (
        <Modal
            title={initialValues?.id ? t('pages.key_1865') : t('pages.key_1154')}
            open={open}
            onCancel={onCancel}
            onOk={handleOk}
            confirmLoading={loading}
            destroyOnHidden
            maskClosable={false}
            width={700}
        >
            <Form form={form} layout="vertical" initialValues={initialValues}>
                <Form.Item name="title" label={t('pages.key_483')} rules={[{ required: true, message: t('pages.key_2181') }]}><Input placeholder={t('pages.key_2181')} /></Form.Item>
                <Form.Item name="url" label={t('pages.key_2014')} rules={[{ required: true, message: t('pages.key_2278') }]}>
                    <Select placeholder={t('pages.key_2257')}>
                        {(activeDomains || []).map(domain => (
                            <Select.Option key={domain.id} value={domain.name}>{domain.name}</Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item name="slogan" label={t('pages.key_1026')} rules={[{ required: true, message: t('pages.key_2201') }]}><Input placeholder={t('pages.key_2201')} /></Form.Item>
                <Form.Item name="appIcon" label="App图标" valuePropName="fileList" getValueFromEvent={normFile} rules={[{ required: true, message: t('pages.key_2146') }]} extra={<span style={{ color: '#999' }}>{t('pages.key_3001') || '建议1:1，支持jpg/png'}</span>}>
                    <Upload
                        listType="picture-card"
                        maxCount={1}
                        showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
                        customRequest={customUpload}
                        beforeUpload={file => {
                            const isImage = file.type.startsWith('image/');
                            if (!isImage) {
                                message.error(t('pages.key_2149'));
                                return Upload.LIST_IGNORE;
                            }
                            return true;
                        }}
                        onPreview={handlePreview}
                        disabled={uploading}
                    >
                        {uploadButton}
                    </Upload>
                </Form.Item>
                <Form.Item name="background" label={t('pages.key_1900')} valuePropName="fileList" getValueFromEvent={normFile} rules={[{ required: true, message: t('pages.key_2151') }]} extra={<span style={{ color: '#999' }}>{t('pages.key_3002') || '无大小限制，支持jpg/png'}</span>}>
                    <Upload
                        listType="picture-card"
                        maxCount={1}
                        showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
                        customRequest={customUpload}
                        onPreview={handlePreview}
                        disabled={uploading}
                    >
                        {uploadButton}
                    </Upload>
                </Form.Item>
                <Form.Item name="description" label="App描述" rules={[{ required: true, message: t('pages.key_2170') }]}><Input.TextArea rows={3} placeholder={t('pages.key_2170')} /></Form.Item>
                <Form.Item name="star" label={t('pages.key_1199')} rules={[{ required: true, message: t('pages.key_2206') }]}><InputNumber min={0} max={5} step={0.5} style={{ width: 120 }} placeholder="0-5" /></Form.Item>
                <Form.Item name="promoImages" label={t('pages.key_1022')} valuePropName="fileList" getValueFromEvent={normFile} rules={[{ required: true, message: t('pages.key_2150') }]} extra={<span style={{ color: '#999' }}>{t('pages.key_3003') || '最少3张，建议尺寸252x360，支持jpg/png'}</span>}>
                    <Upload
                        listType="picture-card"
                        multiple
                        maxCount={10}
                        showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
                        customRequest={customUpload}
                        onPreview={handlePreview}
                        disabled={uploading}
                    >
                        {uploadButton}
                    </Upload>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default LandingPageModal; 