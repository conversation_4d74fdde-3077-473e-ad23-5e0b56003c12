import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import {
	Field,
	ID,
	ObjectType,
	GraphQLISODateTime,
	InputType,
	Int
} from '@nestjs/graphql';
import { FilterableField } from '@ptc-org/nestjs-query-graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@ObjectType('materialManagement')
export class materialManagementDTO {
	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	id?: number;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	fileName: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	tags: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	group?: string;

	
	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	facebookImageId?: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	facebookVedioId?: string;


	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	@ApiPropertyOptional({ type: Number, nullable: true, default: 0 })
	adCount?: number;

	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	@ApiPropertyOptional({ type: Number, nullable: true, default: 0 })
	activeAdCount?: number;

	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	@ApiPropertyOptional({ type: Number, nullable: true, default: 0 })
	bannedCount?: number;

	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	@ApiPropertyOptional({ type: Number, nullable: true, default: 0 })
	spend?: number;

	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	@ApiPropertyOptional({ type: Number, nullable: true, default: 0 })
	conversion?: number;

	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	@ApiPropertyOptional({ type: Number, nullable: true, default: 0 })
	commission?: number;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	file: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	tenantId: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	notes?: string;
	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	createdAt?: Date;
	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	updatedAt?: Date;
}

@InputType('materialManagementInput')
export class materialManagementInput {
	@Field(() => Int, { nullable: true })
	id?: number;
	@Field({ nullable: true })
	tenantId?: string;
	@Field({ nullable: true })
	fileName?: string;
	@Field({ nullable: true })
	tags?: string;
	@Field({ nullable: true })
	group?: string;
	@Field({ nullable: true })
	notes?: string;
	@Field({ nullable: true })
	file?: string;
	@Field({ nullable: true })
	createdAt?: Date;
	@Field({ nullable: true })
	updatedAt?: Date;
	@Field({ nullable: true })
	adCount?: number;
	@Field({ nullable: true })
	activeAdCount?: number;
	@Field({ nullable: true })
	bannedCount?: number;
	@Field({ nullable: true })
	spend?: number;
	@Field({ nullable: true })
	conversion?: number;
	@Field({ nullable: true })
	commission?: number;
}

