import { MigrationInterface, QueryRunner } from "typeorm";

export class DropAllAdSetAdAccountIdFK20240612042000 implements MigrationInterface {
    name = 'DropAllAdSetAdAccountIdFK20240612042000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 查找并删除所有 ad_set.adAccountId 外键
        const dropFkSql = `
        DO $$
        DECLARE
            r RECORD;
        BEGIN
            FOR r IN (
                SELECT conname
                FROM pg_constraint
                WHERE conrelid = 'ad_set'::regclass
                  AND contype = 'f'
                  AND pg_get_constraintdef(oid) LIKE '%adAccountId%'
            ) LOOP
                EXECUTE 'ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS ' || quote_ident(r.conname);
            END LOOP;
        END$$;`;
        await queryRunner.query(dropFkSql);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 不自动恢复外键
    }
} 