import { Injectable, BadRequestException } from '@nestjs/common';
import { TypeOrmCrudService } from '@dataui/crud-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AdCampaign } from '../ad-platform/entities/ad-campaign.entity';
import { Repository } from 'typeorm';

@Injectable()
export class AdCampaignService extends TypeOrmCrudService<AdCampaign> {
    constructor(
        @InjectRepository(AdCampaign) readonly repo: Repository<AdCampaign>
    ) {
        super(repo);
    }
} 