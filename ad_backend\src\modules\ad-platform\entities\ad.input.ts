import { InputType, Field, ID } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

@InputType()
export class CreateAdInput {
    @Field()
    name: string;

    @Field()
    adset_id: string;

    @Field({ nullable: true })
    campaign_id?: string;

    @Field({ nullable: true })
    account_id?: string;

    @Field()
    status: string;

    @Field(() => GraphQLJSON, { nullable: true })
    creative?: any;

    @Field(() => GraphQLJSON, { nullable: true })
    tracking_specs?: any;

    @Field({ nullable: true })
    bid_amount?: number;
}

@InputType()
export class UpdateAdInput {
    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    status?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    creative?: any;

    @Field(() => GraphQLJSON, { nullable: true })
    tracking_specs?: any;

    @Field({ nullable: true })
    bid_amount?: number;
} 