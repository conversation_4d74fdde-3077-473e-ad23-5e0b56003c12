import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Domain } from '../../entity/domain.entity';
import { CloudflareService } from './cloudflare.service';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver('Domain')
export class DomainResolver {
    constructor(
        @InjectRepository(Domain) private readonly repo: Repository<Domain>,
        private readonly cf: CloudflareService,
    ) { }

    @Query(() => [Domain])
    async myDomains(@Context() ctx: any) {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? {} : { tenantId };
        return this.repo.find({ where, order: { createdAt: 'DESC' } });
    }

    @Mutation(() => Domain)
    async addDomain(@Args('name') name: string, @Context() ctx: any) {
        const { tenantId, isSuperAdmin } = ctx;
        if (!tenantId && !isSuperAdmin) throw new Error('未获取到租户ID，请检查认证和context注入！');
        if (!/^[a-zA-Z0-9.-]+$/.test(name)) throw new Error('域名格式不正确');
        const where = isSuperAdmin ? { name } : { name, tenantId };
        if (await this.repo.findOne({ where })) throw new Error('域名已存在');
        let cfId = '', status = 'pending', sslStatus = 'pending', error = '';
        try {
            const res = await this.cf.createCustomHostname(name);
            cfId = res.id;
        } catch (e) {
            error = e.message;
            status = 'failed';
            sslStatus = 'failed';
        }
        const domain = this.repo.create({
            name,
            tenantId,
            status,
            sslStatus,
            cnameTarget: 'pwa.yeeu.net',
            cfId,
            error,
        });
        return this.repo.save(domain);
    }

    @Mutation(() => Boolean)
    async deleteDomain(@Args('id') id: string, @Context() ctx: any) {
        const { tenantId, isSuperAdmin } = ctx;
        if (!tenantId && !isSuperAdmin) throw new Error('未获取到租户ID，请检查认证和context注入！');
        const where = isSuperAdmin ? { id } : { id, tenantId };
        const domain = await this.repo.findOneBy(where);
        if (!domain) throw new Error('未找到');
        if (domain.cfId) await this.cf.deleteCustomHostname(domain.cfId);
        await this.repo.delete({ id });
        return true;
    }

    @Mutation(() => Domain)
    async refreshDomainStatus(@Args('id') id: string, @Context() ctx: any) {
        const { tenantId, isSuperAdmin } = ctx;
        if (!tenantId && !isSuperAdmin) throw new Error('未获取到租户ID，请检查认证和context注入！');
        const where = isSuperAdmin ? { id } : { id, tenantId };
        const domain = await this.repo.findOneBy(where);
        if (!domain || !domain.cfId) throw new Error('未找到');
        try {
            const { status, ssl } = await this.cf.getCustomHostnameStatus(domain.name);
            domain.status = status;
            domain.sslStatus = ssl.status;
            domain.error = '';
        } catch (e) {
            domain.error = e.message;
        }
        return this.repo.save(domain);
    }
} 