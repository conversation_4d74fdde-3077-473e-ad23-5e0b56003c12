import { useTranslation } from 'react-i18next';
import { Icon } from "@/components/icon";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Progress } from "@/ui/progress";
import { cn } from "@/utils";
import { useQuery } from '@apollo/client';
import { GET_CAMPAIGN_OVERVIEW } from '@/api/campaignOverview.graphql';

interface CampaignStatProps {
  title: string;
  value: string;
  icon: string;
  change: number;
  iconColor?: string;
}

const CampaignStat = ({ title, value, icon, change, iconColor = "text-primary" }: CampaignStatProps) => {
  const { t } = useTranslation();
  const isPositive = change >= 0;

  return (
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <p className="text-sm text-muted-foreground">{title}</p>
        <div className="flex items-center gap-2">
          <p className="text-2xl font-semibold">{value}</p>
          <span className={cn(
            "flex items-center text-xs font-medium",
            isPositive ? "text-green-600" : "text-red-600"
          )}>
            <Icon
              icon={isPositive ? "solar:arrow-up-linear" : "solar:arrow-down-linear"}
              className="mr-1"
              size={14}
            />
            {Math.abs(change)}%
          </span>
        </div>
      </div>
      <div className={cn("p-2 rounded-full", iconColor)}>
        <Icon icon={icon} size={24} />
      </div>
    </div>
  );
};

interface CampaignItemProps {
  name: string;
  status: "active" | "paused" | "completed";
  budget: string;
  spent: string;
  progress: number;
  impressions: number;
  clicks: number;
  conversions: number;
}

const CampaignItem = ({
  name, status, budget, spent, progress, impressions, clicks, conversions
}: CampaignItemProps) => {
  const { t } = useTranslation();
  const statusColors = {
    active: "bg-green-500",
    paused: "bg-amber-500",
    completed: "bg-blue-500"
  };

  const statusText = {
    active: t('pages.key_2431'),
    paused: t('pages.key_745'),
    completed: t('pages.key_740')
  };

  return (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={cn("w-2 h-2 rounded-full", statusColors[status])} />
          <h3 className="font-medium">{name}</h3>
        </div>
        <span className="text-xs px-2 py-1 rounded-full bg-muted">{statusText[status]}</span>
      </div>

      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">{t('pages.key_2559')}</span>
          <span>{spent} / {budget}</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div>
          <p className="text-xs text-muted-foreground">{t('pages.key_725')}</p>
          <p className="font-medium">{impressions.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-xs text-muted-foreground">{t('pages.key_1590')}</p>
          <p className="font-medium">{clicks.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-xs text-muted-foreground">{t('pages.key_2362')}</p>
          <p className="font-medium">{conversions.toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
};

type CampaignOverviewProps = {
  startDate: string;
  endDate: string;
};

const CampaignOverview = ({ startDate, endDate }: CampaignOverviewProps) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(GET_CAMPAIGN_OVERVIEW, {
    variables: { startDate, endDate },
    fetchPolicy: 'network-only',
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;

  const overview = data?.campaignOverview || {};
  const stats = [
    {
      title: t('pages.key_1510'),
      value: overview.activeCount ?? 0,
      icon: "solar:play-circle-bold-duotone",
      change: 0,
      iconColor: "bg-green-100 text-green-700"
    },
    {
      title: t('pages.key_917'),
      value: `¥${(overview.totalSpend ?? 0).toLocaleString()}`,
      icon: "solar:dollar-minimalistic-bold-duotone",
      change: 0,
      iconColor: "bg-blue-100 text-blue-700"
    },
    {
      title: t('pages.key_925'),
      value: overview.totalConversions ?? 0,
      icon: "solar:flag-bold-duotone",
      change: 0,
      iconColor: "bg-purple-100 text-purple-700"
    },
    {
      title: "平均ROI",
      value: `${overview.avgRoi ?? 0}%`,
      icon: "solar:chart-bold-duotone",
      change: 0,
      iconColor: "bg-amber-100 text-amber-700"
    }
  ];

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">{t('pages.key_798')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-5">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats.map((stat, idx) => (
            <CampaignStat key={idx} {...stat} />
          ))}
        </div>
        <div className="space-y-3">
          <h3 className="text-sm font-medium">{t('pages.key_2399')}</h3>
          <div className="space-y-3">
            {(overview.recentCampaigns || []).map((item: any, idx: number) => (
              <CampaignItem
                key={idx}
                name={item.name}
                status={item.status}
                budget={`¥${item.budget?.toLocaleString?.() ?? 0}`}
                spent={`¥${item.spent?.toLocaleString?.() ?? 0}`}
                progress={item.progress}
                impressions={item.impressions}
                clicks={item.clicks}
                conversions={item.conversions}
              />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CampaignOverview;
