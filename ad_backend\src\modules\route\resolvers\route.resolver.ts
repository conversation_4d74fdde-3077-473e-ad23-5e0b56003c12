import { Resolver, Query, Args, Mutation, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueryService } from '@ptc-org/nestjs-query-core';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';
import { RouteDTO, CreateRouteInput, UpdateRouteInput, UpdateOneRouteInput } from '../dto/route.dto';
import { Route } from '../route.entity';
import { TypeOrmQueryService } from '@ptc-org/nestjs-query-typeorm';

@Resolver(() => RouteDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class RouteResolver {
    private readonly queryService: QueryService<Route>;

    constructor(
        @InjectRepository(Route)
        private readonly routeRepository: Repository<Route>
    ) {
        this.queryService = new TypeOrmQueryService(routeRepository);
    }

    @Query(() => [RouteDTO])
    async getRouteTree(@Context() ctx: any): Promise<RouteDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const routes = await this.routeRepository.find({
            order: { order: 'ASC' },
            relations: ['parent']
        });
        return routes;
    }

    @Query(() => [RouteDTO])
    async getRoutes(@Context() ctx: any): Promise<RouteDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        return this.routeRepository.find();
    }

    @Query(() => RouteDTO, { nullable: true })
    async getRoute(@Args('id') id: string, @Context() ctx: any): Promise<RouteDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        return this.routeRepository.findOne({
            where: { id },
            relations: ['parent']
        });
    }

    @Mutation(() => RouteDTO)
    async createRoute(
        @Args('input') input: CreateRouteInput,
        @Context() ctx: any
    ): Promise<RouteDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        if (!tenantId && !isSuperAdmin) throw new Error('未获取到租户ID，请检查认证和context注入！');
        const route = this.routeRepository.create(input);
        return this.routeRepository.save(route);
    }

    @Mutation(() => RouteDTO)
    async updateOneRoute(
        @Args('input') input: UpdateOneRouteInput,
        @Context() ctx: any
    ): Promise<RouteDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const { id, update } = input;
        const route = await this.routeRepository.findOne({ where: { id } });
        if (!route) {
            throw new Error(`Route with id ${id} not found`);
        }
        Object.assign(route, update);
        return this.routeRepository.save(route);
    }

    @Mutation(() => Boolean)
    async deleteRoute(
        @Args('id') id: string,
        @Context() ctx: any
    ): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx;
        const result = await this.routeRepository.delete({ id });
        return result.affected > 0;
    }

    @Mutation(() => RouteDTO)
    async toggleRouteStatus(
        @Args('id') id: string,
        @Context() ctx: any
    ): Promise<RouteDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const route = await this.routeRepository.findOne({ where: { id } });
        if (!route) {
            throw new Error(`Route with id ${id} not found`);
        }
        route.status = route.status === 'enabled' ? 'disabled' : 'enabled';
        return this.routeRepository.save(route);
    }
} 