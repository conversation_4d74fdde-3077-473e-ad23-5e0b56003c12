const { Client } = require('pg');

async function main() {
    const client = new Client({
        host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
        port: 5432,
        user: 'yeeu',
        password: 'Yy&20240505',
        database: 'ad_auto',
    });
    await client.connect();
    await client.query('TRUNCATE TABLE ad RESTART IDENTITY CASCADE');
    console.log('ad 表已清空');
    await client.end();
}

main().catch(err => {
    console.error('清空失败:', err);
    process.exit(1);
}); 