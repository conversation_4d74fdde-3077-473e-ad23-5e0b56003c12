import { BadRequestException, Body, Controller, Post, Req, UseGuards, Put, Delete, Param } from '@nestjs/common';
import { <PERSON><PERSON>, CrudController } from '@dataui/crud';
import { AntiBlock } from './antiBlock.entity';
import { AntiBlockService } from './antiBlock.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../user/user.entity';
import { Role } from '../role/role.entity';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Crud({
	model: {
		type: AntiBlock,
	},
	params: {
		id: {
			field: 'id',
			primary: true,
			type: 'uuid',
		},
	},
})
@Controller('api/antiBlock')
export class AntiBlockController implements CrudController<AntiBlock> {
	constructor(
		public readonly service: AntiBlockService,
		@InjectRepository(User)
		private userRepository: Repository<User>,
		@InjectRepository(Role)
		private roleRepository: Repository<Role>
	) { }

	@Post('get-list')
	async getAntiBlockList(
		@Body() body: {
			data?: {
				paging?: { offset: number; limit: number },
				filter?: Record<string, any>,
				sorting?: Array<{ field: string; direction: 'ASC' | 'DESC' }>
			}
		},
		@Req() req
	) {
		try {
			const { paging, filter, sorting } = body.data || {};
			const user = req.user;
			const tenantId = req.tenantId;
			const isSuperAdmin = req.isSuperAdmin;
			if (!user) {
				throw new BadRequestException('用户未登录');
			}
			let allItemsResult = await this.service.repo.find({
				where: filter,
				order: sorting?.reduce((acc, sort) => {
					acc[sort.field] = sort.direction.toLowerCase();
					return acc;
				}, {}),
			});
			console.log('getAntiBlockList allItemsResult:', allItemsResult);
			console.log('getAntiBlockList tenantId:', tenantId, 'isSuperAdmin:', isSuperAdmin);
			let filteredItems = [];
			if (isSuperAdmin) {
				filteredItems = allItemsResult;
			} else {
				filteredItems = allItemsResult.filter(item => item.tenantId === tenantId);
			}
			console.log('getAntiBlockList filteredItems:', filteredItems);
			const totalCount = filteredItems.length;
			const offset = paging?.offset || 0;
			const limit = paging?.limit || 10;
			const pagedItems = filteredItems.slice(offset, offset + limit);
			return {
				success: true,
				status: 0,
				data: {
					nodes: pagedItems,
					totalCount: totalCount
				}
			};
		} catch (error) {
			console.error('获取防封列表失败:', error.message);
			throw new BadRequestException(`获取防封列表失败: ${error.message}`);
		}
	}

	@Post('create')
	async createAntiBlock(@Body() body, @Req() req) {
		const user = req.user;
		const tenantId = req.tenantId;
		const isSuperAdmin = req.isSuperAdmin;
		if (!user) throw new BadRequestException('用户未登录');
		const data = isSuperAdmin ? body : { ...body, tenantId };
		return this.service.createWithTenant(data, data.tenantId || tenantId);
	}

	@Put('update/:id')
	async updateAntiBlock(@Param('id') id: number, @Body() body, @Req() req) {
		const user = req.user;
		const tenantId = req.tenantId;
		const isSuperAdmin = req.isSuperAdmin;
		if (!user) throw new BadRequestException('用户未登录');
		const old = await this.service.repo.findOne({ where: { id } });
		if (!old) throw new BadRequestException('数据不存在');
		if (!isSuperAdmin && old.tenantId !== tenantId) throw new BadRequestException('无权操作其他租户数据');
		return this.service.updateWithTenant(id, body, isSuperAdmin ? (body.tenantId || old.tenantId) : tenantId);
	}

	@Delete('delete/:id')
	async deleteAntiBlock(@Param('id') id: number, @Req() req) {
		const user = req.user;
		const tenantId = req.tenantId;
		const isSuperAdmin = req.isSuperAdmin;
		if (!user) throw new BadRequestException('用户未登录');
		const old = await this.service.repo.findOne({ where: { id } });
		if (!old) throw new BadRequestException('数据不存在');
		if (!isSuperAdmin && old.tenantId !== tenantId) throw new BadRequestException('无权操作其他租户数据');
		return this.service.repo.delete({ id });
	}
}
