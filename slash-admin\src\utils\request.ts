/**
 * 通用请求工具，支持 REST/GraphQL，自动处理 baseURL、token、错误提示等。
 * 可根据需要扩展拦截器、全局 loading、自动重试等功能。
 */
import axios, { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// 可根据实际项目配置
const BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// 获取本地 token 的方法（可根据实际项目调整）
function getToken(): string | null {
  return localStorage.getItem('token');
}

// 创建 axios 实例
const request = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
});

// 请求拦截器：自动带 token
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken();
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器：统一错误处理
request.interceptors.response.use(
  (response: AxiosResponse) => {
    // 可按项目约定处理 response.data.code !== 0 的情况
    return response.data;
  },
  (error) => {
    // 可按需弹窗/全局提示
    if (error.response) {
      // 后端有响应
      const msg = error.response.data?.message || error.response.statusText;
      // 这里可用全局 message/notification 组件弹窗
      // e.g. message.error(msg)
      console.error('API Error:', msg);
    } else {
      // 网络等异常
      console.error('Network Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default request;

/**
 * 通用 GET 请求
 */
export function get<T = any>(url: string, params?: any, config?: AxiosRequestConfig) {
  return request.get<T>(url, { params, ...config });
}

/**
 * 通用 POST 请求
 */
export function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig) {
  return request.post<T>(url, data, config);
}

/**
 * 通用 PUT 请求
 */
export function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig) {
  return request.put<T>(url, data, config);
}

/**
 * 通用 DELETE 请求
 */
export function del<T = any>(url: string, config?: AxiosRequestConfig) {
  return request.delete<T>(url, config);
}
