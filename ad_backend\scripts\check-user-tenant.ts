import { DataSource } from 'typeorm';
import { User } from '../src/modules/user/user.entity';
import { Tenant } from '../src/entity/tenant.entity';
import * as path from 'path';

// 适配你的 TypeORM 数据源配置
const AppDataSource = new DataSource({
    type: 'sqlite',
    database: path.resolve(__dirname, '../db.sqlite3'),
    entities: [User, Tenant],
});

async function main() {
    await AppDataSource.initialize();
    const userRepo = AppDataSource.getRepository(User);
    const users = await userRepo.find();
    console.log('id\tusername\ttenantId');
    users.forEach(u => {
        console.log(`${u.id}\t${u.username}\t${u.tenantId || ''}`);
    });
    await AppDataSource.destroy();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 