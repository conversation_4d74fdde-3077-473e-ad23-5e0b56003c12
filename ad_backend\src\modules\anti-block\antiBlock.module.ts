/*
 * @Author: 潘孝权
 * @Date: 2025-05-12 23:19:37
 * @Description: 
 * @FilePath: \sking_frontend\ad_backend\src\modules\anti-block\antiBlock.module.ts
 */
import { ConfigModule } from '@nestjs/config';
import { NestjsQueryGraphQLModule, PagingStrategies } from '@ptc-org/nestjs-query-graphql';
import { NestjsQueryTypeOrmModule } from '@ptc-org/nestjs-query-typeorm';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AntiBlockDTO } from './dto/antiBlock.dto';
import { AntiBlock } from './antiBlock.entity';
import { AntiBlockController } from './antiBlock.controller';
import { AntiBlockService } from './antiBlock.service';
import { AntiBlockResolver } from './resolvers/antiBlock.resolver';
import config from '../../config/config';
import { User } from '../user/user.entity';
import { Role } from '../role/role.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
	imports: [
		ConfigModule.forRoot({
			load: [config],
		}),
		TypeOrmModule.forFeature([AntiBlock, User, Role]),
		NestjsQueryGraphQLModule.forFeature({
			imports: [NestjsQueryTypeOrmModule.forFeature([AntiBlock])],
			resolvers: [
				{
					DTOClass: AntiBlockDTO,
					EntityClass: AntiBlock,
					enableSubscriptions: true,
					delete: {
						disabled: false,
					},
					create: {
						many: { disabled: true },
					},
					pagingStrategy: PagingStrategies.OFFSET
				},
			],
		}),
		AuthModule,
	],
	controllers: [AntiBlockController],
	providers: [AntiBlockService, AntiBlockResolver],
	exports: [AntiBlockService],
})
export class AntiBlockModule { }
