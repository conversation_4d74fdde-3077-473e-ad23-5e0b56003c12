// src/modules/your-module/entities/anti-block.entity.ts

import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class ipRestriction {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ nullable: true })
  ip?: string;

  @Column({ nullable: true })
  userId?: string;

  @Column({ nullable: true })
  status?: string;

  @Column({ nullable: true })
  operator?: string;

  @Column({ nullable: true })
  tenantId?: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt?: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;
}