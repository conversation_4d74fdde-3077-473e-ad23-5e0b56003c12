import { InputType, Field, ID } from '@nestjs/graphql';
import { IsEmail, IsNotEmpty, IsOptional, IsString, MinLength, IsArray, IsUUID } from 'class-validator';
import { UserStatus } from '../user.entity';

@InputType()
export class CreateUserInput {
    @Field()
    @IsNotEmpty()
    @IsString()
    username: string;

    @Field()
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    @MinLength(6)
    password: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    avatar?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    fullName?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    address?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    registerIp?: string;

    @Field(() => UserStatus, { nullable: true, defaultValue: UserStatus.ACTIVE })
    @IsOptional()
    status?: UserStatus;

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID("4", { each: true })
    roleIds?: string[];

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID("4", { each: true })
    groupIds?: string[];

    @Field(() => String, { nullable: true })
    @IsOptional()
    tenantId?: string;
} 