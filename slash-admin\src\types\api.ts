export interface Result<T = any> {
	status: number;
	message: string;
	data?: T;
}

export interface AdAccountParams {
	tenantId: string;
	accessToken: string;
	accountId: string;
}

export interface PaginationParams {
	page: number;
	pageSize: number;
}

export interface ListParams extends PaginationParams {
	tenantId: string;
}

export interface MaterialSyncParams {
	tenantId: string;
	materialList: any[]; // TODO: Define specific material type
}

export interface ReportParams {
	tenantId: string;
	type: string;
	date: string;
	page: number;
	pageSize: number;
}

export interface SaasReportParams {
	type: string;
	date: string;
	page: number;
	pageSize: number;
}

export interface ExportReportParams {
	tenantId: string;
	type: string;
	date: string;
	limit: number;
}

export interface VccSyncResponse {
	success: number;
	failed: number;
	errors?: string[];
}

export interface VccSyncParams {
	tenantId: string;
	vccList: any[]; // TODO: Define specific VCC type
}

export interface SyncRisksParams {
	tenantId: string;
}

export interface ExportRisksParams {
	tenantId: string;
}

export interface ExportRisksResponse {
	url: string;
	logs?: string[];
}
