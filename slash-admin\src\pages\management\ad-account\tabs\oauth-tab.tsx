import { useTranslation } from 'react-i18next';
import React from "react";
import { Button, Table, Modal, message } from "antd";
import { gql, useQuery, useLazyQuery, useMutation } from "@apollo/client";
import { DELETE_AD_ACCOUNT } from '@/api/adAccount.graphql';

const UNAUTHORIZED_LIST = gql`
  query {
    unauthorizedAdAccounts {
      accountId
      status
    }
  }
`;

const GET_OAUTH_URL = gql`
  query GetAdAccountOAuth2Url($account: String!) {
    getAdAccountOAuth2Url(account: $account)
  }
`;

export default function OAuthTab() {
  const { t } = useTranslation();
  const { data, refetch } = useQuery(UNAUTHORIZED_LIST);
  const [getOAuthUrl] = useLazyQuery(GET_OAUTH_URL);
  const [deleteAdAccount, { loading: deleting }] = useMutation(DELETE_AD_ACCOUNT);
  const [deleteModal, setDeleteModal] = React.useState<{ visible: boolean, record: any | null }>({ visible: false, record: null });

  const handleAuthorize = async (accountId: string) => {
    // 【Facebook OAuth 授权流程说明】
    // 1. 需要传递参数 account（广告账户唯一标识，通常为 accountId）。
    // 2. 后端 getAdAccountOAuth2Url 查询会生成 Facebook 授权链接，需配置：
    //    - client_id: Facebook App ID（需在 .env 或配置文件中设置）
    //    - redirect_uri: 授权回调地址，需在 Facebook 开发者后台配置白名单
    //    - state: 建议传递 accountId 作为回调时的标识
    //    - scope: 需申请的权限（如 ads_management, ads_read 等）
    // 3. 用户授权后，Facebook 会回调 redirect_uri，并带上 code 和 state
    // 4. 后端回调接口用 code 换取 access_token，并根据 state 找到对应广告账户，保存 access_token
    // 5. 前端无需关心 client_id、redirect_uri 等参数，只需传递 accountId 即可
    const { data } = await getOAuthUrl({ variables: { account: accountId } });
    if (data?.getAdAccountOAuth2Url) {
      window.location.href = data.getAdAccountOAuth2Url;
    } else {
      message.error(t('pages.key_1973'));
    }
  };

  const handleDelete = (record: any) => {
    setDeleteModal({ visible: true, record });
  };

  const handleDeleteOk = async () => {
    const record = deleteModal.record;
    if (!record) return;
    try {
      await deleteAdAccount({ variables: { accountId: record.accountId } });
      message.success(t('pages.key_294'));
      refetch();
    } catch (e: any) {
      message.error(e.message || t('pages.key_293'));
    } finally {
      setDeleteModal({ visible: false, record: null });
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({ visible: false, record: null });
  };

  return (
    <>
      <Table
        columns={[
          { title: t('pages.key_2320'), dataIndex: "accountId", key: "accountId" },
          {
            title: t('pages.key_1013'),
            dataIndex: "status",
            key: "status",
            render: (v: string) => v === 'pending' ? t('pages.key_1309') : t('pages.key_744'),
          },
          {
            title: t('pages.key_1058'),
            key: "action",
            render: (_: any, record: any) => (
              <>
                {record.status === "pending" && (
                  <Button type="primary" onClick={() => handleAuthorize(record.accountId)} style={{ marginRight: 8 }}>{t('pages.key_399')}</Button>
                )}
                <Button danger loading={deleting} onClick={() => handleDelete(record)} size="small">{t('pages.key_287')}</Button>
              </>
            ),
          },
        ]}
        dataSource={data?.unauthorizedAdAccounts || []}
        rowKey="accountId"
        pagination={false}
      />
      <Modal
        open={deleteModal.visible}
        title="确认删除该广告账户？"
        onOk={handleDeleteOk}
        onCancel={handleDeleteCancel}
        okText={t('pages.key_287')}
        okType="danger"
        cancelText={t('pages.key_407')}
        destroyOnHidden
      >
        <div>账户ID：{deleteModal.record?.accountId}</div>
      </Modal>
    </>
  );
}
