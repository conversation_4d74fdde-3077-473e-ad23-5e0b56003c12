// 删除 ad_set 表中无效的广告组记录（adAccountId 为空、为 null，或在 ad_account 表中找不到的记录）
const { Client } = require('pg');

// 参考 data-source.ts 的实际数据库配置
const client = new Client({
    host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
    port: 5432,
    user: 'yeeu',
    password: 'Yy&********',
    database: 'ad_auto',
});

async function main() {
    await client.connect();

    // 1. 统计无效记录数量
    const { rows: countRows } = await client.query(`
    SELECT COUNT(*) FROM "ad_set"
    WHERE "adAccountId" IS NULL OR "adAccountId" = ''
      OR "adAccountId" NOT IN (SELECT "accountId" FROM "ad_account")
  `);
    const count = parseInt(countRows[0].count, 10);
    if (count === 0) {
        console.log('没有无效的 ad_set 记录');
        await client.end();
        return;
    }
    console.log(`将要删除 ${count} 条无效 ad_set 记录...`);

    // 2. 删除无效记录
    const { rowCount } = await client.query(`
    DELETE FROM "ad_set"
    WHERE "adAccountId" IS NULL OR "adAccountId" = ''
      OR "adAccountId" NOT IN (SELECT "accountId" FROM "ad_account")
  `);
    console.log(`已删除 ${rowCount} 条无效 ad_set 记录`);
    await client.end();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 