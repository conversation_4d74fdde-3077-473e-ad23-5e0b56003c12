import { Resolver, Query, Mutation, Args, Context, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { plainToInstance } from 'class-transformer';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';
import { VccCard } from './entities/vcc-card.entity';
import { AdAccount } from '../ad-platform/entities/ad-account.entity';
import { Tenant } from '../../entity/tenant.entity';
import {
    VccCardDTO,
    CreateVccCardInputDTO,
    UpdateVccCardInputDTO,
    VccCardFilterInputDTO,
    BindAdAccountInputDTO
} from './dto/vcc.dto';

@Resolver(() => VccCardDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class VccCardResolver {
    constructor(
        @InjectRepository(VccCard)
        private readonly vccCardRepo: Repository<VccCard>,
        @InjectRepository(AdAccount)
        private readonly adAccountRepo: Repository<AdAccount>,
        @InjectRepository(Tenant)
        private readonly tenantRepo: Repository<Tenant>,
    ) { }

    @Query(() => [VccCardDTO], { description: '获取VCC卡片列表' })
    async vccCards(
        @Args('filter', { type: () => VccCardFilterInputDTO, nullable: true }) filter: VccCardFilterInputDTO = {},
        @Context() ctx?: any,
    ): Promise<VccCardDTO[]> {
        const { tenantId, isSuperAdmin } = ctx || {};
        const where: any = {};
        if (!isSuperAdmin) {
            where.tenant = { id: tenantId };
        }
        // 添加其他过滤条件
        if (filter?.channel) where.channel = filter.channel;
        if (filter?.country) where.country = filter.country;
        if (filter?.cardHolder) where.cardHolder = filter.cardHolder;
        if (filter?.cardNumber) where.cardNumber = filter.cardNumber;
        if (filter?.status) where.status = filter.status;
        const cards = await this.vccCardRepo.find({
            where,
            relations: ['tenant'],
            order: { createdAt: 'DESC' }
        });
        return plainToInstance(VccCardDTO, cards);
    }

    @Query(() => VccCardDTO, { description: '根据ID获取VCC卡片详情' })
    async vccCard(
        @Args('id', { type: () => ID }) id: string,
        @Context() ctx?: any,
    ): Promise<VccCardDTO> {
        const { tenantId, isSuperAdmin } = ctx || {};
        const where: any = { id };
        if (!isSuperAdmin) {
            where.tenant = { id: tenantId };
        }
        const card = await this.vccCardRepo.findOne({
            where,
            relations: ['tenant']
        });
        if (!card) {
            throw new Error('VCC卡片不存在');
        }
        return plainToInstance(VccCardDTO, card);
    }

    @Mutation(() => VccCardDTO, { description: '创建VCC卡片' })
    async createVccCard(
        @Args('input') input: CreateVccCardInputDTO,
        @Context() ctx?: any,
    ): Promise<VccCardDTO> {
        const { tenantId, isSuperAdmin } = ctx || {};
        const useTenantId = isSuperAdmin ? (input.tenantId || tenantId) : tenantId;
        // 验证租户是否存在
        const tenant = await this.tenantRepo.findOne({ where: { id: useTenantId } });
        if (!tenant) {
            throw new Error('租户不存在');
        }
        // 检查卡号是否已存在
        const existingCard = await this.vccCardRepo.findOne({
            where: { cardNumber: input.cardNumber }
        });
        if (existingCard) {
            throw new Error('卡号已存在');
        }
        // 创建VCC卡片
        const card = this.vccCardRepo.create({
            ...input,
            tenant,
            tenantId: useTenantId,
            consumption: input.consumption || 0,
            transactionCount: input.transactionCount || 0,
            adAccountStatus: input.adAccountStatus || 0,
            usedCount: input.usedCount || 0,
            bindCount: input.bindCount || 0,
            totalAdAccounts: input.totalAdAccounts || 0,
            limitCount: input.limitCount || 10,
            status: input.status || '未使用'
        });
        const savedCard = await this.vccCardRepo.save(card);
        return plainToInstance(VccCardDTO, savedCard);
    }

    @Mutation(() => VccCardDTO, { description: '更新VCC卡片' })
    async updateVccCard(
        @Args('id', { type: () => ID }) id: string,
        @Args('input') input: UpdateVccCardInputDTO,
        @Context() ctx?: any,
    ): Promise<VccCardDTO> {
        const { tenantId, isSuperAdmin } = ctx || {};
        const where: any = { id };
        if (!isSuperAdmin) {
            where.tenant = { id: tenantId };
        }
        const card = await this.vccCardRepo.findOne({
            where,
            relations: ['tenant']
        });
        if (!card) {
            throw new Error('VCC卡片不存在');
        }
        // 更新字段
        Object.assign(card, input);
        const updatedCard = await this.vccCardRepo.save(card);
        return plainToInstance(VccCardDTO, updatedCard);
    }

    @Mutation(() => Boolean, { description: '删除VCC卡片' })
    async deleteVccCard(
        @Args('id', { type: () => ID }) id: string,
        @Context() ctx?: any,
    ): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx || {};
        const where: any = { id };
        if (!isSuperAdmin) {
            where.tenant = { id: tenantId };
        }
        const card = await this.vccCardRepo.findOne({ where });
        if (!card) {
            throw new Error('VCC卡片不存在');
        }
        await this.vccCardRepo.remove(card);
        return true;
    }

    @Mutation(() => VccCardDTO, { description: '绑定广告账户到VCC卡片' })
    async bindAdAccountToVccCard(
        @Args('input') input: BindAdAccountInputDTO,
        @Context() ctx?: any,
    ): Promise<VccCardDTO> {
        const tenantId = ctx?.tenantId;

        // 查找VCC卡片
        const where: any = { id: input.vccCardId };
        if (tenantId) {
            where.tenant = { id: tenantId };
        }

        const card = await this.vccCardRepo.findOne({
            where,
            relations: ['tenant']
        });

        if (!card) {
            throw new Error('VCC卡片不存在');
        }

        // 验证广告账户是否存在且属于同一租户
        const adAccounts = await this.adAccountRepo.find({
            where: {
                id: In(input.adAccountIds),
                tenant: { id: tenantId }
            },
            relations: ['tenant']
        });

        if (adAccounts.length !== input.adAccountIds.length) {
            throw new Error('部分广告账户不存在或无权限访问');
        }

        // 检查绑定限制
        if (input.adAccountIds.length > card.limitCount) {
            throw new Error(`绑定账户总数不能超过限制数量 ${card.limitCount}`);
        }

        // 全部替换绑定的账户
        const accountNames = adAccounts.map(acc => acc.account).join(', ');

        // 计算存活的广告账户数量（状态为active的）
        const activeAccounts = adAccounts.filter(acc => acc.status === 'active');

        // 更新VCC卡片的绑定信息（全部替换）
        card.boundAdAccount = accountNames;
        card.boundAdAccountIds = input.adAccountIds;
        card.bindCount = input.adAccountIds.length;
        card.totalAdAccounts = input.adAccountIds.length;
        card.adAccountStatus = activeAccounts.length;

        if (card.status === '未使用' && input.adAccountIds.length > 0) {
            card.status = '已使用';
        } else if (input.adAccountIds.length === 0 && card.status === '已使用') {
            card.status = '未使用';
        }

        const updatedCard = await this.vccCardRepo.save(card);
        return plainToInstance(VccCardDTO, updatedCard);
    }

    @Mutation(() => [VccCardDTO], { description: '批量创建VCC卡片' })
    async createVccCardsBatch(
        @Args('inputs', { type: () => [CreateVccCardInputDTO] }) inputs: CreateVccCardInputDTO[],
        @Context() ctx?: any,
    ): Promise<VccCardDTO[]> {
        const tenantId = ctx?.tenantId || inputs[0]?.tenantId;

        // 验证租户是否存在
        const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
        if (!tenant) {
            throw new Error('租户不存在');
        }

        // 检查卡号重复
        const cardNumbers = inputs.map(input => input.cardNumber);
        const existingCards = await this.vccCardRepo.find({
            where: { cardNumber: In(cardNumbers) }
        });

        if (existingCards.length > 0) {
            const existingNumbers = existingCards.map(card => card.cardNumber);
            throw new Error(`以下卡号已存在: ${existingNumbers.join(', ')}`);
        }

        // 批量创建
        const cards = inputs.map(input => this.vccCardRepo.create({
            ...input,
            tenant,
            tenantId,
            consumption: input.consumption || 0,
            transactionCount: input.transactionCount || 0,
            adAccountStatus: input.adAccountStatus || 0,
            usedCount: input.usedCount || 0,
            bindCount: input.bindCount || 0,
            totalAdAccounts: input.totalAdAccounts || 0,
            limitCount: input.limitCount || 10,
            status: input.status || '未使用'
        }));

        const savedCards = await this.vccCardRepo.save(cards);
        return plainToInstance(VccCardDTO, savedCards);
    }
} 