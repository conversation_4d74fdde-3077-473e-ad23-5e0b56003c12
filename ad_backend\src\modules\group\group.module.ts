import { ConfigModule } from '@nestjs/config';
import { NestjsQueryGraphQLModule, PagingStrategies } from '@ptc-org/nestjs-query-graphql';
import { NestjsQueryTypeOrmModule } from '@ptc-org/nestjs-query-typeorm';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GroupDTO } from './dto/group.dto';
import { Group } from './group.entity';
import { GroupController } from './group.controller';
import { GroupService } from './group.service';
import { GroupResolver } from './resolvers/group.resolver';
import { AuthModule } from '../auth/auth.module';
import config from '../../config/config';
import { CreateGroupInput } from './dto/group.dto';

@Module({
    imports: [
        ConfigModule.forRoot({
            load: [config],
        }),
        TypeOrmModule.forFeature([Group]),
        AuthModule,
        NestjsQueryGraphQLModule.forFeature({
            imports: [NestjsQueryTypeOrmModule.forFeature([Group])],
            resolvers: [
                {
                    DTOClass: GroupDTO,
                    CreateDTOClass: CreateGroupInput,
                    UpdateDTOClass: CreateGroupInput,
                    EntityClass: Group,
                    enableSubscriptions: true,
                    create: {
                        disabled: false,
                        many: { disabled: false }
                    },
                    update: {
                        disabled: false,
                        many: { disabled: false }
                    },
                    delete: {
                        disabled: false,
                        many: { disabled: false }
                    },
                    pagingStrategy: PagingStrategies.OFFSET,
                    enableTotalCount: true,
                },
            ],
        }),
    ],
    controllers: [GroupController],
    providers: [GroupService, GroupResolver],
    exports: [GroupService],
})
export class GroupModule { } 