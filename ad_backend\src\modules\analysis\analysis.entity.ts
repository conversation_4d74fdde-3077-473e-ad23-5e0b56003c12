import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export class Analysis {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ nullable: true })
  date?: Date;

  @Column({ nullable: true, type: 'int' })
  impressions?: number;

  @Column({ nullable: true, type: 'int' })
  clicks?: number;

  @Column({ nullable: true, type: 'int' })
  conversions?: number;

  @Column({ nullable: true, type: 'decimal', precision: 10, scale: 2 })
  spend?: number;

  @Column({ nullable: true })
  adAccountId?: string;

  @Column({ nullable: true })
  campaignId?: string;

  @Column({ nullable: true })
  adSetId?: string;

  @Column({ nullable: true })
  adId?: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt?: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;
} 