import { useTranslation } from 'react-i18next';
import React, { useMemo, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/ui/card';
import { Chart } from '@/components/chart/chart';
import { Icon } from '@/components/icon';
import { Button } from '@/ui/button';

const typeMeta: Record<string, { icon: string; color: string }> = {
    'pages.key_2320': { icon: 'solar:user-bold-duotone', color: 'text-blue-600' },
    'pages.key_774': { icon: 'solar:megaphone-bold-duotone', color: 'text-green-600' },
    'pages.key_1883': { icon: 'solar:users-group-rounded-bold-duotone', color: 'text-purple-600' },
    'pages.key_408': { icon: 'solar:target-bold-duotone', color: 'text-amber-600' },
    'pages.key_806': { icon: 'solar:flag-bold-duotone', color: 'text-pink-600' },
    'pages.key_1782': { icon: 'solar:image-bold-duotone', color: 'text-gray-600' },
    'pages.key_1723': { icon: 'solar:home-2-bold-duotone', color: 'text-indigo-600' },
};

interface TenantDetailTableProps {
    data: any[];
    total: number;
    page: number;
    pageSize: number;
    loading?: boolean;
    onPageChange?: (page: number) => void;
    showTenant?: boolean;
}

export default function TenantDetailTable({ data, total, page, pageSize, loading, onPageChange, showTenant }: TenantDetailTableProps) {
    const { t } = useTranslation();
    // 类型多选过滤
    const allTypes = useMemo(() => Array.from(new Set(data.map(d => d.type))), [data]);
    // const [selectedTypes, setSelectedTypes] = useState<string[]>(allTypes);
    // React.useEffect(() => {
    //     setSelectedTypes(allTypes);
    // }, [allTypes.join(",")]);
    // const toggleType = (type: string) => {
    //     setSelectedTypes(prev => prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]);
    // };
    // const allSelected = selectedTypes.length === allTypes.length;
    // const toggleAll = () => {
    //     setSelectedTypes(allSelected ? [] : allTypes);
    // };
    // 过滤数据
    const filteredData = data; // 直接使用全部数据
    // 饼图统计
    const typeCountMap = filteredData.reduce<Record<string, number>>((acc, cur) => {
        acc[cur.type] = (acc[cur.type] || 0) + 1;
        return acc;
    }, {});
    const chartLabels = Object.keys(typeCountMap);
    const chartSeries = Object.values(typeCountMap);
    const chartOptions = {
        chart: { type: 'pie' as const, toolbar: { show: false }, background: 'transparent' },
        labels: chartLabels,
        legend: {
            position: 'bottom' as const,
            fontSize: '13px',
            fontWeight: 500,
            itemMargin: { horizontal: 8, vertical: 2 },
            markers: { size: 12, strokeWidth: 0 },
        },
        colors: ['#7FB3FF', '#A3E4DB', '#D6C6F7', '#FFE082', '#B2B1CF', '#B5EAD7'],
        dataLabels: {
            enabled: true,
            formatter: function (val: number) {
                return val.toFixed(1) + '%';
            },
            style: { fontSize: '12px', fontWeight: 500 },
            dropShadow: { enabled: false },
        },
        tooltip: {
            y: {
                formatter: (val: number, opts: any) => {
                    const percent = ((val / (chartSeries.reduce((a, b) => a + b, 0) || 1)) * 100).toFixed(1);
                    const label = opts && opts.w && opts.w.globals && opts.w.globals.labels
                        ? opts.w.globals.labels[opts.seriesIndex]
                        : '';
                    return `${label} ${val}项（${percent}%）`;
                },
            },
            style: { fontSize: '13px' },
        },
        stroke: { show: false },
        states: {
            hover: { filter: { type: 'lighten', value: 0.06 } },
            active: { filter: { type: 'darken', value: 0.10 } },
        },
        plotOptions: {
            pie: {
                expandOnClick: true,
                offsetY: 0,
                customScale: 1.02,
                dataLabels: {
                    offset: -18,
                },
            },
        },
        grid: { padding: { top: 0, bottom: 0 } },
    };
    const pageCount = Math.ceil(filteredData.length / pageSize);
    const pageData = filteredData.slice((page - 1) * pageSize, page * pageSize);
    return (
        <Card>
            <CardHeader>
                <CardTitle>{showTenant ? t('pages.key_180') : t('pages.key_1733')}</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="mb-2 flex flex-col items-center">
                    <div className="w-full max-w-md" style={{ minWidth: 320 }}>
                        {chartSeries.length > 0 ? (
                            <Chart options={chartOptions} series={chartSeries} type="pie" height={260} />
                        ) : (
                            <div className="text-muted-foreground text-center py-8">{t('pages.key_1228')}</div>
                        )}
                    </div>
                </div>
                <div className="overflow-x-auto">
                    <table className="min-w-full text-xs text-center border rounded-lg">
                        <thead>
                            <tr className="bg-gray-50">
                                <th className="px-2 py-2">{t('pages.key_1765')}</th>
                                <th className="px-2 py-2">名称/ID</th>
                                <th className="px-2 py-2">{t('pages.key_1603')}</th>
                                <th className="px-2 py-2">{t('pages.key_872')}</th>
                                <th className="px-2 py-2">{t('pages.key_250')}</th>
                                <th className="px-2 py-2">{t('pages.key_588')}</th>
                                {showTenant && <th className="px-2 py-2">{t('pages.key_1723')}</th>}
                            </tr>
                        </thead>
                        <tbody>
                            {loading ? (
                                <tr><td colSpan={showTenant ? 7 : 6} className="py-8 text-muted-foreground">加载中...</td></tr>
                            ) : pageData.length === 0 ? (
                                <tr><td colSpan={showTenant ? 7 : 6} className="py-8 text-muted-foreground">{t('pages.key_1227')}</td></tr>
                            ) : pageData.map((row, idx) => (
                                <tr key={idx} className={idx % 2 === 0 ? 'bg-white' : 'bg-gray-50 hover:bg-blue-50'}>
                                    <td className="px-2 py-2 font-medium flex items-center gap-1 justify-center">
                                        <Icon icon={typeMeta[row.type]?.icon} className={typeMeta[row.type]?.color + ' text-lg'} />
                                        <span>{row.type}</span>
                                    </td>
                                    <td className="px-2 py-2">{row.name}</td>
                                    <td className="px-2 py-2">{row.status}</td>
                                    <td className="px-2 py-2">{row.group}</td>
                                    <td className="px-2 py-2">{row.createdAt ? row.createdAt.slice(0, 10) : ''}</td>
                                    <td className="px-2 py-2">{row.remark}</td>
                                    {showTenant && <td className="px-2 py-2 text-xs text-gray-500">{row.tenantName || '-'}<br />{row.tenantId || '-'}</td>}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                {/* 分页控件 */}
                <div className="flex justify-end items-center gap-2 mt-4">
                    <Button size="sm" variant="outline" disabled={page === 1} onClick={() => onPageChange && onPageChange(page - 1)}>{t('pages.key_1')}</Button>
                    <span className="text-xs">{t('pages.key_1749')}</span>
                    <Button size="sm" variant="outline" disabled={page === pageCount} onClick={() => onPageChange && onPageChange(page + 1)}>{t('pages.key_9')}</Button>
                </div>
            </CardContent>
        </Card>
    );
} 