import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, message, Tabs } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery, useMutation } from '@apollo/client';
import { GET_CAMPAIGN_LIST, CREATE_CAMPAIGN, UPDATE_CAMPAIGN, DELETE_CAMPAIGN } from '@/api/campaign.graphql';
import { GET_AD_ACCOUNT_LIST } from '@/api/adAccount.graphql';
import AdSetManagement from '../AdSetManagement';

// Facebook Campaign 主要字段
// https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/

export default function FacebookCampaignTabs() {
    const { t } = useTranslation();
    return (
        <Tabs defaultActiveKey="campaigns" items={[
            {
                key: 'campaigns',
                label: t('pages.key_808'),
                children: <FacebookCampaignManagementNoTitle />,
            },
            {
                key: 'adsets',
                label: t('pages.key_818'),
                children: <AdSetManagement />,
            },
        ]} />
    );
}

function FacebookCampaignManagementNoTitle() {
    const { t } = useTranslation();
    const [modalVisible, setModalVisible] = useState(false);
    const [editing, setEditing] = useState<any>(null);
    const [form] = Form.useForm();
    const [deleteId, setDeleteId] = useState<string | null>(null);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [deleteLoading, setDeleteLoading] = useState(false);

    // 查询广告系列
    const { data, loading, refetch } = useQuery(GET_CAMPAIGN_LIST);
    const campaigns = data?.campaigns || [];

    // 查询广告账户
    const { data: adAccountData } = useQuery(GET_AD_ACCOUNT_LIST);
    const adAccounts = adAccountData?.adAccounts || [];

    // 新建/编辑/删除
    const [createCampaign] = useMutation(CREATE_CAMPAIGN);
    const [updateCampaign] = useMutation(UPDATE_CAMPAIGN);
    const [deleteCampaign] = useMutation(DELETE_CAMPAIGN);
    const adAccountOptions = adAccounts.map((acc: any) => ({
        label: `${acc.account}（${acc.accountId}）`,
        value: String(acc.accountId)
    }));

    const openModal = (record?: any) => {
        setEditing(record || null);
        setModalVisible(true);
        if (record) {
            form.resetFields();
            form.setFieldsValue({
                ...record,
                accountId: record.accountId ? String(record.accountId) : undefined,
            });
        } else {
            form.resetFields();
        }
    };

    const handleModalOk = async () => {
        setConfirmLoading(true);
        try {
            const values = await form.validateFields();
            if (!values.specialAdCategories) values.specialAdCategories = [];
            console.log(values, "values")
            if (!values.accountId || values.accountId === 'undefined' || values.accountId === '') {
                message.error(t('pages.key_2268'));
                setConfirmLoading(false);
                return;
            }
            if (editing) {
                await updateCampaign({ variables: { id: editing.id, input: values } });
                message.success(t('pages.key_145'));
            } else {
                await createCampaign({ variables: { input: values } });
                message.success(t('pages.key_1149'));
            }
            setModalVisible(false);
            setEditing(null);
            refetch();
        } catch { } finally {
            setConfirmLoading(false);
        }
    };

    const handleDelete = (record: any) => setDeleteId(record.id);
    const handleDeleteOk = async () => {
        setDeleteLoading(true);
        try {
            await deleteCampaign({ variables: { id: deleteId } });
            setDeleteId(null);
            message.success(t('pages.key_294'));
            refetch();
        } finally {
            setDeleteLoading(false);
        }
    };
    const handleDeleteCancel = () => setDeleteId(null);

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            render: (id: string) => (
                <span title={id}>{id.length > 10 ? id.slice(0, 8) + '...' : id}</span>
            )
        },
        { title: t('pages.key_483'), dataIndex: 'name', key: 'name' },
        { title: t('pages.key_1674'), dataIndex: 'objective', key: 'objective', render: (v: string) => OBJECTIVE_OPTIONS.find(o => o.value === v)?.label || v },
        { title: t('pages.key_1602'), dataIndex: 'status', key: 'status', render: (v: string) => STATUS_OPTIONS.find(o => o.value === v)?.label || v },
        { title: t('pages.key_1601'), dataIndex: 'specialAdCategories', key: 'specialAdCategories', render: (arr: string[]) => arr && arr.length ? arr.join(', ') : '-' },
        { title: t('pages.key_990'), dataIndex: 'accountId', key: 'accountId', render: (id: string) => adAccounts.find((a: any) => a.accountId === id)?.account || id },
        {
            title: t('pages.key_1058'),
            key: 'actions',
            render: (_: any, record: any) => (
                <Space>
                    <Button size="small" icon={<EditOutlined />} onClick={() => openModal(record)}>{t('pages.key_1853')}</Button>
                    <Button size="small" icon={<DeleteOutlined />} danger onClick={() => handleDelete(record)}>{t('pages.key_287')}</Button>
                </Space>
            ),
        },
    ];

    const OBJECTIVE_OPTIONS = [
        { label: '线索收集 (OUTCOME_LEADS)', value: 'OUTCOME_LEADS' },
        { label: '销售转化 (OUTCOME_SALES)', value: 'OUTCOME_SALES' },
        { label: '互动 (OUTCOME_ENGAGEMENT)', value: 'OUTCOME_ENGAGEMENT' },
        { label: '品牌认知 (OUTCOME_AWARENESS)', value: 'OUTCOME_AWARENESS' },
        { label: '流量 (OUTCOME_TRAFFIC)', value: 'OUTCOME_TRAFFIC' },
        { label: '应用推广 (OUTCOME_APP_PROMOTION)', value: 'OUTCOME_APP_PROMOTION' },
    ];
    const STATUS_OPTIONS = [
        { label: t('pages.key_496'), value: 'ACTIVE' },
        { label: t('pages.key_1221'), value: 'PAUSED' },
    ];
    const SPECIAL_AD_CATEGORIES_OPTIONS = [
        { label: '住房 (HOUSING)', value: 'HOUSING' },
        { label: '就业 (EMPLOYMENT)', value: 'EMPLOYMENT' },
        { label: '信贷 (CREDIT)', value: 'CREDIT' },
        { label: '政治/选举/社会议题 (ISSUES_ELECTIONS_POLITICS)', value: 'ISSUES_ELECTIONS_POLITICS' },
    ];

    return (
        <div>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
                <Button type="primary" icon={<PlusOutlined />} onClick={() => openModal()}>{t('pages.key_1147')}</Button>
            </div>
            <Table
                rowKey="id"
                columns={columns}
                dataSource={campaigns}
                loading={loading}
                pagination={{ pageSize: 10 }}
            />
            <Modal
                open={modalVisible}
                title={editing ? t('pages.key_1856') : t('pages.key_1147')}
                onOk={handleModalOk}
                onCancel={() => setModalVisible(false)}
                okText={t('pages.key_1693')}
                cancelText={t('pages.key_407')}
                destroyOnClose
                confirmLoading={confirmLoading}
            >
                <Form form={form} layout="vertical" preserve={false}>
                    <Form.Item name="name" label={t('pages.key_483')} rules={[{ required: true, message: t('pages.key_2195') }]}>
                        <Input placeholder={t('pages.key_2181')} />
                    </Form.Item>
                    <Form.Item name="objective" label={t('pages.key_803')} rules={[{ required: true, message: t('pages.key_2258') }]}>
                        <Select options={OBJECTIVE_OPTIONS} placeholder={t('pages.key_2258')} />
                    </Form.Item>
                    <Form.Item name="status" label={t('pages.key_1602')} rules={[{ required: true, message: t('pages.key_2273') }]}>
                        <Select options={STATUS_OPTIONS} placeholder={t('pages.key_2273')} />
                    </Form.Item>
                    <Form.Item name="specialAdCategories" label={t('pages.key_1601')}>
                        <Select
                            mode="multiple"
                            options={SPECIAL_AD_CATEGORIES_OPTIONS}
                            placeholder={t('pages.key_602')}
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item name="accountId" label={t('pages.key_990')} rules={[{ required: true, message: t('pages.key_2268') }]}>
                        <Select
                            placeholder={t('pages.key_2268')}
                            options={adAccountOptions}
                            allowClear
                            onChange={v => console.log('Select changed:', v)}
                            disabled={!!editing}
                        />
                    </Form.Item>
                </Form>
            </Modal>
            <Modal
                open={!!deleteId}
                title="确认删除该广告系列？"
                onOk={handleDeleteOk}
                onCancel={handleDeleteCancel}
                okText={t('pages.key_287')}
                okType="danger"
                cancelText={t('pages.key_407')}
                destroyOnClose
                confirmLoading={deleteLoading}
            >
                <div>ID：{deleteId}</div>
            </Modal>
        </div>
    );
} 