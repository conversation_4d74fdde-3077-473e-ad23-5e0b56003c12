import { useTranslation } from 'react-i18next';
import { useState, useContext, useRef, useEffect } from "react";
import type { ApexOptions } from "apexcharts";
type ChartType = "line" | "area" | "bar" | "pie" | "donut" | "radialBar" | "scatter" | "bubble" | "heatmap" | "candlestick" | "boxPlot" | "radar" | "polarArea" | "rangeBar" | "rangeArea" | "treemap";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { Chart, useChart } from "@/components/chart";
import { Icon } from "@/components/icon";
import { Badge } from "@/ui/badge";
import { Button } from "@/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { useQuery, gql } from '@apollo/client';
import { useTenant } from '@/hooks/useTenant';
import { TabsContext } from "@/ui/tabs";

const TENANT_TREND = gql`
  query TenantTrend($tenantId: String!, $startDate: String, $endDate: String) {
    tenantTrend(tenantId: $tenantId, startDate: $startDate, endDate: $endDate) {
      date
      impressions
      clicks
      spend
      conversion
    }
  }
`;

const CHANNEL_DISTRIBUTION = gql`
  query ChannelDistribution($tenantId: String!) {
    channelDistribution(tenantId: $tenantId) {
      channel
      channelName
      count
      totalImpressions
      totalClicks
      totalConversion
      totalSpend
    }
  }
`;

const AUDIENCE_ANALYSIS = gql`
  query AudienceAnalysis($tenantId: String!) {
    audienceAnalysis(tenantId: $tenantId) {
      total
      gender { key label value source }
      age { key label value source }
      platform { key label value source }
      region { key label value source }
      interest { key label value source }
    }
  }
`;

const CREATIVE_ANALYSIS = gql`
  query CreativeAnalysis {
    creativeAnalysis {
      items {
        key
        label
        impressions
        clicks
        conversions
        ctr
        cvr
        spend
        type
      }
    }
  }
`;

const ChannelTabContent = ({ tenantId, options, safeColors, onRef }: { tenantId: string | null, options: any, safeColors: string[], onRef?: (refetch: () => void) => void }) => {
  const { t } = useTranslation();
  const { selectedTab } = useContext(TabsContext) || {};
  const { data: channelDataRes, loading: channelLoading, refetch } = useQuery(CHANNEL_DISTRIBUTION, {
    variables: { tenantId },
    skip: !tenantId || selectedTab !== 'channels',
    fetchPolicy: 'network-only',
  });
  useEffect(() => { onRef && onRef(refetch); }, [onRef, refetch]);
  const channelDistribution = channelDataRes?.channelDistribution || [];
  const channelDistributionOptions = {
    ...options,
    labels: channelDistribution.map((item: any) => item.channelName),
    colors: safeColors.slice(0, 4),
    stroke: { show: false },
    legend: { position: "bottom" as const },
    tooltip: { y: { formatter: (value: number) => `${value}个账户` } },
  };
  const channelDistributionSeries = channelDistribution.map((item: any) => item.count);

  // 新增：loading/无数据提示
  if (channelLoading) {
    return <div className="flex items-center justify-center h-[300px]">加载中...</div>;
  }
  if (!channelDistribution.length) {
    return <div className="flex items-center justify-center h-[300px] text-muted-foreground">{t('pages.key_1227')}</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('pages.key_801')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Chart
              type="pie"
              height={300}
              options={channelDistributionOptions}
              series={channelDistributionSeries}
            />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('pages.key_1563')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {channelDistribution.map((channel: any, index: any) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-2 bg-${index === 0 ? 'primary' : index === 1 ? 'info' : index === 2 ? 'success' : index === 3 ? 'warning' : 'error'}`}></div>
                    <span className="font-medium">{channel.channelName}</span>
                  </div>
                  <span className="text-sm">账户数: {channel.count}</span>
                </div>
                {/* 预留更多维度展示 */}
                {/* <div className="flex gap-4 text-xs text-muted-foreground">
                  <span>展示量: {channel.totalImpressions || '-'}</span>
                  <span>点击量: {channel.totalClicks || '-'}</span>
                  <span>转化量: {channel.totalConversion || '-'}</span>
                  <span>消耗: {channel.totalSpend || '-'}</span>
                </div> */}
                <div className="h-2 bg-muted rounded-full overflow-hidden">
                  <div
                    className={`h-full ${index === 0 ? 'bg-primary' : index === 1 ? 'bg-info' : index === 2 ? 'bg-success' : index === 3 ? 'bg-warning' : 'bg-error'}`}
                    style={{ width: `${Math.min(channel.count * 10, 100)}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const AudienceTabContent = ({ tenantId, options, safeColors, onRef }: { tenantId: string | null, options: any, safeColors: string[], onRef?: (refetch: () => void) => void }) => {
  const { t } = useTranslation();
  const { selectedTab } = useContext(TabsContext) || {};
  const { data, loading, refetch } = useQuery(AUDIENCE_ANALYSIS, {
    variables: { tenantId },
    skip: !tenantId || selectedTab !== 'audience',
    fetchPolicy: 'network-only',
  });
  useEffect(() => { onRef && onRef(refetch); }, [onRef, refetch]);
  const analysis = data?.audienceAnalysis;
  if (loading) return <div className="flex items-center justify-center h-[300px]">加载中...</div>;
  if (!analysis) return <div className="flex items-center justify-center h-[300px] text-muted-foreground">{t('pages.key_1227')}</div>;
  // 性别分布
  const genderOptions = {
    ...options,
    labels: analysis.gender.map((item: any) => `${item.label}（${item.source === 'facebook' ? 'FB' : t('pages.key_1318')}）`),
    colors: safeColors.slice(0, analysis.gender.length),
    legend: { position: "bottom" },
  };
  const genderSeries = analysis.gender.map((item: any) => item.value);
  // 年龄分布
  const ageOptions = {
    ...options,
    labels: analysis.age.map((item: any) => `${item.label}（${item.source === 'facebook' ? 'FB' : t('pages.key_1318')}）`),
    colors: safeColors.slice(0, analysis.age.length),
    legend: { position: "bottom" },
  };
  const ageSeries = analysis.age.map((item: any) => item.value);
  // 平台分布
  const platformOptions = {
    ...options,
    labels: analysis.platform.map((item: any) => `${item.label}（${item.source === 'facebook' ? 'FB' : t('pages.key_1318')}）`),
    colors: safeColors.slice(0, analysis.platform.length),
    legend: { position: "bottom" },
  };
  const platformSeries = analysis.platform.map((item: any) => item.value);
  // 地域分布
  const regionOptions = {
    ...options,
    labels: analysis.region.map((item: any) => `${item.label}（${item.source === 'facebook' ? 'FB' : t('pages.key_1318')}）`),
    colors: safeColors.slice(0, analysis.region.length),
    legend: { position: "bottom" },
  };
  const regionSeries = analysis.region.map((item: any) => item.value);
  // 兴趣分布
  const interestOptions = {
    ...options,
    labels: analysis.interest.map((item: any) => `${item.label}（${item.source === 'facebook' ? 'FB' : t('pages.key_1318')}）`),
    colors: safeColors.slice(0, analysis.interest.length),
    legend: { position: "bottom" },
  };
  const interestSeries = analysis.interest.map((item: any) => item.value);
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('pages.key_416')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Chart type="pie" height={300} options={genderOptions} series={genderSeries} />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('pages.key_415')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Chart type="pie" height={300} options={ageOptions} series={ageSeries} />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('pages.key_414')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Chart type="pie" height={300} options={platformOptions} series={platformSeries} />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('pages.key_413')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Chart type="pie" height={300} options={regionOptions} series={regionSeries} />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle className="text-base">{t('pages.key_409')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Chart type="pie" height={300} options={interestOptions} series={interestSeries} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const CreativeTabContent = ({ options, safeColors, onRef }: { options: any, safeColors: string[], onRef?: (refetch: () => void) => void }) => {
  const { t } = useTranslation();
  const { selectedTab } = useContext(TabsContext) || {};
  const { data, loading, refetch } = useQuery(CREATIVE_ANALYSIS, {
    skip: selectedTab !== 'creative',
    fetchPolicy: 'network-only',
  });
  useEffect(() => { onRef && onRef(refetch); }, [onRef, refetch]);
  const items = data?.creativeAnalysis?.items || [];
  if (loading) return <div className="flex items-center justify-center h-[300px]">加载中...</div>;
  if (!items.length) return <div className="flex items-center justify-center h-[300px] text-muted-foreground">{t('pages.key_1227')}</div>;
  // 按类型分组
  const getTypeData = (type: string) => items.filter((i: any) => i.type === type);
  const imageData = getTypeData(t('pages.key_530'));
  const videoData = getTypeData(t('pages.key_2062'));
  const carouselData = getTypeData(t('pages.key_193'));
  // 图表配置
  const chartOptions = {
    ...options,
    legend: { position: "bottom" },
    colors: safeColors,
  };
  return (
    <Tabs defaultValue="images">
      <TabsList className="mb-4">
        <TabsTrigger value="images">{t('pages.key_532')}</TabsTrigger>
        <TabsTrigger value="videos">{t('pages.key_2066')}</TabsTrigger>
        <TabsTrigger value="carousels">{t('pages.key_2370')}</TabsTrigger>
      </TabsList>
      <TabsContent value="images">
        <Chart
          type="bar"
          options={{ ...chartOptions, xaxis: { categories: imageData.map((i: any) => i.label) } }}
          series={[
            { name: t('pages.key_725'), data: imageData.map((i: any) => i.impressions) },
            { name: t('pages.key_1590'), data: imageData.map((i: any) => i.clicks) },
            { name: t('pages.key_2362'), data: imageData.map((i: any) => i.conversions) },
            { name: t('pages.key_1587'), data: imageData.map((i: any) => i.ctr) },
            { name: t('pages.key_2359'), data: imageData.map((i: any) => i.cvr) },
          ]}
          height={350}
        />
      </TabsContent>
      <TabsContent value="videos">
        <Chart
          type="bar"
          options={{ ...chartOptions, xaxis: { categories: videoData.map((i: any) => i.label) } }}
          series={[
            { name: t('pages.key_725'), data: videoData.map((i: any) => i.impressions) },
            { name: t('pages.key_1590'), data: videoData.map((i: any) => i.clicks) },
            { name: t('pages.key_2362'), data: videoData.map((i: any) => i.conversions) },
            { name: t('pages.key_1587'), data: videoData.map((i: any) => i.ctr) },
            { name: t('pages.key_2359'), data: videoData.map((i: any) => i.cvr) },
          ]}
          height={350}
        />
      </TabsContent>
      <TabsContent value="carousels">
        <Chart
          type="bar"
          options={{ ...chartOptions, xaxis: { categories: carouselData.map((i: any) => i.label) } }}
          series={[
            { name: t('pages.key_725'), data: carouselData.map((i: any) => i.impressions) },
            { name: t('pages.key_1590'), data: carouselData.map((i: any) => i.clicks) },
            { name: t('pages.key_2362'), data: carouselData.map((i: any) => i.conversions) },
            { name: t('pages.key_1587'), data: carouselData.map((i: any) => i.ctr) },
            { name: t('pages.key_2359'), data: carouselData.map((i: any) => i.cvr) },
          ]}
          height={350}
        />
      </TabsContent>
    </Tabs>
  );
};

const DataAnalysis = () => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState("7d");
  const [chartType, setChartType] = useState<ChartType>("line");
  const tenantId = useTenant();
  const tabsContext = useContext(TabsContext);
  const selectedTab = tabsContext?.selectedTab;
  const channelRef = useRef<(() => void) | null>(null);
  const audienceRef = useRef<(() => void) | null>(null);
  const creativeRef = useRef<(() => void) | null>(null);
  // 计算时间范围
  const getDateRange = () => {
    const end = new Date();
    let start = new Date();
    if (timeRange === '7d') start.setDate(end.getDate() - 6);
    else if (timeRange === '30d') start.setDate(end.getDate() - 29);
    else if (timeRange === '90d') start.setDate(end.getDate() - 89);
    else if (timeRange === '1y') start.setFullYear(end.getFullYear() - 1);
    return {
      startDate: start.toISOString().slice(0, 10),
      endDate: end.toISOString().slice(0, 10)
    };
  };
  const { startDate, endDate } = getDateRange();
  const { data, loading, refetch } = useQuery(TENANT_TREND, {
    variables: { tenantId, startDate, endDate },
    skip: !tenantId,
    fetchPolicy: 'network-only',
  });
  const trendData = data?.tenantTrend || [];

  // 使用Chart组件的配置
  const { colors, series } = useChart({});
  const options = useChart({});
  // colors 容错处理，保证为对象且有 primary/info/warning/error
  const safeColors = colors || ['#409EFF', '#909399', '#E6A23C', '#F56C6C'];

  // 性能指标图表配置
  const performanceChartOptions = {
    ...options,
    colors: [safeColors[0]],
    chart: {
      type: chartType,
      stacked: false,
      toolbar: { show: true },
    },
    stroke: { width: [3], curve: "smooth" as "smooth" },
    xaxis: {
      type: "category" as const,
      categories: trendData.map((item: any) => item.date),
    },
    yaxis: [
      { title: { text: t('pages.key_725') } },
    ],
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: function (y: number) { return y.toLocaleString() + " 次展示"; },
      },
    },
    legend: { position: "top" as const, horizontalAlign: "right" as const },
  };

  // 性能指标系列数据（用impressions做纵轴）
  const performanceSeries = [
    {
      name: t('pages.key_725'),
      type: chartType,
      data: trendData.map((item: any) => item.impressions || 0),
    },
  ];
  // 右侧卡片统计
  const totalImpressions = trendData.reduce((sum: number, item: any) => sum + (item.impressions || 0), 0);
  const totalClicks = trendData.reduce((sum: number, item: any) => sum + (item.clicks || 0), 0);
  const totalConversion = trendData.reduce((sum: number, item: any) => sum + (item.conversion || 0), 0);
  const totalSpend = trendData.reduce((sum: number, item: any) => sum + (item.spend || 0), 0);

  const handleRefresh = () => {
    if (selectedTab === 'channels') channelRef.current && channelRef.current();
    else if (selectedTab === 'audience') audienceRef.current && audienceRef.current();
    else if (selectedTab === 'creative') creativeRef.current && creativeRef.current();
    else if (selectedTab === 'performance') refetch && refetch();
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t('pages.key_795')}</CardTitle>
          <div className="flex items-center gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder={t('pages.key_2451')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">{t('pages.key_1284')}</SelectItem>
                <SelectItem value="30d">{t('pages.key_1283')}</SelectItem>
                <SelectItem value="90d">{t('pages.key_1285')}</SelectItem>
                <SelectItem value="1y">{t('pages.key_1282')}</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon" onClick={handleRefresh}>
              <Icon icon="solar:refresh-bold-duotone" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="performance" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="performance">{t('pages.key_905')}</TabsTrigger>
            <TabsTrigger value="channels">{t('pages.key_1560')}</TabsTrigger>
            <TabsTrigger value="audience">{t('pages.key_410')}</TabsTrigger>
            <TabsTrigger value="creative">{t('pages.key_267')}</TabsTrigger>
          </TabsList>

          <TabsContent value="performance" className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">{t('pages.key_783')}</h3>
              <div className="flex items-center gap-2">
                <Button
                  variant={chartType === "line" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setChartType("line")}
                >
                  <Icon icon="solar:chart-line-bold-duotone" className="mr-1" />{t('pages.key_1797')}</Button>
                <Button
                  variant={chartType === "bar" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setChartType("bar")}
                >
                  <Icon icon="solar:chart-column-bold-duotone" className="mr-1" />{t('pages.key_1370')}</Button>
              </div>
            </div>

            <div className="h-[320px]">
              <Chart options={performanceChartOptions} series={performanceSeries} type={chartType} height={320} />
            </div>

            <div className="grid grid-cols-4 gap-4 mt-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">{t('pages.key_915')}</p>
                      <h4 className="text-2xl font-bold">{totalImpressions.toLocaleString()}</h4>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">{t('pages.key_920')}</p>
                      <h4 className="text-2xl font-bold">{totalClicks.toLocaleString()}</h4>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">{t('pages.key_926')}</p>
                      <h4 className="text-2xl font-bold">{totalConversion.toLocaleString()}</h4>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">{t('pages.key_917')}</p>
                      <h4 className="text-2xl font-bold">¥{totalSpend.toLocaleString()}</h4>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="channels">
            <ChannelTabContent tenantId={tenantId} options={options} safeColors={safeColors} onRef={fn => (channelRef.current = fn)} />
          </TabsContent>

          <TabsContent value="audience">
            <AudienceTabContent tenantId={tenantId} options={options} safeColors={safeColors} onRef={fn => (audienceRef.current = fn)} />
          </TabsContent>

          <TabsContent value="creative">
            <CreativeTabContent options={options} safeColors={safeColors} onRef={fn => (creativeRef.current = fn)} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DataAnalysis;
