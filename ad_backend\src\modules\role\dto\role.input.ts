import { Field, ID, InputType } from '@nestjs/graphql';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

/**
 * 这个InputType用于GraphQL中的角色输入参数
 * 解决"Cannot determine a GraphQL input type ("Role") for the "roles""错误
 */
@InputType()
export class RoleInput {
    @Field(() => ID)
    @IsNotEmpty()
    @IsUUID('4')
    id: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    name?: string;
}

/**
 * 这个InputType用于创建或更新用户时的角色输入
 */
@InputType()
export class UserRoleInput {
    @Field(() => [ID])
    @IsNotEmpty()
    @IsUUID('4', { each: true })
    roleIds: string[];
} 