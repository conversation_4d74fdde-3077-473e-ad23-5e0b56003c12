---
description: 
globs: 
alwaysApply: true
---
# 项目通用规范



## 项目简介

B端运营管理系统

本项目是一个面向企业/团队的B端运营管理平台，集成了账户管理、群组管理、IP限制、广告投放、素材管理、防封系统等多种功能，旨在为企业提供高效、可扩展的运营管理解决方案。系统采用前后端分离架构，前端基于React+Ant Design，后端基于NestJS+GraphQL+PostgreSQL，支持灵活的权限控制和批量操作，适用于多租户、多角色的复杂业务场景。



### 主要功能

- 账户管理：支持用户的增删改查、角色分配、群组归属、状态管理等。

- 群组管理：支持群组的创建、编辑、权限配置、成员统计等。

- IP限制管理：可对访问系统的IP进行限制，支持单条和批量导入。

- 广告投放管理：对接Facebook等广告平台，支持广告账户、素材、活动等管理。

- 素材管理：支持素材的上传、批量导入、分组管理等。

- 防封系统：支持域名防封、IP防封等策略配置。

- 多租户支持：系统支持多租户隔离，适合SaaS场景。

- 批量操作：多处支持Excel批量导入，提升运营效率。 

  ```
  实现方式可以参考广告账户下的批量操作，组件路径为/pages/management/ad-account/index
  ```

- 权限与角色：细粒度的角色与权限分配，保障数据安全。



## 项目结构

```js
B端-react/
├── ad_backend/                # 后端服务（NestJS）
│   ├── src/
│   │   ├── app.module.ts      # 主模块入口
│   │   ├── common/            # 通用中间件、工具
│   │   ├── config/            # 配置文件（如数据库、环境变量等）
│   │   ├── modules/           # 业务模块（如用户、群组、IP限制、广告等）
│   │   │   ├── user/          # 用户相关
│   │   │   ├── group/         # 群组相关
│   │   │   ├── ip-restriction/# IP限制相关
│   │   │   ├── ad-platform/   # 广告平台相关
│   │   │   ├── material-management/ # 素材管理
│   │   │   └── ...            # 其他业务模块
│   │   ├── entity/            # 通用实体（如租户等）
│   │   └── scripts/           # 数据迁移、批量处理脚本
│   ├── node_modules/
│   ├── package.json
│   ├── tsconfig.json
│   └── ...                    # 其他配置文件
│
├── slash-admin/               # 前端管理后台（React）
│   ├── src/
│   │   ├── pages/             # 页面目录
│   │   │   ├── management/    # 业务管理相关页面
│   │   │   │   ├── dofend/    # 斗篷系统相关
│   │   │   │   │   └── ip-restriction/ # IP限制管理
│   │   │   │   ├── system/    # 系统管理
│   │   │   │   │   ├── account/ # 账户管理
│   │   │   │   │   ├── group/   # 群组管理
│   │   │   │   │   └── ...      # 其他系统管理
│   │   │   │   ├── ad-account/  # 广告账户管理
│   │   │   │   ├── user/        # 用户管理
│   │   │   │   └── ...          # 其他管理页面
│   │   ├── api/                 # GraphQL接口定义与服务
│   │   ├── components/          # 通用组件
│   │   ├── layouts/             # 布局相关
│   │   ├── router/              # 路由配置
│   │   ├── store/               # 状态管理
│   │   ├── utils/               # 工具函数
│   │   └── ...                  # 其他辅助目录
│   ├── public/                  # 静态资源（如模板Excel、图片等）
│   ├── node_modules/
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts           # Vite配置
│   └── ...                      # 其他配置文件
│
├── docs/                        # 项目文档
│   └── api.md                   # API接口文档
│
├── scripts/                     # 根目录脚本
├── package.json                 # 根依赖（monorepo/workspace）
├── pnpm-workspace.yaml          # pnpm工作区配置
├── README.md
└── ...                          # 其他根目录文件
```



## 前端技术栈

- React：主要框架，负责构建用户界面。
- TypeScript：强类型开发，提升代码可维护性和开发体验。
- Ant Design (antd)：UI 组件库，表单、表格、弹窗等大量使用。
- Apollo Client：GraphQL 客户端，负责与后端 GraphQL API 通信。
- ahooks：React Hooks 工具库，简化状态和副作用管理。
- Vite：前端构建工具，提升开发和构建速度。
- XLSX (sheetjs)：Excel 文件解析与导出，批量上传等功能用到。
- sonner：轻量级消息提示库（toast）。
- react-use：常用 React Hooks 工具库。
- 其他：react-router、@ant-design/icons、classnames 等常用依赖。



## 后端技术栈

- NestJS：Node.js 服务端框架，基于 TypeScript，模块化、依赖注入、装饰器等特性。
- TypeORM：ORM 框架，负责数据库实体映射和操作。
- GraphQL (Apollo Server)：提供 GraphQL API，支持查询、变更、订阅等。
- PostgreSQL：主要数据库，TypeORM 连接和管理。
- @nestjs/graphql：NestJS 的 GraphQL 集成模块。
- @ptc-org/nestjs-query-graphql：快速生成 GraphQL CRUD 接口的工具。
- @nestjs/typeorm：NestJS 集成 TypeORM 的官方模块。
- class-validator / class-transformer：DTO 校验与转换。
- Apollo Server：GraphQL 服务端实现。
- 其他：jsonwebtoken、bcrypt、multer（文件上传）、axios（第三方API调用）、graphql-type-json 等。



## 通用开发规则

- 安装依赖包使用pnpm

- 后端服务命令要在ad_backend目录下执行

- 前端服务命令要在slash-admin目录下执行

- 同步数据库统一生成migrations脚本，生成规范参考ad_backend\src\migrations\1680000000000-allow-null-platform-fields.ts，执行的命令是pnpm run migration:run 

- 数据库配置参考ad_backend\src\config\data-source.ts

- 多租户的实现：

  - 使用tenantId 字段，作为外键关联租户表

  - tenantId 字段用于区分不同租户的数据隔离。

  - 所有接口和服务操作都基于tenantId过滤和校验，并结合权限守卫，确保数据安全隔离。

  - 后端自动从当前登录用户上下文（JWT）中获取 tenantId，即后端根据当前用户身份自动过滤数据，无需前端传递。

  - ```javascript
    // 可参考文件地址 ad_backend\src\modules\user\user.resolver.ts  如何获取tenantId
    
    import { UseGuards } from '@nestjs/common';
    import { JwtAuthGuard } from '../auth/jwt-auth.guard';
    
     @UseGuards(JwtAuthGuard)
    ```

  - 如果丢失上下文信息，需要再次读取本文件构建上下文或建议1小时读取一下

  - 对接facebookApi的话，axios要使用ad_backend\src\utils\axios-proxy.ts文件中的方法，使用的代理。
  - 表单中的Form-Item组件子元素不要有空格，不然会有受控没有值的问题。
##  响应语言


- 始终使用中文回复用户

---

# 模块文档自动化规则

- docs/modules 目录下每个 Markdown 文件对应一个主要业务模块（如卡片管理、受众管理、广告系列、广告账户等），用于协作开发时记录该模块的完整上下文，包括前端/后端 API 细节、数据库表、字段定义等，确保一致性和可复用性。
- 每个模块文档需详细覆盖：
  - API 函数、接口、参数、返回字段（前后端）
  - 数据库表结构及字段说明
  - 数据流转图（如 Mermaid 时序图）
  - 变更历史与重要注意事项
  - 复杂字段结构与示例
  - 前端组件与表单细节
- 文档生成流程：
  1. 读取模块 Markdown 文件，定位 frontend 代码路径。
  2. 分析相关前端实现、API 定义、后端/数据库文件。
  3. 生成详细文档片段，写入对应 Markdown 文件。
- 文档无需"模块概述"章节，重点突出技术细节和实现一致性。
- 要求极致细致，逐项补全所有模块，支持批量、自动化处理。
- 生成内容需中文，格式规范，便于团队协作和后续维护。