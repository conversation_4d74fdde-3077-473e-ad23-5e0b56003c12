const { Client } = require('pg');

// 数据库配置
const client = new Client({
    host: "pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com",
    port: 5432,
    user: "yeeu",
    password: "Yy&20240505",
    database: "ad_auto",
});

async function main() {
    await client.connect();
    // 1. 查询所有 audience
    const { rows } = await client.query('SELECT id, interests FROM audience');
    for (const row of rows) {
        if (!Array.isArray(row.interests)) continue;
        let needUpdate = false;
        const newInterests = [];
        for (const i of row.interests) {
            if (i && i.id && (!i.name || i.name === null)) {
                newInterests.push({ id: i.id, name: '旅游' });
                needUpdate = true;
            } else {
                newInterests.push(i);
            }
        }
        // 3. 更新数据库
        if (needUpdate) {
            await client.query(
                'UPDATE audience SET interests = $1 WHERE id = $2',
                [JSON.stringify(newInterests), row.id]
            );
            console.log(`已修复 audience ${row.id}`);
        }
    }
    await client.end();
    console.log('全部修复完成');
}

main();