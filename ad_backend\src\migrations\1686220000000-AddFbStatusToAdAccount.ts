import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFbStatusToAdAccount1686220000000 implements MigrationInterface {
    name = 'AddFbStatusToAdAccount1686220000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD "fbStatus" character varying(32)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "fbStatus"`);
    }
} 