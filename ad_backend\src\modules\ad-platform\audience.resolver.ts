import { Resolver, Mutation, Args, Query, ID, Context, ObjectType, Field } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Audience } from './entities/audience.entity';
import { CreateAudience, AudienceDTO, AudienceCursorPaging, AudienceCustomFilter } from './entities/audience.dto';
import { plainToInstance } from 'class-transformer';
import { Tenant } from '../../entity/tenant.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/auth/jwt-auth.guard';
import { TenantGuard } from 'src/guards/tenant.guard';
import { v4 as uuidv4 } from 'uuid';
import { AdAccount } from './entities/ad-account.entity';
import { FacebookApiService } from './services/facebook-api.service';

@ObjectType('AudienceOption')
export class AudienceOption {
    @Field()
    id: string;
    @Field({ nullable: true })
    name?: string;
}

@ObjectType()
export class AudienceAnalysisItem {
    @Field()
    key: string;
    @Field()
    label: string;
    @Field()
    value: number;
    @Field({ nullable: true })
    source?: string; // "local" | "facebook"
}

@ObjectType()
export class AudienceAnalysisResult {
    @Field()
    total: number;
    @Field(() => [AudienceAnalysisItem])
    gender: AudienceAnalysisItem[];
    @Field(() => [AudienceAnalysisItem])
    age: AudienceAnalysisItem[];
    @Field(() => [AudienceAnalysisItem])
    platform: AudienceAnalysisItem[];
    @Field(() => [AudienceAnalysisItem])
    region: AudienceAnalysisItem[];
    @Field(() => [AudienceAnalysisItem])
    interest: AudienceAnalysisItem[];
}

@Resolver(() => AudienceDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class AudienceResolver {
    constructor(
        @InjectRepository(Audience) private readonly audienceRepo: Repository<Audience>,
        @InjectRepository(AdAccount) private readonly adAccountRepo: Repository<AdAccount>,
        private readonly facebookApiService: FacebookApiService,
    ) { }

    @Query(() => [AudienceDTO])
    async audiences(
        @Args('filter', { type: () => AudienceCustomFilter, nullable: true }) filter?: any,
        @Args('paging', { type: () => AudienceCursorPaging, nullable: true }) paging?: any,
    ): Promise<AudienceDTO[]> {
        // 只支持 name 筛选和分页，移除 country 等已删除字段
        let qb = this.audienceRepo.createQueryBuilder('audience');
        if (filter?.name) {
            qb = qb.andWhere('audience.name LIKE :name', { name: `%${filter.name}%` });
        }
        if (paging?.first) {
            qb = qb.take(paging.first);
        }
        if (paging?.after) {
            qb = qb.andWhere('audience.id > :after', { after: paging.after });
        }
        const list = await qb.getMany();
        return list.map(item => {
            const dto = plainToInstance(AudienceDTO, item);
            dto.gender = typeof item.gender === 'number' ? item.gender : null;
            dto.geo_locations = item.geo_locations ?? { countries: [], regions: [], cities: [] };
            dto.excluded_geo_locations = item.excluded_geo_locations ?? { countries: [], regions: [], cities: [] };
            dto.age_min = typeof item.age_min === 'number' ? item.age_min : null;
            dto.age_max = typeof item.age_max === 'number' ? item.age_max : null;
            dto.interests = Array.isArray(item.interests) ? item.interests.filter(i => i && i.id) : [];
            dto.behaviors = Array.isArray(item.behaviors) ? item.behaviors.filter(b => b && b.id) : [];
            dto.locales = Array.isArray(item.locales) ? item.locales : [];
            dto.notes = item.notes ?? '';
            dto.platform = Array.isArray(item.platform) ? item.platform : [];
            return dto;
        });
    }

    @Query(() => AudienceDTO, { nullable: true })
    async audience(@Args('id', { type: () => ID }) id: string): Promise<AudienceDTO | null> {
        const entity = await this.audienceRepo.findOne({ where: { id } });
        return entity ? plainToInstance(AudienceDTO, entity) : null;
    }

    @Mutation(() => AudienceDTO)
    async createAudience(
        @Args('input', { type: () => CreateAudience }) input: CreateAudience,
        @Context() ctx: any,
    ): Promise<AudienceDTO> {
        const tenantId = ctx.tenantId;
        if (!tenantId) throw new Error('No tenantId in context');
        const tenant = await this.audienceRepo.manager.findOne(Tenant, { where: { id: tenantId } });
        if (!tenant) throw new Error('Tenant not found');
        // 防御性处理，防止 "[]" 字符串传入数组字段
        const fixArray = (v: any) => (v === '[]' ? [] : v);
        // interests/behaviors 防御性处理，始终转为 {id: string} 对象数组
        const fixInterestArray = (arr: any) =>
            Array.isArray(arr) ? arr.map(i => typeof i === 'string' ? { id: i } : i).filter(i => i && i.id) : [];
        // geo_locations/excluded_geo_locations 防御性处理
        const fixGeo = (geo: any) => ({
            countries: Array.isArray(geo?.countries) ? geo.countries : [],
            regions: Array.isArray(geo?.regions) ? geo.regions : [],
            cities: Array.isArray(geo?.cities) ? geo.cities : [],
        });
        const entity = this.audienceRepo.create({
            ...input,
            interests: fixInterestArray(input.interests),
            behaviors: fixInterestArray(input.behaviors),
            platform: fixArray(input.platform) ?? [],
            audienceId: uuidv4(),
            tenant,
            geo_locations: fixGeo(input.geo_locations),
            excluded_geo_locations: fixGeo(input.excluded_geo_locations),
            age_min: typeof input.age_min === 'number' ? input.age_min : null,
            age_max: typeof input.age_max === 'number' ? input.age_max : null,
            notes: input.notes ?? '',
            locales: Array.isArray(fixArray(input.locales)) ? fixArray(input.locales) : [],
        });
        // 详细日志
        console.log('createAudience input:', input);
        console.log('createAudience entity:', entity);
        console.log('interests:', input.interests, typeof input.interests);
        console.log('behaviors:', input.behaviors, typeof input.behaviors);
        console.log('platform:', input.platform, typeof input.platform);
        console.log('locales:', input.locales, typeof input.locales);
        const saved = await this.audienceRepo.save(entity);
        return plainToInstance(AudienceDTO, saved);
    }

    @Mutation(() => AudienceDTO)
    async updateAudience(
        @Args('id', { type: () => ID }) id: string,
        @Args('input', { type: () => CreateAudience }) input: CreateAudience,
        @Context() ctx: any,
    ): Promise<AudienceDTO> {
        const entity = await this.audienceRepo.findOne({ where: { id } });
        if (!entity) throw new Error('Audience not found');
        // 防御性处理，防止 "[]" 字符串传入数组字段
        const fixArray = (v: any) => (v === '[]' ? [] : v);
        // interests/behaviors 防御性处理，始终转为 {id: string} 对象数组
        const fixInterestArray = (arr: any) =>
            Array.isArray(arr) ? arr.map(i => typeof i === 'string' ? { id: i } : i).filter(i => i && i.id) : [];
        // geo_locations/excluded_geo_locations 防御性处理
        const fixGeo = (geo: any) => ({
            countries: Array.isArray(geo?.countries) ? geo.countries : [],
            regions: Array.isArray(geo?.regions) ? geo.regions : [],
            cities: Array.isArray(geo?.cities) ? geo.cities : [],
        });
        const patch = {
            ...input,
            interests: fixInterestArray(input.interests),
            behaviors: fixInterestArray(input.behaviors),
            platform: fixArray(input.platform) ?? [],
            geo_locations: fixGeo(input.geo_locations),
            excluded_geo_locations: fixGeo(input.excluded_geo_locations),
            age_min: typeof input.age_min === 'number' ? input.age_min : null,
            age_max: typeof input.age_max === 'number' ? input.age_max : null,
            notes: input.notes ?? '',
            locales: Array.isArray(fixArray(input.locales)) ? fixArray(input.locales) : [],
        };
        Object.assign(entity, patch);
        const saved = await this.audienceRepo.save(entity);
        return plainToInstance(AudienceDTO, saved);
    }

    @Mutation(() => Boolean)
    async deleteAudience(@Args('id', { type: () => ID }) id: string): Promise<boolean> {
        const res = await this.audienceRepo.delete(id);
        return res.affected > 0;
    }

    @Query(() => [AudienceOption], { description: '兴趣选项列表' })
    async audienceInterestOptions(): Promise<AudienceOption[]> {
        const all = await this.audienceRepo.find();
        const map = new Map<string, AudienceOption>();
        all.forEach(aud => {
            (aud.interests || []).forEach((item: any) => {
                if (item?.id) map.set(item.id, { id: item.id, name: item.name });
            });
        });
        return Array.from(map.values());
    }

    @Query(() => [AudienceOption], { description: '行为选项列表' })
    async audienceBehaviorOptions(): Promise<AudienceOption[]> {
        const all = await this.audienceRepo.find();
        const map = new Map<string, AudienceOption>();
        all.forEach(aud => {
            (aud.behaviors || []).forEach((item: any) => {
                if (item?.id) map.set(item.id, { id: item.id, name: item.name });
            });
        });
        return Array.from(map.values());
    }

    @Query(() => AudienceAnalysisResult)
    async audienceAnalysis(
        @Context() ctx: any,
    ): Promise<AudienceAnalysisResult> {
        const { tenantId, isSuperAdmin } = ctx;
        // 1. 本地受众
        const localAudiences = await this.audienceRepo.find({ where: isSuperAdmin ? {} : { tenant: { id: tenantId } } });
        // 2. Facebook账户
        const adAccount = await this.adAccountRepo.findOne({ where: isSuperAdmin ? {} : { tenant: { id: tenantId }, platform: 'facebook' } });
        let fbAudiences: any[] = [];
        if (adAccount?.accountId && adAccount?.accessToken) {
            try {
                fbAudiences = await this.facebookApiService.getCustomAudiences(adAccount.accountId, adAccount.accessToken);
            } catch (e) {
                fbAudiences = [];
            }
        }
        // 3. 聚合分析
        const groupBy = (arr: any[], key: string) => arr.reduce((acc, cur) => {
            const k = cur[key] ?? '未知';
            acc[k] = (acc[k] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);
        const genderMap = { 1: '男', 2: '女', 0: '不限', null: '不限', undefined: '不限', '未知': '不限' };
        // 性别
        const localGender = Object.entries(groupBy(localAudiences, 'gender')).map(([k, v]) => ({
            key: String(k), label: genderMap[k] || '不限', value: v as number, source: 'local'
        }));
        const fbGender = fbAudiences.length ? Object.entries(groupBy(fbAudiences, 'gender')).map(([k, v]) => ({
            key: String(k), label: genderMap[k] || '不限', value: v as number, source: 'facebook'
        })) : [];
        // 年龄段
        const ageBuckets = [
            { key: '18-24', min: 18, max: 24 },
            { key: '25-34', min: 25, max: 34 },
            { key: '35-44', min: 35, max: 44 },
            { key: '45-54', min: 45, max: 54 },
            { key: '55+', min: 55, max: 200 },
        ];
        const bucketAge = (arr: any[], source: string) => ageBuckets.map(b => ({
            key: b.key,
            label: b.key,
            value: arr.filter(a => (a.age_min ?? 0) >= b.min && (a.age_max ?? 200) <= b.max).length,
            source,
        }));
        const localAge = bucketAge(localAudiences, 'local');
        const fbAge = fbAudiences.length ? bucketAge(fbAudiences, 'facebook') : [];
        // 平台
        const flatPlatform = (arr: any[]) => arr.flatMap(a => Array.isArray(a.platform) ? a.platform : []);
        const groupPlatform = (arr: any[], source: string) => {
            const map = groupBy(flatPlatform(arr), 'platform');
            return Object.entries(map).map(([k, v]) => ({ key: String(k), label: k, value: v as number, source }));
        };
        const localPlatform = groupPlatform(localAudiences, 'local');
        const fbPlatform = fbAudiences.length ? groupPlatform(fbAudiences, 'facebook') : [];
        // 地域（以国家为例）
        const getCountries = (a: any) => (a.geo_locations?.countries || []);
        const flatCountries = (arr: any[]) => arr.flatMap(getCountries);
        const groupRegion = (arr: any[], source: string) => {
            const map = groupBy(flatCountries(arr), 'country');
            return Object.entries(map).map(([k, v]) => ({ key: String(k), label: k, value: v as number, source }));
        };
        const localRegion = groupRegion(localAudiences, 'local');
        const fbRegion = fbAudiences.length ? groupRegion(fbAudiences, 'facebook') : [];
        // 兴趣
        const getInterests = (a: any) => (a.interests || []).map((i: any) => i.name || i.id);
        const flatInterests = (arr: any[]) => arr.flatMap(getInterests);
        const groupInterest = (arr: any[], source: string) => {
            const map = groupBy(flatInterests(arr), 'interest');
            return Object.entries(map).map(([k, v]) => ({ key: String(k), label: k, value: v as number, source }));
        };
        const localInterest = groupInterest(localAudiences, 'local');
        const fbInterest = fbAudiences.length ? groupInterest(fbAudiences, 'facebook') : [];
        return {
            total: localAudiences.length + fbAudiences.length,
            gender: [...localGender, ...fbGender],
            age: [...localAge, ...fbAge],
            platform: [...localPlatform, ...fbPlatform],
            region: [...localRegion, ...fbRegion],
            interest: [...localInterest, ...fbInterest],
        };
    }
} 