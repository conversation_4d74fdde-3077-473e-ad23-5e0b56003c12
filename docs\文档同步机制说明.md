# 文档同步机制说明

## 概述

本项目建立了完整的文档同步机制，确保代码变更时能够自动检测并更新相关的模块文档，保持文档与代码的一致性。

## 架构组成

### 1. 代码变更检测
- **脚本**: `scripts/doc-sync/detect-changes.js`
- **功能**: 检测 Entity、GraphQL Schema、Resolver 等关键代码的变更
- **触发**: Git Hooks、CI/CD、手动执行

### 2. 文档自动更新
- **脚本**: `scripts/doc-sync/update-docs.js`
- **功能**: 根据代码变更自动更新模块文档中的API接口、字段定义等
- **支持**: 数据库表结构、GraphQL API、参数说明等

### 3. 文档验证
- **脚本**: `scripts/doc-sync/validate-docs.js`
- **功能**: 验证文档与代码的一致性，生成验证报告
- **检查**: 字段类型、API接口、文档结构等

### 4. Git Hooks 集成
- **pre-commit**: 提交前检测代码变更，提示文档更新
- **post-commit**: 提交后自动更新文档并创建新提交
- **pre-push**: 推送前验证文档一致性

### 5. CI/CD 集成
- **GitHub Actions**: 自动化文档同步检查和更新
- **PR 评论**: 在 Pull Request 中自动评论文档检查结果
- **报告上传**: 保存验证报告和更新记录

## 使用方法

### 安装和配置

1. **安装 Git Hooks**:
   ```bash
   npm run doc:setup
   ```

2. **安装依赖**:
   ```bash
   npm install
   npm install -g glob
   ```

### 日常使用

1. **手动检测变更**:
   ```bash
   npm run doc:detect
   ```

2. **手动更新文档**:
   ```bash
   npm run doc:update
   ```

3. **验证文档一致性**:
   ```bash
   npm run doc:validate
   ```

4. **一键同步**:
   ```bash
   npm run doc:sync
   ```

### 自动化流程

1. **提交代码时**:
   - pre-commit hook 自动检测变更
   - 如有变更，提示是否需要更新文档
   - post-commit hook 自动更新文档并创建新提交

2. **推送代码时**:
   - pre-push hook 验证文档一致性
   - 如有问题，阻止推送并提示修复

3. **CI/CD 流程**:
   - 自动检测代码变更
   - 验证文档一致性
   - 在主分支自动更新文档
   - 在 PR 中评论检查结果

## 文档审查流程

### 自动更新后的人工审查

1. **审查触发条件**:
   - 自动更新后的文档变更
   - 验证报告中的警告或错误
   - 复杂的代码变更

2. **审查内容**:
   - 检查自动生成的表结构是否准确
   - 验证 API 接口描述是否完整
   - 确认字段类型和约束是否正确
   - 检查中文描述是否合适

3. **审查流程**:
   ```bash
   # 1. 查看变更检测结果
   cat scripts/doc-sync/changes.json
   
   # 2. 查看更新报告
   cat scripts/doc-sync/update-report.md
   
   # 3. 查看验证报告
   cat scripts/doc-sync/validation-report.md
   
   # 4. 手动检查文档变更
   git diff docs/
   
   # 5. 如需修正，手动编辑文档
   # 6. 重新验证
   npm run doc:validate
   ```

### 质量保证措施

1. **多层验证**:
   - 代码变更检测
   - 自动更新验证
   - 人工审查确认

2. **回滚机制**:
   - Git 版本控制
   - 自动提交记录
   - 手动回滚支持

3. **报告机制**:
   - 详细的变更记录
   - 验证结果报告
   - CI/CD 状态反馈

## 支持的变更类型

### Entity 变更
- 新增/删除字段
- 字段类型变更
- 约束条件变更
- 关联关系变更

### GraphQL 变更
- 新增/删除查询
- 新增/删除变更
- 参数变更
- 返回字段变更

### 组件变更
- 路径变更
- API 调用变更
- 参数结构变更

## 配置说明

### 模块映射配置
在 `scripts/doc-sync/update-docs.js` 中配置代码模块到文档文件的映射：

```javascript
this.moduleMapping = {
  'ad-platform': ['z 广告账户.md', 'z 广告系列.md', 'z 受众管理.md'],
  'vcc': ['z 卡片管理.md'],
  'material-management': ['z 素材管理.md'],
  // ...
};
```

### 类型映射配置
在 `scripts/doc-sync/validate-docs.js` 中配置 TypeORM 类型到文档类型的映射：

```javascript
const typeMapping = {
  'string': 'varchar',
  'number': 'int',
  'Date': 'timestamp with time zone',
  // ...
};
```

## 故障排除

### 常见问题

1. **检测脚本失败**:
   - 检查 Node.js 版本 (需要 18+)
   - 确认 glob 包已安装
   - 检查文件路径是否正确

2. **文档更新失败**:
   - 检查文档文件是否存在
   - 确认文档格式是否正确
   - 检查权限问题

3. **验证失败**:
   - 查看验证报告详情
   - 手动检查代码与文档差异
   - 确认映射配置是否正确

### 调试方法

1. **启用详细日志**:
   ```bash
   DEBUG=doc-sync npm run doc:sync
   ```

2. **单独运行各步骤**:
   ```bash
   node scripts/doc-sync/detect-changes.js
   node scripts/doc-sync/update-docs.js
   node scripts/doc-sync/validate-docs.js
   ```

3. **查看生成的文件**:
   ```bash
   cat scripts/doc-sync/changes.json
   cat scripts/doc-sync/update-report.md
   cat scripts/doc-sync/validation-report.md
   ```

## 最佳实践

1. **定期验证**:
   - 每周运行一次完整验证
   - 重大变更后手动检查
   - 发布前确保文档同步

2. **团队协作**:
   - 新成员培训文档同步流程
   - 代码审查时检查文档变更
   - 建立文档质量标准

3. **持续改进**:
   - 收集使用反馈
   - 优化检测规则
   - 完善映射配置

## 联系支持

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 检查 GitHub Issues
3. 联系项目维护者
