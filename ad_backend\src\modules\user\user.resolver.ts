import { Resolver, Query, Mutation, Args, Context, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { UserService } from './user.service';
import { UserDTO } from './dto/user.dto';
import { UserConnection } from './dto/user-connection.dto';
import { CreateUserInput } from './dto/create-user.input';
import { UpdateUserInput } from './dto/update-user.input';
import { UserListInput, UserSortInput } from './dto/user-filter.input';
import { UserStatus } from './user.entity';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../auth/../../guards/tenant.guard';
import { plainToInstance } from 'class-transformer';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => UserDTO)
export class UserResolver {
    constructor(private readonly userService: UserService) { }

    @Query(() => UserConnection)
    async users(
        @Args('input', { nullable: true, type: () => UserListInput }) input: UserListInput,
        @Args('sorting', { nullable: true, type: () => [UserSortInput] }) sorting: UserSortInput[],
        @Context() context: any
    ): Promise<UserConnection> {
        const result = await this.userService.getUsers(input, sorting, context.req.user);
        const page = input?.page || 1;
        const limit = input?.limit || 10;
        return {
            nodes: plainToInstance(UserDTO, result.nodes),
            totalCount: result.totalCount,
            pageInfo: {
                hasNextPage: page * limit < result.totalCount,
                hasPreviousPage: page > 1,
            },
        };
    }

    @Query(() => UserDTO)
    async user(
        @Args('id', { type: () => ID }) id: string,
        @Context() context: any
    ): Promise<UserDTO> {
        const currentUser = context.req.user ? await this.userService.findOne(context.req.user.userId, undefined, false) : undefined;
        const user = await this.userService.findOne(id, currentUser);
        return plainToInstance(UserDTO, user);
    }

    @Mutation(() => UserDTO)
    async createUser(
        @Args('input') createUserInput: CreateUserInput,
        @Context() context: any
    ): Promise<UserDTO> {
        const user = await this.userService.create(createUserInput, context.req.user);
        return plainToInstance(UserDTO, user);
    }

    @Mutation(() => UserDTO)
    async updateUser(
        @Args('input') updateUserInput: UpdateUserInput,
        @Context() context: any
    ): Promise<UserDTO> {
        const user = await this.userService.update(updateUserInput.id, updateUserInput, context.req.user);
        return plainToInstance(UserDTO, user);
    }

    @Mutation(() => UserDTO)
    async deleteUser(
        @Args('id', { type: () => ID }) id: string,
        @Context() context: any
    ): Promise<UserDTO> {
        const user = await this.userService.delete(id);
        return plainToInstance(UserDTO, user);
    }

    @Mutation(() => UserDTO)
    async changeUserStatus(
        @Args('id', { type: () => ID }) id: string,
        @Args('status', { type: () => UserStatus }) status: UserStatus,
        @Context() context: any
    ): Promise<UserDTO> {
        const user = await this.userService.changeStatus(id, status);
        return plainToInstance(UserDTO, user);
    }
} 