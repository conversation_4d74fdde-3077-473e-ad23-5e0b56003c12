import { InputType, Field, ObjectType, ID } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';
import { IsString, IsOptional } from 'class-validator';
import { LandingPageDTO } from '../../landing-page/dto/landing-page.dto';

@InputType()
export class CreatePromotionDto {
    @Field()
    @IsString()
    code: string;
    @Field()
    @IsString()
    platform: string;
    @Field()
    @IsString()
    landingPageCode: string;
    @Field()
    @IsString()
    landingPageUrl: string;
    @Field()
    @IsString()
    status: string;
    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    platformParams?: any;
    @Field({ nullable: true, description: '由后端自动填充' })
    @IsOptional()
    @IsString()
    tenantId?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    appDomain?: string;
}

@InputType()
export class UpdatePromotionDto {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    code?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    platform?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    landingPageCode?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    landingPageUrl?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    status?: string;
    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    platformParams?: any;
    @Field({ nullable: true, description: '由后端自动填充' })
    @IsOptional()
    @IsString()
    tenantId?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    appDomain?: string;
}

@ObjectType()
export class PromotionDto {
    @Field(() => ID)
    id: string;
    @Field()
    code: string;
    @Field()
    platform: string;
    @Field()
    landingPageCode: string;
    @Field()
    landingPageUrl: string;
    @Field()
    status: string;
    @Field()
    createdAt: Date;
    @Field({ nullable: true })
    creator?: string;
    @Field(() => GraphQLJSON, { nullable: true })
    platformParams?: any;
    @Field({ nullable: true })
    landingPageName?: string;
    @Field()
    tenantId: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    appDomain?: string;
}

@ObjectType()
export class PromotionDetailDto {
    @Field(() => PromotionDto)
    promotion: PromotionDto;
    @Field(() => LandingPageDTO, { nullable: true })
    landingPage?: LandingPageDTO;
} 