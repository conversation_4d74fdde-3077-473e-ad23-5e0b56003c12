# 组件路径   /pages/management/landing-page/index

## 前端

- **实际路径**：slash-admin/src/pages/management/landing-page/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，落地页管理表单与列表，含增删改查、分页、搜索、弹窗、推广渠道管理等）
  - LandingPageModal.tsx（落地页表单弹窗）
- **主要依赖API定义**：./graphql.ts

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取落地页列表  | query    | getLandingPages        | 无           | id, code, title, url, status, createdAt, creator, domain | 支持分页、搜索、过滤 |
| 新增落地页      | mutation | createLandingPage      | input: LandingPageInput | id, code, title, url, ... | 新建落地页，id为UUID |
| 编辑落地页      | mutation | updateLandingPage      | input: LandingPageInput | id, code, title, url, ... | 编辑指定落地页 |
| 删除落地页      | mutation | deleteLandingPage      | id: string    | id       | 删除指定落地页 |
| 获取推广渠道列表| query    | getPromotions          | 无           | id, code, platform, landingPageName, landingPageUrl, ... | 获取渠道推广配置 |
| 新增推广渠道    | mutation | createPromotion        | input: PromotionInput | id, code, ... | 新建推广渠道 |
| 编辑推广渠道    | mutation | updatePromotion        | input: PromotionInput | id, code, ... | 编辑推广渠道 |
| 删除推广渠道    | mutation | deletePromotion        | id: string    | id       | 删除推广渠道 |

#### 主要参数结构说明

- **LandingPageInput**（用于新增/编辑）  
  ```ts
  {
    id?: string; // 落地页ID，编辑时必填
    code: string; // 编码
    title: string; // 标题
    url: string; // 落地页URL
    status?: string; // 状态（active/inactive）
    domain?: string; // 域名
    ... // 其他业务字段
  }
  ```
- **PromotionInput**（用于渠道推广配置）
  ```ts
  {
    id?: string;
    code: string;
    platform: string; // Facebook/Google/Instagram/TikTok等
    landingPageCode: string;
    landingPageUrl: string;
    status?: string; // enabled/disabled
    platformParams?: object; // pixel_id、access_token等
    ...
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | id          | string    | 主键，UUID                 |
  | code        | string    | 编码                       |
  | title       | string    | 标题                       |
  | url         | string    | 落地页URL                  |
  | status      | string    | 状态                       |
  | createdAt   | Date      | 创建时间                   |
  | creator     | string    | 创建人                     |
  | domain      | string    | 域名                       |

### 前端表单/组件说明

- 支持落地页的增删改查，表单字段与上方LandingPageInput结构一致
- 支持推广渠道配置，自动拼接落地页URL及参数
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）
- 支持多平台推广参数配置（pixel_id、access_token、conversion_id等）

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（landing_page, promotion，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| code        | varchar     | 编码                       | UNIQUE, NOT NULL |
| title       | varchar     | 标题                       | NOT NULL     |
| url         | varchar     | 落地页URL                  | NOT NULL     |
| status      | varchar     | 状态                       | 默认active   |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| creator     | varchar     | 创建人                     | 可空         |
| domain      | varchar     | 域名                       | 可空         |
| ...         | ...         | 其他业务字段               | ...          |

- **推广渠道表结构**（promotion）：
  - id, code, platform, landingPageCode, landingPageUrl, status, platformParams, createdAt, ...

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query getLandingPages / mutation createLandingPage(input) / ...
    后端->>数据库: 操作 landing_page, promotion 表
    数据库-->>后端: 返回落地页/推广数据
    后端-->>前端: 返回落地页/推广数据
```

---

## 变更历史/注意事项

- 落地页URL需唯一，所有操作需校验唯一性
- 支持多平台推广参数扩展（pixel_id、access_token等）
- 删除落地页时需校验是否被推广渠道等引用
- 复杂业务字段建议在表结构中用jsonb扩展