import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { AdAccount } from './ad-account.entity';

@ObjectType('Campaign')
@Entity('campaign')
export class Campaign {
    @Field(() => ID)
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Field()
    @Column({ length: 128 })
    name: string;

    @Field()
    @Column({ length: 64 })
    objective: string;

    @Field()
    @Column({ length: 32 })
    status: string;

    @Field(() => [String])
    @Column({ type: 'text', array: true, default: '{}' })
    specialAdCategories: string[];

    @Field({ nullable: true })
    @Column({ type: 'varchar', length: 64, nullable: true })
    facebookCampaignId?: string;

    @Field()
    @CreateDateColumn()
    createdAt: Date;

    @Field()
    @UpdateDateColumn()
    updatedAt: Date;

    @Field(() => AdAccount)
    @ManyToOne(() => AdAccount, { nullable: false })
    @JoinColumn({ name: 'accountId', referencedColumnName: 'accountId' })
    adAccount: AdAccount;

    @Column({ name: 'accountId', type: 'varchar', length: 64 })
    private _accountId: string;

    @Field()
    get accountId(): string {
        return this._accountId || this.adAccount?.accountId;
    }

    @Field({ nullable: true })
    @Column({ nullable: true })
    syncStatus?: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    syncError?: string;
} 