import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { VccTransactionService, PaginatedResult } from '../services/vcc-transaction.service';
import {
    VccTransactionDTO,
    CreateVccTransactionInputDTO,
    UpdateVccTransactionInputDTO,
    VccTransactionFilterInputDTO,
    VccTransactionPaginationInputDTO,
    VccTransactionStatsDTO,
    PaginatedVccTransactionResult,
    SyncFacebookTransactionsInputDTO
} from '../dto/vcc-transaction.dto';

@Resolver(() => VccTransactionDTO)
@UseGuards(JwtAuthGuard)
export class VccTransactionResolver {
    constructor(private readonly vccTransactionService: VccTransactionService) { }

    // ===== 查询操作 =====

    /**
     * 查询交易记录列表（带分页和过滤）- 对应前端Table组件
     */
    @Query(() => PaginatedVccTransactionResult, {
        name: 'vccTransactions',
        description: '查询VCC交易记录列表（支持前端所有过滤条件）'
    })
    async getVccTransactions(
        @Args('filter', { type: () => VccTransactionFilterInputDTO, nullable: true })
        filter: VccTransactionFilterInputDTO = {},
        @Args('pagination', { type: () => VccTransactionPaginationInputDTO, nullable: true })
        pagination: VccTransactionPaginationInputDTO = {},
        @Context() context: any
    ): Promise<PaginatedVccTransactionResult> {
        const tenantId = context.req.user.tenantId;

        const result = await this.vccTransactionService.findTransactions(
            filter,
            pagination,
            tenantId
        );

        return {
            data: result.data,
            total: result.total,
            page: result.page,
            limit: result.limit,
            totalPages: result.totalPages
        };
    }

    /**
     * 根据ID查询单个交易记录 - 对应前端交易详情Modal
     */
    @Query(() => VccTransactionDTO, {
        name: 'vccTransaction',
        description: '根据ID查询单个VCC交易记录'
    })
    async getVccTransaction(
        @Args('id', { type: () => ID }) id: string,
        @Context() context: any
    ): Promise<VccTransactionDTO> {
        const tenantId = context.req.user.tenantId;
        return await this.vccTransactionService.findTransactionById(id, tenantId);
    }

    /**
     * 获取交易统计数据 - 用于前端Dashboard
     */
    @Query(() => VccTransactionStatsDTO, {
        name: 'vccTransactionStats',
        description: '获取VCC交易统计数据'
    })
    async getVccTransactionStats(
        @Args('filter', { type: () => VccTransactionFilterInputDTO, nullable: true })
        filter: VccTransactionFilterInputDTO = {},
        @Context() context: any
    ): Promise<VccTransactionStatsDTO> {
        const tenantId = context.req.user.tenantId;
        return await this.vccTransactionService.getTransactionStats(filter, tenantId);
    }

    /**
     * 导出交易记录 - 对应前端导出按钮
     */
    @Query(() => String, {
        name: 'exportVccTransactions',
        description: '导出VCC交易记录（CSV或JSON格式）'
    })
    async exportVccTransactions(
        @Args('filter', { type: () => VccTransactionFilterInputDTO, nullable: true })
        filter: VccTransactionFilterInputDTO = {},
        @Args('format', { type: () => String, defaultValue: 'csv' }) format: 'csv' | 'json',
        @Context() context: any
    ): Promise<string> {
        const tenantId = context.req.user.tenantId;
        return await this.vccTransactionService.exportTransactions(filter, tenantId, format);
    }

    // ===== 变更操作 =====

    /**
     * 创建交易记录 - 手动添加交易记录
     */
    @Mutation(() => VccTransactionDTO, {
        name: 'createVccTransaction',
        description: '创建VCC交易记录'
    })
    async createVccTransaction(
        @Args('input', { type: () => CreateVccTransactionInputDTO })
        input: CreateVccTransactionInputDTO,
        @Context() context: any
    ): Promise<VccTransactionDTO> {
        const tenantId = context.req.user.tenantId;
        return await this.vccTransactionService.createTransaction(input, tenantId);
    }

    /**
     * 更新交易记录
     */
    @Mutation(() => VccTransactionDTO, {
        name: 'updateVccTransaction',
        description: '更新VCC交易记录'
    })
    async updateVccTransaction(
        @Args('id', { type: () => ID }) id: string,
        @Args('input', { type: () => UpdateVccTransactionInputDTO })
        input: UpdateVccTransactionInputDTO,
        @Context() context: any
    ): Promise<VccTransactionDTO> {
        const tenantId = context.req.user.tenantId;
        return await this.vccTransactionService.updateTransaction(id, input, tenantId);
    }

    /**
     * 删除交易记录
     */
    @Mutation(() => Boolean, {
        name: 'deleteVccTransaction',
        description: '删除VCC交易记录'
    })
    async deleteVccTransaction(
        @Args('id', { type: () => ID }) id: string,
        @Context() context: any
    ): Promise<boolean> {
        const tenantId = context.req.user.tenantId;
        return await this.vccTransactionService.deleteTransaction(id, tenantId);
    }

    /**
     * 🚀 从Facebook同步交易记录 - 核心功能
     */
    @Mutation(() => [VccTransactionDTO], {
        name: 'syncVccTransactionsFromFacebook',
        description: '从Facebook API同步VCC卡片的广告消费记录'
    })
    async syncVccTransactionsFromFacebook(
        @Args('input', { type: () => SyncFacebookTransactionsInputDTO })
        input: SyncFacebookTransactionsInputDTO,
        @Context() context: any
    ): Promise<VccTransactionDTO[]> {
        const tenantId = context.req.user.tenantId;
        return await this.vccTransactionService.syncFacebookTransactions(input, tenantId);
    }

    /**
     * 手动添加充值记录 - 快捷操作
     */
    @Mutation(() => VccTransactionDTO, {
        name: 'addVccRechargeTransaction',
        description: '手动添加VCC充值记录'
    })
    async addVccRechargeTransaction(
        @Args('cardId', { type: () => ID }) cardId: string,
        @Args('amount', { type: () => Number }) amount: number,
        @Args('description', { type: () => String, nullable: true }) description = '',
        @Context() context: any
    ): Promise<VccTransactionDTO> {
        const tenantId = context.req.user.tenantId;

        const input: CreateVccTransactionInputDTO = {
            cardId,
            amount,
            merchant: '手动充值',
            transactionTime: new Date().toISOString(),
            status: 'success' as any,
            type: 'deposit' as any,
            description: description || `手动充值 $${amount}`
        };

        return await this.vccTransactionService.createTransaction(input, tenantId);
    }
} 