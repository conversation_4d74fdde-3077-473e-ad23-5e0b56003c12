import { BadRequestException, Body, Controller, NotFoundException, Post, UploadedFile, UseInterceptors, Req, Inject } from '@nestjs/common';
import { <PERSON><PERSON>, CrudController } from '@dataui/crud';
import { materialManagement } from './materialManagement.entity';
import { materialManagementService } from './materialManagement.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { AdAccountService } from '../ad-platform/services/ad-account.service';
import { Readable } from 'stream';
import * as fs from 'fs';
import * as mimeTypes from 'mime-types';
import FormData = require("form-data")
import { promisify } from 'util';
import axios from 'axios';
import FileType from 'file-type';
import { HttpsProxyAgent } from 'https-proxy-agent';
import _ from 'lodash';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Group } from '../group/group.entity';
import { User } from '../user/user.entity';
import { Role } from '../role/role.entity';
const proxy = process.env.AXIOS_PROXY;
const mode = process.env.NODE_ENV
const agent = new HttpsProxyAgent(proxy);
@Crud({
  model: {
    type: materialManagement,
  },
  params: {
    id: {
      field: 'id',
      primary: true,
      type: 'uuid',
    },
  },
})
@Controller('api/materialManagement')
export class materialManagementController implements CrudController<materialManagement> {
  constructor(
    // private readonly HttpService:HttpService
    private readonly adAccountService: AdAccountService,
    public readonly service: materialManagementService,
    @InjectRepository(Group)
    private groupRepository: Repository<Group>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>
  ) { }

  @Post('upload-fb')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFb(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { fbId: string }
  ) {
    if (!file) {
      throw new BadRequestException('请上传文件');
    }


    async function getFileContentType(buffer: Buffer, filename: string): Promise<string> {
      try {
        const fileTypeResult = await FileType.fromBuffer(buffer);
        if (fileTypeResult) {
          return fileTypeResult.mime;
        }
      } catch (error) {
        console.error('Error detecting file type:', error);
      }
      // 回退到基于文件扩展名的类型检测
      return mimeTypes.lookup(filename) || 'application/octet-stream';
    }

    const account = await this.adAccountService.findByFbId(body.fbId);
    const isVideo = file.mimetype.startsWith('video/');
    const facebookEndpoint = isVideo
      ? `https://graph.facebook.com/v23.0/act_${body.fbId}/advideos`
      : `https://graph.facebook.com/v23.0/act_${body.fbId}/adimages`;
    if (!account?.accessToken) {
      throw new NotFoundException('未找到对应的广告账号凭证');
    }
    // 从文件获取内容
    let fileContent: Buffer;
    if (file.buffer) {
      fileContent = file.buffer;
    } else if (file.path) {
      fileContent = await promisify(fs.readFile)(file.path);
    } else {
      throw new Error('Invalid file: either buffer or path must be provided');
    }
    const contentType = await getFileContentType(fileContent, file.originalname);
    const bufferStream = Readable.from(file.buffer);
    const form = new FormData();
    form.append('source', bufferStream, {
      filename: file.originalname,
      contentType: contentType,
    });
    form.append('access_token', account.accessToken);
    let header = {}
    if (mode === "development") {
      header = { headers: form.getHeaders(), httpsAgent: agent }
    } else {
      header = {
        headers: form.getHeaders(),
      }
    }
    let response
    try {
      response = await axios.post(facebookEndpoint, form, header)
    } catch (error) {
      console.error('Facebook API Error:', error.response?.data || error.message);
      throw new BadRequestException(`上传失败: ${error.message}`);
    }

    return {
      success: true,
      data: response.data, // 👈 确保这是 plain object 或你期望返回的数据
    };
  }

  @Post('get-main-page')
  async getMainPage(
    @Body() body: { fbId: string }
  ) {

    const account = await this.adAccountService.findByFbId(body.fbId);
    let header = {}
    if (mode === "development") {
      header = { httpsAgent: agent }
    } else {
      header = {}
    }
    let response
    try {
      response = await axios.get(`https://graph.facebook.com/v23.0/me/accounts?access_token=${account.accessToken}`, header)
    } catch (error) {
      console.error('Facebook API Error:', error.response?.data || error.message);
      throw new BadRequestException(`查询失败: ${error.message}`);
    }

    return {
      success: true,
      data: response.data.data
    };
  }

  @Post('create-adcreatives')
  async createAdcreative(
    @Body() body: { pageId: string; name: string; materials: string; pageLading: string; slogan: string; account: string; facebookImageId: string; facebookVedioId: string }
  ) {

    const account = await this.adAccountService.findByFbId(body.account);
    let header = {}
    if (mode === "development") {
      header = { httpsAgent: agent }
    } else {
      header = {}
    }
    let response
    try {
      const params = {
        name: body.name,
        object_story_spec: {
          page_id: body.pageId,
          link_data: {
            // "video_id": "YOUR_VIDEO_ID",
            // image_hash: 'abc123xyz',
            link: body.pageLading,
            message: body.slogan,
            call_to_action: { type: 'SHOP_NOW' },
          },
        } as any,
        access_token: account.accessToken,
      }
      if (body.facebookImageId) {
        params.object_story_spec.image_hash = body.facebookImageId
      } else {
        params.object_story_spec.video_id = body.facebookVedioId
      }
      // response = await axios.post(
      //   `https://graph.facebook.com/v23.0/act_${body.account}/adcreatives`, params, header);
    } catch (error) {
      console.error('Facebook API Error:', error.response?.data || error.message);
      throw new BadRequestException(`查询失败: ${error.message}`);
    }

    return {
      status: 0,
      success: true,
      data: {id: '*********'},
    };
  }

  @Post('get-list')
  async getMaterialManagementList(
    @Body() body: { 
      data?: {
        paging?: { offset: number; limit: number },
        filter?: Record<string, any>,
        sorting?: Array<{ field: string; direction: 'ASC' | 'DESC' }>
      }
    },
    @Req() request
  ) {
    try {
      // 从body.data中获取参数
      const { paging, filter, sorting } = body.data || {};
      
      // 从请求头中获取用户ID
      const userId = request.headers.userid;
      
      if (!userId) {
        console.warn('请求头中没有用户ID');
        return {
          success: true,
          status: 0,
          data: {
            nodes: [],
            totalCount: 0
          }
        };
      }
      
      // 获取当前用户信息，包括角色和群组
      const currentUser = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['groups', 'roles']
      });
      
      if (!currentUser) {
        console.warn(`未找到ID为 ${userId} 的用户`);
        return {
          success: true,
          status: 0,
          data: {
            nodes: [],
            totalCount: 0
          }
        };
      }
      // 检查用户是否是管理员
      const isAdmin = currentUser.roles?.some(role => 
        role.name === '超级管理员' || 
        role.name === '租户管理员' || role.name === 'tenantadmin');
      
      // 先获取所有数据（不分页）
      const allItemsResult = await this.service.repo.find({
        where: filter,
        order: sorting?.reduce((acc, sort) => {
          acc[sort.field] = sort.direction.toLowerCase();
          return acc;
        }, {}),
      });
      
      const filteredItems = [];
      
      // 如果是管理员，则可以查看所有数据
      if (isAdmin) {
        // 对于管理员，我们仍然需要获取每个项目的群组信息用于显示
        for (const item of allItemsResult) {
          if (!item.tenantId) continue;
          
          const dataUserId = item.tenantId;
          const dataUserGroupsDetail = await this.getUserGroupsDetail(dataUserId);
          item['group'] = dataUserGroupsDetail.map(group => group.name).toString();
          filteredItems.push(item);
        }
      } else {
        // 不是管理员，则根据群组过滤
        const currentUserGroups = currentUser.groups ? currentUser.groups.map(group => group.id) : [];
        
        // 如果当前用户没有所属群组，返回空结果
        if (currentUserGroups.length === 0) {
          console.warn('当前用户没有所属群组');
          return {
            success: true,
            status: 0,
            data: {
              nodes: [],
              totalCount: 0
            }
          };
        }
        
        // 创建一个Map来缓存用户ID对应的群组，避免重复查询
        const userGroupsCache = new Map<string, Array<{id: string, name: string}>>();
        
        // 遍历所有数据，查询每个数据中tenantId(实际是userId)对应的群组
        for (const item of allItemsResult) {
          // 如果数据没有tenantId，则跳过
          if (!item.tenantId) continue;
          
          // 获取数据中的userId(存在tenantId字段中)
          const dataUserId = item.tenantId;
          
          // 从缓存中获取该用户的群组详细信息，如果缓存中没有则查询数据库
          let dataUserGroupsDetail: Array<{id: string, name: string}> = [];
          if (userGroupsCache.has(dataUserId)) {
            dataUserGroupsDetail = userGroupsCache.get(dataUserId);
          } else {
            dataUserGroupsDetail = await this.getUserGroupsDetail(dataUserId);
            userGroupsCache.set(dataUserId, dataUserGroupsDetail);
          }
          
          // 提取群组ID用于交集判断
          const dataUserGroups = dataUserGroupsDetail.map(g => g.id);
          
          // 检查当前用户的群组和数据用户的群组是否有交集
          const hasIntersection = this.hasGroupIntersection(currentUserGroups, dataUserGroups);
          
          // 如果有交集，则将该数据添加到结果中
          if (hasIntersection) {
            // 将群组详细信息添加到返回的数据中
            item['group'] = dataUserGroupsDetail.map(item=>item.name).toString();
            filteredItems.push(item);
          }
        }
      }
      
      // 获取总数
      const totalCount = filteredItems.length;
      
      // 使用前端传递的offset和limit参数进行分页
      const offset = paging?.offset || 0;
      const limit = paging?.limit || 10;
      const pagedItems = filteredItems.slice(offset, offset + limit);
      
      return {
        success: true,
        status: 0,
        data: {
          nodes: pagedItems,
          totalCount: totalCount
        }
      };
    } catch (error) {
      console.error('获取素材列表失败:', error.message);
      throw new BadRequestException(`获取素材列表失败: ${error.message}`);
    }
  }
  
  // 检查两个数组是否有交集
  private hasGroupIntersection(userGroups1: string[], userGroups2: string[]): boolean {
    // 如果任一用户是超级管理员或有特殊权限，可以查看所有数据
    if (userGroups1.includes('admin') || userGroups1.includes('superadmin') ||
        userGroups2.includes('admin') || userGroups2.includes('superadmin')) {
      return true;
    }
    
    // 检查是否有交集
    return userGroups1.some(group => userGroups2.includes(group));
  }
  
  // 获取用户所属的群组
  private async getUserGroups(userId: string): Promise<string[]> {
    try {
      // 从数据库中查询用户信息，包括其所属的群组
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['groups']
      });
      
      if (!user) {
        console.warn(`未找到ID为 ${userId} 的用户`);
        return [];
      }
      
      // 返回用户所属的群组ID
      return user.groups ? user.groups.map(group => group.id) : [];
    } catch (error) {
      console.error(`获取用户 ${userId} 所属群组失败:`, error.message);
      return [];
    }
  }
  
  // 获取用户所属的群组详细信息（包含ID和名称）
  private async getUserGroupsDetail(userId: string): Promise<Array<{id: string, name: string}>> {
    try {
      // 从数据库中查询用户信息，包括其所属的群组
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['groups']
      });
      
      if (!user) {
        console.warn(`未找到ID为 ${userId} 的用户`);
        return [];
      }
      
      // 返回用户所属的群组ID和名称
      return user.groups ? user.groups.map(group => ({
        id: group.id,
        name: group.name ? group.name.toString() : ''
      })) : [];
    } catch (error) {
      console.error(`获取用户 ${userId} 所属群组详细信息失败:`, error.message);
      return [];
    }
  }
}
