import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/ui/select';
import { Button } from '@/ui/button';
import { Icon } from '@/components/icon';
import TenantTrendChart from './TenantTrendChart';
import TenantDetailTable from './TenantDetailTable';
import { useQuery, gql } from '@apollo/client';

const GLOBAL_RESOURCE_STATS = gql`
  query {
    globalResourceStats {
      type
      count
    }
  }
`;

const GLOBAL_RESOURCE_DETAILS = gql`
  query GlobalResourceDetails($page: Int, $pageSize: Int) {
    globalResourceDetails(page: $page, pageSize: $pageSize) {
      total
      nodes {
        type
        name
        status
        group
        createdAt
        remark
        tenantName
        tenantId
      }
    }
  }
`;

const GLOBAL_TREND = gql`
  query GlobalTrend($startDate: String, $endDate: String) {
    globalTrend(startDate: $startDate, endDate: $endDate) {
      date
      spend
      conversion
      tenantCount
      userCount
      adCount
    }
  }
`;

export default function TenantOverview() {
  const { t } = useTranslation();
  const [page, setPage] = useState(1);
  const pageSize = 6;

  // 全局统计卡片/饼图数据
  const { data: statsData, loading: statsLoading } = useQuery(GLOBAL_RESOURCE_STATS);
  // 全局明细表分页数据
  const { data: detailData, loading: detailLoading } = useQuery(GLOBAL_RESOURCE_DETAILS, {
    variables: { page, pageSize },
  });

  // 统计卡片数据
  const statsMap = (statsData?.globalResourceStats || []).reduce((acc: any, cur: any) => {
    acc[cur.type] = cur.count;
    return acc;
  }, {});

  return (
    <div className="space-y-8">
      <h2 className="text-3xl font-bold tracking-tight">{t('pages.key_181')}</h2>
      {/* 统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4">
        <SummaryCard title={t('pages.key_1729')} value={statsMap[t('pages.key_1723')] ?? 0} icon="solar:home-2-bold-duotone" color="bg-indigo-100 text-indigo-700" />
        <SummaryCard title={t('pages.key_2322')} value={statsMap[t('pages.key_2320')] ?? 0} icon="solar:user-bold-duotone" color="bg-blue-100 text-blue-700" />
        <SummaryCard title={t('pages.key_794')} value={statsMap[t('pages.key_774')] ?? 0} icon="solar:megaphone-bold-duotone" color="bg-green-100 text-green-700" />
        <SummaryCard title={t('pages.key_1887')} value={statsMap[t('pages.key_1883')] ?? 0} icon="solar:users-group-rounded-bold-duotone" color="bg-purple-100 text-purple-700" />
        <SummaryCard title={t('pages.key_417')} value={statsMap[t('pages.key_408')] ?? 0} icon="solar:target-bold-duotone" color="bg-amber-100 text-amber-700" />
        <SummaryCard title={t('pages.key_807')} value={statsMap[t('pages.key_806')] ?? 0} icon="solar:flag-bold-duotone" color="bg-pink-100 text-pink-700" />
        <SummaryCard title={t('pages.key_1786')} value={statsMap[t('pages.key_1782')] ?? 0} icon="solar:image-bold-duotone" color="bg-gray-100 text-gray-700" />
      </div>
      {/* 趋势图/分布图 */}
      <TenantTrendChart />
      {/* 明细表 */}
      <TenantDetailTable
        data={detailData?.globalResourceDetails?.nodes || []}
        total={detailData?.globalResourceDetails?.total || 0}
        page={page}
        pageSize={pageSize}
        loading={detailLoading}
        onPageChange={setPage}
        showTenant
      />
    </div>
  );
}

function SummaryCard({ title, value, icon, color }: { title: string; value: number; icon: string; color: string }) {
  return (
    <Card className="h-full">
      <CardContent className="flex flex-col items-center justify-center py-6">
        <div className={`rounded-full p-3 mb-2 text-2xl ${color}`}><Icon icon={icon} /></div>
        <div className="text-2xl font-bold">{value}</div>
        <div className="text-sm text-muted-foreground">{title}</div>
      </CardContent>
    </Card>
  );
} 