import { useTenantContext } from '@/context/tenant';
import { useQuery } from '@apollo/client';
import { TENANT_DETAIL } from '@/api/graphql/tenant.graphql';

export function useTenant() {
  const ctx = useTenantContext();
  return ctx?.tenantId || null;
}

export function useTenantDetail() {
  const tenantId = useTenant();
  const { data, loading, error, refetch } = useQuery(TENANT_DETAIL, {
    variables: { id: tenantId },
    skip: !tenantId,
    fetchPolicy: 'network-only',
  });
  return { tenant: data?.tenant, loading, error, refetch };
}
