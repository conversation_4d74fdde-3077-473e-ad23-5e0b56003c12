const { Client } = require('pg');

const client = new Client({
    host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
    port: 5432,
    user: 'yeeu',
    password: 'Yy&********',
    database: 'ad_auto',
});

async function main() {
    await client.connect();

    // 查询 ad_set.adAccountId 字段类型
    const result = await client.query(`
    SELECT table_name, column_name, data_type
    FROM information_schema.columns
    WHERE table_name = 'ad_set' AND column_name = 'adAccountId'
  `);
    console.log('ad_set.adAccountId 字段类型:');
    result.rows.forEach(row => console.log(row));

    await client.end();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 