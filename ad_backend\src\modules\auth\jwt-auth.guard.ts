import { Injectable, ExecutionContext, UnauthorizedException, CanActivate } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { SetMetadata } from '@nestjs/common';
import { IS_PUBLIC_KEY } from '../../common/decorators/public.decorator';

// 创建一个装饰器，表示路由已由JwtAuthGuard保护
export const HasJwtGuard = () => SetMetadata('hasJwtGuard', true);

@Injectable()
export class JwtAuthGuard implements CanActivate {
    constructor(
        private jwtService: JwtService,
        private reflector: Reflector,
        private configService: ConfigService
    ) { }

    getRequest(context: ExecutionContext) {
        if (context.getType() === 'http') {
            return context.switchToHttp().getRequest();
        }
        const ctx = GqlExecutionContext.create(context);
        return ctx.getContext().req;
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        // 设置hasJwtGuard元数据，表示此路由已由JwtAuthGuard保护
        SetMetadata('hasJwtGuard', true)(context.getHandler());

        // 检查是否标记为公开路由
        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        if (isPublic) {
            return true;
        }

        const request = this.getRequest(context);
        const authHeader = request.headers.authorization;

        if (!authHeader) {
            throw new UnauthorizedException('缺少Authorization头信息');
        }

        const [bearer, token] = authHeader.split(' ');

        if (bearer !== 'Bearer' || !token) {
            throw new UnauthorizedException('无效的令牌格式');
        }

        try {
            const secret = this.configService.get('JWT_SECRET') || 'defaultSecretKey';
            const payload = this.jwtService.verify(token, { secret });

            // 将用户信息添加到请求对象中
            request.user = {
                userId: payload.sub,
                username: payload.username,
                roles: payload.roles || [],
                groups: payload.groups || [],
                tenantId: payload.tenantId,
            };

            return true;
        } catch (error) {
            throw new UnauthorizedException('无效或过期的令牌');
        }
    }
} 