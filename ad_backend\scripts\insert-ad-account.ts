import { AppDataSource } from '../src/config/data-source';
import { AdAccount } from '../src/modules/ad-platform/entities/ad-account.entity';

async function main() {
    await AppDataSource.initialize();
    const repo = AppDataSource.getRepository(AdAccount);
    const uuid = require('uuid').v4();
    const account = repo.create({
        id: uuid,
        platform: 'facebook',
        accountId: 'fb-123456',
        name: '测试广告账户',
        status: 'active',
        tenant: { id: '3f04876f-6742-49ed-aa6c-05fa9b3fa426' }
    });
    await repo.save(account);
    console.log('已添加广告账户:', account);
    process.exit(0);
}

main(); 
