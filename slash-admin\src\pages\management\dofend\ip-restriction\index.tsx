import { useTranslation } from 'react-i18next';
import { useEffect, useState } from "react";
import { Table, Button, Select, Input, Form, Space, Tag, Modal } from "antd";
import {
  SearchOutlined, PlusOutlined, EditOutlined,
  DeleteOutlined
} from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table/interface";
import { GET_IPRESTRICTION, CREATE_IPRESTRICTION_MUTATION, UPDATE_IP_RESTRICTION, DELETE_IP_RESTRICTION } from "@/api/ipRestriction.graphql"
import { useLazyQuery, useMutation } from '@apollo/client';
import { useBoolean, useSetState } from "ahooks";
import { ipRestrictionType } from "./type"
import userStore from "@/store/userStore";
import { toast } from 'sonner'
import * as XLSX from 'xlsx';
import ipRestrictionService from "@/api/services/ipRestrictionService";

export default function IPRestrictionManagement() {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const { userInfo } = userStore.getState()
  const [data, setData] = useState<ipRestrictionType[]>([]);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [loadData] = useLazyQuery(GET_IPRESTRICTION, { fetchPolicy: 'network-only' });
  const [createIpRestriction] = useMutation(CREATE_IPRESTRICTION_MUTATION);
  const [updateIpRestriction] = useMutation(UPDATE_IP_RESTRICTION, {
    refetchQueries: [{ query: GET_IPRESTRICTION }],
  });
  const [deleteIprestriction] = useMutation(DELETE_IP_RESTRICTION, {
    refetchQueries: [{ query: GET_IPRESTRICTION }],
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [isEdit, { setTrue, setFalse }] = useBoolean(false);
  const [id, setId] = useState<number | undefined>(0)

  const [paginationProps, setPaginationProps] = useSetState({
    total: 0, // 数据总数
    pageSize: 10, // 每页条数
    current: 1, // 当前页码
  })
  const [isBatchModalOpen, setIsBatchModalOpen] = useState(false);
  const [batchLoading, setBatchLoading] = useState(false);
  const [batchFile, setBatchFile] = useState<File | null>(null);
  const [batchData, setBatchData] = useState<any[]>([]);

  const showEditModal = (record: ipRestrictionType) => {
    setTrue()
    setId(record.id)
    setIsModalOpen(true)
    form.setFieldsValue(record)
  }
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "IP",
      dataIndex: "ip",
      key: "ip",
      width: 150,
    },
    {
      title: t('pages.key_1602'),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => {
        let color = "green";
        if (status === t('pages.key_863')) {
          color = "red";
        } else if (status === t('pages.key_893')) {
          color = "gold";
        }
        return <Tag color={color}>{status}</Tag>;
      }
    },
    {
      title: t('pages.key_1059'),
      dataIndex: "operator",
      key: "operator",
      width: 120,
    },
    {
      title: t('pages.key_1254'),
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 180,
    },
    {
      title: t('pages.key_1058'),
      key: "actions",
      width: 160,
      render: (_: any, record: ipRestrictionType) => (
        <Space>
          <Button type="link" icon={<EditOutlined />} size="small" onClick={() => showEditModal(record)}>{t('pages.key_1853')}</Button>
          <Button type="link" danger icon={<DeleteOutlined />} size="small" onClick={() => handleDelete(record)}>{t('pages.key_287')}</Button>
        </Space>
      )
    }
  ];
  const handleStandardTableChange = (pagination: TablePaginationConfig) => {
    setPaginationProps({
      current: pagination.current as number,
      pageSize: pagination.pageSize as number,
    });
  };
  // const handleSearch = (value: string) => {
  //   setSearchText(value);
  //   // filterData(value, statusFilter);
  // };

  // const handleStatusChange = (value: string | null) => {
  //   setStatusFilter(value);
  //   // filterData(searchText, value);
  // };

  // const filterData = (search: string, status: string | null) => {
  //   let filteredData = mockData;

  //   // if (search) {
  //   //   filteredData = filteredData.filter(item => 
  //   //     item.ip.includes(search) || 
  //   //     item.id.includes(search) ||
  //   //     item.operator.toLowerCase().includes(search.toLowerCase())
  //   //   );
  //   // }

  //   if (status) {
  //     filteredData = filteredData.filter(item => item.status === status);
  //   }

  //   setData(filteredData);
  // };

  const initData = async () => {
    const data = await ipRestrictionService.getIpRestrictionList({
      paging: {
        limit: paginationProps.pageSize,
        offset: (paginationProps.current - 1) * paginationProps.pageSize,
      },
      filter: {},
      sorting: [
        { "field": "updatedAt", "direction": "DESC" }
      ]
    }) as any
    let arr = [] as ipRestrictionType[]
    setPaginationProps({ total: data?.totalCount })
    data?.nodes?.forEach((item: ipRestrictionType) => {
      arr.push({ ...item, updatedAt: (item.updatedAt as string).slice(0, 19).replace('T', ' ') })
    }) as ipRestrictionType[]
    setData(arr)
  }

  const handleDelete = async (record: ipRestrictionType) => {
    await deleteIprestriction({
      variables: {
        input: { id: record.id }
      }
    });
    toast.success(t('pages.key_294'));
    setPaginationProps({ current: 1 })
    initData();
  };


  const handleOk = () => {
    form.validateFields().then(async values => {
      if (isEdit) {
        const data = await updateIpRestriction({
          variables: {
            input: {
              id: id,
              update: {
                ...values,
                operator: userInfo.username,
                userId: userInfo.id
              }
            }
          }
        })
        if (data.data.updateOneIpRestriction) {
          toast.success(t('pages.key_1858'));
        }
      } else {
        const data = await createIpRestriction({
          variables: {
            ip: values.ip,
            status: values.status,
            operator: userInfo.username,
            userId: userInfo.id
          },
        })
        if (data.data.createOneIpRestriction) {
          toast.success(t('pages.key_1149'));
        }
      }
      setIsModalOpen(false);
      setId(undefined)
      form.resetFields();
      initData()
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  useEffect(() => {
    initData()
  }, [paginationProps.current, paginationProps.pageSize])

  // 解析Excel
  const handleBatchParse = async () => {
    if (!batchFile) {
      toast.error(t('pages.key_2147'));
      return;
    }
    setBatchLoading(true);
    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const json: any[] = XLSX.utils.sheet_to_json(sheet);
        // 校验字段
        if (!json.length || !('ip' in json[0]) || !('status' in json[0])) {
          toast.error(t('pages.key_2665'));
          setBatchData([]);
          setBatchLoading(false);
          return;
        }
        setBatchData(json);
        toast.success(t('pages.key_2093'));
        setBatchLoading(false);
      };
      reader.onerror = () => {
        toast.error(t('pages.key_2091'));
        setBatchLoading(false);
      };
      reader.readAsArrayBuffer(batchFile);
    } catch (e) {
      toast.error(t('pages.key_2091'));
      setBatchLoading(false);
    }
  };

  // 批量上传
  const handleBatchUpload = async () => {
    if (!batchData.length) {
      toast.error(t('pages.key_2666'));
      return;
    }
    setBatchLoading(true);
    let success = 0;
    for (const row of batchData) {
      try {
        await createIpRestriction({
          variables: {
            input: {
              ipRestriction: {
                ip: row.ip ? String(row.ip) : '',
                status: row.status ? String(row.status) : '',
                operator: userInfo.username,
                userId: userInfo.id
              }
            }
          }
        });
        success++;
      } catch (e) {
        // 可记录失败行
      }
    }
    toast.success(`批量上传完成，成功${success}条`);
    setIsBatchModalOpen(false);
    setBatchFile(null);
    setBatchData([]);
    setBatchLoading(false);
    initData();
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{t('pages.key_2667')}</h1>
        <div>
          <Button onClick={() => { setFalse(); setIsModalOpen(true); setId(undefined) }} type="primary" icon={<PlusOutlined />}>{t('pages.key_1136')}</Button>
          <Button onClick={() => {
            setIsBatchModalOpen(true);
            setBatchFile(null);
            setBatchData([]);
          }} style={{ marginLeft: 8 }} type="default">{t('pages.key_969')}</Button>
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        {/* <div className="mb-4">
          <Space>
            <Input
              placeholder="搜索IP"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: 200 }}
            />
          </Space>
        </div> */}

        <Table
          onChange={handleStandardTableChange}
          columns={columns}
          dataSource={data}
          rowKey="id"
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={t('pages.key_1142')}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}

      >

        <Form
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ status: t('pages.key_1465') }}
          style={{ maxWidth: 600 }}
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="ip"
            label="Ip"
          >
            <Input placeholder={t('pages.key_2668')} />
          </Form.Item>
          <Form.Item
            name="status"
            label={t('pages.key_1602')}
          >
            <Select placeholder={t('pages.key_2244')}>
              <Select.Option value={t('pages.key_1465')}>{t('pages.key_1465')}</Select.Option>
              <Select.Option value={t('pages.key_863')}>{t('pages.key_863')}</Select.Option>
              <Select.Option value={t('pages.key_893')}>{t('pages.key_893')}</Select.Option>
            </Select>
          </Form.Item>

        </Form>
      </Modal>
      <Modal
        title={t('pages.key_970')}
        open={isBatchModalOpen}
        onCancel={() => { setIsBatchModalOpen(false); setBatchFile(null); setBatchData([]); }}
        footer={[
          <Button key="cancel" onClick={() => { setIsBatchModalOpen(false); setBatchFile(null); setBatchData([]); }}>{t('pages.key_407')}</Button>,
          <Button key="parse" onClick={handleBatchParse} loading={batchLoading} disabled={!batchFile}>{t('pages.key_2088')}</Button>,
          <Button key="upload" type="primary" onClick={handleBatchUpload} loading={batchLoading} disabled={!batchData.length}>{t('pages.key_969')}</Button>
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ display: 'block', width: '100%' }}>
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={e => setBatchFile(e.target.files?.[0] || null)}
              disabled={batchLoading}
              style={{ display: 'none' }}
            />
            <div
              style={{
                width: '100%',
                height: 40,
                border: '1px dashed #d9d9d9',
                borderRadius: 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'left',
                paddingLeft: 12,
                color: batchFile ? '#333' : '#bfbfbf',
                cursor: batchLoading ? 'not-allowed' : 'pointer',
                background: batchLoading ? '#f5f5f5' : '#fff',
              }}
              onClick={e => {
                if (!batchLoading) {
                  const input = e.currentTarget.parentElement?.querySelector('input[type=file]') as HTMLInputElement | null;
                  input?.click();
                }
              }}
            >
              {t('pages.key_2148')}<a href="/template.xlsx" download style={{ marginLeft: 8 }}>{t('pages.key_16')}</a>
            </div>
          </label>
        </div>
        {batchData.length > 0 && (
          <Table
            size="small"
            columns={[
              { title: 'IP', dataIndex: 'ip', key: 'ip' },
              { title: t('pages.key_1602'), dataIndex: 'status', key: 'status' }
            ]}
            dataSource={batchData}
            rowKey={(row, idx) => row.ip + idx}
            pagination={false}
          />
        )}
      </Modal>
    </div>
  );
}
