import { Resolver, Query, Args } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdCampaign } from './entities/ad-campaign.entity';
import { CampaignOverviewDTO, CampaignOverviewItemDTO } from './entities/campaign-overview.dto';

@Resolver(() => CampaignOverviewDTO)
export class CampaignOverviewResolver {
    constructor(
        @InjectRepository(AdCampaign)
        private readonly campaignRepo: Repository<AdCampaign>,
    ) { }

    @Query(() => CampaignOverviewDTO)
    async campaignOverview(
        @Args('startDate') startDate: string,
        @Args('endDate') endDate: string,
    ): Promise<CampaignOverviewDTO> {
        // 活跃广告活动数
        const activeCount = await this.campaignRepo.count({ where: { status: 'active' } });

        // 总支出、总转化、平均ROI（假设raw里有spend/conversions/roi等）
        const { totalSpend, totalConversions, avgRoi } = await this.campaignRepo
            .createQueryBuilder('c')
            .select('SUM((c.raw->>\'spend\')::float)', 'totalSpend')
            .addSelect('SUM((c.raw->>\'conversions\')::int)', 'totalConversions')
            .addSelect('AVG((c.raw->>\'roi\')::float)', 'avgRoi')
            .where('c.startDate >= :start AND c.endDate <= :end', { start: startDate, end: endDate })
            .getRawOne();

        // 最近活动
        const recent = await this.campaignRepo.find({
            order: { startDate: 'DESC' },
            take: 3,
        });

        const recentCampaigns: CampaignOverviewItemDTO[] = recent.map(c => ({
            name: c.name,
            status: c.status,
            budget: c.budget,
            spent: Number(c.raw?.spend) || 0,
            progress: Math.round((Number(c.raw?.spend) || 0) / (c.budget || 1) * 100),
            impressions: Number(c.raw?.impressions) || 0,
            clicks: Number(c.raw?.clicks) || 0,
            conversions: Number(c.raw?.conversions) || 0,
        }));

        return {
            activeCount,
            totalSpend: Number(totalSpend) || 0,
            totalConversions: Number(totalConversions) || 0,
            avgRoi: Number(avgRoi) || 0,
            recentCampaigns,
        };
    }
} 