import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export class materiaCreate {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 255 })
  userId: string;

  @Column({ type: 'int' })
  materials: number;

  @Column({ length: 255 })
  pageLading: string;

  @Column({ length: 255 })
  slogan?: string;

  @Column({ length: 255 })
  account: string;

  @Column({ length: 255 })
  pageId: string;

  @Column({ length: 255 })
  createId: string;

  @Column({ length: 64 })
  tenantId: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt?: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;
} 