import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './user.entity';
import { Role } from '../role/role.entity';
import { Group } from '../group/group.entity';
import { UserService } from './user.service';
import { UserResolver } from './user.resolver';
import { UserSeedService } from './user.seed';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Tenant } from '../../entity/tenant.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([User, Role, Group, Tenant]),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => ({
                secret: configService.get('JWT_SECRET') || 'defaultSecretKey',
                signOptions: {
                    expiresIn: configService.get('JWT_EXPIRES_IN') || '24h',
                },
            }),
        }),
    ],
    providers: [UserService, UserResolver, UserSeedService],
    exports: [UserService, UserSeedService],
})
export class UserModule { } 