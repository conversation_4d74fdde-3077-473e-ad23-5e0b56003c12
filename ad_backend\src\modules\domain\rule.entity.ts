import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class RuleCondition {
    @Field()
    field: string;

    @Field()
    operator: string;

    @Field()
    value: string;

    @Field({ nullable: true })
    unit?: string;
}

@ObjectType()
@Entity()
export class Rule {
    @Field(() => ID)
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Field()
    @Column()
    name: string;

    @Field(() => [RuleCondition])
    @Column('jsonb')
    conditions: RuleCondition[];

    @Field()
    @Column()
    action: string;

    @Field()
    @Column()
    object: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    scheduleType: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    timeRange: string;

    @Field({ nullable: true })
    @Column({ default: 'ENABLED' })
    status: string;

    @Field(() => [String], { nullable: true })
    @Column({ type: 'simple-array', nullable: true })
    custom_schedule?: string[];

    @Field()
    @CreateDateColumn()
    createdAt: Date;

    @Field()
    @UpdateDateColumn()
    updatedAt: Date;
} 