import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAudienceToAdSet20240605120000 implements MigrationInterface {
    name = 'AddAudienceToAdSet20240605120000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" ADD "audienceId" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "ad_set" ADD CONSTRAINT "FK_adset_audience" FOREIGN KEY ("audienceId") REFERENCES "audience"("id") ON DELETE RESTRICT`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT "FK_adset_audience"`);
        await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN "audienceId"`);
    }
} 