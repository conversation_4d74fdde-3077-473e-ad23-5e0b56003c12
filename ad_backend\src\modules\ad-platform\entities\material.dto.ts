import { ObjectType, Field, ID } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import GraphQLJSON from 'graphql-type-json';
import { TenantDTO } from '../../../entity/tenant.dto';

@ObjectType('Material')
export class MaterialDTO {
  @IDField(() => ID)
  id: string;

  @FilterableField()
  type: string;

  @FilterableField()
  name: string;

  @FilterableField({ nullable: true })
  url?: string;

  @FilterableField({ nullable: true })
  content?: string;

  @FilterableField()
  status: string;

  @Field(() => TenantDTO)
  tenant: TenantDTO;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  raw?: any;
}
