import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAdSetAdAccountIdFK1685612345678 implements MigrationInterface {
    name = 'FixAdSetAdAccountIdFK1685612345678'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 删除所有可能的 adAccountId 外键
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "FK_dcb3ef81acf5eae82b9cdb2358f"`);
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "fk_042d9ae5cc62dfa2181f47dca16"`);
        // 2. 修改 adAccountId 字段类型为 varchar(64)
        await queryRunner.query(`ALTER TABLE "ad_set" ALTER COLUMN "adAccountId" TYPE varchar(64)`);
        // 3. 建立新的外键，指向 ad_account.accountId
        await queryRunner.query(`ALTER TABLE "ad_set" ADD CONSTRAINT "FK_dcb3ef81acf5eae82b9cdb2358f" FOREIGN KEY ("adAccountId") REFERENCES "ad_account"("accountId") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "FK_dcb3ef81acf5eae82b9cdb2358f"`);
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "fk_042d9ae5cc62dfa2181f47dca16"`);
        // 如需恢复原类型和外键，请补充
    }
}