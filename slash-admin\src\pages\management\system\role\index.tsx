import { useTranslation } from 'react-i18next';
import React, { useEffect, useState, useMemo } from "react";
import { useApolloClient } from "@apollo/client";
import { Table, Button, Space, Modal, Form, Input, Select, Tag, Card, Row, Col, Spin, Popconfirm, Tree, notification } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { RoleService } from "@/api/services/role.service";
import { RouteService } from "@/api/services/route.service";
import { RoleItem, RoleStatus } from "./types";
import { Icon } from "@/components/icon";
import { message } from "antd";
import { useRoutes } from '@/hooks/useRoutes';
import type { Route } from '#/route';

// UUID正则表达式验证 - 修正以符合UUID v4格式要求
const isValidUUID = (uuid: string): boolean => {
	// 标准UUID v4格式：第三组以4开头，第四组以8,9,a,b开头
	const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
	return uuidRegex.test(uuid);
};

// 从系统中查询是否存在此路由ID
const isExistingRouteId = (id: string, routes: Route[]): boolean => {
	return routes.some((route: Route) => route.id === id);
};

const { Option } = Select;

const RolePage: React.FC = () => {
	const { t } = useTranslation();
	const client = useApolloClient();
	const roleService = new RoleService(client);
	const routeService = new RouteService(client);

	const [loading, setLoading] = useState(false);
	const [roles, setRoles] = useState<RoleItem[]>([]);
	const [routes, setRoutes] = useState<Route[]>([]);
	const [totalCount, setTotalCount] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [modalVisible, setModalVisible] = useState(false);
	const [editingRole, setEditingRole] = useState<RoleItem | null>(null);
	const [form] = Form.useForm();
	const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
	const [deleteLoadingId, setDeleteLoadingId] = useState<string | null>(null);

	// 加载角色列表
	const loadRoles = async (page = currentPage, limit = pageSize) => {
		try {
			setLoading(true);
			console.log('请求参数:', { page, limit });

			const response = await roleService.getRoles({ page, limit });
			console.log('接收到角色数据:', response);

			if (response && response.items) {
				// 检查每个角色是否有绑定的路由
				response.items.forEach(role => {
					if (role.routeIds && role.routeIds.length > 0) {
						console.log(`角色 "${role.name}" 绑定了 ${role.routeIds.length} 个路由:`, role.routeIds);
					} else {
						console.log(`角色 "${role.name}" 没有绑定路由`);
					}
				});

				setRoles(response.items);
				setTotalCount(response.totalCount || 0);
				setCurrentPage(response.pageInfo?.page || 1);
			} else {
				console.error('角色数据结构:', response);
				notification.error({ message: t('pages.key_2075'), description: t('pages.key_2164') });
			}
		} catch (error) {
			console.error("加载角色列表失败详情:", error);
			notification.error({
				message: t('pages.key_338'),
				description: (error as Error).message || t('pages.key_1312'),
			});
		} finally {
			setLoading(false);
		}
	};

	// 加载路由列表
	const loadRoutes = async () => {
		try {
			const routeList = await routeService.getRoutes();
			console.log('加载到的路由列表:', routeList);

			// 显示所有路由的ID，便于检查格式
			if (routeList && routeList.length > 0) {
				console.log('==== 系统中的有效路由ID ====');
				routeList.forEach(route => {
					console.log(`${route.name}: ${route.id}`);
				});
				console.log('===========================');
			}

			setRoutes(routeList || []);
		} catch (error) {
			console.error("加载路由列表失败详情:", error);
			notification.error({
				message: t('pages.key_344'),
				description: (error as Error).message || t('pages.key_1312'),
			});
		}
	};

	useEffect(() => {
		loadRoles();
		loadRoutes();
	}, []);

	// 构建路由树形数据
	const routeTreeData = useMemo(() => {
		const buildTreeData = (items: any[]): any[] => {
			if (!items || items.length === 0) return [];

			const map = new Map<string, any>();
			const tree: any[] = [];

			// 第一步：创建所有节点的映射
			items.forEach(item => {
				map.set(item.id, {
					title: (
						<span>
							{item.icon && <Icon icon={item.icon} size={16} className="mr-1" />}
							{item.name}
						</span>
					),
					key: item.id,
					children: []
				});
			});

			// 第二步：构建树形结构
			items.forEach(item => {
				const node = map.get(item.id);
				if (!node) return;

				if (!item.parentId) {
					// 根节点直接加入树中
					tree.push(node);
				} else {
					// 子节点加入到父节点的 children 中
					const parent = map.get(item.parentId);
					if (parent) {
						if (!parent.children) {
							parent.children = [];
						}
						parent.children.push(node);
					} else {
						// 如果找不到父节点，放到根节点
						tree.push(node);
					}
				}
			});

			return tree;
		};

		return buildTreeData(routes);
	}, [routes]);

	// 添加或编辑角色
	const handleSaveRole = async () => {
		try {
			const values = await form.validateFields();
			console.log('表单数据:', values);
			console.log('选中的权限ID:', checkedKeys);

			// 验证并过滤routeIds，确保只包含有效的UUID
			let validRouteIds: string[] = [];
			if (checkedKeys && checkedKeys.length > 0) {
				// 根据后端验证规则，要求必须是UUID v4格式的字符串
				validRouteIds = checkedKeys.filter(id => {
					if (!id) return false;

					// 验证是否是有效的UUID v4格式
					const isValid = isValidUUID(id);

					// 验证并记录日志
					if (!isValid) {
						console.warn(`路由ID [${id}] 不符合UUID v4格式，将被过滤`);
					}

					// 只有通过UUID格式验证的ID才会被接受
					return isValid;
				});

				if (validRouteIds.length !== checkedKeys.length) {
					console.warn(`过滤了 ${checkedKeys.length - validRouteIds.length} 个无效的路由ID`);

					// 如果所有ID都被过滤掉了，提示用户
					if (validRouteIds.length === 0 && checkedKeys.length > 0) {
						notification.warning({
							message: t('pages.key_1323'),
							description: t('pages.key_953'),
						});
					}
				}
			}

			// 将选中的有效路由ID添加到表单值中
			values.routeIds = validRouteIds;
			console.log('过滤后的有效路由ID:', validRouteIds);

			if (editingRole) {
				// 更新角色
				console.log('更新角色:', { id: editingRole.id, ...values });
				const result = await roleService.updateRole({
					id: editingRole.id,
					...values
				});
				console.log('更新结果:', result);
				notification.success({ message: t('pages.key_2077') });

				// 验证返回的结果是否包含路由ID
				if (result && (!result.routeIds || result.routeIds.length === 0) && validRouteIds.length > 0) {
					notification.warning({
						message: t('pages.key_1323'),
						description: t('pages.key_1303'),
					});
				}
			} else {
				// 创建角色
				console.log('创建角色:', values);
				const result = await roleService.createRole(values);
				console.log('创建结果:', result);
				notification.success({ message: t('pages.key_2070') });

				// 验证返回的结果是否包含路由ID
				if (result && (!result.routeIds || result.routeIds.length === 0) && validRouteIds.length > 0) {
					notification.warning({
						message: t('pages.key_1323'),
						description: t('pages.key_1303'),
					});
				}
			}

			setModalVisible(false);
			form.resetFields();
			setCheckedKeys([]);
			loadRoles();
		} catch (error) {
			console.error("保存角色失败详情:", error);
			notification.error({
				message: editingRole ? t('pages.key_1264') : t('pages.key_264'),
				description: (error as Error).message || t('pages.key_1312'),
			});
		}
	};

	// 删除角色
	const handleDeleteRole = async (id: string) => {
		setDeleteLoadingId(id);
		try {
			console.log('删除角色:', id);
			const result = await roleService.deleteRole(id);
			console.log('删除结果:', result);
			notification.success({ message: t('pages.key_2071') });
			loadRoles();
		} catch (error) {
			console.error("删除角色失败详情:", error);
			notification.error({
				message: t('pages.key_302'),
				description: (error as Error).message || t('pages.key_1312'),
			});
		} finally {
			setDeleteLoadingId(null);
		}
	};

	// 切换角色状态
	const handleToggleStatus = async (role: RoleItem) => {
		try {
			const newStatus = role.status === RoleStatus.ENABLED ? RoleStatus.DISABLED : RoleStatus.ENABLED;
			console.log('切换角色状态:', { id: role.id, status: newStatus });

			const result = await roleService.updateRole({
				id: role.id,
				status: newStatus
			});

			console.log('状态更新结果:', result);
			notification.success({ message: t('pages.key_2080') });
			loadRoles();
		} catch (error) {
			console.error("更新角色状态失败详情:", error);
			notification.error({
				message: t('pages.key_1265'),
				description: (error as Error).message || t('pages.key_1312'),
			});
		}
	};

	// 打开添加角色对话框
	const showAddModal = () => {
		setEditingRole(null);
		form.resetFields();
		setCheckedKeys([]);
		setModalVisible(true);
	};

	// 打开编辑角色对话框
	const showEditModal = (role: RoleItem) => {
		console.log('编辑角色:', role);
		setEditingRole(role);
		form.setFieldsValue({
			name: role.name,
			description: role.description,
			status: role.status,
			order: role.order
		});
		setCheckedKeys(role.routeIds || []);
		setModalVisible(true);
	};

	// 处理树节点选择变化
	const handleTreeCheck = (checked: any, info: any) => {
		console.log('选中的权限变化原始数据:', checked);
		console.log('选中的权限附加信息:', info);

		// Tree的onCheck可能返回字符串数组或者{checked, halfChecked}对象
		let selectedKeys: string[] = [];

		if (Array.isArray(checked)) {
			selectedKeys = checked;
		} else if (checked && checked.checked) {
			selectedKeys = checked.checked;
		}

		console.log('处理后的选中权限:', selectedKeys);
		setCheckedKeys(selectedKeys);
	};

	// 表格列定义
	const columns = [
		{
			title: t('pages.key_2072'),
			dataIndex: "name",
			key: "name",
		},
		{
			title: t('pages.key_1027'),
			dataIndex: "description",
			key: "description",
		},
		{
			title: t('pages.key_1602'),
			dataIndex: "status",
			key: "status",
			render: (status: RoleStatus) => (
				<Tag color={status === RoleStatus.ENABLED ? "success" : "error"}>
					{status === RoleStatus.ENABLED ? t('pages.key_496') : t('pages.key_1714')}
				</Tag>
			),
		},
		{
			title: t('pages.key_1014'),
			dataIndex: "order",
			key: "order",
		},
		{
			title: t('pages.key_250'),
			dataIndex: "createdAt",
			key: "createdAt",
			render: (date: string) => new Date(date).toLocaleString(),
		},
		{
			title: t('pages.key_1058'),
			key: "actions",
			render: (_: any, record: RoleItem) => (
				<Space size="middle">
					<Button
						type="primary"
						icon={<EditOutlined />}
						size="small"
						onClick={() => showEditModal(record)}
					>{t('pages.key_1853')}</Button>
					<Button
						type={record.status === RoleStatus.ENABLED ? "default" : "primary"}
						size="small"
						onClick={() => handleToggleStatus(record)}
					>
						{record.status === RoleStatus.ENABLED ? t('pages.key_1714') : t('pages.key_496')}
					</Button>
					<Popconfirm
						title="确定要删除此角色吗？"
						onConfirm={() => handleDeleteRole(record.id)}
						onCancel={() => setDeleteLoadingId(null)}
						okText={t('pages.key_1693')}
						cancelText={t('pages.key_407')}
						okButtonProps={{ loading: deleteLoadingId === record.id }}
					>
						<Button type="primary" danger icon={<DeleteOutlined />} size="small" disabled={deleteLoadingId === record.id}>{t('pages.key_287')}</Button>
					</Popconfirm>
				</Space>
			),
		},
	];

	return (
		<Card title={t('pages.key_2082')}>
			{roles.length === 0 && !loading && (
				<div style={{ marginBottom: 16, color: 'red' }}>{t('pages.key_1229')}</div>
			)}

			<Row justify="end" style={{ marginBottom: 16 }}>
				<Col>
					<Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>{t('pages.key_1156')}</Button>
				</Col>
			</Row>

			<Spin spinning={loading}>
				<Table
					columns={columns}
					dataSource={roles}
					rowKey="id"
					pagination={{
						current: currentPage,
						pageSize: pageSize,
						total: totalCount,
						onChange: (page, pageSize) => {
							setCurrentPage(page);
							setPageSize(pageSize || 10);
							loadRoles(page, pageSize);
						},
						showSizeChanger: true,
						showTotal: (total) => `共 ${total} 条记录`,
					}}
				/>
			</Spin>

			<Modal
				title={editingRole ? t('pages.key_1866') : t('pages.key_1156')}
				open={modalVisible}
				onOk={handleSaveRole}
				onCancel={() => setModalVisible(false)}
				okText={t('pages.key_134')}
				cancelText={t('pages.key_407')}
				width={600}
			>
				<Form
					form={form}
					layout="vertical"
				>
					<Form.Item
						name="name"
						label={t('pages.key_2072')}
						rules={[{ required: true, message: t('pages.key_2234') }]}
					>
						<Input placeholder={t('pages.key_2234')} />
					</Form.Item>
					<Form.Item
						name="description"
						label={t('pages.key_1027')}
					>
						<Input.TextArea rows={3} placeholder={t('pages.key_2235')} />
					</Form.Item>
					<Form.Item
						name="status"
						label={t('pages.key_1602')}
						initialValue={RoleStatus.ENABLED}
					>
						<Select>
							<Option value={RoleStatus.ENABLED}>{t('pages.key_496')}</Option>
							<Option value={RoleStatus.DISABLED}>{t('pages.key_1714')}</Option>
						</Select>
					</Form.Item>
					<Form.Item
						name="order"
						label={t('pages.key_1014')}
						initialValue={0}
					>
						<Input type="number" placeholder={t('pages.key_2200')} />
					</Form.Item>
					<Form.Item
						label={t('pages.key_1324')}
					>
						<div style={{ maxHeight: '300px', overflow: 'auto', border: '1px solid #d9d9d9', padding: '8px', borderRadius: '2px' }}>
							{routeTreeData.length > 0 ? (
								<Tree
									checkable
									checkedKeys={checkedKeys}
									treeData={routeTreeData}
									defaultExpandAll
									onCheck={handleTreeCheck}
									checkStrictly={false}
								/>
							) : (
								<div style={{ textAlign: 'center', padding: '20px 0' }}>{t('pages.key_1230')}</div>
							)}
						</div>
					</Form.Item>
				</Form>
			</Modal>
		</Card>
	);
};

export default RolePage;

export function hasPermission(id: string) {
	const { t } = useTranslation();
	const routes = useRoutes();
	return routes.some((route: Route) => route.id === id);
}
