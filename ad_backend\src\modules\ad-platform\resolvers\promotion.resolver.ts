import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { PromotionService } from '../promotion.service';
import { PromotionDto, CreatePromotionDto, UpdatePromotionDto, PromotionDetailDto } from '../entities/promotion.dto';
import { Promotion } from '../entities/promotion.entity';
import { FacebookApiService } from '../services/facebook-api.service';
import { OptionDTO } from '../entities/audience.dto';
import { Injectable, UseGuards } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtAuthGuard } from '../../../modules/auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';

@Resolver(() => PromotionDto)
export class PromotionResolver {
    constructor(private readonly service: PromotionService, private readonly facebookApi: FacebookApiService) { }

    @UseGuards(JwtAuthGuard, TenantGuard)
    @Query(() => [PromotionDto], { name: 'getPromotions' })
    async getPromotions(@Context() ctx): Promise<Promotion[]> {
        return this.service.findAll(ctx);
    }

    @Query(() => PromotionDetailDto, { name: 'getPromotionByCode', nullable: true })
    async getPromotionByCode(
        @Args('code') code: string,
        @Context() ctx?: any
    ): Promise<PromotionDetailDto | null> {
        return this.service.findByCode(code, ctx);
    }

    @Query(() => PromotionDetailDto, { name: 'getPromotionDetail', nullable: true })
    async getPromotionDetail(
        @Args('code') code: string,
        @Args('tenantId') tenantId: string
    ): Promise<PromotionDetailDto | null> {
        return this.service.findByCode(code, tenantId);
    }

    @UseGuards(JwtAuthGuard, TenantGuard)
    @Mutation(() => PromotionDto)
    async createPromotion(@Args('input') input: CreatePromotionDto, @Context() ctx): Promise<Promotion> {
        return this.service.create(input, ctx);
    }

    @Mutation(() => PromotionDto)
    async updatePromotion(@Args('id') id: string, @Args('input') input: UpdatePromotionDto): Promise<Promotion> {
        return this.service.update(id, input);
    }

    @Mutation(() => Boolean)
    async deletePromotion(@Args('id') id: string): Promise<boolean> {
        await this.service.remove(id);
        return true;
    }
}

@Injectable()
@Resolver()
export class FacebookOptionResolver {
    constructor(
        private readonly facebookApi: FacebookApiService,
        private readonly configService: ConfigService,
    ) { }

    @Query(() => [OptionDTO], { description: 'Facebook国家/地区选项' })
    async facebookCountries(): Promise<OptionDTO[]> {
        return this.facebookApi.getCountries();
    }

    @Query(() => [OptionDTO], { description: 'Facebook语言ID选项' })
    async facebookLocales(): Promise<OptionDTO[]> {
        return this.facebookApi.getLocales();
    }

    @Query(() => [OptionDTO], { description: 'Facebook兴趣选项' })
    async facebookInterests(@Args('query', { nullable: true }) query?: string): Promise<OptionDTO[]> {
        const accessToken = this.configService.get<string>('VITE_FACRBOOK_ACCESStOKEN');
        return this.facebookApi.getInterests(query, accessToken);
    }

    @Query(() => [OptionDTO], { description: 'Facebook行为选项（API v22.0）' })
    async facebookBehaviors(@Args('query', { nullable: true }) query?: string): Promise<OptionDTO[]> {
        const accessToken = this.configService.get<string>('VITE_FACRBOOK_ACCESStOKEN');
        return this.facebookApi.getBehaviors(query, accessToken);
    }
} 