import request from "@/utils/request"
import axios from "axios";
import { axiosInstance } from '../apiClient';
// const uploadImage = (adAccountId: string, params: any) => request.post(`/facebook${adAccountId}`, { data: params })
// export async function uploadToFaccebook(adAccountId: string, file: any): Promise<any> {
//   const response = await axios.post(`https://graph.facebook.com/v23.0/${adAccountId}/adimages`, file, {
//   });
//   return response.data;
// }

// export async function uploadToFaccebookVedio(adAccountId: string, file: any): Promise<any> {
//   const response = await axios.post(`https://graph.facebook.com/v23.0/${adAccountId}/advideos`, file, {
//   });
//   return response.data;
// }

// // vedio ****************

const getMeInfo = (token: string) => {
  const accessToken = token;

 return axios.get(`https://graph.facebook.com/v23.0/me/accounts`, {
    params: {
      access_token: accessToken,
    },
  })
    .then(response => {
      console.log('Pages:', response.data.data);
      return response
    })
    .catch(error => {
      console.error('Error fetching pages:', error.response?.data || error.message);
    });
}

const uploadToFaccebook = async(file: Blob, fileName: string, fbId:string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('fileName', fileName);
  formData.append('fbId', fbId);
  const res = await axios.post('/api/materialManagement/upload-fb', formData, {
    headers: {
      "Content-Type":'multipart/form-data'
    }
  })
  return res.data;
}

const getfbMainPage = async(fbId:string) => {
  const res = await axios.post('/api/materialManagement/get-main-page', {fbId})
  return res.data;
}


const adcreatives = async(params:any) => {
  // const accessToken = token;

 const res = await axiosInstance.post(`/materialManagement/create-adcreatives`,params)
  return res
}

export default {
  // uploadToFaccebook,
  getMeInfo,
  // uploadToFaccebookVedio
  uploadToFaccebook,
  adcreatives,
  getfbMainPage
}
