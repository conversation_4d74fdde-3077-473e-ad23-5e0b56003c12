import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { AdGroupsOverviewDTO, AdGroupOverviewItemDTO } from './entities/ad-groups-overview.dto';
import { AdSet } from './entities/adset.entity';
import { Ad } from './entities/ad.entity';
import { PieDataDTO } from './entities/creative-analysis.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => AdGroupsOverviewDTO)
export class AdGroupsOverviewResolver {
    constructor(
        @InjectRepository(AdSet)
        private readonly adSetRepo: Repository<AdSet>,
        @InjectRepository(Ad)
        private readonly adRepo: Repository<Ad>,
    ) { }

    @Query(() => AdGroupsOverviewDTO)
    async adGroupsOverview(
        @Args('startDate') startDate: string,
        @Args('endDate') endDate: string,
        @Context() ctx,
    ): Promise<AdGroupsOverviewDTO> {
        // 获取当前租户ID（如有多租户需求）
        const { tenantId, isSuperAdmin } = ctx;
        // 查询所有广告组
        const adSets = await this.adSetRepo.find({
            where: isSuperAdmin ? {} : { tenantId },
            relations: ['adAccount', 'adCampaign', 'tenant', 'audience'],
        });
        // 查询所有广告（用于统计消耗、CTR、CVR等）
        const adsetIdList = adSets.map(a => a.adsetId);
        const ads = adsetIdList.length > 0 ? await this.adRepo.find({
            where: { adId: In(adsetIdList) },
        }) : [];
        // 按广告组聚合
        const adGroups: AdGroupOverviewItemDTO[] = adSets.map(adSet => {
            // 过滤属于该adSet的广告（通过adId和adsetId关联）
            const groupAds = ads.filter(ad => ad.adId === adSet.adsetId);
            // 示例：假设insight字段有impressions、clicks、conversions、spend
            let impressions = 0, clicks = 0, conversions = 0, spent = 0;
            groupAds.forEach(ad => {
                const insight = ad.insight || {};
                impressions += Number(insight.impressions || 0);
                clicks += Number(insight.clicks || 0);
                conversions += Number(insight.conversions || 0);
                spent += Number(insight.spend || 0);
            });
            const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
            const cvr = clicks > 0 ? (conversions / clicks) * 100 : 0;
            const roi = spent > 0 ? (conversions * 100 / spent) : 0; // ROI示例
            const progress = adSet.dailyBudget > 0 ? Math.round((spent / adSet.dailyBudget) * 100) : 0;
            return {
                name: adSet.name,
                status: adSet.status,
                budget: adSet.dailyBudget,
                spent,
                progress: progress > 100 ? 100 : progress,
                ctr: Number(ctr.toFixed(2)),
                cvr: Number(cvr.toFixed(2)),
                roi: Number(roi.toFixed(2)),
            };
        });
        // 统计分布（按系列/名称聚合）
        const distributionMap: Record<string, number> = {};
        adGroups.forEach(g => {
            distributionMap[g.name] = (distributionMap[g.name] || 0) + 1;
        });
        const distribution: PieDataDTO[] = Object.entries(distributionMap).map(([name, value]) => ({ name, value }));
        return { adGroups, distribution };
    }
} 