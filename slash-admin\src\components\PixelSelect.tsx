import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Select, message } from 'antd';
import { useQuery, useMutation, gql } from '@apollo/client';

const GET_PIXELS_BY_CAMPAIGN = gql`
  query GetPixelsByCampaign($campaignId: String!) {
    pixelsByCampaign(campaignId: $campaignId) {
      id
      name
    }
  }
`;

const CREATE_PIXEL = gql`
  mutation CreatePixelAndAssign($campaignId: String!, $pixelName: String!, $businessId: String!) {
    createPixelAndAssign(campaignId: $campaignId, pixelName: $pixelName, businessId: $businessId) {
      id
      name
    }
  }
`;

export default function PixelSelect({
    campaignId,
    businessId,
    value,
    onChange,
    disabled = false,
}: {
    campaignId: string;
    businessId: string;
    value: string;
    onChange: (id: string) => void;
    disabled?: boolean;
}) {
    const { data, loading: loadingPixels, refetch } = useQuery(GET_PIXELS_BY_CAMPAIGN, {
        variables: { campaignId },
        fetchPolicy: 'network-only',
    });
    const [createPixel] = useMutation(CREATE_PIXEL);
    const [creating, setCreating] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const { t } = useTranslation();

    const options = (data?.pixelsByCampaign || []).map((p: any) => ({
        label: p.name,
        value: p.id,
    }));

    const handleCreatePixel = async (name: string) => {
        if (!name) return;
        setCreating(true);
        try {
            const { data } = await createPixel({
                variables: {
                    campaignId,
                    pixelName: name,
                    businessId,
                },
            });
            message.success('Pixel 创建成功');
            await refetch();
            onChange(data.createPixelAndAssign.id);
        } catch (e: any) {
            message.error('Pixel 创建失败: ' + (e.message || ''));
        }
        setCreating(false);
    };

    return (
        <Select
            showSearch
            value={value}
            loading={loadingPixels || creating}
            onChange={onChange}
            style={{ width: 300 }}
            placeholder="请选择或新建 Pixel"
            options={options}
            disabled={disabled}
            dropdownRender={menu => (
                <>
                    {menu}
                    <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
                        <input
                            style={{ flex: 1, marginRight: 8 }}
                            placeholder={t('pages.key_2378')}
                            value={inputValue}
                            onChange={e => setInputValue(e.target.value)}
                            onKeyDown={e => {
                                if (e.key === 'Enter' && inputValue) {
                                    handleCreatePixel(inputValue);
                                    setInputValue('');
                                }
                            }}
                            disabled={disabled}
                        />
                        <a
                            style={{ whiteSpace: 'nowrap', cursor: disabled ? 'not-allowed' : 'pointer', color: disabled ? '#ccc' : undefined }}
                            onClick={() => {
                                if (!disabled && inputValue) {
                                    handleCreatePixel(inputValue);
                                    setInputValue('');
                                }
                            }}
                        >{t('pages.key_1164')}</a>
                    </div>
                </>
            )}
        />
    );
} 