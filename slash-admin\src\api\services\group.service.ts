import { useTranslation } from 'react-i18next';
import { ApolloClient } from '@apollo/client';
import { GET_GROUPS } from '../group.graphql';

// 群组项接口
interface GroupItem {
  id: string;
  name: string;
  status: string;
  [key: string]: any;
}

// 群组列表返回接口，统一格式与角色返回
interface GroupListResponse {
  items: GroupItem[];
  totalCount?: number;
}

export class GroupService {
  constructor(private client: ApolloClient<any>) { }

  /**
   * 获取群组列表
   * @returns 群组列表
   */
  async getGroups(): Promise<GroupListResponse> {
    console.log('[GroupService] 获取群组列表');

    try {
      // 使用项目现有的GET_GROUPS查询
      const { data, error } = await this.client.query({
        query: GET_GROUPS,
        variables: {
          paging: { limit: 50 } // 设置分页参数，确保获取足够多的数据
        },
        fetchPolicy: 'network-only'
      });

      if (error) {
        console.error('[GroupService] 获取群组列表失败：', error);
        throw new Error(`获取群组列表失败：${error.message}`);
      }

      // 转换为统一格式
      const groups = data?.groups?.nodes || [];
      const response: GroupListResponse = {
        items: groups,
        totalCount: data?.groups?.totalCount || groups.length
      };

      console.log('[GroupService] 获取群组列表成功：', response);
      return response;
    } catch (err) {
      console.error('[GroupService] 获取群组列表异常：', err);
      throw err;
    }
  }

  /**
   * 获取只有活跃状态的群组列表
   * @param statusKey 活跃状态的key（由组件传入t('...')后的值）
   * @returns 活跃状态的群组列表
   */
  async getActiveGroups(statusKey: string): Promise<GroupListResponse> {
    const groups = await this.getGroups();
    const activeGroups = groups.items.filter((group: GroupItem) => group.status === statusKey);
    return {
      items: activeGroups,
      totalCount: activeGroups.length
    };
  }
} 