import { Resolver, Query, Args, Context, Mutation } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdCampaignDTO } from '../entities/ad-campaign.dto';
import { AdCampaign } from '../entities/ad-campaign.entity';
import { AdAccount } from '../entities/ad-account.entity';
import { UseGuards } from '@nestjs/common';
import { TenantGuard } from '../../../guards/tenant.guard';
import { plainToInstance } from 'class-transformer';
import { AdCampaignFilterInput, CreateOneAdCampaignInput } from '../entities/ad-campaign.input';
import { FacebookApiService } from '../services/facebook-api.service';
import { GoogleApiService } from '../services/google-api.service';
import { TiktokApiService } from '../services/tiktok-api.service';
import { JwtAuthGuard } from '../../../modules/auth/jwt-auth.guard';

@Resolver(() => AdCampaignDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class AdCampaignResolver {
    constructor(
        @InjectRepository(AdCampaign)
        private readonly adCampaignRepo: Repository<AdCampaign>,
        @InjectRepository(AdAccount)
        private readonly adAccountRepo: Repository<AdAccount>,
        private readonly facebookApi: FacebookApiService,
        private readonly googleApi: GoogleApiService,
        private readonly tiktokApi: TiktokApiService,
    ) { }

    @Query(() => [AdCampaignDTO])
    async adCampaigns(
        @Args('filter', { type: () => AdCampaignFilterInput, nullable: true }) filter: AdCampaignFilterInput,
        @Context() ctx,
    ): Promise<AdCampaignDTO[]> {
        const tenantId = ctx.tenantId;
        // 只查当前租户的广告系列
        const where = { tenant: { id: tenantId }, ...(filter || {}) };
        const entities = await this.adCampaignRepo.find({ where, relations: ['adAccount'] });
        return plainToInstance(AdCampaignDTO, entities);
    }

    @Mutation(() => AdCampaignDTO)
    async createOneAdCampaign(
        @Args('name') name: string,
        @Args('platform') platform: string,
        @Args('budget') budget: number,
        @Args('status') status: string,
        @Args('startDate') startDate: Date,
        @Args('endDate') endDate: Date,
        @Args('tags', { type: () => [String], nullable: true }) tags: string[],
        @Args('description', { nullable: true }) description: string,
        @Args('adAccountId') adAccountId: string,
        @Context() ctx,
    ): Promise<AdCampaignDTO> {
        const tenantId = ctx.tenantId;
        const adAccount = await this.adAccountRepo.findOne({ where: { id: adAccountId, tenant: { id: tenantId } } });
        if (!adAccount) throw new Error('AdAccount not found');
        // 多平台适配
        let campaignId: string;
        switch (platform) {
            case 'facebook':
            case 'instagram':
                campaignId = await this.facebookApi.createCampaign({
                    name,
                    status,
                    budget,
                    startDate,
                    endDate,
                    tags,
                    description,
                    placement: platform === 'instagram' ? 'INSTAGRAM' : 'FACEBOOK',
                    adAccountId,
                }, adAccount.accessToken);
                break;
            case 'google':
                campaignId = await this.googleApi.createCampaign({ name, platform, budget, status, startDate, endDate, tags, description, adAccountId }, adAccount.accessToken);
                break;
            case 'tiktok':
                campaignId = await this.tiktokApi.createCampaign({ name, platform, budget, status, startDate, endDate, tags, description, adAccountId }, adAccount.accessToken);
                break;
            default:
                throw new Error('Unsupported platform');
        }
        const entity = this.adCampaignRepo.create({
            name,
            platform,
            budget,
            status,
            startDate,
            endDate,
            tags,
            description,
            campaignId,
            adAccount,
            tenant: { id: tenantId },
        });
        const saved = await this.adCampaignRepo.save(entity);
        return plainToInstance(AdCampaignDTO, saved);
    }
} 