import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
    ManyToOne,
    Index
} from 'typeorm';
import { GroupStatus } from './dto/group.dto';
import { Tenant } from '../../entity/tenant.entity';

@Entity()
export class Group {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    belongTo?: string;

    @Column({ nullable: true })
    contactInfo?: string;

    @Column({ nullable: true })
    description?: string;

    @Column({ nullable: true, default: 0 })
    memberCount?: number;

    @Column({ type: 'json', nullable: true })
    permissions?: any;

    @Column({
        type: 'enum',
        enum: GroupStatus,
        default: GroupStatus.ACTIVE
    })
    status: GroupStatus;

    @Index()
    @Column({ nullable: true })
    tenantId?: string;

    @ManyToOne(() => Tenant, { nullable: true, onDelete: 'CASCADE' })
    tenant?: Tenant;

    @CreateDateColumn({
        type: 'timestamp with time zone',
        nullable: true,
        name: 'create_time',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createTime?: Date;

    @UpdateDateColumn({
        type: 'timestamp with time zone',
        nullable: true,
        name: 'update_time',
        default: () => 'CURRENT_TIMESTAMP',
        onUpdate: 'CURRENT_TIMESTAMP',
    })
    updateTime?: Date;
} 