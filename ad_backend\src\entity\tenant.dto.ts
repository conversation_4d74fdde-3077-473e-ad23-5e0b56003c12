import { ObjectType, Field, ID } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import GraphQLJSON from 'graphql-type-json';

@ObjectType('TenantDTO')
export class TenantDTO {
  @IDField(() => ID)
  id: string;

  @FilterableField()
  name: string;

  @FilterableField({ nullable: true })
  logo?: string;

  @FilterableField()
  type: string;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  db_config?: any;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  sync_config?: any;
}
