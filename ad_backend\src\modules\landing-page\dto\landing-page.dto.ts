import { ObjectType, InputType, Field, ID, GraphQLISODateTime, Float } from '@nestjs/graphql';
import { IsString, IsOptional, IsNumber, IsArray } from 'class-validator';

@ObjectType('LandingPage')
@InputType('LandingPageInput')
export class LandingPageDTO {
    @Field(() => ID)
    id: string;

    @Field()
    code: string;

    @Field()
    title: string;

    @Field()
    url: string;

    @Field()
    slogan: string;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    appIcon?: string[];

    @Field(() => [String], { nullable: true })
    @IsOptional()
    background?: string[];

    @Field({ nullable: true })
    @IsOptional()
    description?: string;

    @Field(() => Float)
    star: number;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    promoImages?: string[];

    @Field()
    creator: string;

    @Field(() => GraphQLISODateTime)
    createdAt: Date;

    @Field()
    tenantId: string;
}

@InputType('CreateLandingPageInput')
export class CreateLandingPageInput {
    @Field()
    title: string;
    @Field()
    url: string;
    @Field()
    slogan: string;
    @Field(() => [String], { nullable: true })
    @IsOptional()
    appIcon?: string[];
    @Field(() => [String], { nullable: true })
    @IsOptional()
    background?: string[];
    @Field({ nullable: true })
    @IsOptional()
    description?: string;
    @Field(() => Float)
    star: number;
    @Field(() => [String], { nullable: true })
    @IsOptional()
    promoImages?: string[];
    @Field({ nullable: true })
    @IsOptional()
    creator?: string;
    @Field({ nullable: true, description: '由后端自动填充' })
    tenantId?: string;
}

@InputType('UpdateLandingPageInput')
export class UpdateLandingPageInput {
    @Field({ nullable: true })
    @IsOptional()
    title?: string;
    @Field({ nullable: true })
    @IsOptional()
    url?: string;
    @Field({ nullable: true })
    @IsOptional()
    slogan?: string;
    @Field(() => [String], { nullable: true })
    @IsOptional()
    appIcon?: string[];
    @Field(() => [String], { nullable: true })
    @IsOptional()
    background?: string[];
    @Field({ nullable: true })
    @IsOptional()
    description?: string;
    @Field(() => Float, { nullable: true })
    @IsOptional()
    star?: number;
    @Field(() => [String], { nullable: true })
    @IsOptional()
    promoImages?: string[];
    @Field({ nullable: true })
    @IsOptional()
    creator?: string;
    @Field({ nullable: true, description: '由后端自动填充' })
    tenantId?: string;
} 