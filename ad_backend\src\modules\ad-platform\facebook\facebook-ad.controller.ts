import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { FacebookAdService } from '../services/facebook-ad.service';

@Controller('ad-platform/facebook/ads')
export class FacebookAdController {
  constructor(private readonly facebookAdService: FacebookAdService) { }

  @Post('sync')
  async syncAds(@Body() body: any) {
    return this.facebookAdService.syncAds(body);
  }

  @Get('preview')
  async getAdPreview(@Query('adId') adId: string) {
    return this.facebookAdService.getAdPreview(adId);
  }

  @Get('insights')
  async getAdInsights(@Query('adId') adId: string) {
    return this.facebookAdService.getAdInsights(adId);
  }
}
