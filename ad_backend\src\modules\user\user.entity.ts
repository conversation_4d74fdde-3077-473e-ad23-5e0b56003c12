import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable, BeforeInsert, BeforeUpdate, ManyToOne } from 'typeorm';
import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import * as bcrypt from 'bcryptjs';
import { Role } from '../role/role.entity';
import { Group } from '../group/group.entity';
import { Tenant } from '../../entity/tenant.entity';

export enum UserStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    BLOCKED = 'blocked'
}

registerEnumType(UserStatus, {
    name: 'UserStatus',
    description: '用户状态枚举',
});

@Entity('system_user')
@ObjectType()
export class User {
    @PrimaryGeneratedColumn('uuid')
    @Field(() => ID)
    id: string;

    @Column({ length: 50, unique: true })
    @Field()
    username: string;

    @Column({ unique: true })
    @Field()
    email: string;

    @Column()
    password: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    avatar?: string;

    @Column({ name: 'full_name', nullable: true })
    @Field({ nullable: true })
    fullName?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    phone?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    address?: string;

    @Column({ name: 'register_ip', nullable: true })
    @Field({ nullable: true })
    registerIp?: string;

    @Column({
        type: 'enum',
        enum: UserStatus,
        default: UserStatus.ACTIVE
    })
    @Field(() => UserStatus)
    status: UserStatus;

    @ManyToMany(() => Role)
    @JoinTable({
        name: 'system_user_roles',
        joinColumn: {
            name: 'userId',
            referencedColumnName: 'id'
        },
        inverseJoinColumn: {
            name: 'roleId',
            referencedColumnName: 'id'
        }
    })
    @Field(() => [Role], { nullable: true })
    roles: Role[];

    @ManyToMany(() => Group)
    @JoinTable({
        name: 'system_user_groups',
        joinColumn: {
            name: 'userId',
            referencedColumnName: 'id'
        },
        inverseJoinColumn: {
            name: 'groupId',
            referencedColumnName: 'id'
        }
    })
    @Field(() => [Group], { nullable: true })
    groups: Group[];

    @Column({ nullable: true })
    @Field({ nullable: true })
    tenantId: string;

    @ManyToOne(() => Tenant, { nullable: true })
    @Field(() => Tenant, { nullable: true })
    tenant: Tenant;

    @CreateDateColumn({
        type: 'timestamp with time zone',
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    @Field(() => Date)
    createdAt: Date;

    @UpdateDateColumn({
        type: 'timestamp with time zone',
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
        onUpdate: 'CURRENT_TIMESTAMP',
    })
    @Field(() => Date)
    updatedAt: Date;

    @BeforeInsert()
    async hashPassword() {
        if (this.password) {
            const salt = await bcrypt.genSalt(10);
            this.password = await bcrypt.hash(this.password, salt);
        }
    }

    @BeforeUpdate()
    async hashPasswordOnUpdate() {
        if (this.password) {
            const salt = await bcrypt.genSalt(10);
            this.password = await bcrypt.hash(this.password, salt);
        }
    }

    async comparePassword(attempt: string): Promise<boolean> {
        return bcrypt.compare(attempt, this.password);
    }
}
