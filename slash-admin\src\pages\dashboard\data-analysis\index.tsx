import { useTranslation } from 'react-i18next';
import DataAnalysisComponent from "../analysis/data-analysis";

export default function DataAnalysis() {
  const { t } = useTranslation();
  return (
    <div className="space-y-8">
      <div className="flex items-center">
        <h2 className="text-3xl font-bold tracking-tight">{t('pages.key_795')}</h2>
      </div>
      
      <div className="space-y-4">
        <DataAnalysisComponent />
      </div>
    </div>
  );
}
