import { useTranslation } from 'react-i18next';
/**
 * Facebook Marketing API 直接调用服务
 * 直接调用Facebook第三方接口获取广告数据
 */

// Facebook API配置
const FACEBOOK_API_VERSION = 'v18.0';
const FACEBOOK_BASE_URL = `https://graph.facebook.com/${FACEBOOK_API_VERSION}`;

// 数据接口定义
export interface FacebookAccountData {
    accountId: string;
    accountName: string;
    accountStatus: 'ACTIVE' | 'DISABLED' | 'PAUSED' | 'PENDING_REVIEW' | 'DISAPPROVED';
    spend: number;
    impressions: number;
    clicks: number;
    ctr: number;
    cpm: number;
    cpc: number;
}

export interface FacebookAggregatedData {
    totalSpend: number;
    totalImpressions: number;
    totalClicks: number;
    activeAccounts: number;
    totalAccounts: number;
    accountStatuses: { [accountId: string]: 'ACTIVE' | 'DISABLED' | 'PAUSED' };
    detailData: FacebookAccountData[];
}

export interface AdAccountOAuth {
    accountId: string;
    accessToken: string;
}

/**
 * Facebook Marketing API 直接调用服务
 */
export class FacebookDirectApiService {
    /**
     * 获取广告账户基本信息
     */
    async getAccountInfo(accountId: string, accessToken: string): Promise<FacebookAccountData> {
        try {
            console.log(`🔍 获取广告账户信息: ${accountId}`);

            const { t } = useTranslation();

            const url = `${FACEBOOK_BASE_URL}/act_${accountId}?` +
                `fields=id,name,account_status,currency,timezone_name&` +
                `access_token=${encodeURIComponent(accessToken)}`;

            const response = await fetch(url);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Facebook API错误 ${response.status}: ${errorText}`);
            }

            const data = await response.json();

            if (data.error) {
                throw new Error(`Facebook API错误: ${data.error.message} (代码: ${data.error.code})`);
            }

            // 转换账户状态
            const getAccountStatus = (status: number): FacebookAccountData['accountStatus'] => {
                switch (status) {
                    case 1: return 'ACTIVE';
                    case 2: return 'DISABLED';
                    case 3: return 'PAUSED';
                    case 7: return 'PENDING_REVIEW';
                    case 8: return 'DISAPPROVED';
                    default: return 'DISABLED';
                }
            };

            return {
                accountId: data.id,
                accountName: data.name || t('pages.key_1306'),
                accountStatus: getAccountStatus(data.account_status),
                spend: 0, // 将在Insights中获取
                impressions: 0,
                clicks: 0,
                ctr: 0,
                cpm: 0,
                cpc: 0
            };
        } catch (error: any) {
            console.error(`❌ 获取账户 ${accountId} 信息失败:`, error);
            throw new Error(`获取账户信息失败: ${error.message}`);
        }
    }

    /**
     * 获取广告账户Insights数据
     */
    async getAccountInsights(accountId: string, accessToken: string): Promise<Partial<FacebookAccountData>> {
        try {
            console.log(`📊 获取广告账户Insights: ${accountId}`);

            // 设置时间范围 - 最近30天
            const endDate = new Date().toISOString().split('T')[0];
            const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

            const timeRange = JSON.stringify({
                since: startDate,
                until: endDate
            });

            const url = `${FACEBOOK_BASE_URL}/act_${accountId}/insights?` +
                `fields=spend,impressions,clicks,ctr,cpm,cpc&` +
                `time_range=${encodeURIComponent(timeRange)}&` +
                `access_token=${encodeURIComponent(accessToken)}`;

            const response = await fetch(url);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Facebook Insights API错误 ${response.status}: ${errorText}`);
            }

            const data = await response.json();

            if (data.error) {
                throw new Error(`Facebook Insights API错误: ${data.error.message} (代码: ${data.error.code})`);
            }

            // 解析Insights数据
            if (data.data && data.data.length > 0) {
                const insights = data.data[0];
                return {
                    spend: parseFloat(insights.spend || '0'),
                    impressions: parseInt(insights.impressions || '0'),
                    clicks: parseInt(insights.clicks || '0'),
                    ctr: parseFloat(insights.ctr || '0'),
                    cpm: parseFloat(insights.cpm || '0'),
                    cpc: parseFloat(insights.cpc || '0')
                };
            } else {
                // 没有数据，返回默认值
                return {
                    spend: 0,
                    impressions: 0,
                    clicks: 0,
                    ctr: 0,
                    cpm: 0,
                    cpc: 0
                };
            }
        } catch (error: any) {
            console.error(`❌ 获取账户 ${accountId} Insights失败:`, error);
            // 不抛出错误，返回默认值
            return {
                spend: 0,
                impressions: 0,
                clicks: 0,
                ctr: 0,
                cpm: 0,
                cpc: 0
            };
        }
    }

    /**
     * 获取单个广告账户的完整数据
     */
    async getCompleteAccountData(accountId: string, accessToken: string): Promise<FacebookAccountData> {
        try {
            console.log(`🔄 获取账户 ${accountId} 完整数据`);

            // 并行获取账户信息和Insights数据
            const [accountInfo, insightsData] = await Promise.all([
                this.getAccountInfo(accountId, accessToken),
                this.getAccountInsights(accountId, accessToken)
            ]);

            return {
                ...accountInfo,
                ...insightsData
            };
        } catch (error: any) {
            console.error(`❌ 获取账户 ${accountId} 完整数据失败:`, error);

            // 返回错误状态的账户数据
            return {
                accountId: accountId,
                accountName: `账户 ${accountId}`,
                accountStatus: 'DISABLED',
                spend: 0,
                impressions: 0,
                clicks: 0,
                ctr: 0,
                cpm: 0,
                cpc: 0
            };
        }
    }

    /**
     * 获取多个广告账户的聚合数据
     */
    async getAggregatedData(accountOAuths: AdAccountOAuth[]): Promise<FacebookAggregatedData> {
        try {
            console.log(`📈 开始获取 ${accountOAuths.length} 个账户的聚合数据`);

            const { t } = useTranslation();

            if (accountOAuths.length === 0) {
                throw new Error(t('pages.key_1486'));
            }

            // 并行获取所有账户的数据
            const accountDataPromises = accountOAuths.map(auth =>
                this.getCompleteAccountData(auth.accountId, auth.accessToken)
            );

            const accountDataList = await Promise.all(accountDataPromises);

            // 计算聚合数据
            const totalSpend = accountDataList.reduce((sum, account) => sum + account.spend, 0);
            const totalImpressions = accountDataList.reduce((sum, account) => sum + account.impressions, 0);
            const totalClicks = accountDataList.reduce((sum, account) => sum + account.clicks, 0);
            const activeAccounts = accountDataList.filter(account => account.accountStatus === 'ACTIVE').length;

            // 构建账户状态映射
            const accountStatuses: { [accountId: string]: 'ACTIVE' | 'DISABLED' | 'PAUSED' } = {};
            accountDataList.forEach(account => {
                accountStatuses[account.accountId] = account.accountStatus === 'PENDING_REVIEW' || account.accountStatus === 'DISAPPROVED'
                    ? 'DISABLED'
                    : account.accountStatus;
            });

            const result: FacebookAggregatedData = {
                totalSpend,
                totalImpressions,
                totalClicks,
                activeAccounts,
                totalAccounts: accountDataList.length,
                accountStatuses,
                detailData: accountDataList
            };

            console.log('✅ 聚合数据获取成功:', {
                totalSpend: result.totalSpend,
                totalClicks: result.totalClicks,
                activeAccounts: result.activeAccounts,
                totalAccounts: result.totalAccounts
            });

            return result;
        } catch (error: any) {
            console.error('❌ 获取聚合数据失败:', error);
            throw new Error(`获取Facebook数据失败: ${error.message}`);
        }
    }

    /**
     * 验证访问令牌
     */
    async validateToken(accessToken: string): Promise<{ valid: boolean; userId?: string; userName?: string; error?: string }> {
        try {
            const url = `${FACEBOOK_BASE_URL}/me?fields=id,name&access_token=${encodeURIComponent(accessToken)}`;

            const response = await fetch(url);
            const data = await response.json();

            if (data.error) {
                return {
                    valid: false,
                    error: `${data.error.message} (代码: ${data.error.code})`
                };
            }

            return {
                valid: true,
                userId: data.id,
                userName: data.name
            };
        } catch (error: any) {
            return {
                valid: false,
                error: error.message
            };
        }
    }
} 