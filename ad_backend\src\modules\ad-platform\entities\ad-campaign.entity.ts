import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';
import { AdAccount } from './ad-account.entity';

@Entity('ad_campaign')
export class AdCampaign {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '平台类型（facebook/google/tiktok/adjust/af等）' })
  @Column({ length: 32 })
  platform: string;

  @ApiProperty({ description: '广告平台Campaign ID' })
  @Index()
  @Column({ length: 64 })
  campaignId: string;

  @ApiProperty({ description: '广告系列名称' })
  @Column({ length: 128 })
  name: string;

  @ApiProperty({ description: '所属广告账户' })
  @ManyToOne(() => AdAccount, { nullable: false })
  adAccount: AdAccount;

  @ApiProperty({ description: '租户ID' })
  @Index()
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始campaign数据（JSON）' })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;

  @ApiProperty({ description: '系列状态' })
  @Column({ length: 32, default: 'active' })
  status: string;

  @Column({ type: 'float', nullable: false })
  budget: number;

  @Column({ type: 'timestamp', nullable: false })
  startDate: Date;

  @Column({ type: 'timestamp', nullable: false })
  endDate: Date;

  @Column({ type: 'text', array: true, nullable: true })
  tags?: string[];

  @Column({ type: 'text', nullable: true })
  description?: string;
}
