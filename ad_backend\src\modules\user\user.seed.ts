import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserStatus } from './user.entity';
import { Role } from '../role/role.entity';
import { Group } from '../group/group.entity';

@Injectable()
export class UserSeedService {
    constructor(
        @InjectRepository(User)
        private readonly userRepository: Repository<User>,
        @InjectRepository(Role)
        private readonly roleRepository: Repository<Role>,
        @InjectRepository(Group)
        private readonly groupRepository: Repository<Group>
    ) { }

    async seed() {
        const count = await this.userRepository.count();

        if (count === 0) {
            console.log('📦 开始初始化用户数据...');

            // 获取管理员角色
            const adminRole = await this.roleRepository.findOne({
                where: { name: '超级管理员' }
            });

            // 获取技术部群组
            const techGroup = await this.groupRepository.findOne({
                where: { name: '技术部' }
            });

            // 创建管理员用户
            const adminUser = this.userRepository.create({
                username: 'admin',
                email: '<EMAIL>',
                password: 'Admin@123', // 这将通过@BeforeInsert钩子自动加密
                fullName: '系统管理员',
                phone: '13800000000',
                status: UserStatus.ACTIVE,
                registerIp: '127.0.0.1'
            });

            if (adminRole) {
                adminUser.roles = [adminRole];
            }

            if (techGroup) {
                adminUser.groups = [techGroup];
            }

            // 创建测试用户
            const testUser = this.userRepository.create({
                username: 'test',
                email: '<EMAIL>',
                password: 'Test@123', // 这将通过@BeforeInsert钩子自动加密
                fullName: '测试用户',
                phone: '13900000000',
                status: UserStatus.ACTIVE,
                registerIp: '127.0.0.1'
            });

            // 获取普通用户角色
            const userRole = await this.roleRepository.findOne({
                where: { name: '普通用户' }
            });

            // 获取运营部群组
            const operationGroup = await this.groupRepository.findOne({
                where: { name: '运营部' }
            });

            if (userRole) {
                testUser.roles = [userRole];
            }

            if (operationGroup) {
                testUser.groups = [operationGroup];
            }

            await this.userRepository.save([adminUser, testUser]);
            console.log('✅ 用户数据初始化完成');
        } else {
            console.log('📦 用户数据已存在，跳过初始化');
        }
    }
} 