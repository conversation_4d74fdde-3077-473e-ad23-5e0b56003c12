const { Client } = require('pg');

async function main() {
    const client = new Client({
        host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
        port: 5432,
        user: 'yeeu',
        password: 'Yy&********',
        database: 'ad_auto',
    });
    await client.connect();
    const res = await client.query('SELECT COUNT(*) FROM ad_account');
    console.log(`ad_account 表当前有 ${res.rows[0].count} 条数据，将全部清空。`);
    await client.query('DELETE FROM ad_account');
    console.log('ad_account 表已清空。');
    await client.end();
}

main().catch(err => {
    console.error('清理失败:', err);
    process.exit(1);
}); 