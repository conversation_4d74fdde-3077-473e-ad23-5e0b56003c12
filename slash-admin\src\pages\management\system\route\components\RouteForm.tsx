import { useTranslation } from 'react-i18next';
import { Form, Input, Select, Switch, TreeSelect, Modal } from 'antd';
import { useEffect, useMemo } from 'react';
import type { RouteFormData, RouteItem, RouteTreeItem } from '../types';
import { Icon } from '@/components/icon';
import type { DefaultOptionType } from 'antd/es/select';

interface RouteFormProps {
    open: boolean;
    type: 'add' | 'edit';
    initialValues: RouteItem | null;
    onCancel: () => void;
    onOk: (values: RouteFormData) => void;
    routes?: RouteItem[];
}

export default function RouteForm({ open, type, initialValues, onCancel, onOk, routes = [] }: RouteFormProps) {
    const { t } = useTranslation();
    const [form] = Form.useForm<RouteFormData>();

    // 图标选项
    const iconOptions: DefaultOptionType[] = [
        { label: t('pages.key_95'), value: 'solar:chart-bold-duotone' },
        { label: t('pages.key_2113'), value: 'solar:settings-bold-duotone' },
        { label: t('pages.key_1619'), value: 'solar:user-bold-duotone' },
        { label: t('pages.key_2067'), value: 'solar:users-group-rounded-bold-duotone' },
        { label: t('pages.key_1320'), value: 'solar:shield-keyhole-bold-duotone' },
        { label: t('pages.key_1774'), value: 'solar:widget-bold-duotone' },
        { label: t('pages.key_2345'), value: 'solar:routing-bold-duotone' },
        { label: t('pages.key_2476'), value: 'solar:settings-minimalistic-bold-duotone' },
    ];

    // 构建父级路由的树形选择数据
    const parentRouteOptions = useMemo(() => {
        const buildTreeData = (items: RouteItem[]): any[] => {
            const map = new Map<string, any>();
            const tree: any[] = [];

            // 第一步：创建所有菜单节点的映射
            items.forEach(item => {
                if (item.type === 'menu') {
                    map.set(item.id, {
                        title: (
                            <span>
                                {item.icon && <Icon icon={item.icon} size={16} className="mr-1" />}
                                {item.name}
                            </span>
                        ),
                        value: item.id,
                        children: []
                    });
                }
            });

            // 第二步：构建树形结构
            items.forEach(item => {
                // 跳过非菜单类型的路由
                if (item.type !== 'menu') return;

                // 跳过当前编辑的节点及其子节点
                if (initialValues && (item.id === initialValues.id || hasParent(items, item, initialValues.id))) {
                    return;
                }

                const node = map.get(item.id);
                if (!node) return;

                if (item.parentId === null) {
                    // 根节点直接加入树中
                    tree.push(node);
                } else {
                    // 将节点添加到父节点的children中
                    const parentNode = map.get(item.parentId);
                    if (parentNode) {
                        parentNode.children.push(node);
                    }
                }
            });

            return tree;
        };

        // 检查是否是某个节点的父节点
        const hasParent = (items: RouteItem[], item: RouteItem, targetId: string): boolean => {
            if (item.parentId === null) return false;
            if (item.parentId === targetId) return true;
            const parent = items.find(i => i.id === item.parentId);
            if (parent) {
                return hasParent(items, parent, targetId);
            }
            return false;
        };

        return buildTreeData(routes);
    }, [routes, initialValues]);

    useEffect(() => {
        if (open && type === 'edit' && initialValues) {
            form.setFieldsValue(initialValues);
        } else {
            form.resetFields();
        }
    }, [open, type, initialValues, form]);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            onOk(values);
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    return (
        <Modal
            title={type === 'add' ? t('pages.key_1543') : t('pages.key_1867')}
            open={open}
            onCancel={onCancel}
            onOk={handleSubmit}
            width={700}
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={{
                    type: 'menu',
                    status: 'enabled',
                    order: 1,
                    isHidden: false
                }}
            >
                <Form.Item
                    name="parentId"
                    label={t('pages.key_1595')}
                >
                    <TreeSelect
                        treeData={parentRouteOptions}
                        placeholder={t('pages.key_2272')}
                        allowClear
                        treeDefaultExpandAll
                        showSearch
                        treeNodeFilterProp="title"
                    />
                </Form.Item>

                <Form.Item
                    name="name"
                    label={t('pages.key_2346')}
                    rules={[{ required: true, message: t('pages.key_2236') }]}
                >
                    <Input placeholder={t('pages.key_2236')} />
                </Form.Item>

                <Form.Item
                    name="path"
                    label={t('pages.key_2351')}
                    rules={[{ required: true, message: t('pages.key_2237') }]}
                >
                    <Input placeholder="请输入路由路径，如: /system/route" />
                </Form.Item>

                <Form.Item
                    name="component"
                    label={t('pages.key_1804')}
                    tooltip={t('pages.key_2543')}
                >
                    <Input placeholder="请输入组件路径，如: /pages/system/route/index" />
                </Form.Item>

                <Form.Item
                    name="icon"
                    label={t('pages.key_528')}
                >
                    <Select
                        placeholder={t('pages.key_2256')}
                        allowClear
                        options={iconOptions}
                        optionLabelProp="label"
                        optionRender={(option: DefaultOptionType) => {
                            const iconValue = option.value?.toString() || '';
                            return (
                                <div className="flex items-center gap-2">
                                    <Icon icon={iconValue} size={20} />
                                    <span>{option.label}</span>
                                </div>
                            );
                        }}
                    />
                </Form.Item>

                <Form.Item
                    name="type"
                    label={t('pages.key_2350')}
                    rules={[{ required: true, message: t('pages.key_2291') }]}
                >
                    <Select
                        options={[
                            { label: t('pages.key_2006'), value: 'menu' },
                            { label: t('pages.key_2540'), value: 'page' }
                        ]}
                    />
                </Form.Item>

                <Form.Item
                    name="order"
                    label={t('pages.key_1014')}
                    rules={[{ required: true, message: t('pages.key_2199') }]}
                >
                    <Input type="number" placeholder={t('pages.key_2199')} />
                </Form.Item>

                <Form.Item
                    name="status"
                    label={t('pages.key_1602')}
                    rules={[{ required: true, message: t('pages.key_2273') }]}
                >
                    <Select
                        options={[
                            { label: t('pages.key_496'), value: 'enabled' },
                            { label: t('pages.key_1714'), value: 'disabled' }
                        ]}
                    />
                </Form.Item>

                <Form.Item
                    name="isHidden"
                    label={t('pages.key_1204')}
                    valuePropName="checked"
                >
                    <Switch />
                </Form.Item>

                <Form.Item
                    name="description"
                    label={t('pages.key_1027')}
                >
                    <Input.TextArea placeholder={t('pages.key_2202')} />
                </Form.Item>
            </Form>
        </Modal>
    );
} 