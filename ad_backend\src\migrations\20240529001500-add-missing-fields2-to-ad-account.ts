import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMissingFields2ToAdAccount20240529001500 implements MigrationInterface {
    name = 'AddMissingFields2ToAdAccount20240529001500'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "updatedAt" TIMESTAMP WITH TIME ZONE;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "updatedAt";`);
    }
} 