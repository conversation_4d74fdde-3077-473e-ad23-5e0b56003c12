import { Resolver, Query, Mutation, Args, ID, Context, ObjectType, Field, Int, InputType } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';
import { Tenant } from '../../entity/tenant.entity';
import { TenantDTO } from '../../entity/tenant.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { plainToInstance } from 'class-transformer';
import GraphQLJSON from 'graphql-type-json';
import { IsOptional, IsString } from 'class-validator';
import { UserService } from '../user/user.service';
import { User } from '../user/user.entity';
import { AdAccount } from './entities/ad-account.entity';
import { Group } from '../../modules/group/group.entity';
import { Audience } from './entities/audience.entity';
import { Campaign } from './entities/campaign.entity';
import { Material } from './entities/material.entity';
import { Report } from './report/report.entity';
import { LandingPageService } from '../landing-page/landing-page.service';
import { TenantGuard } from '../../guards/tenant.guard';

@InputType()
class CreateTenantInput {
    @Field()
    @IsString()
    name: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    logo?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    type?: string;
    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    db_config?: any;
    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    sync_config?: any;
    @Field({ nullable: true })
    @IsOptional()
    createAccount?: boolean;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    accountUsername?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    accountPassword?: string;
}

@InputType()
class UpdateTenantInput {
    @Field(() => ID)
    @IsString()
    id: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    name?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    logo?: string;
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    type?: string;
    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    db_config?: any;
    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    sync_config?: any;
}

@ObjectType()
class TenantPage {
    @Field(() => [TenantDTO])
    nodes: TenantDTO[];
    @Field(() => Int)
    total: number;
}

@ObjectType()
class TenantResourceStats {
    @Field()
    type: string;
    @Field(() => Int)
    count: number;
}

@ObjectType()
class TenantResourceDetail {
    @Field()
    type: string;
    @Field()
    name: string;
    @Field({ nullable: true })
    status?: string;
    @Field({ nullable: true })
    group?: string;
    @Field({ nullable: true })
    createdAt?: string;
    @Field({ nullable: true })
    remark?: string;
}

@ObjectType()
class TenantResourceDetailPage {
    @Field(() => Int)
    total: number;
    @Field(() => [TenantResourceDetail])
    nodes: TenantResourceDetail[];
}

@ObjectType()
class TenantTrendPoint {
    @Field()
    date: string;
    @Field({ nullable: true })
    impressions?: number;
    @Field({ nullable: true })
    clicks?: number;
    @Field({ nullable: true })
    spend?: number;
    @Field({ nullable: true })
    conversion?: number;
}

@ObjectType()
class GlobalResourceStats {
    @Field()
    type: string;
    @Field(() => Int)
    count: number;
}

@ObjectType()
class GlobalResourceDetail {
    @Field()
    type: string;
    @Field()
    name: string;
    @Field({ nullable: true })
    status?: string;
    @Field({ nullable: true })
    group?: string;
    @Field({ nullable: true })
    createdAt?: string;
    @Field({ nullable: true })
    remark?: string;
    @Field({ nullable: true })
    tenantName?: string;
    @Field({ nullable: true })
    tenantId?: string;
}

@ObjectType()
class GlobalResourceDetailPage {
    @Field(() => Int)
    total: number;
    @Field(() => [GlobalResourceDetail])
    nodes: GlobalResourceDetail[];
}

@ObjectType()
class GlobalTrendPoint {
    @Field()
    date: string;
    @Field({ nullable: true })
    spend?: number;
    @Field({ nullable: true })
    conversion?: number;
    @Field({ nullable: true })
    tenantCount?: number;
    @Field({ nullable: true })
    userCount?: number;
    @Field({ nullable: true })
    adCount?: number;
}

@ObjectType()
export class ChannelDistributionDTO {
    @Field()
    channel: string; // 渠道编码，与落地页code一致
    @Field()
    channelName: string; // 渠道名称（落地页title）
    @Field(() => Int)
    count: number;   // 该渠道下广告账户数量
    @Field(() => Int, { nullable: true })
    totalImpressions?: number; // 展示量
    @Field(() => Int, { nullable: true })
    totalClicks?: number;      // 点击量
    @Field(() => Int, { nullable: true })
    totalConversion?: number;  // 转化量
    @Field(() => Int, { nullable: true })
    totalSpend?: number;       // 总消耗
}

@Resolver(() => TenantDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class TenantResolver {
    constructor(
        @InjectRepository(Tenant)
        private readonly tenantRepo: Repository<Tenant>,
        @InjectRepository(User)
        private readonly userRepo: Repository<User>,
        @InjectRepository(AdAccount)
        private readonly adRepo: Repository<AdAccount>,
        @InjectRepository(Group)
        private readonly groupRepo: Repository<Group>,
        @InjectRepository(Audience)
        private readonly audienceRepo: Repository<Audience>,
        @InjectRepository(Campaign)
        private readonly campaignRepo: Repository<Campaign>,
        @InjectRepository(Material)
        private readonly materialRepo: Repository<Material>,
        private readonly userService: UserService,
        @InjectRepository(Report)
        private readonly reportRepo: Repository<Report>,
        private readonly landingPageService: LandingPageService,
    ) { }

    @Query(() => TenantPage)
    @UseGuards(JwtAuthGuard)
    async tenants(
        @Args('search', { nullable: true }) search?: string,
        @Args('page', { nullable: true, type: () => Int }) page = 1,
        @Args('limit', { nullable: true, type: () => Int }) limit = 10,
    ): Promise<TenantPage> {
        const [result, total] = await this.tenantRepo.findAndCount({
            where: search ? { name: Like(`%${search}%`) } : {},
            skip: (page - 1) * limit,
            take: limit,
            order: { name: 'ASC' },
        });
        return { nodes: plainToInstance(TenantDTO, result), total };
    }

    @Query(() => TenantDTO)
    @UseGuards(JwtAuthGuard)
    async tenant(@Args('id', { type: () => ID }) id: string): Promise<TenantDTO> {
        const entity = await this.tenantRepo.findOneOrFail({ where: { id } });
        return plainToInstance(TenantDTO, entity);
    }

    @Mutation(() => TenantDTO)
    @UseGuards(JwtAuthGuard)
    async createTenant(@Args('input') input: CreateTenantInput): Promise<TenantDTO> {
        const entity = this.tenantRepo.create(input);
        const saved = await this.tenantRepo.save(entity);
        if (input.createAccount && input.accountUsername && input.accountPassword) {
            // 查找"租户管理员"角色
            const tenantAdminRole = await this.userService['roleRepository'].findOne({
                where: [
                    { name: '租户管理员' },
                    { name: 'tenantadmin' }
                ]
            });
            let roleIds = [];
            if (tenantAdminRole) {
                roleIds = [tenantAdminRole.id];
            }
            let username = input.accountUsername;
            let suffix = 1;
            while (await this.userService.findByUsername(username)) {
                username = `${input.accountUsername}${suffix++}`;
            }
            await this.userService.create({
                username,
                password: input.accountPassword,
                email: `${username}@${saved.id}.com`,
                tenantId: saved.id,
                roleIds,
            });
        }
        return plainToInstance(TenantDTO, saved);
    }

    @Mutation(() => TenantDTO)
    @UseGuards(JwtAuthGuard)
    async updateTenant(@Args('input') input: UpdateTenantInput): Promise<TenantDTO> {
        const entity = await this.tenantRepo.findOneOrFail({ where: { id: input.id } });
        Object.assign(entity, input);
        const saved = await this.tenantRepo.save(entity);
        return plainToInstance(TenantDTO, saved);
    }

    @Mutation(() => Boolean)
    @UseGuards(JwtAuthGuard)
    async deleteTenant(@Args('id', { type: () => ID }) id: string): Promise<boolean> {
        const result = await this.tenantRepo.delete(id);
        return result.affected > 0;
    }

    @Mutation(() => Boolean)
    @UseGuards(JwtAuthGuard)
    async disableTenant(@Args('id', { type: () => ID }) id: string): Promise<boolean> {
        const entity = await this.tenantRepo.findOneOrFail({ where: { id } });
        entity.isActive = false;
        await this.tenantRepo.save(entity);
        return true;
    }

    @Mutation(() => Boolean)
    @UseGuards(JwtAuthGuard)
    async enableTenant(@Args('id', { type: () => ID }) id: string): Promise<boolean> {
        const entity = await this.tenantRepo.findOneOrFail({ where: { id } });
        entity.isActive = true;
        await this.tenantRepo.save(entity);
        return true;
    }

    @Query(() => [TenantResourceStats])
    async tenantResourceStats(@Args('tenantId') tenantId: string): Promise<TenantResourceStats[]> {
        const [userCount, adCount, groupCount, audienceCount, campaignCount, materialCount] = await Promise.all([
            this.userRepo.count({ where: { tenantId } }),
            this.adRepo.count({ where: { tenant: { id: tenantId } } }),
            this.groupRepo.count({ where: { tenantId } }),
            this.audienceRepo.count({ where: { tenant: { id: tenantId } } }),
            this.campaignRepo.find({ relations: ['adAccount'] }).then(list => list.filter((c: any) => c.adAccount && c.adAccount.tenant && c.adAccount.tenant.id === tenantId).length),
            this.materialRepo.count({ where: { tenant: { id: tenantId } } }),
        ]);
        return [
            { type: '账号', count: userCount },
            { type: '广告', count: adCount },
            { type: '群组', count: groupCount },
            { type: '受众', count: audienceCount },
            { type: '广告系列', count: campaignCount },
            { type: '素材', count: materialCount },
        ];
    }

    @Query(() => TenantResourceDetailPage)
    async tenantResourceDetails(
        @Args('tenantId') tenantId: string,
        @Args('page', { type: () => Int, defaultValue: 1 }) page: number,
        @Args('pageSize', { type: () => Int, defaultValue: 10 }) pageSize: number,
    ): Promise<TenantResourceDetailPage> {
        const users = await this.userRepo.find({ where: { tenantId } });
        const ads = await this.adRepo.find({ where: { tenant: { id: tenantId } } });
        const groups = await this.groupRepo.find({ where: { tenantId } });
        const audiences = await this.audienceRepo.find({ where: { tenant: { id: tenantId } } });
        const campaignsAll = await this.campaignRepo.find({ relations: ['adAccount'] });
        const campaigns = campaignsAll.filter(c => c.adAccount && c.adAccount.tenant && c.adAccount.tenant.id === tenantId);
        const materials = await this.materialRepo.find({ where: { tenant: { id: tenantId } } });

        const all: TenantResourceDetail[] = [
            ...users.map(u => ({ type: '账号', name: u.username, status: u.status, group: '', createdAt: u.createdAt?.toISOString(), remark: '' })),
            ...ads.map(a => ({ type: '广告', name: a.account, status: a.status, group: a.group, createdAt: a.createdAt?.toISOString(), remark: a.remark })),
            ...groups.map(g => ({ type: '群组', name: g.name, status: g.status, group: '', createdAt: g.createTime?.toISOString(), remark: g.description })),
            ...audiences.map(a => ({ type: '受众', name: a.name, status: '', group: '', createdAt: '', remark: '' })),
            ...campaigns.map(c => ({ type: '广告系列', name: c.name, status: c.status, group: '', createdAt: c.createdAt?.toISOString(), remark: '' })),
            ...materials.map(m => ({ type: '素材', name: m.name, status: m.status, group: '', createdAt: '', remark: '' })),
        ];
        all.sort((a, b) => (b.createdAt || '').localeCompare(a.createdAt || ''));
        const total = all.length;
        const nodes = all.slice((page - 1) * pageSize, page * pageSize);
        return { total, nodes };
    }

    @Query(() => [TenantTrendPoint])
    async tenantTrend(
        @Args('tenantId') tenantId: string,
        @Args('startDate', { nullable: true }) startDate?: string,
        @Args('endDate', { nullable: true }) endDate?: string,
    ): Promise<TenantTrendPoint[]> {
        const where: any = { tenant: { id: tenantId } };
        if (startDate || endDate) {
            where.date = {};
            if (startDate) where.date['$gte'] = startDate;
            if (endDate) where.date['$lte'] = endDate;
        }
        where.type = In(['ad', 'campaign', 'account']);
        const reports = await this.reportRepo.find({ where });
        // 按天聚合impressions/clicks/spend/conversion
        const trendMap: Record<string, { impressions: number; clicks: number; spend: number; conversion: number }> = {};
        for (const r of reports) {
            const d = r.date;
            if (!trendMap[d]) trendMap[d] = { impressions: 0, clicks: 0, spend: 0, conversion: 0 };
            if (typeof r.data?.impressions === 'number') trendMap[d].impressions += r.data.impressions;
            if (typeof r.data?.clicks === 'number') trendMap[d].clicks += r.data.clicks;
            if (typeof r.data?.spend === 'number') trendMap[d].spend += r.data.spend;
            if (typeof r.data?.conversion === 'number') trendMap[d].conversion += r.data.conversion;
        }
        return Object.entries(trendMap).sort(([a], [b]) => a.localeCompare(b)).map(([date, v]) => ({ date, ...v }));
    }

    @Query(() => [GlobalResourceStats])
    async globalResourceStats(): Promise<GlobalResourceStats[]> {
        const [userCount, adCount, groupCount, audienceCount, campaignCount, materialCount, tenantCount] = await Promise.all([
            this.userRepo.count(),
            this.adRepo.count(),
            this.groupRepo.count(),
            this.audienceRepo.count(),
            this.campaignRepo.count(),
            this.materialRepo.count(),
            this.tenantRepo.count(),
        ]);
        return [
            { type: '账号', count: userCount },
            { type: '广告', count: adCount },
            { type: '群组', count: groupCount },
            { type: '受众', count: audienceCount },
            { type: '广告系列', count: campaignCount },
            { type: '素材', count: materialCount },
            { type: '租户', count: tenantCount },
        ];
    }

    @Query(() => GlobalResourceDetailPage)
    async globalResourceDetails(
        @Args('page', { type: () => Int, defaultValue: 1 }) page: number,
        @Args('pageSize', { type: () => Int, defaultValue: 10 }) pageSize: number,
    ): Promise<GlobalResourceDetailPage> {
        const users = await this.userRepo.find({ relations: ['tenant'] });
        const ads = await this.adRepo.find({ relations: ['tenant'] });
        const groups = await this.groupRepo.find({ relations: ['tenant'] });
        const audiences = await this.audienceRepo.find({ relations: ['tenant'] });
        const campaigns = await this.campaignRepo.find({ relations: ['adAccount', 'adAccount.tenant'] });
        const materials = await this.materialRepo.find({ relations: ['tenant'] });
        const tenants = await this.tenantRepo.find();
        const all: GlobalResourceDetail[] = [
            ...users.map(u => ({ type: '账号', name: u.username, status: u.status, group: '', createdAt: u.createdAt?.toISOString(), remark: '', tenantName: u.tenant?.name, tenantId: u.tenantId })),
            ...ads.map(a => ({ type: '广告', name: a.account, status: a.status, group: a.group, createdAt: a.createdAt?.toISOString(), remark: a.remark, tenantName: a.tenant?.name, tenantId: a.tenant?.id })),
            ...groups.map(g => ({ type: '群组', name: g.name, status: g.status, group: '', createdAt: g.createTime?.toISOString(), remark: g.description, tenantName: g.tenant?.name, tenantId: g.tenant?.id })),
            ...audiences.map(a => ({ type: '受众', name: a.name, status: '', group: '', createdAt: '', remark: '', tenantName: a.tenant?.name, tenantId: a.tenant?.id })),
            ...campaigns.map(c => ({ type: '广告系列', name: c.name, status: c.status, group: '', createdAt: c.createdAt?.toISOString(), remark: '', tenantName: c.adAccount?.tenant?.name, tenantId: c.adAccount?.tenant?.id })),
            ...materials.map(m => ({ type: '素材', name: m.name, status: m.status, group: '', createdAt: '', remark: '', tenantName: m.tenant?.name, tenantId: m.tenant?.id })),
            ...tenants.map(t => ({ type: '租户', name: t.name, status: '', group: '', createdAt: t.createdAt?.toISOString(), remark: '', tenantName: t.name, tenantId: t.id })),
        ];
        all.sort((a, b) => (b.createdAt || '').localeCompare(a.createdAt || ''));
        const total = all.length;
        const nodes = all.slice((page - 1) * pageSize, page * pageSize);
        return { total, nodes };
    }

    @Query(() => [GlobalTrendPoint])
    async globalTrend(
        @Args('startDate', { nullable: true }) startDate?: string,
        @Args('endDate', { nullable: true }) endDate?: string,
    ): Promise<GlobalTrendPoint[]> {
        const where: any = {};
        if (startDate || endDate) {
            where.date = {};
            if (startDate) where.date['$gte'] = startDate;
            if (endDate) where.date['$lte'] = endDate;
        }
        // 统计所有类型
        const reports = await this.reportRepo.find({ where });
        // 按天聚合spend/conversion
        const trendMap: Record<string, GlobalTrendPoint> = {};
        for (const r of reports) {
            const d = r.date;
            if (!trendMap[d]) trendMap[d] = { date: d, spend: 0, conversion: 0 };
            if (typeof r.data?.spend === 'number') trendMap[d].spend! += r.data.spend;
            if (typeof r.data?.conversion === 'number') trendMap[d].conversion! += r.data.conversion;
        }
        // 可选：统计每日租户/账号/广告等数量
        // ...如需更细致可补充
        return Object.values(trendMap).sort((a, b) => a.date.localeCompare(b.date));
    }

    @Query(() => [ChannelDistributionDTO])
    async channelDistribution(
        @Args('tenantId') tenantId: string,
    ): Promise<ChannelDistributionDTO[]> {
        // 1. 获取所有落地页code-title映射
        const landingPages = await this.landingPageService.findAll(tenantId);
        const codeMap = new Map(landingPages.map(lp => [lp.code, lp.title]));

        // 2. 聚合ad_account表的channel字段，统计每个渠道的账户数量
        const qb = this.adRepo.createQueryBuilder('ad_account')
            .select('ad_account.channel', 'channel')
            .addSelect('COUNT(*)', 'count')
            .where('ad_account.tenantId = :tenantId', { tenantId })
            .groupBy('ad_account.channel');
        const channelCounts = await qb.getRawMany();

        // 3. 从Report表聚合渠道性能数据
        const channelPerformance = new Map();

        // 获取该租户下所有广告账户的渠道信息
        const adAccounts = await this.adRepo.find({
            where: { tenant: { id: tenantId } },
            select: ['id', 'channel']
        });

        // 按渠道分组广告账户ID
        const channelAccountsMap = new Map<string, string[]>();
        adAccounts.forEach(account => {
            if (account.channel) {
                if (!channelAccountsMap.has(account.channel)) {
                    channelAccountsMap.set(account.channel, []);
                }
                channelAccountsMap.get(account.channel)!.push(account.id);
            }
        });

        // 为每个渠道聚合Report数据
        for (const [channel, accountIds] of channelAccountsMap.entries()) {
            if (accountIds.length === 0) continue;

            // 查询该渠道下所有账户的Report数据
            // 注意：Report表中使用objectId字段存储账户ID，并且需要匹配type为'account'的记录
            const reports = await this.reportRepo
                .createQueryBuilder('report')
                .where('report.objectId IN (:...accountIds)', { accountIds })
                .andWhere('report.tenant.id = :tenantId', { tenantId })
                .andWhere('report.type = :type', { type: 'account' })
                .getMany();

            // 聚合该渠道的性能数据
            let totalImpressions = 0;
            let totalClicks = 0;
            let totalConversion = 0;
            let totalSpend = 0;

            reports.forEach(report => {
                if (report.data) {
                    totalImpressions += Number(report.data.impressions) || 0;
                    totalClicks += Number(report.data.clicks) || 0;
                    totalConversion += Number(report.data.conversion) || 0;
                    totalSpend += Number(report.data.spend) || 0;
                }
            });

            channelPerformance.set(channel, {
                totalImpressions,
                totalClicks,
                totalConversion,
                totalSpend
            });
        }

        // 4. 返回带完整性能数据的渠道分布
        return channelCounts
            .filter(r => r.channel)
            .map(r => {
                const performance = channelPerformance.get(r.channel) || {
                    totalImpressions: 0,
                    totalClicks: 0,
                    totalConversion: 0,
                    totalSpend: 0
                };

                return {
                    channel: r.channel,
                    channelName: codeMap.get(r.channel) || r.channel,
                    count: Number(r.count),
                    totalImpressions: performance.totalImpressions,
                    totalClicks: performance.totalClicks,
                    totalConversion: performance.totalConversion,
                    totalSpend: performance.totalSpend,
                };
            })
            .sort((a, b) => b.count - a.count); // 按账户数量排序
    }
} 