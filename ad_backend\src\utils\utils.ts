import { S3Client } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import * as fs from 'fs';
import * as mimeTypes from 'mime-types';
import FileType from 'file-type';
import { promisify } from 'util';

// 从环境变量中获取配置，确保敏感信息不会硬编码在代码中
export const cloudflareConfig = {
  CF_ACCOUNT_ID: '0c4a4baaede601e5df87282261feff21',
  CF_ACCESS_KEY_ID: '6eb79fc6100565bc7afd14717bd882fd',
  CF_SECRET_KEY_SECRET: '8194421a0551b4be7c4894e334666dc2fbf935de6d6b8b5e2d3d3bd80908da27',
  CF_BUCKET_NAME: 'yeeuad'
};

// 创建 S3 客户端
export const cfClient = new S3Client({
  region: 'auto',
  endpoint: `https://${cloudflareConfig.CF_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: cloudflareConfig.CF_ACCESS_KEY_ID,
    secretAccessKey: cloudflareConfig.CF_SECRET_KEY_SECRET,
  },
});

// 定义上传文件的接口
interface UploadFile {
  buffer?: Buffer;
  path?: string;
  originalname: string;
}

/**
 * 从文件获取内容类型
 * @param {Buffer} buffer - 文件缓冲区
 * @param {string} filename - 文件名
 * @returns {Promise<string>} 内容类型
 */
async function getFileContentType(buffer: Buffer, filename: string): Promise<string> {
  try {
    const fileTypeResult = await FileType.fromBuffer(buffer);
    if (fileTypeResult) {
      return fileTypeResult.mime;
    }
  } catch (error) {
    console.error('Error detecting file type:', error);
  }
  // 回退到基于文件扩展名的类型检测
  return mimeTypes.lookup(filename) || 'application/octet-stream';
}

/**
 * 从表单文件上传到 Cloudflare R2
 * @param {string} userId - 用户ID
 * @param {UploadFile} file - 上传的文件对象
 * @param {string} source - 素材来源
 * @returns {Promise<string>} 上传后的文件键名
 */
export async function uploadToCf(
  userId: string,
  file: UploadFile,
  source = 'default'
): Promise<string> {
  // 验证参数
  if (!userId) {
    throw new Error('User ID is required');
  }

  if (!file || (!file.buffer && !file.path)) {
    throw new Error('Invalid file: either buffer or path must be provided');
  }

  if (!file.originalname) {
    throw new Error('File originalname is required');
  }

  try {
    // 从文件获取内容
    let fileContent: Buffer;
    if (file.buffer) {
      fileContent = file.buffer;
    } else if (file.path) {
      fileContent = await promisify(fs.readFile)(file.path);
    } else {
      throw new Error('Invalid file: either buffer or path must be provided');
    }

    // 获取文件类型
    const contentType = await getFileContentType(fileContent, file.originalname);
    console.log("UploadToCf contentType:", contentType);

    // 构建文件名，添加source前缀
    const filename = `${userId}/${source}/${file.originalname}`;

    // 创建上传对象
    const upload = new Upload({
      client: cfClient,
      params: {
        Bucket: cloudflareConfig.CF_BUCKET_NAME,
        Key: filename,
        Body: fileContent,
        ContentType: contentType,
        ACL: 'public-read',
      },
    });

    // 执行上传
    const result = await upload.done();

    if (!result || !result.Key) {
      throw new Error('Upload failed: No key returned from S3');
    }

    return result.Key;
  } catch (error) {
    console.error('Error uploading file to Cloudflare R2:', error);
    throw error;
  }
}