import { BadRequestException, Req } from '@nestjs/common';
import { Controller } from '@nestjs/common';
import { PermissionService } from './permission.service';

@Controller('api/permissions')
export class PermissionController {
    constructor(private readonly service: PermissionService) { }
    async getMany(@Req() req) {
        if (!req.query.tenantId) throw new BadRequestException('tenantId required');
        // PermissionService 暂无 find 方法，返回空数组或自定义实现
        return [];
    }
} 