const { Client } = require('pg');

const client = new Client({
    host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
    port: 5432,
    user: 'yeeu',
    password: 'Yy&********',
    database: 'ad_auto',
});

const joinPairs = [
    { left: { table: 'ad_set', column: 'adAccountId' }, right: { table: 'ad_account', column: 'accountId' } },
    { left: { table: 'ad_set', column: 'adCampaignId' }, right: { table: 'campaign', column: 'id' } },
    { left: { table: 'ad_set', column: 'tenantId' }, right: { table: 'tenant', column: 'id' } },
    { left: { table: 'ad_set', column: 'audienceId' }, right: { table: 'audience', column: 'id' } },
];

async function main() {
    await client.connect();

    // 查询所有相关字段类型
    const tables = ['ad_set', 'ad_account', 'campaign', 'tenant', 'audience'];
    const result = await client.query(`
    SELECT table_name, column_name, data_type
    FROM information_schema.columns
    WHERE table_name = ANY($1)
    ORDER BY table_name, column_name
  `, [tables]);

    // 构建类型映射
    const typeMap = {};
    result.rows.forEach(row => {
        if (!typeMap[row.table_name]) typeMap[row.table_name] = {};
        typeMap[row.table_name][row.column_name] = row.data_type;
    });

    // 比对 join 字段类型
    console.log('ad_set join 相关字段类型比对:');
    joinPairs.forEach(pair => {
        const leftType = typeMap[pair.left.table]?.[pair.left.column];
        const rightType = typeMap[pair.right.table]?.[pair.right.column];
        const ok = leftType === rightType ? '✅一致' : '❌不一致';
        console.log(
            `${pair.left.table}.${pair.left.column} (${leftType}) <-> ${pair.right.table}.${pair.right.column} (${rightType}) => ${ok}`
        );
    });

    await client.end();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 