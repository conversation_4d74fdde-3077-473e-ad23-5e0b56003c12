import { v4 as uuidv4 } from 'uuid';
import { AppDataSource } from '../src/config/data-source';
import { Audience } from '../src/modules/ad-platform/entities/audience.entity';

async function fixAudienceIds() {
    await AppDataSource.initialize();
    const repo = AppDataSource.getRepository(Audience);

    // 1. 为 audienceId 为空或 NULL 的行补 uuid
    const empties = await repo.createQueryBuilder('a')
        .where('a.audienceId IS NULL OR a.audienceId = :empty', { empty: '' })
        .getMany();
    for (const row of empties) {
        row.audienceId = uuidv4();
        await repo.save(row);
        console.log(`Fixed empty audienceId for id: ${row.id}`);
    }

    // 2. 为重复 audienceId 的行补 uuid（只保留每组的第一条）
    const duplicates = await repo.createQueryBuilder('a')
        .select('a.audienceId')
        .addSelect('COUNT(*)', 'cnt')
        .where('a.audienceId IS NOT NULL AND a.audienceId <> :empty', { empty: '' })
        .groupBy('a.audienceId')
        .having('COUNT(*) > 1')
        .getRawMany();

    for (const dup of duplicates) {
        const { audienceId } = dup;
        const rows = await repo.createQueryBuilder('a')
            .where('a.audienceId = :audienceId', { audienceId })
            .orderBy('a.id', 'ASC')
            .getMany();
        // 保留第一条，其余补 uuid
        for (const row of rows.slice(1)) {
            row.audienceId = uuidv4();
            await repo.save(row);
            console.log(`Fixed duplicate audienceId for id: ${row.id}`);
        }
    }

    await AppDataSource.destroy();
    console.log('All audienceId values are now unique and non-empty.');
}

fixAudienceIds().catch(e => {
    console.error(e);
    process.exit(1);
}); 