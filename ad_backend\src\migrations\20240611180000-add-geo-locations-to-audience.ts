import { MigrationInterface, QueryRunner } from "typeorm";

export class AddGeoLocationsToAudience20240611180000 implements MigrationInterface {
    name = 'AddGeoLocationsToAudience20240611180000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "geo_locations" jsonb`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "excluded_geo_locations" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "geo_locations"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "excluded_geo_locations"`);
    }
} 