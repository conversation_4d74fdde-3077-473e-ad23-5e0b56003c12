# 组件路径 /pages/management/domain/anti-block/index

## 前端

- **实际路径**：slash-admin/src/pages/management/domain/anti-block/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，防封域名管理表单与列表，含增删查、分页、搜索、弹窗等）
- **主要依赖API定义**：@/api/antiBlock

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取域名列表    | query    | GET_ANTIBLOCKS         | paging, filter, sorting | id, url, status, relatedDomain, createdAt, ... | 支持分页、搜索、过滤 |
| 新增域名        | mutation | CREATE_MANY_ANTIBLOCKS_MUTATION | input: antiBlocks[] | id, url, ... | 批量新增域名 |
| 删除域名        | mutation | DELETE_ANTI_BLOCKS     | input: {id}  | id       | 删除指定域名 |

#### 主要参数结构说明

- **antiBlockType**（用于新增/展示）  
  ```ts
  {
    id: string; // 域名ID
    url: string; // 域名地址
    status: '正常' | '异常' | '待审核'; // 状态
    relatedDomain: string; // 归属主域名
    createdAt: string; // 创建时间
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名         | 类型      | 说明                       |
  |----------------|-----------|----------------------------|
  | id             | string    | 主键，UUID                 |
  | url            | string    | 域名地址                   |
  | status         | string    | 状态（正常/异常/待审核）   |
  | relatedDomain  | string    | 归属主域名                 |
  | createdAt      | Date      | 创建时间                   |

### 前端表单/组件说明

- 支持域名的批量新增、删除，表单字段与上方antiBlockType结构一致
- 支持分页、模糊搜索、过滤
- 支持状态、归属主域名等字段展示
- 域名格式校验，主域名自动提取

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（anti_block，需结合实际后端实现）：

| 字段名         | 类型         | 说明                       | 约束         |
|----------------|-------------|----------------------------|--------------|
| id             | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| url            | varchar     | 域名地址                   | NOT NULL, UNIQUE |
| status         | varchar     | 状态（正常/异常/待审核）   | 默认正常     |
| relatedDomain  | varchar     | 归属主域名                 | 可空         |
| createdAt      | timestamp   | 创建时间                   | 默认now()    |
| updatedAt      | timestamp   | 更新时间                   | 自动生成     |
| ...            | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query GET_ANTIBLOCKS / mutation CREATE_MANY_ANTIBLOCKS_MUTATION / ...
    后端->>数据库: 操作 anti_block 表
    数据库-->>后端: 返回域名数据
    后端-->>前端: 返回域名数据
```

---

## 变更历史/注意事项

- 域名地址需唯一，所有操作需校验唯一性
- 支持批量新增，建议用事务处理
- 删除域名时需校验是否被业务引用，防止误删
- 状态字段建议用枚举类型，便于扩展
- 复杂业务字段建议在表结构中用jsonb扩展