import { gql, useQuery, useMutation } from '@apollo/client';

export const GET_AD_SET_LIST = gql`
  query GetAdSets($filter: AdSetFilterInput) {
    adSetsCustom(filter: $filter) {
      id
      name
      status
      adAccount { accountId account }
      adCampaign { id name }
      tenant { id }
      audience { id name }
      dailyBudget
      startTime
      endTime
      optimizationGoal
      billingEvent
    }
  }
`;

export const CREATE_AD_SET = gql`
  mutation CreateAdSet(
    $name: String!
    $campaignId: ID!
    $audienceId: ID!
    $dailyBudget: Float!
    $startTime: String!
    $endTime: String
    $optimizationGoal: String!
    $billingEvent: String!
    $status: String!
    $bid_amount: Float
    $promoted_object: JSON
    $device_platforms: [String!]
    $user_os: [String!]
  ) {
    createAdSet(
      name: $name
      campaignId: $campaignId
      audienceId: $audienceId
      dailyBudget: $dailyBudget
      startTime: $startTime
      endTime: $endTime
      optimizationGoal: $optimizationGoal
      billingEvent: $billingEvent
      status: $status
      bid_amount: $bid_amount
      promoted_object: $promoted_object
      device_platforms: $device_platforms
      user_os: $user_os
    ) {
      id
      name
      status
      adCampaign { id name }
      audience { id name }
      dailyBudget
      startTime
      endTime
      optimizationGoal
      billingEvent
    }
  }
`;

export const UPDATE_AD_SET = gql`
  mutation UpdateAdSet($input: UpdateAdSetInput!) {
    updateAdSet(input: $input) {
      id
      name
      status
      adCampaign { id name }
      audience { id name }
      dailyBudget
      startTime
      endTime
      optimizationGoal
      billingEvent
    }
  }
`;

export const DELETE_AD_SET = gql`
  mutation DeleteAdSet($id: ID!) {
    deleteAdSet(id: $id)
  }
`;

export function useCreateAdSet() {
  return useMutation(CREATE_AD_SET);
}

export function useUpdateAdSet() {
  return useMutation(UPDATE_AD_SET);
}

export function useDeleteAdSet() {
  return useMutation(DELETE_AD_SET);
}
