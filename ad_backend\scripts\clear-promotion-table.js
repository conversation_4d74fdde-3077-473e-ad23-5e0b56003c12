// clear-promotion-table.js
const { DataSource } = require('typeorm');

const AppDataSource = new DataSource({
    type: 'postgres',
    host: "pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com",
    port: 5432,
    username: "yeeu",
    password: "Yy&20240505",
    database: "ad_auto",
    synchronize: false,
    logging: true,
    entities: [],
});

async function clearPromotionTable() {
    await AppDataSource.initialize();
    await AppDataSource.query('TRUNCATE TABLE "promotion" RESTART IDENTITY CASCADE;');
    console.log('promotion 表已清空');
    await AppDataSource.destroy();
}

clearPromotionTable().catch(err => {
    console.error('清空 promotion 表失败:', err);
    process.exit(1);
});