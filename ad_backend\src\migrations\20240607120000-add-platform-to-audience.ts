import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPlatformToAudience20240607120000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "platform" text[];`);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "platform";`);
    }
} 