import { useTranslation } from 'react-i18next';
import { VCCCard, TransactionRecord, WarningSettings } from './types';

// 模拟VCC卡片数据
export const mockVCCCards: VCCCard[] = [
  {
    id: "1",
    cardNo: "**** **** **** 4242",
    balance: 5000,
    status: "active",
    tags: ["pages.key_774", "Facebook"],
    bindAccount: "FB广告账户001",
    createTime: "2023-01-01",
    lastUsed: "2023-05-01",
    warningThreshold: 1000
  },
  {
    id: "2",
    cardNo: "**** **** **** 5678",
    balance: 800,
    status: "active",
    tags: ["pages.key_774", "Google"],
    bindAccount: "Google广告账户002",
    createTime: "2023-02-15",
    lastUsed: "2023-05-10",
    warningThreshold: 500
  },
  {
    id: "3",
    cardNo: "**** **** **** 9012",
    balance: 200,
    status: "warning",
    tags: ["pages.key_774", "TikTok"],
    bindAccount: "TikTok广告账户003",
    createTime: "2023-03-20",
    lastUsed: "2023-05-15",
    warningThreshold: 300
  },
  {
    id: "4",
    cardNo: "**** **** **** 3456",
    balance: 0,
    status: "frozen",
    tags: ["pages.key_774", "Twitter"],
    bindAccount: "Twitter广告账户004",
    createTime: "2023-04-10",
    lastUsed: "2023-04-30",
    warningThreshold: 200
  }
];

// 模拟交易记录数据
export const mockTransactions: TransactionRecord[] = [
  {
    id: "1",
    cardId: "1",
    cardNo: "**** **** **** 4242",
    amount: 150.75,
    merchant: "Facebook Inc.",
    transactionTime: "2023-05-01 14:30:25",
    status: "success",
    type: "payment"
  },
  {
    id: "2",
    cardId: "2",
    cardNo: "**** **** **** 5678",
    amount: 200.00,
    merchant: "Google Ads",
    transactionTime: "2023-05-10 09:15:30",
    status: "success",
    type: "payment"
  },
  {
    id: "3",
    cardId: "3",
    cardNo: "**** **** **** 9012",
    amount: 100.50,
    merchant: "TikTok Ads",
    transactionTime: "2023-05-15 16:45:10",
    status: "success",
    type: "payment"
  },
  {
    id: "4",
    cardId: "3",
    cardNo: "**** **** **** 9012",
    amount: 50.25,
    merchant: "TikTok Ads",
    transactionTime: "2023-05-16 11:20:45",
    status: "pending",
    type: "payment"
  }
];

// 默认预警设置
export const defaultWarningSettings: WarningSettings = {
  enabled: true,
  threshold: 500,
  notifyEmail: true,
  notifySMS: false,
  notifyPush: true,
  emails: ["<EMAIL>"],
  phones: []
};
