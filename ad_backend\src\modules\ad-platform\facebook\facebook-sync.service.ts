import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdAccount } from '../entities/ad-account.entity';
import { AdCampaign } from '../entities/ad-campaign.entity';
import { AdCreative } from '../entities/ad-creative.entity';
import { Ad } from '../entities/ad.entity';
import { AdSet } from '../entities/adset.entity';
import { Pixel } from '../entities/pixel.entity';
import { Audience } from '../entities/audience.entity';
import { Tenant } from '../../../entity/tenant.entity';

@Injectable()
export class FacebookSyncService {
  constructor(
    @InjectRepository(AdAccount) private adAccountRepo: Repository<AdAccount>,
    @InjectRepository(AdCampaign) private adCampaignRepo: Repository<AdCampaign>,
    @InjectRepository(AdCreative) private adCreativeRepo: Repository<AdCreative>,
    @InjectRepository(Ad) private adRepo: Repository<Ad>,
    @InjectRepository(AdSet) private adSetRepo: Repository<AdSet>,
    @InjectRepository(Pixel) private pixelRepo: Repository<Pixel>,
    @InjectRepository(Audience) private audienceRepo: Repository<Audience>,
    @InjectRepository(Tenant) private tenantRepo: Repository<Tenant>,
  ) { }

  // 示例：同步广告组 AdSet
  async syncAdSets({ adsets, tenant, adAccount, campaignMap }: { adsets: any[]; tenant: Tenant; adAccount: AdAccount; campaignMap: Map<string, AdCampaign> }) {
    let inserted = 0, updated = 0, skipped = 0, errors = [];
    for (const item of adsets) {
      try {
        const adCampaign = campaignMap.get(item.campaign_id) || null;
        const exist = await this.adSetRepo.findOne({ where: { adsetId: item.id, tenant, platform: 'facebook' } });
        if (exist) {
          await this.adSetRepo.save({ ...exist, name: item.name, status: item.status, raw: item, adAccount, adCampaign });
          updated++;
        } else {
          await this.adSetRepo.save(this.adSetRepo.create({
            adsetId: item.id,
            name: item.name,
            platform: 'facebook',
            status: item.status,
            raw: item,
            adAccount,
            adCampaign,
            tenant,
          }));
          inserted++;
        }
      } catch (e) {
        errors.push({ adsetId: item.id, error: e.message });
        skipped++;
      }
    }
    return { inserted, updated, skipped, errors };
  }

  // 同步广告（Ad）
  async syncAds({ ads, tenant, adAccount, campaignMap, adsetMap, creativeMap }: { ads: any[]; tenant: Tenant; adAccount: AdAccount; campaignMap: Map<string, AdCampaign>; adsetMap: Map<string, AdSet>; creativeMap: Map<string, AdCreative> }) {
    // 1. 查出本地所有 Facebook 广告ID
    const localAds = await this.adRepo.find();
    const localAdIdSet = new Set(localAds.map(ad => ad.adId));
    const fbAdIdSet = new Set(ads.map(item => item.id));

    let inserted = 0, updated = 0, deleted = 0;

    // 2. 以 Facebook 返回为准：有的更新/新增
    for (const item of ads) {
      if (!item.id) {
        console.warn('跳过无效广告数据:', item);
        continue;
      }
      const adCampaign = campaignMap.get(item.campaign_id) || null;
      const adSet = adsetMap.get(item.adset_id) || null;
      const adCreative = creativeMap.get(item.creative && item.creative.id ? item.creative.id : '') || null;
      if (!adCampaign) throw new Error('找不到本地 Campaign: ' + item.campaign_id);
      if (!adSet) throw new Error('找不到本地 AdSet: ' + item.adset_id);
      // 只用 adId 判断唯一性
      const exist = await this.adRepo.findOne({ where: { adId: item.id } });
      if (exist) {
        // 已存在，自动更新本地广告
        await this.adRepo.save({
          ...exist,
          name: item.name,
          status: item.status,
          raw: item,
          adAccount,
          adCampaign,
          adCreative,
          tenant,
        });
        updated++;
      } else {
        try {
          console.log('[syncAds] 准备插入广告:', item.id, item.name);
          await this.adRepo.save(this.adRepo.create({
            adId: item.id,
            name: item.name,
            platform: 'facebook',
            status: item.status,
            raw: item,
            adAccount,
            adCampaign,
            adCreative,
            tenant,
          }));
          console.log('[syncAds] 插入完成:', item.id);
        } catch (e) {
          console.error('[syncAds] 插入广告失败:', item.id, e);
        }
        inserted++;
      }
    }

    // 3. 本地多余的（不在 Facebook 返回列表里的）要删除
    // for (const ad of localAds) {
    //   if (!fbAdIdSet.has(ad.adId)) {
    //     await this.adRepo.remove(ad);
    //     deleted++;
    //   }
    // }

    return { inserted, updated, deleted };
  }

  // 同步广告创意（AdCreative）
  async syncAdCreatives({ creatives, tenant, adAccount }: { creatives: any[]; tenant: Tenant; adAccount: AdAccount }) {
    let inserted = 0, updated = 0, skipped = 0, errors = [];
    for (const item of creatives) {
      try {
        const exist = await this.adCreativeRepo.findOne({ where: { creativeId: item.id, tenant, platform: 'facebook' } });
        if (exist) {
          await this.adCreativeRepo.save({ ...exist, name: item.name, status: item.status, raw: item, adAccount });
          updated++;
        } else {
          await this.adCreativeRepo.save(this.adCreativeRepo.create({
            creativeId: item.id,
            name: item.name,
            platform: 'facebook',
            status: item.status,
            raw: item,
            adAccount,
            tenant,
          }));
          inserted++;
        }
      } catch (e) {
        errors.push({ creativeId: item.id, error: e.message });
        skipped++;
      }
    }
    return { inserted, updated, skipped, errors };
  }

  // 同步像素（Pixel）
  async syncPixels({ pixels, tenant, adAccount }: { pixels: any[]; tenant: Tenant; adAccount: AdAccount }) {
    let inserted = 0, updated = 0, skipped = 0, errors = [];
    for (const item of pixels) {
      try {
        const exist = await this.pixelRepo.findOne({ where: { pixelId: item.id, tenant } });
        if (exist) {
          await this.pixelRepo.save({ ...exist, name: item.name, status: item.code ? 'active' : 'inactive', raw: item, adAccount });
          updated++;
        } else {
          await this.pixelRepo.save(this.pixelRepo.create({
            pixelId: item.id,
            name: item.name,
            status: item.code ? 'active' : 'inactive',
            raw: item,
            adAccount,
            tenant,
          }));
          inserted++;
        }
      } catch (e) {
        errors.push({ pixelId: item.id, error: e.message });
        skipped++;
      }
    }
    return { inserted, updated, skipped, errors };
  }

  // 同步受众（Audience）
  async syncAudiences({ audiences, tenant }: { audiences: any[]; tenant: Tenant }) {
    let inserted = 0, updated = 0, skipped = 0, errors = [];
    for (const item of audiences) {
      try {
        // 用 name + tenant 作为唯一性查找（如有更合适唯一标识可补充）
        const exist = await this.audienceRepo.findOne({ where: { name: item.name, tenant: { id: tenant.id } } });
        // 只同步必填字段
        const base = {
          name: item.name,
          geo_locations: item.geo_locations,
          locales: item.locales,
          tenant,
        };
        // 其余字段有值才同步
        if (item.excluded_geo_locations && item.excluded_geo_locations.countries && item.excluded_geo_locations.countries.length > 0) {
          base['excluded_geo_locations'] = item.excluded_geo_locations;
        }
        if (typeof item.gender === 'number') base['gender'] = item.gender;
        if (typeof item.age_min === 'number') base['age_min'] = item.age_min;
        if (typeof item.age_max === 'number') base['age_max'] = item.age_max;
        if (Array.isArray(item.interests) && item.interests.length > 0) base['interests'] = item.interests;
        if (Array.isArray(item.behaviors) && item.behaviors.length > 0) base['behaviors'] = item.behaviors;
        if (Array.isArray(item.platform) && item.platform.length > 0) base['platform'] = item.platform;
        if (item.notes && typeof item.notes === 'string' && item.notes.trim()) base['notes'] = item.notes.trim();
        if (exist) {
          await this.audienceRepo.save({ ...exist, ...base });
          updated++;
        } else {
          await this.audienceRepo.save(this.audienceRepo.create(base));
          inserted++;
        }
      } catch (e) {
        errors.push({ name: item.name, error: e.message });
        skipped++;
      }
    }
    return { inserted, updated, skipped, errors };
  }

  // 同步广告系列（Campaign）
  async syncCampaigns({ campaigns, tenant, adAccount }: { campaigns: any[]; tenant: Tenant; adAccount: AdAccount }) {
    let inserted = 0, updated = 0, skipped = 0, errors = [];
    for (const item of campaigns) {
      try {
        const exist = await this.adCampaignRepo.findOne({ where: { campaignId: item.id, tenant, platform: 'facebook' } });
        if (exist) {
          await this.adCampaignRepo.save({ ...exist, name: item.name, status: item.status, raw: item, adAccount });
          updated++;
        } else {
          await this.adCampaignRepo.save(this.adCampaignRepo.create({
            campaignId: item.id,
            name: item.name,
            platform: 'facebook',
            status: item.status,
            raw: item,
            adAccount,
            tenant,
          }));
          inserted++;
        }
      } catch (e) {
        errors.push({ campaignId: item.id, error: e.message });
        skipped++;
      }
    }
    return { inserted, updated, skipped, errors };
  }

  // 同步广告账户（AdAccount）
  async syncAccounts({ accounts, tenant }: { accounts: any[]; tenant: Tenant }) {
    // 配额校验：广告账户数
    if (tenant && tenant.features && tenant.features.maxAccounts) {
      const accountCount = await this.adAccountRepo.count({ where: { tenant } });
      if (accountCount + accounts.length > tenant.features.maxAccounts) {
        throw new Error('当前套餐广告账户数已达上限，请升级套餐');
      }
    }
    let inserted = 0, updated = 0, skipped = 0, errors = [];
    for (const item of accounts) {
      try {
        const exist = await this.adAccountRepo.findOne({ where: { accountId: item.id, tenant, platform: 'facebook' } });
        if (exist) {
          await this.adAccountRepo.save({ ...exist, account: item.name, status: item.account_status, raw: item });
          updated++;
        } else {
          await this.adAccountRepo.save(this.adAccountRepo.create({
            accountId: item.id,
            account: item.name,
            platform: 'facebook',
            status: item.account_status,
            raw: item,
            tenant,
          }));
          inserted++;
        }
      } catch (e) {
        errors.push({ accountId: item.id, error: e.message });
        skipped++;
      }
    }
    return { inserted, updated, skipped, errors };
  }
}
