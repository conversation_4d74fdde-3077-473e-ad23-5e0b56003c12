import { useTranslation } from 'react-i18next';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/ui/card";
import { useEffect, useState } from "react";
import AdPerformanceAnalysis from "./ad-performance-analysis";
import AdTrafficAnalysis from "./ad-traffic-analysis";
import CreativePerformance from "./creative-performance";
import ChannelAnalysis from "./channel-analysis";
import { AnalysisCard } from "./analysis-card-new";
import analysisService from "@/api/services/analysisService";

interface MetricData {
	value: number;
	percentage: number;
}

interface OverviewData {
	impressions: MetricData;
	clicks: MetricData;
	conversions: MetricData;
	spend: MetricData;
}

interface ApiResponse {
	success: boolean;
	status: number;
	data: OverviewData;
}

function Analysis() {
	const { t } = useTranslation();
	const [overviewData, setOverviewData] = useState<OverviewData | null>(null);
	const [adPerformanceAnalysis, setAdPerformanceAnalysis] = useState<any>(null);
	const [adTrafficAnalysis, setAdTrafficAnalysis] = useState<any>(null);
	const [creativePerformance, setCreativePerformance] = useState<any>(null);
	const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('day');
	const [adTrafficTimeRange, setAdTrafficTimeRange] = useState<'day' | 'week' | 'month'>('day');
	const [channelTimeRange, setChannelTimeRange] = useState<'week' | 'month' | 'quarter'>('month');
	const [loading, setLoading] = useState(true);

	// 初始化数据 - 只获取不依赖时间范围的数据
	const initData = async () => {
		try {
			setLoading(true);
			const [overviewCards, adPerformanceAnalysis, adTrafficAnalysis, creativePerformance] = await Promise.all([
				analysisService.getOverviewCards(),
				analysisService.getAdPerformanceAnalysis(timeRange),
				analysisService.getAdTrafficAnalysis(adTrafficTimeRange),
				analysisService.getCreativePerformance()
			]);
			setOverviewData(overviewCards as any);
			setAdPerformanceAnalysis(adPerformanceAnalysis as any);
			setAdTrafficAnalysis(adTrafficAnalysis as any);
			setCreativePerformance(creativePerformance as any);
		} catch (error) {
			console.error('获取数据失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 广告效果分析数据获取
	const fetchAdPerformanceData = async (newTimeRange: 'day' | 'week' | 'month') => {
		try {
			const data = await analysisService.getAdPerformanceAnalysis(newTimeRange);
			setAdPerformanceAnalysis(data as any);
		} catch (error) {
			console.error('获取广告效果分析数据失败:', error);
		}
	};

	// 广告跑量分析数据获取
	const fetchAdTrafficData = async (newTimeRange: 'day' | 'week' | 'month') => {
		try {
			const data = await analysisService.getAdTrafficAnalysis(newTimeRange);
			setAdTrafficAnalysis(data as any);
		} catch (error) {
			console.error('获取广告跑量分析数据失败:', error);
		}
	};

	// 处理广告效果分析时间范围变化
	const handleAdPerformanceTimeRangeChange = (newTimeRange: 'day' | 'week' | 'month') => {
		setTimeRange(newTimeRange);
		fetchAdPerformanceData(newTimeRange);
	};

	// 处理广告跑量分析时间范围变化
	const handleAdTrafficTimeRangeChange = (newTimeRange: 'day' | 'week' | 'month') => {
		setAdTrafficTimeRange(newTimeRange);
		fetchAdTrafficData(newTimeRange);
	};

	// 处理渠道分析时间范围变化
	const handleChannelTimeRangeChange = (newTimeRange: 'week' | 'month' | 'quarter') => {
		setChannelTimeRange(newTimeRange);
	};

	useEffect(() => {
		initData();
	}, []);

	// 格式化数值显示
	const formatValue = (value: number, type: 'number' | 'currency' = 'number') => {
		if (type === 'currency') {
			return `¥${value.toLocaleString()}`;
		}
		return value.toLocaleString();
	};

	return (
		<div className="space-y-8">
			<div className="flex items-center">
				<h2 className="text-3xl font-bold tracking-tight">{t('pages.key_795')}</h2>
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
				{loading ? (
					// 加载状态
					Array.from({ length: 4 }).map((_, index) => (
						<Card key={index}>
							<CardContent className="p-6">
								<div className="animate-pulse">
									<div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
									<div className="h-8 bg-gray-200 rounded w-3/4"></div>
								</div>
							</CardContent>
						</Card>
					))
				) : overviewData ? (
					// 数据加载完成
					<>
						<AnalysisCard
							title={t('pages.key_915')}
							value={formatValue(overviewData.impressions.value)}
							percentage={Number(overviewData.impressions.percentage.toFixed(1))}
							color="bg-[#C7B6EC]/20 text-[#7C5DEC]"
							icon="solar:eye-bold-duotone"
						/>
						<AnalysisCard
							title={t('pages.key_920')}
							value={formatValue(overviewData.clicks.value)}
							percentage={Number(overviewData.clicks.percentage.toFixed(1))}
							color="bg-[#95DAC1]/20 text-[#01B574]"
							icon="solar:cursor-bold-duotone"
						/>
						<AnalysisCard
							title={t('pages.key_926')}
							value={formatValue(overviewData.conversions.value)}
							percentage={Number(overviewData.conversions.percentage.toFixed(1))}
							color="bg-[#FFDA7A]/20 text-[#FFDA7A]"
							icon="solar:flag-bold-duotone"
						/>
						<AnalysisCard
							title={t('pages.key_917')}
							value={formatValue(overviewData.spend.value, 'currency')}
							percentage={Number(overviewData.spend.percentage.toFixed(1))}
							color="bg-[#FE938C]/20 text-[#FE938C]"
							icon="solar:dollar-minimalistic-bold-duotone"
						/>
					</>
				) : (
					// 错误状态
					<div className="col-span-4 text-center text-gray-500">{t('pages.key_1095')}</div>
				)}
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-1 lg:grid-cols-1">
				{adPerformanceAnalysis && (
					<AdPerformanceAnalysis
						data={adPerformanceAnalysis}
						timeRange={timeRange}
						onTimeRangeChange={handleAdPerformanceTimeRangeChange}
					/>
				)}
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-1 lg:grid-cols-1">
				{adTrafficAnalysis && (
					<AdTrafficAnalysis
						data={adTrafficAnalysis}
						timeRange={adTrafficTimeRange}
						onTimeRangeChange={handleAdTrafficTimeRangeChange}
					/>
				)}
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-1 lg:grid-cols-1">
				{creativePerformance && <CreativePerformance data={creativePerformance} />}
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-1 lg:grid-cols-1">
				<ChannelAnalysis
					timeRange={channelTimeRange}
					onTimeRangeChange={handleChannelTimeRangeChange}
				/>
			</div>
		</div>
	);
}

export default Analysis;
