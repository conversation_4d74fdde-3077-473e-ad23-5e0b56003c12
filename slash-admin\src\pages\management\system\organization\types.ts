// VCC卡片类型定义
export interface VCCCard {
  id: string;
  cardNo: string;       // 卡号（模糊处理）
  balance: number;      // 余额
  status: string;       // 状态：正常、冻结、失效等
  tags: string[];       // 标签
  bindAccount: string;  // 绑定账户
  createTime: string;   // 创建时间
  lastUsed: string;     // 最后使用时间
  warningThreshold: number; // 余额预警阈值
}

// 交易记录类型 - 与后端API完全对应
export interface TransactionRecord {
  id: string;
  cardId: string;
  cardNo: string;
  amount: number;
  merchant: string;
  transactionTime: string;
  status: 'success' | 'pending' | 'failed';
  type: 'payment' | 'deposit' | 'refund';
  facebookAccountId?: string;
  facebookTransactionId?: string;
  campaignName?: string;
  description?: string;
}

// 交易过滤条件
export interface TransactionFilter {
  cardId?: string;
  cardNo?: string;
  merchant?: string;
  status?: 'success' | 'pending' | 'failed';
  type?: 'payment' | 'deposit' | 'refund';
  startTime?: string;
  endTime?: string;
  minAmount?: number;
  maxAmount?: number;
}

// 分页参数
export interface TransactionPagination {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// 分页结果
export interface PaginatedTransactionResult {
  data: TransactionRecord[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 交易统计
export interface TransactionStats {
  totalCount: number;
  totalAmount: number;
  averageAmount: number;
  successCount: number;
  pendingCount: number;
  failedCount: number;
  successRate: number;
}

// 预警设置类型
export interface WarningSettings {
  enabled: boolean;
  threshold: number;
  notifyEmail: boolean;
  notifySMS: boolean;
  notifyPush: boolean;
  emails: string[];
  phones: string[];
}

// 搜索表单类型
export interface SearchFormType {
  cardNo?: string;
  status?: string;
  bindAccount?: string;
  tag?: string;
}

// VCC模态框属性
export interface VCCModalProps {
  open: boolean;
  title: string;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: any;
}
