import { gql } from '@apollo/client';

// ===== Fragment - 交易记录基础字段 =====
export const VCC_TRANSACTION_FRAGMENT = gql`
    fragment VccTransactionFragment on VccTransactionDTO {
        id
        cardId
        cardNo
        amount
        merchant
        transactionTime
        status
        type
        facebookAccountId
        facebookTransactionId
        campaignName
        description
    }
`;

// ===== Queries - 查询操作 =====

/**
 * 查询交易记录列表（分页，过滤）- 对应前端TransactionHistoryTab
 */
export const GET_VCC_TRANSACTIONS = gql`
    ${VCC_TRANSACTION_FRAGMENT}
    query GetVccTransactions(
        $filter: VccTransactionFilterInputDTO
        $pagination: VccTransactionPaginationInputDTO
    ) {
        vccTransactions(filter: $filter, pagination: $pagination) {
            data {
                ...VccTransactionFragment
            }
            total
            page
            limit
            totalPages
        }
    }
`;

/**
 * 查询单个交易记录详情 - 对应前端交易详情Modal
 */
export const GET_VCC_TRANSACTION = gql`
    ${VCC_TRANSACTION_FRAGMENT}
    query GetVccTransaction($id: ID!) {
        vccTransaction(id: $id) {
            ...VccTransactionFragment
        }
    }
`;

/**
 * 获取交易统计数据 - 用于前端Dashboard
 */
export const GET_VCC_TRANSACTION_STATS = gql`
    query GetVccTransactionStats($filter: VccTransactionFilterInputDTO) {
        vccTransactionStats(filter: $filter) {
            totalCount
            totalAmount
            averageAmount
            successCount
            pendingCount
            failedCount
            successRate
        }
    }
`;

/**
 * 导出交易记录 - 对应前端导出按钮
 */
export const EXPORT_VCC_TRANSACTIONS = gql`
    query ExportVccTransactions(
        $filter: VccTransactionFilterInputDTO
        $format: String = "csv"
    ) {
        exportVccTransactions(filter: $filter, format: $format)
    }
`;

// ===== Mutations - 变更操作 =====

/**
 * 创建交易记录 - 手动添加交易记录
 */
export const CREATE_VCC_TRANSACTION = gql`
    ${VCC_TRANSACTION_FRAGMENT}
    mutation CreateVccTransaction($input: CreateVccTransactionInputDTO!) {
        createVccTransaction(input: $input) {
            ...VccTransactionFragment
        }
    }
`;

/**
 * 更新交易记录
 */
export const UPDATE_VCC_TRANSACTION = gql`
    ${VCC_TRANSACTION_FRAGMENT}
    mutation UpdateVccTransaction($id: ID!, $input: UpdateVccTransactionInputDTO!) {
        updateVccTransaction(id: $id, input: $input) {
            ...VccTransactionFragment
        }
    }
`;

/**
 * 删除交易记录
 */
export const DELETE_VCC_TRANSACTION = gql`
    mutation DeleteVccTransaction($id: ID!) {
        deleteVccTransaction(id: $id)
    }
`;

/**
 * 🚀 从Facebook同步交易记录 - 核心功能
 */
export const SYNC_VCC_TRANSACTIONS_FROM_FACEBOOK = gql`
    ${VCC_TRANSACTION_FRAGMENT}
    mutation SyncVccTransactionsFromFacebook($input: SyncFacebookTransactionsInputDTO!) {
        syncVccTransactionsFromFacebook(input: $input) {
            ...VccTransactionFragment
        }
    }
`;

/**
 * 手动添加充值记录 - 快捷操作
 */
export const ADD_VCC_RECHARGE_TRANSACTION = gql`
    ${VCC_TRANSACTION_FRAGMENT}
    mutation AddVccRechargeTransaction(
        $cardId: ID!
        $amount: Float!
        $description: String
    ) {
        addVccRechargeTransaction(
            cardId: $cardId
            amount: $amount
            description: $description
        ) {
            ...VccTransactionFragment
        }
    }
`;

// ===== TypeScript 类型定义 - 与后端完全对应 =====

export interface VccTransactionRecord {
    id: string;
    cardId: string;
    cardNo: string;
    amount: number;
    merchant: string;
    transactionTime: string;
    status: 'success' | 'pending' | 'failed';
    type: 'payment' | 'deposit' | 'refund';
    facebookAccountId?: string;
    facebookTransactionId?: string;
    campaignName?: string;
    description?: string;
}

export interface VccTransactionFilter {
    cardId?: string;
    cardNo?: string;
    merchant?: string;
    status?: 'success' | 'pending' | 'failed';
    type?: 'payment' | 'deposit' | 'refund';
    startTime?: string;
    endTime?: string;
    minAmount?: number;
    maxAmount?: number;
}

export interface VccTransactionPagination {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedVccTransactionResult {
    data: VccTransactionRecord[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

export interface VccTransactionStats {
    totalCount: number;
    totalAmount: number;
    averageAmount: number;
    successCount: number;
    pendingCount: number;
    failedCount: number;
    successRate: number;
}

export interface CreateVccTransactionInput {
    cardId: string;
    amount: number;
    merchant: string;
    transactionTime: string;
    status: 'success' | 'pending' | 'failed';
    type: 'payment' | 'deposit' | 'refund';
    facebookAccountId?: string;
    facebookTransactionId?: string;
    campaignName?: string;
    description?: string;
}

export interface UpdateVccTransactionInput {
    status?: 'success' | 'pending' | 'failed';
    description?: string;
}

export interface SyncFacebookTransactionsInput {
    cardId: string;
    startDate?: string;
    endDate?: string;
    forceOverwrite?: boolean;
} 