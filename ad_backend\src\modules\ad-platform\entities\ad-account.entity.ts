import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

@ObjectType('AdAccount')
@Entity('ad_account')
export class AdAccount {
  @ApiProperty()
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '平台类型（facebook/google/tiktok/adjust/af等）' })
  @Field()
  @Column({ length: 32 })
  platform: string;

  @ApiProperty({ description: '广告平台账户ID' })
  @Index()
  @Field()
  @Column({ length: 64, unique: true })
  accountId: string;

  @ApiProperty({ description: '账户名称' })
  @Field()
  @Column({ length: 128 })
  account: string;

  // 预留：账号密码批量鉴权和token存储（仅Facebook等平台用到）
  @ApiProperty({ description: '登录账号（如邮箱）', required: false })
  @Field({ nullable: true })
  @Column({ length: 128, nullable: true })
  login?: string;

  @ApiProperty({ description: '登录密码（加密存储）', required: false })
  @Field({ nullable: true })
  @Column({ length: 128, nullable: true })
  password?: string;

  @ApiProperty({ description: '平台鉴权token', required: false })
  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  accessToken?: string;

  @ApiProperty({ description: '租户ID' })
  @Index()
  @Field(() => Tenant)
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始账户数据（JSON）' })
  @Field(() => GraphQLJSON, { nullable: true })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;

  @ApiProperty({ description: '账户状态' })
  @Field()
  @Column({ length: 32, default: 'active' })
  status: string;

  @ApiProperty({ description: '风险等级', required: false })
  @Field({ nullable: true })
  @Column({ length: 32, nullable: true })
  riskLevel?: string;

  @ApiProperty({ description: '分组', required: false })
  @Field({ nullable: true })
  @Column({ length: 64, nullable: true })
  group?: string;

  @ApiProperty({ description: 'OAuth 信息', required: false })
  @Field(() => GraphQLJSON, { nullable: true })
  @Column({ type: 'jsonb', nullable: true })
  oauth?: any;

  @ApiProperty({ description: '创建时间', required: false })
  @Field({ nullable: true })
  @Column({ type: 'timestamp with time zone', nullable: true, default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @ApiProperty({ description: '标签', required: false })
  @Field({ nullable: true })
  @Column({ length: 128, nullable: true })
  tag?: string;

  @ApiProperty({ description: '渠道', required: false })
  @Field({ nullable: true })
  @Column({ length: 128, nullable: true })
  channel?: string;

  @ApiProperty({ description: '持有人', required: false })
  @Field({ nullable: true })
  @Column({ length: 128, nullable: true })
  holder?: string;

  @ApiProperty({ description: '广告号', required: false })
  @Field({ nullable: true })
  @Column({ length: 128, nullable: true })
  adNumber?: string;

  @ApiProperty({ description: '备注', required: false })
  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  remark?: string;

  @ApiProperty({ description: '更新时间', required: false })
  @Field({ nullable: true })
  @UpdateDateColumn({ type: 'timestamp with time zone', nullable: true })
  updatedAt?: Date;

  @ApiProperty({ description: 'Facebook账户状态', required: false })
  @Field({ nullable: true })
  @Column({ length: 32, nullable: true })
  fbStatus?: string;

  // 配额校验由 service 层实现
}
