import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Promotion } from './entities/promotion.entity';
import { CreatePromotionDto, UpdatePromotionDto, PromotionDto, PromotionDetailDto } from './entities/promotion.dto';
import { ContextIdFactory } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { LandingPage } from '../landing-page/landing-page.entity';
import { LandingPageDTO } from '../landing-page/dto/landing-page.dto';

@Injectable()
export class PromotionService {
    constructor(
        @InjectRepository(Promotion)
        private readonly repo: Repository<Promotion>,
        @InjectRepository(LandingPage)
        private readonly landingPageRepo: Repository<LandingPage>,
    ) { }

    async findAll(context: any): Promise<any[]> {
        const tenantId = context?.user?.tenantId || context?.req?.user?.tenantId;
        if (!tenantId) throw new BadRequestException('tenantId is required');
        const promotions = await this.repo.find({
            where: { tenantId },
            order: { createdAt: 'DESC' }
        });
        const landingPages = await this.landingPageRepo.find({
            where: { tenantId }
        });
        const codeNameMap = new Map(landingPages.map(lp => [lp.code, lp.title]));
        return promotions.map(p => ({
            ...p,
            landingPageName: codeNameMap.get(p.landingPageCode) || p.landingPageCode,
        }));
    }

    async findOne(id: string): Promise<any> {
        const entity = await this.repo.findOne({ where: { id } });
        if (!entity) throw new NotFoundException('Promotion not found');
        const lp = await this.landingPageRepo.findOne({ where: { code: entity.landingPageCode } });
        return {
            ...entity,
            landingPageName: lp?.title || entity.landingPageCode,
        };
    }

    async create(dto: CreatePromotionDto, context: any): Promise<Promotion> {
        const entity = this.repo.create(dto);
        const user = context?.user || context?.req?.user;
        entity.creator = user?.username || '系统';
        entity.tenantId = user?.tenantId;
        if (!entity.tenantId) throw new BadRequestException('tenantId is required');
        return this.repo.save(entity);
    }

    async update(id: string, dto: UpdatePromotionDto): Promise<Promotion> {
        const entity = await this.findOne(id);
        Object.assign(entity, dto);
        return this.repo.save(entity);
    }

    async remove(id: string): Promise<void> {
        await this.repo.delete(id);
    }

    async findByCode(code: string, tenantOrContext: string | any): Promise<PromotionDetailDto | null> {
        let tenantId: string | undefined;
        if (typeof tenantOrContext === 'string') {
            tenantId = tenantOrContext;
        } else {
            tenantId = tenantOrContext?.user?.tenantId || tenantOrContext?.req?.user?.tenantId;
        }
        const where: any = { code };
        if (tenantId) where.tenantId = tenantId;
        const promotion = await this.repo.findOne({ where });
        if (!promotion) return null;
        const landingPage = await this.landingPageRepo.findOne({ where: { code: promotion.landingPageCode } });
        return {
            promotion: promotion as any,
            landingPage: landingPage as any,
        };
    }
} 