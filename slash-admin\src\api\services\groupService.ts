import { useQuery, useMutation } from '@apollo/client';
import {
    GET_GROUPS,
    GET_GROUP,
    CREATE_GROUP,
    UPDATE_GROUP,
    DELETE_GROUP,
    GET_GROUPS_AGGREGATE
} from '../group.graphql';

export const useGroups = (variables = {}) => {
    return useQuery(GET_GROUPS, {
        variables,
        fetchPolicy: 'network-only',
    });
};

export const useGroup = (id: string) => {
    return useQuery(GET_GROUP, {
        variables: { id },
        skip: !id,
    });
};

export const useCreateGroup = () => {
    return useMutation(CREATE_GROUP, {
        refetchQueries: [{ query: GET_GROUPS }],
    });
};

export const useUpdateGroup = () => {
    return useMutation(UPDATE_GROUP, {
        refetchQueries: [{ query: GET_GROUPS }],
    });
};

export const useDeleteGroup = () => {
    return useMutation(DELETE_GROUP, {
        refetchQueries: [{ query: GET_GROUPS }],
    });
};

export const useGroupsAggregate = (variables = {}) => {
    return useQuery(GET_GROUPS_AGGREGATE, {
        variables,
        fetchPolicy: 'network-only',
    });
}; 