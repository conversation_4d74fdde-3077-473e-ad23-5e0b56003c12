import { IsOptional, IsString, IsUUID, } from 'class-validator';
import {
	Field,
	ID,
	ObjectType,
	GraphQLISODateTime,
	InputType,
	Int
} from '@nestjs/graphql';
import { FilterableField } from '@ptc-org/nestjs-query-graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@ObjectType('AntiBlock')
@InputType('AntiBlockInput')
export class AntiBlockDTO {
	// @FilterableField(() => ID, { nullable: true })
	// @ApiPropertyOptional({ type: String })
	// @IsOptional()
	// @IsUUID()
	// id?: string;
	@FilterableField(() => Int, { nullable: true })  // 使用 Int 类型
	id?: number;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	userId?: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	url?: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	status?: string;

	@Field({ nullable: true })
	name?: string;

	@Field({ nullable: true })
	relatedDomain?: string;

	@Field({ nullable: true })
	tenantId?: string;

	// When record was created in our DB
	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	createdAt?: Date;

	// When record was updated in our DB
	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	updatedAt?: Date;
}

