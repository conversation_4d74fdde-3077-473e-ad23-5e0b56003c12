import { RoleItem, RoleStatus } from '../types';

// 分页信息
export interface PageInfo {
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
}

// 角色列表响应
export interface RoleListResponse {
    items: RoleItem[];
    pageInfo: PageInfo;
    totalCount: number;
}

// GraphQL查询响应类型
export interface RolesQueryResponse {
    roles: RoleListResponse;
}

export interface RoleQueryResponse {
    role: RoleItem;
}

// GraphQL变更响应类型
export interface CreateRoleResponse {
    createRole: RoleItem;
}

export interface UpdateRoleResponse {
    updateRole: RoleItem;
}

export interface DeleteRoleResponse {
    deleteRole: boolean;
}

// GraphQL查询变量类型
export interface RolesQueryVariables {
    input: {
        page?: number;
        limit?: number;
        search?: string;
        status?: RoleStatus;
    };
}

export interface RoleQueryVariables {
    id: string;
}

// GraphQL变更变量类型
export interface CreateRoleVariables {
    input: {
        name: string;
        description?: string;
        status?: RoleStatus;
        order?: number;
        routeIds?: string[];
    };
}

export interface UpdateRoleVariables {
    input: {
        id: string;
        name?: string;
        description?: string;
        status?: RoleStatus;
        order?: number;
        routeIds?: string[];
    };
}

export interface DeleteRoleVariables {
    input: {
        id: string;
    };
} 