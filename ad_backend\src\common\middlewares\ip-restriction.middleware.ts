/*
 * @Author: 潘孝权
 * @Date: 2025-05-16 23:08:50
 * @Description: 
 * @FilePath: \sking_frontend\ad_backend\src\common\middlewares\ip-restriction.middleware.ts
 */
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ipRestrictionService } from '../../modules/ip-restriction/ipRestriction.service';

@Injectable()
export class IpRestrictionMiddleware implements NestMiddleware {
  constructor(private readonly ipRestrictionService: ipRestrictionService) { }

  async use(req: Request, res: Response, next: NextFunction) {
    // 如果是开发环境，跳过IP验证
    if ( process.env.STAGE === 'dev') {
      return next();
    }

    // 获取客户端IP地址
    // 获取客户端IP地址
    const clientIp = req.ip ||
      req.headers['x-forwarded-for'] as string ||
      req.socket.remoteAddress;
    // 从数据库获取所有启用状态的IP限制记录
    const ipRestrictions = await this.ipRestrictionService.getEnabledIpRestrictions();
    // 检查当前IP是否在限制列表中
    const isRestricted = ipRestrictions.some(restriction => {
      return restriction.ip === clientIp;
    });

    if (isRestricted) {
      // 如果IP被限制，返回一个包含重定向URL的JSON响应
      return res.status(200).json({ redirect: true, url: 'http://www.baidu.com' });
    }

    // 如果IP不在限制列表中，继续处理请求
    next();
  }
}