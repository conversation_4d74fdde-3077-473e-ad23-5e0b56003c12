export interface RouteItem {
    id: string;
    parentId: string | null;
    name: string;
    path: string;
    component?: string;
    icon?: string;
    order: number;
    type: 'menu' | 'page';
    status: 'enabled' | 'disabled';
    isHidden: boolean;
    description?: string;
    createdAt: string;
    updatedAt: string;
}

export interface RouteFormData {
    name: string;
    path: string;
    parentId: string | null;
    component?: string;
    icon?: string;
    order: number;
    type: 'menu' | 'page';
    status: 'enabled' | 'disabled';
    isHidden: boolean;
    description?: string;
}

export interface RouteTreeItem extends RouteItem {
    key: string;
    title: string;
    children?: RouteTreeItem[];
} 