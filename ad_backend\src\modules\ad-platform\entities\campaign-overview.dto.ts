import { ObjectType, Field, Float, Int } from '@nestjs/graphql';

@ObjectType()
export class CampaignOverviewDTO {
    @Field(() => Int)
    activeCount: number;

    @Field(() => Float)
    totalSpend: number;

    @Field(() => Int)
    totalConversions: number;

    @Field(() => Float)
    avgRoi: number;

    @Field(() => [CampaignOverviewItemDTO])
    recentCampaigns: CampaignOverviewItemDTO[];
}

@ObjectType()
export class CampaignOverviewItemDTO {
    @Field()
    name: string;
    @Field()
    status: string;
    @Field(() => Float)
    budget: number;
    @Field(() => Float)
    spent: number;
    @Field(() => Int)
    progress: number;
    @Field(() => Int)
    impressions: number;
    @Field(() => Int)
    clicks: number;
    @Field(() => Int)
    conversions: number;
} 