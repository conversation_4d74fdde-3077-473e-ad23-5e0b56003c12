import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';

@Entity('system_route')
export class Route {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ nullable: true })
    parentId: string | null;

    @Column()
    name: string;

    @Column()
    path: string;

    @Column({ nullable: true })
    component: string;

    @Column({ nullable: true })
    icon: string;

    @Column({ type: 'int' })
    order: number;

    @Column({
        type: 'enum',
        enum: ['menu', 'page'],
        default: 'page'
    })
    type: 'menu' | 'page';

    @Column({
        type: 'enum',
        enum: ['enabled', 'disabled'],
        default: 'enabled'
    })
    status: 'enabled' | 'disabled';

    @Column({ type: 'boolean', default: false })
    isHidden: boolean;

    @Column({ type: 'text', nullable: true })
    description: string;

    @CreateDateColumn({
        type: 'timestamp with time zone',
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        type: 'timestamp with time zone',
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
        onUpdate: 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    // 自引用关系
    @ManyToOne(() => Route, route => route.id, {
        nullable: true,
        onDelete: 'CASCADE'
    })
    @JoinColumn({ name: 'parentId' })
    parent: Route;
} 