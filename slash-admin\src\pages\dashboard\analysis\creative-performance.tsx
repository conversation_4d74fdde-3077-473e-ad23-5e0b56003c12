import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/ui/tabs";
import { useState } from "react";

// 数据类型定义
interface CreativeItem {
  id: number;
  name: string;
  impressions: number;
  clicks: number;
  ctr: number;
  conversions: number;
  cvr: number;
}

interface CreativePerformanceData {
  images: CreativeItem[];
  videos: CreativeItem[];
  carousels: CreativeItem[];
}

interface CreativePerformanceProps {
  data: CreativePerformanceData | null;
}

const CreativePerformance = ({ data }: CreativePerformanceProps) => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState("images");

  // 如果没有数据，显示加载状态
  if (!data) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-base font-medium">{t('pages.key_810')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 获取当前选中的素材数据
  const getCreativeData = () => {
    switch (selectedTab) {
      case "images": return data.images;
      case "videos": return data.videos;
      case "carousels": return data.carousels;
      default: return data.images;
    }
  };

  const currentCreatives = getCreativeData();

  // 计算平均CTR和CVR - 使用正确的公式
  const totalImpressions = currentCreatives.reduce((sum, item) => sum + item.impressions, 0);
  const totalClicks = currentCreatives.reduce((sum, item) => sum + item.clicks, 0);
  const totalConversions = currentCreatives.reduce((sum, item) => sum + item.conversions, 0);

  // 平均点击率 = 总点击量 / 总展示量 × 100%
  const avgCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

  // 平均转化率 = 总转化量 / 总点击量 × 100%
  const avgCVR = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;

  // 素材CTR对比图表配置
  const ctrChartOptions = useChart({
    xaxis: {
      categories: currentCreatives.map(item => item.name),
    },
    yaxis: {
      labels: {
        formatter: (value: number) => `${value.toFixed(2)}%`,
      },
    },
    tooltip: {
      y: {
        formatter: (value: number) => `${value.toFixed(2)}%`,
      },
    },
    colors: ['#3b82f6'],
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: '60%',
        dataLabels: {
          position: 'top',
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(2)}%`,
      offsetY: -20,
      style: {
        fontSize: '12px',
        colors: ['#304758'],
      },
    },
  });

  // 素材CVR对比图表配置
  const cvrChartOptions = useChart({
    xaxis: {
      categories: currentCreatives.map(item => item.name),
    },
    yaxis: {
      labels: {
        formatter: (value: number) => `${value.toFixed(2)}%`,
      },
    },
    tooltip: {
      y: {
        formatter: (value: number) => `${value.toFixed(2)}%`,
      },
    },
    colors: ['#4ade80'],
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: '60%',
        dataLabels: {
          position: 'top',
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(2)}%`,
      offsetY: -20,
      style: {
        fontSize: '12px',
        colors: ['#304758'],
      },
    },
  });

  // 素材表现对比图表配置
  const performanceChartOptions = useChart({
    xaxis: {
      categories: currentCreatives.map(item => item.name),
    },
    yaxis: [
      {
        title: {
          text: t('pages.key_1116'),
        },
        labels: {
          formatter: (value: number) => `${Math.round(value / 1000)}k`,
        },
      },
      {
        opposite: true,
        title: {
          text: '百分比 (%)',
        },
        labels: {
          formatter: (value: number) => `${value.toFixed(2)}%`,
        },
        min: 0,
        max: 10,
      }
    ],
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: (value: number, { seriesIndex }: { seriesIndex: number }) => {
          if (seriesIndex === 0) return `${value.toLocaleString()} 展示`;
          if (seriesIndex === 1) return `${value.toLocaleString()} 点击`;
          if (seriesIndex === 2) return `${value.toFixed(2)}%`;
          return `${value.toFixed(2)}%`;
        },
      },
    },
    colors: ['#94a3b8', '#3b82f6', '#4ade80', '#f59e0b'],
    stroke: {
      width: [0, 0, 3, 3],
      curve: 'smooth',
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
    },
  });

  // 素材表现对比图表数据
  const performanceSeries = [
    {
      name: t('pages.key_725'),
      type: 'column',
      data: currentCreatives.map(item => item.impressions),
    },
    {
      name: t('pages.key_1590'),
      type: 'column',
      data: currentCreatives.map(item => item.clicks),
    },
    {
      name: t('pages.key_1587'),
      type: 'line',
      data: currentCreatives.map(item => item.ctr),
    },
    {
      name: t('pages.key_2359'),
      type: 'line',
      data: currentCreatives.map(item => item.cvr),
    }
  ];

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-base font-medium">{t('pages.key_810')}</CardTitle>
        <div className="flex space-x-2">
          <button
            onClick={() => setSelectedTab('images')}
            className={`px-3 py-1 text-sm rounded-md ${selectedTab === 'images' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >{t('pages.key_532')}</button>
          <button
            onClick={() => setSelectedTab('videos')}
            className={`px-3 py-1 text-sm rounded-md ${selectedTab === 'videos' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >{t('pages.key_2066')}</button>
          <button
            onClick={() => setSelectedTab('carousels')}
            className={`px-3 py-1 text-sm rounded-md ${selectedTab === 'carousels' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >{t('pages.key_2372')}</button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          {/* <TabsList className="mb-4">
            <TabsTrigger value="overview">{t('pages.key_923')}</TabsTrigger>
            <TabsTrigger value="ctr">{t('pages.key_1589')}</TabsTrigger>
            <TabsTrigger value="cvr">{t('pages.key_2361')}</TabsTrigger>
          </TabsList> */}

          {/* <TabsContent value="overview" className="space-y-4"> */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">平均点击率 (CTR)</p>
              <p className="text-2xl font-semibold">{avgCTR.toFixed(2)}%</p>
            </div>

            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">平均转化率 (CVR)</p>
              <p className="text-2xl font-semibold">{avgCVR.toFixed(2)}%</p>
            </div>
          </div>

          <div className="h-[350px]">
            <Chart
              type="line"
              series={performanceSeries}
              options={performanceChartOptions}
              height={350}
            />
          </div>
          {/* </TabsContent> */}

          {/* <TabsContent value="ctr" className="h-[350px]">
            <Chart 
              type="bar" 
              series={[{ name: t('pages.key_1587'), data: currentCreatives.map(item => item.ctr) }]} 
              options={ctrChartOptions} 
              height={350} 
            />
          </TabsContent> */}

          {/* <TabsContent value="cvr" className="h-[350px]">
            <Chart 
              type="bar" 
              series={[{ name: t('pages.key_2359'), data: currentCreatives.map(item => item.cvr) }]} 
              options={cvrChartOptions} 
              height={350} 
            />
          </TabsContent> */}
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CreativePerformance;
