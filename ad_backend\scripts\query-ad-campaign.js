const { Client } = require('pg');

async function main() {
    const client = new Client({
        host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
        port: 5432,
        user: 'yeeu',
        password: 'Yy&20240505',
        database: 'ad_auto',
    });
    await client.connect();

    // 2. 创建唯一索引
    await client.query(`
        CREATE UNIQUE INDEX IF NOT EXISTS uniq_ad_adId
        ON ad ("adId");
    `);
    console.log('唯一索引 uniq_ad_adId 已创建');

    await client.end();
}

main().catch(err => {
    console.error('清理或加唯一索引失败:', err);
    process.exit(1);
});