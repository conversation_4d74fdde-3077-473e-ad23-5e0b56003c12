import { useTranslation } from 'react-i18next';
import AutoOptimization from "./auto-optimization";
import CampaignOverview from "./campaign-overview";
import AdPerformanceMetrics from "./ad-performance-metrics";
import CreativeAnalysis from "./creative-analysis";
import AdGroupsOverview from "./ad-groups-overview";
import BudgetUtilization from "./budget-utilization";
import AudienceInsights from "./audience-insights";
import AdPlatformDistribution from "./ad-platform-distribution";

function Workbench() {
  const { t } = useTranslation();
	// 你可以根据实际需求动态生成日期
	const startDate = "2024-01-01";
	const endDate = "2024-12-31";

	return (
		<div className="space-y-8">
			<div className="flex items-center">
				<h2 className="text-3xl font-bold tracking-tight">{t('pages.key_805')}</h2>
			</div>

			{/* 广告性能指标卡片 */}
			<AdPerformanceMetrics startDate={startDate} endDate={endDate} />

			{/* 广告活动概览和自动优化 */}
			<div className="grid grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3">
				<div className="lg:col-span-2">
					<CampaignOverview startDate={startDate} endDate={endDate} />
				</div>
				<div className="h-full">
					<AutoOptimization type="budget" />
				</div>
			</div>

			{/* 创意分析和预算使用 */}
			<div className="grid grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3">
				<div className="lg:col-span-2">
					<CreativeAnalysis startDate={startDate} endDate={endDate} />
				</div>
				<div className="h-full">
					<BudgetUtilization startDate={startDate} endDate={endDate} />
				</div>
			</div>

			{/* 广告组概览和受众洞察 */}
			<div className="grid grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-2">
				<div className="h-full">
					<AdGroupsOverview startDate={startDate} endDate={endDate} />
				</div>
				<div className="h-full">
					<AudienceInsights startDate={startDate} endDate={endDate} />
				</div>
			</div>

			{/* 广告平台分析 */}
			<div className="grid grid-cols-1">
				<AdPlatformDistribution startDate={startDate} endDate={endDate} />
			</div>
		</div>
	);
}

export default Workbench;
