import { ObjectType, Field, ID, Float, Int, InputType } from '@nestjs/graphql';
import { IsString, IsOptional, IsNumber, IsUUID, IsNotEmpty, MaxLength, Min, Max } from 'class-validator';

@ObjectType('VccCard')
export class VccCardDTO {
    @Field(() => ID)
    id: string;

    @Field({ description: '渠道' })
    channel: string;

    @Field({ description: '国家' })
    country: string;

    @Field({ description: '持卡人' })
    cardHolder: string;

    @Field({ description: '国家代码' })
    countryCode: string;

    @Field({ description: '卡号' })
    cardNumber: string;

    @Field(() => Float, { description: '余额' })
    balance: number;

    @Field(() => Float, { description: '消费' })
    consumption: number;

    @Field({ description: '已绑定广告号', nullable: true })
    boundAdAccount?: string;

    @Field(() => [String], { description: '绑定的广告账户ID数组', nullable: true })
    boundAdAccountIds?: string[];

    @Field({ description: '所属群组', nullable: true })
    group?: string;

    @Field(() => Int, { description: '交易数' })
    transactionCount: number;

    @Field(() => Int, { description: '广告号存活数量' })
    adAccountStatus: number;

    @Field({ description: '过期月' })
    expiryMonth: string;

    @Field({ description: '过期年' })
    expiryYear: string;

    @Field({ description: 'CVV' })
    cvv: string;

    @Field({ description: '邮编' })
    zipCode: string;

    @Field(() => Int, { description: '已使用次数' })
    usedCount: number;

    @Field(() => Int, { description: '绑定次数' })
    bindCount: number;

    @Field(() => Int, { description: '绑定广告号总数' })
    totalAdAccounts: number;

    @Field(() => Int, { description: '限制次数' })
    limitCount: number;

    @Field({ description: '状态' })
    status: string;

    @Field({ description: '备注', nullable: true })
    remark?: string;

    @Field({ description: '创建时间' })
    createdAt: Date;

    @Field({ description: '更新时间' })
    updatedAt: Date;
}

@InputType('CreateVccCardInput')
export class CreateVccCardInputDTO {
    @Field({ description: '渠道' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(50)
    channel: string;

    @Field({ description: '国家' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(50)
    country: string;

    @Field({ description: '持卡人' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(100)
    cardHolder: string;

    @Field({ description: '国家代码' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(10)
    countryCode: string;

    @Field({ description: '卡号' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(20)
    cardNumber: string;

    @Field(() => Float, { description: '余额' })
    @IsNumber()
    @Min(0)
    balance: number;

    @Field(() => Float, { description: '消费', defaultValue: 0 })
    @IsNumber()
    @Min(0)
    @IsOptional()
    consumption?: number;

    @Field({ description: '已绑定广告号', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(200)
    boundAdAccount?: string;

    @Field(() => [String], { description: '绑定的广告账户ID数组', nullable: true })
    @IsUUID(undefined, { each: true })
    @IsOptional()
    boundAdAccountIds?: string[];

    @Field({ description: '所属群组', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(100)
    group?: string;

    @Field(() => Int, { description: '交易数', defaultValue: 0 })
    @IsNumber()
    @Min(0)
    @IsOptional()
    transactionCount?: number;

    @Field(() => Int, { description: '广告号存活数量', defaultValue: 0 })
    @IsNumber()
    @Min(0)
    @IsOptional()
    adAccountStatus?: number;

    @Field({ description: '过期月' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(2)
    expiryMonth: string;

    @Field({ description: '过期年' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(4)
    expiryYear: string;

    @Field({ description: 'CVV' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(4)
    cvv: string;

    @Field({ description: '邮编' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(20)
    zipCode: string;

    @Field(() => Int, { description: '已使用次数', defaultValue: 0 })
    @IsNumber()
    @Min(0)
    @IsOptional()
    usedCount?: number;

    @Field(() => Int, { description: '绑定次数', defaultValue: 0 })
    @IsNumber()
    @Min(0)
    @IsOptional()
    bindCount?: number;

    @Field(() => Int, { description: '绑定广告号总数', defaultValue: 0 })
    @IsNumber()
    @Min(0)
    @IsOptional()
    totalAdAccounts?: number;

    @Field(() => Int, { description: '限制次数', defaultValue: 10 })
    @IsNumber()
    @Min(1)
    @IsOptional()
    limitCount?: number;

    @Field({ description: '状态', defaultValue: '未使用' })
    @IsString()
    @IsOptional()
    @MaxLength(20)
    status?: string;

    @Field({ description: '备注', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(500)
    remark?: string;

    @Field({ description: '租户ID' })
    @IsUUID()
    @IsNotEmpty()
    tenantId: string;
}

@InputType('UpdateVccCardInput')
export class UpdateVccCardInputDTO {
    @Field({ description: '渠道', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(50)
    channel?: string;

    @Field({ description: '国家', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(50)
    country?: string;

    @Field({ description: '持卡人', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(100)
    cardHolder?: string;

    @Field({ description: '国家代码', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(10)
    countryCode?: string;

    @Field(() => Float, { description: '余额', nullable: true })
    @IsNumber()
    @Min(0)
    @IsOptional()
    balance?: number;

    @Field(() => Float, { description: '消费', nullable: true })
    @IsNumber()
    @Min(0)
    @IsOptional()
    consumption?: number;

    @Field({ description: '已绑定广告号', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(200)
    boundAdAccount?: string;

    @Field(() => [String], { description: '绑定的广告账户ID数组', nullable: true })
    @IsUUID(undefined, { each: true })
    @IsOptional()
    boundAdAccountIds?: string[];

    @Field({ description: '所属群组', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(100)
    group?: string;

    @Field(() => Int, { description: '交易数', nullable: true })
    @IsNumber()
    @Min(0)
    @IsOptional()
    transactionCount?: number;

    @Field(() => Int, { description: '广告号存活数量', nullable: true })
    @IsNumber()
    @Min(0)
    @IsOptional()
    adAccountStatus?: number;

    @Field({ description: '过期月', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(2)
    expiryMonth?: string;

    @Field({ description: '过期年', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(4)
    expiryYear?: string;

    @Field({ description: '邮编', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(20)
    zipCode?: string;

    @Field(() => Int, { description: '已使用次数', nullable: true })
    @IsNumber()
    @Min(0)
    @IsOptional()
    usedCount?: number;

    @Field(() => Int, { description: '绑定次数', nullable: true })
    @IsNumber()
    @Min(0)
    @IsOptional()
    bindCount?: number;

    @Field(() => Int, { description: '绑定广告号总数', nullable: true })
    @IsNumber()
    @Min(0)
    @IsOptional()
    totalAdAccounts?: number;

    @Field(() => Int, { description: '限制次数', nullable: true })
    @IsNumber()
    @Min(1)
    @IsOptional()
    limitCount?: number;

    @Field({ description: '状态', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(20)
    status?: string;

    @Field({ description: '备注', nullable: true })
    @IsString()
    @IsOptional()
    @MaxLength(500)
    remark?: string;
}

@InputType('VccCardFilterInput')
export class VccCardFilterInputDTO {
    @Field({ description: '渠道', nullable: true })
    @IsString()
    @IsOptional()
    channel?: string;

    @Field({ description: '国家', nullable: true })
    @IsString()
    @IsOptional()
    country?: string;

    @Field({ description: '持卡人', nullable: true })
    @IsString()
    @IsOptional()
    cardHolder?: string;

    @Field({ description: '卡号', nullable: true })
    @IsString()
    @IsOptional()
    cardNumber?: string;

    @Field({ description: '状态', nullable: true })
    @IsString()
    @IsOptional()
    status?: string;

    @Field({ description: '租户ID', nullable: true })
    @IsUUID()
    @IsOptional()
    tenantId?: string;
}

@InputType('BindAdAccountInput')
export class BindAdAccountInputDTO {
    @Field({ description: 'VCC卡片ID' })
    @IsUUID()
    @IsNotEmpty()
    vccCardId: string;

    @Field(() => [String], { description: '广告账户ID列表' })
    @IsUUID(undefined, { each: true })
    @IsNotEmpty()
    adAccountIds: string[];
} 