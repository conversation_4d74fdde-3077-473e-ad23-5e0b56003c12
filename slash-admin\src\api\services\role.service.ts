import { ApolloClient } from '@apollo/client';
import {
    ROLES_QUERY,
    ROLE_QUERY,
    CREATE_ROLE,
    UPDATE_ROLE,
    DELETE_ROLE
} from '../role.graphql';

import type {
    RolesQueryResponse,
    RoleQueryResponse,
    CreateRoleResponse,
    UpdateRoleResponse,
    DeleteRoleResponse,
    RolesQueryVariables,
    RoleQueryVariables,
    CreateRoleVariables,
    UpdateRoleVariables,
    DeleteRoleVariables
} from '@/pages/management/system/role/graphql/types';

import { CreateRoleParams, RoleItem, RoleQueryParams, RoleStatus, UpdateRoleParams } from '@/pages/management/system/role/types';

export class RoleService {
    constructor(private client: ApolloClient<any>) { }

    /**
     * 获取角色列表
     * @param params 查询参数
     * @returns 角色列表响应
     */
    async getRoles(params: RoleQueryParams = {}) {
        try {
            const { page = 1, limit = 10, search, status } = params;
            const { data, error } = await this.client.query<RolesQueryResponse, RolesQueryVariables>({
                query: ROLES_QUERY,
                variables: {
                    input: {
                        page,
                        limit,
                        search,
                        status
                    }
                },
                fetchPolicy: 'network-only'
            });

            if (error) {
                throw new Error(`获取角色列表失败：${error.message}`);
            }

            return data.roles;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获取所有角色列表（不分页）
     * @returns 角色列表响应
     */
    async getAllRoles() {
        return this.getRoles({ limit: 100 }); // 获取足够大数量的角色
    }

    /**
     * 获取启用状态的角色列表
     * @returns 启用状态的角色列表
     */
    async getEnabledRoles() {
        return this.getRoles({ status: RoleStatus.ENABLED, limit: 100 });
    }

    /**
     * 获取单个角色
     * @param id 角色ID
     * @returns 角色信息
     */
    async getRole(id: string) {
        try {
            const { data, error } = await this.client.query<RoleQueryResponse, RoleQueryVariables>({
                query: ROLE_QUERY,
                variables: { id },
                fetchPolicy: 'network-only'
            });

            if (error) {
                throw new Error(`获取角色详情失败：${error.message}`);
            }

            return data.role;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 创建角色
     * @param roleData 角色数据
     * @returns 创建的角色
     */
    async createRole(roleData: CreateRoleParams) {
        try {
            const { data, errors } = await this.client.mutate<CreateRoleResponse, CreateRoleVariables>({
                mutation: CREATE_ROLE,
                variables: {
                    input: roleData
                },
                refetchQueries: [{
                    query: ROLES_QUERY,
                    variables: { input: { page: 1, limit: 10 } }
                }]
            });

            if (errors && errors.length > 0) {
                throw new Error(`创建角色失败：${errors[0].message}`);
            }

            return data?.createRole;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 更新角色
     * @param roleData 角色更新数据
     * @returns 更新后的角色
     */
    async updateRole(roleData: UpdateRoleParams) {
        try {
            const { id, ...updateData } = roleData;
            const { data, errors } = await this.client.mutate<UpdateRoleResponse, UpdateRoleVariables>({
                mutation: UPDATE_ROLE,
                variables: {
                    input: {
                        id,
                        ...updateData
                    }
                },
                refetchQueries: [{
                    query: ROLES_QUERY,
                    variables: { input: { page: 1, limit: 10 } }
                }]
            });

            if (errors && errors.length > 0) {
                throw new Error(`更新角色失败：${errors[0].message}`);
            }

            return data?.updateRole;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 删除角色
     * @param id 角色ID
     * @returns 是否删除成功
     */
    async deleteRole(id: string) {
        try {
            const { data, errors } = await this.client.mutate<DeleteRoleResponse, DeleteRoleVariables>({
                mutation: DELETE_ROLE,
                variables: {
                    input: { id }
                },
                refetchQueries: [{
                    query: ROLES_QUERY,
                    variables: { input: { page: 1, limit: 10 } }
                }]
            });

            if (errors && errors.length > 0) {
                throw new Error(`删除角色失败：${errors[0].message}`);
            }

            return data?.deleteRole;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 切换角色状态
     * @param id 角色ID
     * @param status 新状态
     * @returns 更新后的角色
     */
    async toggleRoleStatus(id: string, status: 'enabled' | 'disabled') {
        return this.updateRole({ id, status: status === 'enabled' ? RoleStatus.ENABLED : RoleStatus.DISABLED });
    }
} 