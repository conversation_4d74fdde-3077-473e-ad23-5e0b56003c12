import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CreativeAnalysisDTO, CreativeTrendDTO, PieDataDTO } from './entities/creative-analysis.dto';
import { materiaCreate } from '../material-create/materiaCreate.entity';
import { Material } from './entities/material.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => CreativeAnalysisDTO)
export class CreativeAnalysisResolver {
    constructor(
        @InjectRepository(materiaCreate)
        private readonly comboRepo: Repository<materiaCreate>,
        @InjectRepository(Material)
        private readonly materialRepo: Repository<Material>,
    ) { }

    @Query(() => CreativeAnalysisDTO)
    async creativeAnalysis(
        @Args('startDate') startDate: string,
        @Args('endDate') endDate: string,
        @Context() ctx: any,
    ): Promise<CreativeAnalysisDTO> {
        const isSuperAdmin = ctx.isSuperAdmin;
        const tenantId = ctx.tenantId;
        // 查询所有创意组合（多租户隔离）
        const comboWhere = isSuperAdmin ? {} : { tenantId };
        const combos = await this.comboRepo.find({ where: comboWhere });
        // 查询所有素材，便于聚合（多租户隔离）
        let materials = [];
        if (combos.length > 0) {
            const materialIds = combos.map(c => c.materials).filter(Boolean);
            const materialWhere = isSuperAdmin ? {} : { id: In(materialIds), tenant: { id: tenantId } };
            materials = await this.materialRepo.find({ where: materialWhere });
        }
        // 统计类型趋势（按素材type）
        const typeMap: Record<string, number[]> = {};
        const now = new Date();
        for (let i = 0; i < 12; i++) {
            const month = new Date(now.getFullYear(), i, 1);
            const monthCombos = combos.filter(c => c.createdAt && c.createdAt.getMonth() === i);
            monthCombos.forEach(c => {
                const m = materials.find(mat => String(mat.id) === String(c.materials));
                const type = m?.type || '未知';
                if (!typeMap[type]) typeMap[type] = Array(12).fill(0);
                typeMap[type][i]++;
            });
        }
        const typeTrends: CreativeTrendDTO[] = Object.entries(typeMap).map(([type, data]) => ({ type, data }));
        // 统计尺寸分布（假设material.raw.size为尺寸）
        const sizeMap: Record<string, number> = {};
        materials.forEach(m => {
            const size = m.raw?.size || '未知尺寸';
            sizeMap[size] = (sizeMap[size] || 0) + 1;
        });
        const sizePerformance: PieDataDTO[] = Object.entries(sizeMap).map(([name, value]) => ({ name, value }));
        // 统计元素分布（假设material.raw.elements为元素数组）
        const elementMap: Record<string, number> = {};
        materials.forEach(m => {
            const elements: string[] = m.raw?.elements || [];
            elements.forEach(e => {
                elementMap[e] = (elementMap[e] || 0) + 1;
            });
        });
        const elementPerformance: PieDataDTO[] = Object.entries(elementMap).map(([name, value]) => ({ name, value }));
        return { typeTrends, sizePerformance, elementPerformance };
    }
} 