import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPlatformToAudience20240530000600 implements MigrationInterface {
    name = 'AddPlatformToAudience20240530000600'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN "platform" character varying(64);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN "platform";`);
    }
} 