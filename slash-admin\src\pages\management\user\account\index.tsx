import { useTranslation } from 'react-i18next';
import { useState } from "react";
import { Tabs, TabsProps } from "antd";
import { Table, Button, Tag, Space, Upload, message, Input } from "antd";
import { UploadOutlined } from "@ant-design/icons";

const { t } = useTranslation();

// Mock Data
const mockAccounts = [
  { id: "1", name: "广告账户A", status: t('pages.key_1295'), riskLevel: "低", group: t('pages.key_2618'), oauth: true, createdAt: "2025-05-01" },
  { id: "2", name: "广告账户B", status: t('pages.key_863'), riskLevel: "高", group: t('pages.key_223'), oauth: false, createdAt: "2025-05-02" },
];
const mockGroups = [
  { id: "g1", name: t('pages.key_2618'), count: 2 },
  { id: "g2", name: t('pages.key_223'), count: 1 },
];

function AccountListTab() {
  const columns = [
    { title: t('pages.key_2325'), dataIndex: "name", key: "name" },
    { title: t('pages.key_1602'), dataIndex: "status", key: "status", render: (v: string) => <Tag color={v === t('pages.key_1295') ? "green" : "red"}>{v}</Tag> },
    { title: t('pages.key_2586'), dataIndex: "riskLevel", key: "riskLevel" },
    { title: t('pages.key_222'), dataIndex: "group", key: "group" },
    { title: t('pages.key_1011'), dataIndex: "oauth", key: "oauth", render: (v: boolean) => v ? <Tag color="blue">OAuth</Tag> : <Tag color="orange">{t('pages.key_2321')}</Tag> },
    { title: t('pages.key_250'), dataIndex: "createdAt", key: "createdAt" },
    {
      title: t('pages.key_1058'),
      key: "actions",
      render: (_: any, record: any) => (
        <Space>
          <Button size="small">{t('pages.key_464')}</Button>
          <Button size="small">{t('pages.key_222')}</Button>
          <Button size="small" danger>{t('pages.key_287')}</Button>
        </Space>
      ),
    },
  ];
  return (
    <Table
      rowKey="id"
      columns={columns}
      dataSource={mockAccounts}
      pagination={{ pageSize: 10 }}
    />
  );
}

function BatchUploadTab() {
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any[]>([]);

  const props = {
    accept: ".csv,.xlsx",
    beforeUpload: (file: any) => {
      setFileList([file]);
      return false;
    },
    fileList,
    onRemove: () => setFileList([]),
  };

  const handleUpload = () => {
    setUploading(true);
    setTimeout(() => {
      setUploading(false);
      setUploadResult([
        { account: "test1", status: t('pages.key_936'), message: "" },
        { account: "test2", status: t('pages.key_598'), message: t('pages.key_675') },
      ]);
      message.success("上传完成（模拟结果）");
    }, 1500);
  };

  return (
    <div>
      <Upload {...props}>
        <Button icon={<UploadOutlined />}>选择文件（CSV/Excel）</Button>
      </Upload>
      <Button
        type="primary"
        onClick={handleUpload}
        disabled={fileList.length === 0}
        loading={uploading}
        style={{ marginTop: 16 }}
      >{t('pages.key_969')}</Button>
      {uploadResult.length > 0 && (
        <Table
          style={{ marginTop: 24 }}
          columns={[
            { title: t('pages.key_2320'), dataIndex: "account", key: "account" },
            { title: t('pages.key_1602'), dataIndex: "status", key: "status" },
            { title: t('pages.key_2143'), dataIndex: "message", key: "message" },
          ]}
          dataSource={uploadResult}
          rowKey="account"
          pagination={false}
        />
      )}
    </div>
  );
}

function OAuthTab() {
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState([
    { id: "1", name: "账号A", status: t('pages.key_894') },
    { id: "2", name: "账号B", status: t('pages.key_744') },
  ]);

  const handleOAuth = (id: string) => {
    setLoading(true);
    setTimeout(() => {
      setAccounts((prev) => prev.map((acc) => acc.id === id ? { ...acc, status: t('pages.key_744') } : acc));
      setLoading(false);
      message.success("授权成功（模拟）");
    }, 1200);
  };

  return (
    <Table
      columns={[
        { title: t('pages.key_2325'), dataIndex: "name", key: "name" },
        {
          title: t('pages.key_1013'),
          dataIndex: "status",
          key: "status",
          render: (v: string) => <Tag color={v === t('pages.key_744') ? "green" : "orange"}>{v}</Tag>,
        },
        {
          title: t('pages.key_1058'),
          key: "actions",
          render: (_: any, record: any) => (
            record.status === t('pages.key_894') ? (
              <Button size="small" loading={loading} onClick={() => handleOAuth(record.id)}>{t('pages.key_399')}</Button>
            ) : <span>--</span>
          ),
        },
      ]}
      dataSource={accounts}
      rowKey="id"
      pagination={false}
    />
  );
}

function GroupManageTab() {
  const [groups, setGroups] = useState(mockGroups);
  const [newGroup, setNewGroup] = useState("");

  const handleAdd = () => {
    if (!newGroup.trim()) return message.warning(t('pages.key_2177'));
    setGroups([...groups, { id: Date.now().toString(), name: newGroup, count: 0 }]);
    setNewGroup("");
    message.success(t('pages.key_1534'));
  };

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder={t('pages.key_1133')}
          value={newGroup}
          onChange={e => setNewGroup(e.target.value)}
          style={{ width: 200 }}
        />
        <Button type="primary" onClick={handleAdd}>{t('pages.key_1526')}</Button>
      </Space>
      <Table
        columns={[
          { title: t('pages.key_224'), dataIndex: "name", key: "name" },
          { title: t('pages.key_2328'), dataIndex: "count", key: "count" },
        ]}
        dataSource={groups}
        rowKey="id"
        pagination={false}
      />
    </div>
  );
}

export default function AdAccountManagement() {
  const [activeKey, setActiveKey] = useState("account-list");
  const items: TabsProps["items"] = [
    {
      key: "account-list",
      label: t('pages.key_826'),
      children: <AccountListTab />,
    },
    {
      key: "batch-upload",
      label: t('pages.key_969'),
      children: <BatchUploadTab />,
    },
    {
      key: "oauth",
      label: "OAuth授权",
      children: <OAuthTab />,
    },
    {
      key: "group-manage",
      label: t('pages.key_225'),
      children: <GroupManageTab />,
    },
  ];
  return (
    <Tabs
      items={items}
      activeKey={activeKey}
      onChange={setActiveKey}
      destroyOnHidden
    />
  );
}
