import { useTranslation } from 'react-i18next';
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/ui/card';
import { Chart } from '@/components/chart/chart';
import { useQuery, gql } from '@apollo/client';

const GLOBAL_TREND = gql`
  query GlobalTrend($startDate: String, $endDate: String) {
    globalTrend(startDate: $startDate, endDate: $endDate) {
      date
      spend
      conversion
      tenantCount
      userCount
      adCount
    }
  }
`;

export default function TenantTrendChart() {
  const { t } = useTranslation();
    // 这里可根据需要加时间筛选
    const { data, loading } = useQuery(GLOBAL_TREND);
    const trendData = data?.globalTrend || [];

    const chartOptions = {
        chart: {
            type: 'line' as const,
            toolbar: { show: false },
            height: 300,
        },
        xaxis: {
            categories: trendData.map((d: any) => d.date),
            title: { text: t('pages.key_1188') },
        },
        yaxis: [
            {
                title: { text: '消耗 (¥)' },
                labels: { formatter: (val: number) => `¥${val}` },
            },
            {
                opposite: true,
                title: { text: t('pages.key_2357') },
            },
        ],
        stroke: { width: 3, curve: 'smooth' as const },
        colors: ['#409EFF', '#67C23A'],
        legend: { position: 'top' as const, horizontalAlign: 'right' as const },
        tooltip: { shared: true, intersect: false },
    };

    const chartSeries = [
        {
            name: t('pages.key_1519'),
            type: 'line',
            data: trendData.map((d: any) => d.spend),
        },
        {
            name: t('pages.key_2357'),
            type: 'line',
            data: trendData.map((d: any) => d.conversion),
        },
    ];

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t('pages.key_800')}</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="h-[300px]">
                    <Chart options={chartOptions} series={chartSeries} type="line" height={300} />
                </div>
            </CardContent>
        </Card>
    );
} 