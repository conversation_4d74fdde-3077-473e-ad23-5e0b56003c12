# 组件路径 /pages/management/system/organization/index	

## 前端

- **实际路径**：slash-admin/src/pages/management/system/organization/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，Tab页）
  - VCCListTab.tsx（卡片列表）
  - TransactionHistoryTab.tsx（交易历史）

- **API 调用列表**（详见 slash-admin/src/api/vccCard.graphql.ts）：

| API名称 | 方法 | 路径/GraphQL | 参数 | 返回字段 | 定义文件 |
|---------|------|--------------|------|----------|----------|
| 获取VCC卡片列表 | query | vccCards | filter | id, channel, country, ... | vccCard.graphql.ts |
| 获取单个VCC卡片 | query | vccCard | id | id, channel, ... | vccCard.graphql.ts |
| 创建VCC卡片 | mutation | createVccCard | input | id, channel, ... | vccCard.graphql.ts |
| 更新VCC卡片 | mutation | updateVccCard | id, input | id, channel, ... | vccCard.graphql.ts |
| 删除VCC卡片 | mutation | deleteVccCard | id | id | vccCard.graphql.ts |
| 绑定广告账户 | mutation | bindAdAccountToVccCard | input | id, channel, ... | vccCard.graphql.ts |

- **主要字段说明**（前端/后端一致）：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 主键，UUID |
| channel | string | 渠道 |
| country | string | 国家 |
| cardHolder | string | 持卡人 |
| countryCode | string | 国家代码 |
| cardNumber | string | 卡号 |
| balance | number | 余额 |
| consumption | number | 消费 |
| boundAdAccount | string | 已绑定广告号 |
| boundAdAccountIds | string[] | 绑定的广告账户ID数组 |
| group | string | 所属群组 |
| transactionCount | number | 交易数 |
| adAccountStatus | number | 广告号存活数量 |
| expiryMonth | string | 过期月 |
| expiryYear | string | 过期年 |
| cvv | string | CVV |
| zipCode | string | 邮编 |
| usedCount | number | 已使用次数 |
| bindCount | number | 绑定次数 |
| totalAdAccounts | number | 绑定广告号总数 |
| limitCount | number | 限制次数 |
| status | string | 状态: 未使用/已使用/已封禁 |
| remark | string | 备注 |
| tenantId | string | 租户ID |
| createdAt | Date | 创建时间 |
| updatedAt | Date | 更新时间 |

---

## 后端

- **主要接口**（GraphQL，详见上表）

- **数据库表结构**（vcc_cards，详见 ad_backend/src/modules/vcc/entities/vcc-card.entity.ts 和 migration）：

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | uuid | 主键 | PRIMARY KEY |
| channel | varchar(50) | 渠道 | NOT NULL |
| country | varchar(50) | 国家 | NOT NULL |
| cardHolder | varchar(100) | 持卡人 | NOT NULL |
| countryCode | varchar(10) | 国家代码 | NOT NULL |
| cardNumber | varchar(20) | 卡号 | UNIQUE, NOT NULL |
| balance | numeric(10,2) | 余额 | 默认0 |
| consumption | numeric(10,2) | 消费 | 默认0 |
| boundAdAccount | varchar(200) | 已绑定广告号 | 可空 |
| boundAdAccountIds | json | 绑定的广告账户ID数组 | 可空 |
| group | varchar(100) | 所属群组 | 可空 |
| transactionCount | int | 交易数 | 默认0 |
| adAccountStatus | int | 广告号存活数量 | 默认0 |
| expiryMonth | varchar(2) | 过期月 | NOT NULL |
| expiryYear | varchar(4) | 过期年 | NOT NULL |
| cvv | varchar(4) | CVV | NOT NULL |
| zipCode | varchar(20) | 邮编 | NOT NULL |
| usedCount | int | 已使用次数 | 默认0 |
| bindCount | int | 绑定次数 | 默认0 |
| totalAdAccounts | int | 绑定广告号总数 | 默认0 |
| limitCount | int | 限制次数 | 默认10 |
| status | varchar(20) | 状态: 未使用/已使用/已封禁 | 默认'未使用' |
| remark | varchar(500) | 备注 | 可空 |
| tenantId | uuid | 租户ID | 外键, NOT NULL |
| createdAt | timestamp | 创建时间 | 默认now() |
| updatedAt | timestamp | 更新时间 | 默认now() |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query vccCards / mutation createVccCard / ...
    后端->>数据库: 操作 vcc_cards 表
    数据库-->>后端: 返回卡片数据
    后端-->>前端: 返回卡片数据
```

---

## 变更历史/注意事项

- 2024-05：新增 boundAdAccountIds 字段，支持多广告账户绑定。
- 卡号唯一，所有操作均需校验唯一性。
- 绑定广告账户时需校验账户有效性。
- 余额、消费等字段为decimal，前端需注意精度处理。