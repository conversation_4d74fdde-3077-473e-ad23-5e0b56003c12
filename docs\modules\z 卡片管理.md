# 组件路径 /pages/management/system/organization/index

## 前端

- **实际路径**：slash-admin/src/pages/management/system/organization/index.tsx
- **主要页面/组件**：

  - index.tsx（主入口，Tab 页）
  - VCCListTab.tsx（卡片列表）
  - TransactionHistoryTab.tsx（交易历史）

- **API 调用列表**（详见 slash-admin/src/api/vccCard.graphql.ts）：

| API 名称           | 方法     | 路径/GraphQL              | 参数                                     | 返回字段               | 说明                   |
| ------------------ | -------- | ------------------------- | ---------------------------------------- | ---------------------- | ---------------------- |
| 获取 VCC 卡片列表  | query    | vccCards                  | filter: VccCardFilterInput               | 完整字段列表（见下方） | 支持租户过滤           |
| 获取单个 VCC 卡片  | query    | vccCard                   | id: ID!                                  | 完整字段列表（见下方） | 根据 ID 获取详情       |
| 创建 VCC 卡片      | mutation | createVccCard             | input: CreateVccCardInput!               | 完整字段列表（见下方） | 创建新卡片             |
| 更新 VCC 卡片      | mutation | updateVccCard             | id: ID!, input: UpdateVccCardInput!      | 完整字段列表（见下方） | 更新卡片信息           |
| 删除 VCC 卡片      | mutation | deleteVccCard             | id: ID!                                  | id                     | 删除指定卡片           |
| 绑定广告账户       | mutation | bindAdAccountToVccCard    | input: BindAdAccountInput!               | 完整字段列表（见下方） | 绑定广告账户到卡片     |
| 更新 Facebook 数据 | mutation | updateVccCardFacebookData | id: ID!, input: UpdateFacebookDataInput! | 完整字段列表（见下方） | 更新 Facebook 相关数据 |
| 批量创建卡片       | mutation | batchCreateVccCards       | inputs: [CreateVccCardInput!]!           | [VccCardDTO!]!         | 批量创建多个卡片       |

- **主要字段说明**（前端/后端一致）：

| 字段名            | 类型     | 说明                                       |
| ----------------- | -------- | ------------------------------------------ |
| id                | string   | 主键，UUID                                 |
| channel           | string   | 渠道                                       |
| country           | string   | 国家                                       |
| cardHolder        | string   | 持卡人                                     |
| countryCode       | string   | 国家代码                                   |
| cardNumber        | string   | 卡号（唯一）                               |
| balance           | number   | 余额（decimal 类型）                       |
| consumption       | number   | 消费（decimal 类型）                       |
| boundAdAccount    | string   | 已绑定广告号（显示用）                     |
| boundAdAccountIds | string[] | 绑定的广告账户 ID 数组（JSON 格式）        |
| group             | string   | 所属群组                                   |
| transactionCount  | number   | 交易数                                     |
| adAccountStatus   | number   | 广告号存活数量                             |
| expiryMonth       | string   | 过期月（2 位）                             |
| expiryYear        | string   | 过期年（4 位）                             |
| cvv               | string   | CVV（4 位）                                |
| zipCode           | string   | 邮编                                       |
| usedCount         | number   | 已使用次数                                 |
| bindCount         | number   | 绑定次数                                   |
| totalAdAccounts   | number   | 绑定广告号总数                             |
| limitCount        | number   | 限制次数（默认 10）                        |
| status            | string   | 状态: 未使用/已使用/已封禁（默认'未使用'） |
| remark            | string   | 备注                                       |
| tenantId          | string   | 租户 ID（外键）                            |
| tenant            | object   | 租户关联对象                               |
| createdAt         | Date     | 创建时间                                   |
| updatedAt         | Date     | 更新时间                                   |

---

## 后端

- **主要接口**（GraphQL，详见上表）

- **数据库表结构**（vcc_cards，详见 ad_backend/src/modules/vcc/entities/vcc-card.entity.ts 和 migration）：

| 字段名            | 类型                     | 说明                         | 约束                    |
| ----------------- | ------------------------ | ---------------------------- | ----------------------- |
| id                | uuid                     | 主键                         | PRIMARY KEY             |
| channel           | varchar(50)              | 渠道                         | NOT NULL                |
| country           | varchar(50)              | 国家                         | NOT NULL                |
| cardHolder        | varchar(100)             | 持卡人                       | NOT NULL                |
| countryCode       | varchar(10)              | 国家代码                     | NOT NULL                |
| cardNumber        | varchar(20)              | 卡号                         | UNIQUE, NOT NULL, INDEX |
| balance           | decimal(10,2)            | 余额                         | 默认 0                  |
| consumption       | decimal(10,2)            | 消费                         | 默认 0                  |
| boundAdAccount    | varchar(200)             | 已绑定广告号                 | 可空                    |
| boundAdAccountIds | json                     | 绑定的广告账户 ID 数组       | 可空                    |
| group             | varchar(100)             | 所属群组                     | 可空                    |
| transactionCount  | int                      | 交易数                       | 默认 0                  |
| adAccountStatus   | int                      | 广告号存活数量               | 默认 0                  |
| expiryMonth       | varchar(2)               | 过期月                       | NOT NULL                |
| expiryYear        | varchar(4)               | 过期年                       | NOT NULL                |
| cvv               | varchar(4)               | CVV                          | NOT NULL                |
| zipCode           | varchar(20)              | 邮编                         | NOT NULL                |
| usedCount         | int                      | 已使用次数                   | 默认 0                  |
| bindCount         | int                      | 绑定次数                     | 默认 0                  |
| totalAdAccounts   | int                      | 绑定广告号总数               | 默认 0                  |
| limitCount        | int                      | 限制次数                     | 默认 10                 |
| status            | varchar(20)              | 状态: 未使用/已使用/已封禁   | 默认'未使用'            |
| remark            | varchar(500)             | 备注                         | 可空                    |
| tenantId          | uuid                     | 租户 ID                      | 外键, NOT NULL, INDEX   |
| tenant            | 关联关系                 | 租户关联（外键到 tenant 表） | NOT NULL                |
| createdAt         | timestamp with time zone | 创建时间                     | 默认 CURRENT_TIMESTAMP  |
| updatedAt         | timestamp with time zone | 更新时间                     | 自动更新                |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query vccCards / mutation createVccCard / ...
    后端->>数据库: 操作 vcc_cards 表
    数据库-->>后端: 返回卡片数据
    后端-->>前端: 返回卡片数据
```

---

## 变更历史/注意事项

- 2024-05：新增 boundAdAccountIds 字段，支持多广告账户绑定。
- 卡号唯一，所有操作均需校验唯一性。
- 绑定广告账户时需校验账户有效性。
- 余额、消费等字段为 decimal，前端需注意精度处理。
