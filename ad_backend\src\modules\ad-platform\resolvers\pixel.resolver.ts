import { Resolver, Query, Args, Mutation, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Campaign } from '../entities/campaign.entity';
import { PixelDTO } from '../entities/pixel.entity';
import { FacebookService } from '../facebook/facebook.service';
import { PixelService } from '../services/pixel.service';
import { CreatePixelInput } from '../dto/create-pixel.input';
import { PixelOutput } from '../dto/pixel.output';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => PixelDTO)
export class PixelResolver {
    constructor(
        @InjectRepository(Campaign)
        private readonly campaignRepo: Repository<Campaign>,
        private readonly facebookService: FacebookService,
        private readonly pixelService: PixelService
    ) { }

    @Query(() => [PixelDTO])
    async pixelsByCampaign(
        @Args('campaignId', { type: () => String }) campaignId: string,
        @Context() ctx: any
    ): Promise<PixelDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { id: campaignId } : { id: campaignId, adAccount: { tenant: { id: tenantId } } };
        const campaign = await this.campaignRepo.findOne({ where, relations: ['adAccount'] });
        if (!campaign) throw new Error('广告系列不存在');
        const adAccountId = campaign.adAccount?.accountId;
        const accessToken = campaign.adAccount?.accessToken;
        if (!adAccountId || !accessToken) throw new Error('广告系列未绑定广告账户或accessToken');
        // 实时调 Facebook API
        const { pixels } = await this.facebookService.getPixels({ accessToken, accountId: adAccountId });
        // pixels: [{ id, name, ... }]
        return (pixels || []).map((p: any) => ({
            id: p.id,
            pixelId: p.id,
            name: p.name,
        }));
    }

    @Query(() => String, { nullable: true })
    async businessIdByCampaignId(
        @Args('campaignId') campaignId: string,
        @Context() ctx: any
    ): Promise<string | undefined> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { id: campaignId } : { id: campaignId, adAccount: { tenant: { id: tenantId } } };
        const campaign = await this.campaignRepo.findOne({ where, relations: ['adAccount'] });
        if (!campaign) throw new Error('广告系列不存在');
        const adAccountId = campaign.adAccount?.accountId;
        const accessToken = campaign.adAccount?.accessToken;
        if (!adAccountId || !accessToken) throw new Error('广告系列未绑定广告账户或accessToken');
        return this.facebookService.getBusinessIdByAdAccountId(adAccountId, accessToken);
    }

    @Mutation(() => PixelOutput)
    async createPixelAndAssign(
        @Args('campaignId', { type: () => String }) campaignId: string,
        @Args('pixelName', { type: () => String }) pixelName: string,
        @Args('businessId', { type: () => String }) businessId: string,
        @Context() ctx: any
    ): Promise<PixelOutput> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { id: campaignId } : { id: campaignId, adAccount: { tenant: { id: tenantId } } };
        const campaign = await this.campaignRepo.findOne({ where, relations: ['adAccount'] });
        if (!campaign) throw new Error('广告系列不存在');
        const adAccountId = campaign.adAccount?.accountId;
        const accessToken = campaign.adAccount?.accessToken;
        if (!adAccountId || !accessToken) throw new Error('广告系列未绑定广告账户或accessToken');
        return this.pixelService.createAndAssignPixel(pixelName, businessId, adAccountId, accessToken);
    }
} 