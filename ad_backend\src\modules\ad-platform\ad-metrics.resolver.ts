import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { AdMetricsDTO } from './entities/ad-metrics.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ad } from './entities/ad.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => AdMetricsDTO)
export class AdMetricsResolver {
    constructor(
        @InjectRepository(Ad)
        private readonly adRepo: Repository<Ad>,
    ) { }

    @Query(() => AdMetricsDTO)
    async adMetrics(
        @Args('startDate') startDate: string,
        @Args('endDate') endDate: string,
        @Context() ctx: any,
    ): Promise<AdMetricsDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        let qb = this.adRepo
            .createQueryBuilder('ad')
            .select('SUM((ad.insight->>\'impressions\')::int)', 'impressions')
            .addSelect('SUM((ad.insight->>\'clicks\')::int)', 'clicks')
            .addSelect('SUM((ad.insight->>\'conversions\')::int)', 'conversions')
            .addSelect('SUM((ad.insight->>\'spend\')::float)', 'cost')
            .where('ad.updated_time BETWEEN :start AND :end', { start: startDate, end: endDate });
        if (!isSuperAdmin) {
            qb = qb.andWhere('ad.tenantId = :tenantId', { tenantId });
        }
        const { impressions, clicks, conversions, cost } = await qb.getRawOne();
        return {
            impressions: Number(impressions) || 0,
            clicks: Number(clicks) || 0,
            conversions: Number(conversions) || 0,
            cost: Number(cost) || 0,
        };
    }
} 