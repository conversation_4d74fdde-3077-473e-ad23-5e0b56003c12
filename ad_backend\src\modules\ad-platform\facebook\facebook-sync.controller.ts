import { Controller, Post, Body, BadRequestException } from '@nestjs/common';
import { FacebookService } from './facebook.service';
import { FacebookSyncService } from './facebook-sync.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tenant } from '../../../entity/tenant.entity';
import { AdAccount } from '../entities/ad-account.entity';

@Controller('ad-platform/facebook/sync')
export class FacebookSyncController {
  constructor(
    private readonly facebookService: FacebookService,
    private readonly facebookSyncService: FacebookSyncService,
    @InjectRepository(Tenant) private readonly tenantRepo: Repository<Tenant>,
    @InjectRepository(AdAccount) private readonly adAccountRepo: Repository<AdAccount>,
  ) { }

  // 手动同步广告账户、系列、组、广告、创意、像素、受众
  @Post()
  async syncAll(@Body() body: { tenantId: string; accessToken: string; accountId: string }) {
    const { tenantId, accessToken, accountId } = body;
    if (!tenantId || !accessToken || !accountId) throw new BadRequestException('tenantId, accessToken, accountId required');
    const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
    if (!tenant) throw new BadRequestException('tenant not found');
    let adAccount = await this.adAccountRepo.findOne({ where: { accountId, tenant, platform: 'facebook' } });
    const logs = [];
    const result: any = {};
    try {
      // 拉取并同步账户
      const { accounts } = await this.facebookService.getAccounts({ accessToken });
      result.accounts = await this.facebookSyncService.syncAccounts({ accounts, tenant });
      adAccount = await this.adAccountRepo.findOne({ where: { accountId, tenant, platform: 'facebook' } });
    } catch (e) { logs.push({ type: 'accounts', error: e.message }); }
    const campaignMap = new Map();
    let adsetMap = new Map();
    let creativeMap = new Map();
    try {
      // 拉取并同步系列
      const { campaigns } = await this.facebookService.getCampaigns({ accessToken, accountId });
      result.campaigns = await this.facebookSyncService.syncCampaigns({ campaigns, tenant, adAccount });
      // 建立 campaignId -> 实体映射
      for (const c of await this.adAccountRepo.manager.getRepository('AdCampaign').find({ where: { adAccount, tenant, platform: 'facebook' } })) {
        campaignMap.set(c.campaignId, c);
      }
    } catch (e) { logs.push({ type: 'campaigns', error: e.message }); }
    try {
      // 拉取并同步广告组
      const { adsets } = await this.facebookService.getAdSets({ accessToken, accountId });
      // 建立 adset -> adCampaign 外键
      adsetMap = new Map();
      for (const item of adsets) {
        adsetMap.set(item.id, item);
      }
      await this.facebookSyncService.syncAdSets({ adsets, tenant, adAccount, campaignMap });
      result.adsets = { inserted: adsets.length };
    } catch (e) { logs.push({ type: 'adsets', error: e.message }); }
    try {
      // 拉取并同步广告
      const { ads } = await this.facebookService.getAds({ accessToken, accountId });
      creativeMap = new Map();
      for (const creative of await this.adAccountRepo.manager.getRepository('AdCreative').find({ where: { adAccount, tenant, platform: 'facebook' } })) {
        creativeMap.set(creative.creativeId, creative);
      }
      await this.facebookSyncService.syncAds({ ads, tenant, adAccount, campaignMap, adsetMap, creativeMap });
      result.ads = { inserted: ads.length };
    } catch (e) { logs.push({ type: 'ads', error: e.message }); }
    try {
      // 拉取并同步创意
      const { creatives } = await this.facebookService.getAdCreatives({ accessToken, accountId });
      await this.facebookSyncService.syncAdCreatives({ creatives, tenant, adAccount });
      result.creatives = { inserted: creatives.length };
    } catch (e) { logs.push({ type: 'creatives', error: e.message }); }
    try {
      // 拉取并同步像素
      const { pixels } = await this.facebookService.getPixels({ accessToken, accountId });
      result.pixels = await this.facebookSyncService.syncPixels({ pixels, tenant, adAccount });
    } catch (e) { logs.push({ type: 'pixels', error: e.message }); }
    try {
      // 拉取并同步受众
      const { audiences } = await this.facebookService.getAudiences({ accessToken, accountId });
      result.audiences = await this.facebookSyncService.syncAudiences({ audiences, tenant });
    } catch (e) { logs.push({ type: 'audiences', error: e.message }); }
    result.logs = logs;
    return result;
  }
}
