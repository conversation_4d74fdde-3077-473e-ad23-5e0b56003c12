# 联调说明文档（详细版）

## 一、前后端联调流程

1. **后端启动**  
   - 确保 schema.graphql 自动生成，位于 `ad_backend/generated/schemas/schema.graphql`
   - 检查 Entity/DTO 字段一致性，建议每次变更后运行 `npx tsc --noEmit` 检查类型
   - 启动服务后建议用 GraphQL Playground 检查 schema 是否同步

2. **前端开发**  
   - 通过 Apollo Client 调用 GraphQL
   - 推荐统一封装 hooks（如 useQuery/useMutation）
   - 接口类型建议用 graphql-codegen 自动生成
   - 新页面/功能建议先用 mock 数据开发，接口联调前可用 MSW/mockjs

3. **接口变更同步**  
   - 后端变更 Entity/DTO 后需重启服务以同步 schema
   - 前端根据 schema 自动生成 TypeScript 类型（如使用 codegen）
   - 建议接口变更时同步更新接口文档和前端类型定义

4. **接口调试与测试建议**  
   - 推荐使用 GraphQL Playground/Postman/Insomnia 联调接口
   - 前端可直接在 hooks 里调试 GraphQL 查询/变更
   - REST 接口可用 curl、Postman、Swagger UI 调试
   - 建议为核心接口编写 e2e 测试用例（如 jest、supertest）
   - 联调时建议建立测试账号和专用测试环境，避免污染生产数据

5. **数据 mock 与回归**  
   - 前端开发阶段可用 mock service worker (MSW) 或 mockjs 拦截接口
   - 后端可用 factory/seeder 生成测试数据
   - 联调后建议定期回归核心接口，确保变更不影响主流程

6. **接口回归与用例管理**  
   - 所有核心接口建议有自动化测试覆盖
   - 每次接口变更需同步更新/补充测试用例
   - 建议用接口测试平台（如 YApi、Postman Collection）管理接口用例

7. **前后端协作规范**  
   - 变更接口需提前沟通，避免 breaking change
   - 建议每次接口变更都更新接口文档和 changelog
   - 联调前建议双方先对接口 schema、字段、返回结构达成一致
   - 复杂流程建议提前画好流程图/接口时序图

8. **常见问题与排查**
- 类型冲突：优先检查 Entity/DTO 字段可选性、类型是否一致
- schema 未同步：重启后端服务，检查 autoSchemaFile 配置
- 新增字段未生效：确认 DTO、Entity、Resolver 都已更新
- 前端类型报错：检查 codegen 是否同步 schema
- 数据异常：建议用 Playground/数据库工具直接查询定位

9. **接口调试工具推荐**
- GraphQL Playground、Apollo Studio
- Postman、Insomnia
- Swagger UI（REST）
- curl/httpie（命令行调试）
- YApi/ApiPost（接口用例管理）

10. **参考文档**
- [后端说明](../ad_backend/README.md)
- [前端说明](../slash-admin/README.md)
- [接口文档](./api.md)

---

如有特殊联调需求或遇到疑难问题，请及时同步至团队群或负责人。
