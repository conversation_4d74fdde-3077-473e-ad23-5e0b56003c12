const { Client } = require('pg');

const client = new Client({
  host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
  port: 5432,
  user: 'yeeu',
  password: 'Yy&20240505',
  database: 'ad_auto',
});

async function main() {
  await client.connect();

  // 检查所有 schema 下 ad_set 表的 tenantId 字段类型
  const result = await client.query(`
    SELECT table_schema, table_name, column_name, data_type
    FROM information_schema.columns
    WHERE table_name = 'ad_set' AND column_name ILIKE '%tenantid%'
    ORDER BY table_schema, table_name, column_name
  `);
  console.log('所有 schema 下 ad_set 表 tenantId 字段类型:');
  result.rows.forEach(row => console.log(row));

  // 检查当前 schema 下 ad_set 表所有字段类型
  const allColumns = await client.query(`
    SELECT table_schema, column_name, data_type
    FROM information_schema.columns
    WHERE table_name = 'ad_set'
    ORDER BY table_schema, column_name
  `);
  console.log('\n当前 schema 下 ad_set 表所有字段类型:');
  allColumns.rows.forEach(row => console.log(row));

  await client.end();
}

main().catch(e => {
  console.error(e);
  process.exit(1);
}); 