import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsNotEmpty, IsArray, IsIn } from 'class-validator';

@InputType('CreateCampaignInputX')
export class CreateCampaignInputX {
    @Field()
    @IsString()
    @IsNotEmpty()
    name: string;
    @Field()
    @IsString()
    @IsNotEmpty()
    @IsIn([
        'OUTCOME_LEADS',
        'OUTCOME_SALES',
        'OUTCOME_ENGAGEMENT',
        'OUTCOME_AWARENESS',
        'OUTCOME_TRAFFIC',
        'OUTCOME_APP_PROMOTION',
    ], { message: 'objective 仅支持 Facebook 官方目标类型' })
    objective: string;
    @Field()
    @IsString()
    @IsNotEmpty()
    status: string;
    @Field(() => [String])
    @IsArray()
    specialAdCategories: string[];
    @Field()
    @IsString()
    @IsNotEmpty()
    accountId: string;
} 