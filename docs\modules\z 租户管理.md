# 组件路径 /pages/management/system/tenant/index

## 前端

- **实际路径**：slash-admin/src/pages/management/system/tenant/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，租户管理表单与列表，含增删改查、分页、搜索、弹窗等）
- **主要依赖API定义**：@/api/graphql/tenant.graphql

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取租户列表    | query    | tenants                | search, page, limit | id, name, type, logo | 支持分页、搜索、过滤 |
| 获取租户详情    | query    | tenant                 | id: string    | id, name, type, logo | 获取单个租户详情 |
| 新增租户        | mutation | createTenant           | input: TenantInput | id, name, type, logo | 新建租户，id为UUID |
| 编辑租户        | mutation | updateTenant           | input: TenantInput | id, name, type, logo | 编辑指定租户 |
| 删除租户        | mutation | deleteTenant           | id: string    | id       | 删除指定租户 |

#### 主要参数结构说明

- **TenantInput**（用于新增/编辑）  
  ```ts
  {
    id?: string; // 租户ID，编辑时必填
    name: string; // 租户名称
    type?: string; // 类型（如small-tenant、big-tenant等）
    logo?: string; // logo图片URL
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | id          | string    | 主键，UUID                 |
  | name        | string    | 租户名称                   |
  | type        | string    | 类型                       |
  | logo        | string    | logo图片URL                |
  | createdAt   | Date      | 创建时间                   |
  | updatedAt   | Date      | 更新时间                   |

### 前端表单/组件说明

- 支持租户的增删改查，表单字段与上方TenantInput结构一致
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）
- 支持租户类型、logo等字段的录入

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（tenant，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，租户唯一标识         | PRIMARY KEY  |
| name        | varchar     | 租户名称                   | NOT NULL     |
| type        | varchar     | 类型                       | 可空         |
| logo        | varchar     | logo图片URL                | 可空         |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| updatedAt   | timestamp   | 更新时间                   | 自动生成     |
| ...         | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query tenants / mutation createTenant(input) / ...
    后端->>数据库: 操作 tenant 表
    数据库-->>后端: 返回租户数据
    后端-->>前端: 返回租户数据
```

---

## 变更历史/注意事项

- 租户名称需唯一，所有操作需校验唯一性
- 支持多类型租户扩展（type 字段）
- 删除租户时需校验是否被用户、资源等引用
- logo字段为图片URL，前端需校验格式
- 复杂业务字段建议在表结构中用jsonb扩展