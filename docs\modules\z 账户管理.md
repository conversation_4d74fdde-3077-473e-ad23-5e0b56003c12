# 组件路径 /pages/management/system/account/index

## 前端

- **实际路径**：slash-admin/src/pages/management/system/account/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，账户管理表单与列表，含增删改查、分页、搜索、弹窗等）
  - user-modal.tsx（账户表单弹窗）
- **主要依赖API定义**：@/api/services/user.service

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取账户列表    | query    | getUsers               | page, limit, filter | id, username, email, status, roleIds, groupIds, createdAt, ... | 支持分页、搜索、过滤 |
| 新增账户        | mutation | createUser             | input: CreateUserParams | id, username, ... | 新建账户，id为UUID |
| 编辑账户        | mutation | updateUser             | input: UpdateUserParams | id, username, ... | 编辑指定账户 |
| 删除账户        | mutation | deleteUser             | id: string    | id       | 删除指定账户 |
| 更改账户状态    | mutation | changeUserStatus       | id, status    | id, status | 启用/禁用账户 |

#### 主要参数结构说明

- **CreateUserParams/UpdateUserParams**（用于新增/编辑）  
  ```ts
  {
    id?: string; // 账户ID，编辑时必填
    username: string; // 用户名
    email?: string; // 邮箱
    password?: string; // 密码（仅创建时必填）
    status?: string; // 状态（active/blocked）
    roleIds?: string[]; // 绑定的角色ID数组
    groupIds?: string[]; // 绑定的群组ID数组
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | id          | string    | 主键，UUID                 |
  | username    | string    | 用户名                     |
  | email       | string    | 邮箱                       |
  | status      | string    | 状态（active/blocked）     |
  | roleIds     | string[]  | 绑定的角色ID数组           |
  | groupIds    | string[]  | 绑定的群组ID数组           |
  | createdAt   | Date      | 创建时间                   |
  | updatedAt   | Date      | 更新时间                   |

### 前端表单/组件说明

- 支持账户的增删改查，表单字段与上方CreateUserParams结构一致
- 支持角色、群组多选绑定
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）
- 支持账户状态切换（启用/禁用）
- 账户名、邮箱等字段唯一性校验

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（user，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，账户唯一标识         | PRIMARY KEY  |
| username    | varchar     | 用户名                     | NOT NULL, UNIQUE |
| email       | varchar     | 邮箱                       | UNIQUE       |
| password    | varchar     | 密码（加密存储）           | NOT NULL     |
| status      | varchar     | 状态（active/blocked）     | 默认active   |
| roleIds     | uuid[]      | 绑定的角色ID数组           | 可空         |
| groupIds    | uuid[]      | 绑定的群组ID数组           | 可空         |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| updatedAt   | timestamp   | 更新时间                   | 自动生成     |
| ...         | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query getUsers / mutation createUser(input) / ...
    后端->>数据库: 操作 user 表
    数据库-->>后端: 返回账户数据
    后端-->>前端: 返回账户数据
```

---

## 变更历史/注意事项

- 账户名、邮箱需唯一，所有操作需校验唯一性
- 支持角色、群组多选绑定，建议用uuid[]或jsonb存储
- 删除账户时需校验是否为当前登录用户，防止误删
- 密码需加密存储，前端不返回明文
- 复杂业务字段建议在表结构中用jsonb扩展