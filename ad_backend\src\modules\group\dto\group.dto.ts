import { IsOptional, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>num, IsUUID } from 'class-validator';
import {
    Field,
    ID,
    ObjectType,
    GraphQLISODateTime,
    InputType,
    Int,
    registerEnumType
} from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import GraphQLJSON from 'graphql-type-json';
import { Tenant } from '../../../entity/tenant.entity';

export enum GroupStatus {
    ACTIVE = '正常',
    DISABLED = '禁用'
}

registerEnumType(GroupStatus, {
    name: 'GroupStatus',
    description: '群组状态',
});

@ObjectType('Group')
export class GroupDTO {
    @IDField(() => ID, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsOptional()
    @IsUUID()
    id?: string;

    @FilterableField(() => String)
    @ApiProperty({ type: String, description: '群组名称' })
    @IsString()
    name: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String, description: '归属人' })
    @IsString()
    @IsOptional()
    belongTo?: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String, description: '联系方式' })
    @IsString()
    @IsOptional()
    contactInfo?: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String, description: '群组描述' })
    @IsString()
    @IsOptional()
    description?: string;

    @FilterableField(() => Int, { nullable: true })
    @ApiPropertyOptional({ type: Number, description: '成员数量' })
    @IsNumber()
    @IsOptional()
    memberCount?: number;

    @FilterableField(() => GraphQLJSON, { nullable: true })
    @ApiPropertyOptional({ type: 'object', description: '群组权限' })
    @IsOptional()
    permissions?: any;

    @FilterableField(() => String)
    @ApiProperty({ enum: GroupStatus, enumName: 'GroupStatus', description: '群组状态' })
    @IsEnum(GroupStatus)
    status: GroupStatus;

    // 创建时间
    @FilterableField(() => GraphQLISODateTime, { nullable: true })
    @ApiPropertyOptional({
        type: 'string',
        format: 'date-time',
        example: '2023-01-01T00:00:00.000Z',
    })
    @IsOptional()
    createTime?: Date;

    // 更新时间
    @FilterableField(() => GraphQLISODateTime, { nullable: true })
    @ApiPropertyOptional({
        type: 'string',
        format: 'date-time',
        example: '2023-01-01T00:00:00.000Z',
    })
    @IsOptional()
    updateTime?: Date;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String, description: '租户ID' })
    @IsString()
    @IsOptional()
    tenantId?: string;

    @Field(() => Tenant, { nullable: true })
    tenant?: Tenant;
}

@InputType('GroupInput')
export class GroupInput {
    @Field(() => String, { nullable: true })
    tenantId?: string;
    // 其他输入字段按需补充，不包含 tenant 字段
}

@InputType('CreateGroupInput')
export class CreateGroupInput {
    @Field(() => String)
    name: string;
    @Field(() => String, { nullable: true })
    belongTo?: string;
    @Field(() => String, { nullable: true })
    contactInfo?: string;
    @Field(() => String, { nullable: true })
    description?: string;
    @Field(() => Number, { nullable: true })
    memberCount?: number;
    @Field(() => String)
    status: string;
    @Field(() => String, { nullable: true })
    tenantId?: string;
    @Field(() => GraphQLJSON, { nullable: true })
    permissions?: any;
} 