import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';
import { AdAccount } from './ad-account.entity';
import { AdCampaign } from './ad-campaign.entity';

@Entity('ad_creative')
export class AdCreative {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '平台类型（facebook/google/tiktok/adjust/af等）' })
  @Column({ length: 32 })
  platform: string;

  @ApiProperty({ description: '广告平台Creative ID' })
  @Index()
  @Column({ length: 64 })
  creativeId: string;

  @ApiProperty({ description: '广告创意名称' })
  @Column({ length: 128 })
  name: string;

  @ApiProperty({ description: '所属广告账户' })
  @ManyToOne(() => AdAccount, { nullable: false })
  adAccount: AdAccount;

  @ApiProperty({ description: '所属广告系列' })
  @ManyToOne(() => AdCampaign, { nullable: false })
  adCampaign: AdCampaign;

  @ApiProperty({ description: '租户ID' })
  @Index()
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始creative数据（JSON）' })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;

  @ApiProperty({ description: '创意状态' })
  @Column({ length: 32, default: 'active' })
  status: string;
}
