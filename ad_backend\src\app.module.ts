/*
 * @Author: 潘孝权
 * @Date: 2025-05-16 19:59:00
 * @Description: 
 * @FilePath: \sking_frontend\ad_backend\src\app.module.ts
 */
import { Module, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GraphQLModule } from '@nestjs/graphql';
import { CqrsModule } from '@nestjs/cqrs';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { ConfigModule } from '@nestjs/config';
import { join } from 'path';
import GraphQLJSON from 'graphql-type-json';
import { TypeOrmConfigService } from './config/database';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './modules/auth/auth.module';
import { UserModule } from './modules/user/user.module';
import { EmployeeModule } from './modules/employee/employee.module';
import { AntiBlockModule } from './modules/anti-block/antiBlock.module';
import { ipRestrictionModule } from './modules/ip-restriction/ipRestriction.module';
import { materialManagementModule } from './modules/material-management/materialManagement.module';
import { materiaCreateModule } from './modules/material-create/materiaCreate.module';
import { AnalysisModule } from './modules/analysis/analysis.module';
import { GroupModule } from './modules/group/group.module';
import { AdPlatformModule } from './modules/ad-platform/ad-platform.module';
import { RouteModule } from './modules/route/route.module';
import { RoleModule } from './modules/role/role.module';
import { VccCardModule } from './modules/vcc/vcc-card.module';
import { SeedModule } from './modules/seeds/seed.module';
import { AppSagas } from './sagas/app.saga';
import config, { ConfigurationService } from './config/config';
import { IpRestrictionMiddleware } from './common/middlewares/ip-restriction.middleware';
import { ModuleRef } from '@nestjs/core';
import { RoleSeedService } from './modules/role/role.seed';
import { LandingPageModule } from './modules/landing-page/landing-page.module';
import { DomainModule } from './modules/domain/domain.module';

const autoSchemaFile = process.env.IS_NOT_SLS
	? join(process.cwd(), 'generated/schemas/schema.graphql')
	: // TODO: when we run in Serverless, this should probably be different (for now we use the same path)
	join(process.cwd(), 'generated/schemas/schema.graphql') + '';

console.log('Schema stored in: ' + autoSchemaFile);
console.log('IS_NOT_SLS: ' + process.env.IS_NOT_SLS);
console.log(process.env.NODE_ENV, 1111);
const isProd = process.env.NODE_ENV == 'production' ? true : false;

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
			load: [config],
		}),
		GraphQLModule.forRoot<ApolloDriverConfig>({
			include: [
				EmployeeModule,
				AntiBlockModule,
				GroupModule,
				RouteModule,
				ipRestrictionModule,
				materialManagementModule,
				materiaCreateModule,
				AnalysisModule,
				RoleModule,
				UserModule,
				AuthModule,
				VccCardModule,
				LandingPageModule,
				DomainModule
			],
			autoSchemaFile,
			introspection: !isProd,
			driver: ApolloDriver,
			resolvers: { JSON: GraphQLJSON },
			playground: isProd
				? false
				: {
					endpoint: process.env.IS_NOT_SLS
						? '/graphql'
						: `/${process.env.STAGE}/graphql`,
				},
			context: ({ req }) => ({ req }),
		}),
		CqrsModule,
		TypeOrmModule.forRootAsync({
			inject: [ConfigModule, ConfigurationService],
			useClass: TypeOrmConfigService,
		}),
		AuthModule,
		UserModule,
		EmployeeModule,
		AntiBlockModule,
		GroupModule,
		AdPlatformModule,
		RouteModule,
		ipRestrictionModule,
		materialManagementModule,
		materiaCreateModule,
		AnalysisModule,
		RoleModule,
		VccCardModule,
		SeedModule,
		LandingPageModule,
		DomainModule,
	],
	controllers: [AppController],
	providers: [
		ConfigurationService,
		AppService,
		TypeOrmConfigService,
		AppSagas,
	],
})
export class AppModule {
	constructor(private readonly moduleRef: ModuleRef) { }

	async onModuleInit() {
		if (process.env.NODE_ENV !== 'production' || process.env.AUTO_SEED === 'true') {
			const roleSeedService = this.moduleRef.get(RoleSeedService, { strict: false });
			if (roleSeedService && typeof roleSeedService.seed === 'function') {
				await roleSeedService.seed();
			}
		}
	}

	configure(consumer: MiddlewareConsumer) {
		consumer
			.apply(IpRestrictionMiddleware)
			.forRoutes({ path: '*', method: RequestMethod.ALL });
	}
}
