import { Injectable } from '@nestjs/common';
import { axiosProxyPost, axiosProxyGet } from '../../../utils/axios-proxy';
import { OptionDTO } from '../entities/audience.dto';
import { FACEBOOK_COUNTRIES, FACEBOOK_LOCALES, FACEBOOK_GENDERS, FACEBOOK_PLATFORMS, FACEBOOK_AGE_RANGES } from '../constants/facebook-static';
import axios from 'axios';

// Facebook账户数据接口
export interface FacebookAccountData {
    accountId: string;
    accountName: string;
    accountStatus: 'ACTIVE' | 'DISABLED' | 'PAUSED' | 'PENDING_REVIEW' | 'DISAPPROVED';
    spend: number;
    impressions: number;
    clicks: number;
    ctr: number;
    cpm: number;
    cpc: number;
    currency: string;
    lastUpdated: string;
}

export interface FacebookInsightsData {
    spend: number;
    impressions: number;
    clicks: number;
    ctr: number;
    cpm: number;
    cpc: number;
    date_start: string;
    date_stop: string;
}

// 广告账户OAuth信息接口
export interface AdAccountOAuth {
    accountId: string;
    accessToken: string;
}

// 2. 静态语言ID表
// const FACEBOOK_LOCALES: OptionDTO[] = [
//     { id: '2052', name: '中文（简体）' },
//     { id: '1028', name: '中文（繁體）' },
//     { id: '1033', name: '英语（美国）' },
//     { id: '2057', name: '英语（英国）' },
//     { id: '1041', name: '日语' },
//     { id: '1036', name: '法语' },
//     { id: '1031', name: '德语' },
//     { id: '3082', name: '西班牙语（国际）' },
//     { id: '1040', name: '意大利语' },
//     { id: '1042', name: '韩语' },
//     { id: '1049', name: '俄语' },
//     { id: '1046', name: '葡萄牙语（巴西）' },
//     { id: '2070', name: '葡萄牙语（葡萄牙）' },
//     { id: '1055', name: '土耳其语' },
//     { id: '1061', name: '爱沙尼亚语' },
//     { id: '1062', name: '拉脱维亚语' },
//     { id: '1063', name: '立陶宛语' },
//     { id: '1048', name: '罗马尼亚语' },
//     { id: '1051', name: '斯洛伐克语' },
//     { id: '1060', name: '斯洛文尼亚语' },
//     { id: '1053', name: '瑞典语' },
//     { id: '1054', name: '泰语' },
//     { id: '1058', name: '乌克兰语' },
//     { id: '1057', name: '印尼语' },
//     { id: '1059', name: '白俄罗斯语' },
//     { id: '1025', name: '阿拉伯语' },
//     { id: '1037', name: '希伯来语' },
//     { id: '1045', name: '波兰语' },
//     { id: '1043', name: '荷兰语' },
//     { id: '1035', name: '芬兰语' },
//     { id: '1030', name: '丹麦语' },
//     { id: '1039', name: '冰岛语' },
//     { id: '1044', name: '挪威语' },
//     { id: '1052', name: '克罗地亚语' },
//     { id: '1050', name: '塞尔维亚语' },
//     { id: '1047', name: '保加利亚语' },
//     { id: '1042', name: '韩语' },
//     { id: '1029', name: '捷克语' },
//     { id: '1032', name: '希腊语' },
//     { id: '1038', name: '匈牙利语' },
//     { id: '1048', name: '罗马尼亚语' },
//     { id: '1056', name: '乌尔都语' },
//     { id: '1065', name: '波斯语' },
//     { id: '1056', name: '乌尔都语' },
//     { id: '1066', name: '越南语' },
//     { id: '1052', name: '克罗地亚语' },
//     { id: '1051', name: '斯洛伐克语' },
//     { id: '1050', name: '塞尔维亚语' },
//     { id: '1047', name: '保加利亚语' },
//     { id: '1046', name: '葡萄牙语（巴西）' },
//     { id: '2070', name: '葡萄牙语（葡萄牙）' },
//     { id: '1045', name: '波兰语' },
//     { id: '1044', name: '挪威语' },
//     { id: '1043', name: '荷兰语' },
//     { id: '1042', name: '韩语' },
//     { id: '1041', name: '日语' },
//     { id: '1040', name: '意大利语' },
//     { id: '1039', name: '冰岛语' },
//     { id: '1038', name: '匈牙利语' },
//     { id: '1037', name: '希伯来语' },
//     { id: '1036', name: '法语' },
//     { id: '1035', name: '芬兰语' },
//     { id: '1033', name: '英语（美国）' },
//     { id: '2057', name: '英语（英国）' },
//     { id: '1028', name: '中文（繁體）' },
//     { id: '2052', name: '中文（简体）' },
// ];

// 兴趣分类静态表（示例）
export const FACEBOOK_INTEREST_CATEGORIES: OptionDTO[] = [
    { id: '6006', name: '商业与行业' },
    { id: '6008', name: '娱乐' },
    { id: '6012', name: '家庭与关系' },
    { id: '6014', name: '健身与健康' },
    { id: '6016', name: '食品与饮料' },
    { id: '6018', name: '爱好与活动' },
    { id: '6020', name: '购物与时尚' },
    { id: '6022', name: '体育与户外' },
    { id: '6024', name: '科技' },
    { id: '6026', name: '旅游' },
    { id: '6028', name: '其他' },
];

// 行为分类静态表（示例）
export const FACEBOOK_BEHAVIOR_CATEGORIES: OptionDTO[] = [
    { id: '6007101597783', name: '经常出国旅行者' },
    { id: '6004386044572', name: '网购用户' },
    { id: '6002714895372', name: '新科技产品早期采用者' },
    { id: '6002714898572', name: '商务差旅' },
    { id: '6002714892172', name: '高消费能力用户' },
    { id: '6002714896772', name: '节日购物者' },
    { id: '6002714893972', name: '汽车爱好者' },
    { id: '6002714891172', name: '金融服务用户' },
    { id: '6002714897572', name: '房地产投资者' },
    { id: '6002714893372', name: '教育培训用户' },
    { id: '6002714891772', name: '健康关注者' },
    { id: '6002714896172', name: '母婴人群' },
    { id: '6002714892972', name: '宠物主人' },
    { id: '6002714894772', name: '环保人士' },
    { id: '6002714895572', name: '慈善捐赠者' },
    { id: '6002714898572', name: '节日活动参与者' },
    { id: '6002714899972', name: '其他' },
];

// 年龄段静态表（常用分组）
// export const FACEBOOK_AGE_RANGES: OptionDTO[] = [
//     { id: '13-17', name: '13-17岁' },
//     { id: '18-24', name: '18-24岁' },
//     { id: '25-34', name: '25-34岁' },
//     { id: '35-44', name: '35-44岁' },
//     { id: '45-54', name: '45-54岁' },
//     { id: '55-64', name: '55-64岁' },
//     { id: '65+', name: '65岁及以上' },
// ];

@Injectable()
export class FacebookApiService {
    private readonly API_BASE = 'https://graph.facebook.com/v18.0';

    async createCampaign(payload: {
        name: string;
        status: string;
        budget: number;
        startDate: Date;
        endDate: Date;
        tags?: string[];
        description?: string;
        placement: 'FACEBOOK' | 'INSTAGRAM';
        adAccountId: string;
    }, accessToken: string): Promise<string> {
        // 组装 Facebook API 参数
        const params: any = {
            name: payload.name,
            status: payload.status.toUpperCase(), // Facebook: PAUSED/ACTIVE
            objective: 'LINK_CLICKS', // 视业务而定
            special_ad_categories: [],
            // 预算、时间等参数需按 Facebook API 要求组装
        };
        // placement/objective 可根据 payload.placement 区分
        // 这里只是示例，实际参数请查阅 Facebook Marketing API 文档

        // 发起 HTTP 请求
        try {
            const res = await axios.post(
                `${this.API_BASE}/act_${payload.adAccountId}/campaigns`,
                params,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            return res.data.id;
        } catch (error) {
            console.error('Facebook API创建活动失败:', error);
            // 临时返回模拟ID
            return 'temp_campaign_id_' + Date.now();
        }
    }

    // 获取单个广告账户基本信息
    async getAdAccountInfo(accountId: string, accessToken: string): Promise<{
        id: string;
        name: string;
        account_status: number;
        currency: string;
    }> {
        try {
            const response = await axios.get(
                `${this.API_BASE}/act_${accountId}`,
                {
                    params: {
                        fields: 'id,name,account_status,currency',
                        access_token: accessToken
                    }
                }
            );
            return response.data;
        } catch (error) {
            console.error(`获取Facebook账户 ${accountId} 信息失败:`, error);
            throw new Error(`Facebook API Error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // 获取广告账户的Insights数据（消费、展示、点击等）
    async getAdAccountInsights(
        accountId: string,
        accessToken: string,
        dateRange: { since: string; until: string } = {
            since: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30天前
            until: new Date().toISOString().split('T')[0] // 今天
        }
    ): Promise<FacebookInsightsData> {
        try {
            const response = await axios.get(
                `${this.API_BASE}/act_${accountId}/insights`,
                {
                    params: {
                        access_token: accessToken,
                        fields: 'spend,impressions,clicks,ctr,cpm,cpc',
                        time_range: JSON.stringify(dateRange),
                        level: 'account'
                    }
                }
            );

            // Facebook Insights API返回数据数组，取第一个（或聚合）
            const data = response.data.data?.[0] || {
                spend: 0,
                impressions: 0,
                clicks: 0,
                ctr: 0,
                cpm: 0,
                cpc: 0,
                date_start: dateRange.since,
                date_stop: dateRange.until
            };

            return {
                spend: parseFloat(data.spend || '0'),
                impressions: parseInt(data.impressions || '0'),
                clicks: parseInt(data.clicks || '0'),
                ctr: parseFloat(data.ctr || '0'),
                cpm: parseFloat(data.cpm || '0'),
                cpc: parseFloat(data.cpc || '0'),
                date_start: data.date_start,
                date_stop: data.date_stop
            };
        } catch (error) {
            console.error(`获取Facebook账户 ${accountId} Insights失败:`, error);
            throw new Error(`Facebook Insights API Error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // 获取多个广告账户的完整数据（使用各自的OAuth token）
    async getBatchAccountData(accountOAuths: AdAccountOAuth[]): Promise<FacebookAccountData[]> {
        const results: FacebookAccountData[] = [];

        // 并发获取数据，但限制并发数量避免API限制
        const batchSize = 5;
        for (let i = 0; i < accountOAuths.length; i += batchSize) {
            const batch = accountOAuths.slice(i, i + batchSize);

            const batchPromises = batch.map(async (accountOAuth) => {
                try {
                    // 并发获取账户信息和Insights数据
                    const [accountInfo, insightsData] = await Promise.all([
                        this.getAdAccountInfo(accountOAuth.accountId, accountOAuth.accessToken),
                        this.getAdAccountInsights(accountOAuth.accountId, accountOAuth.accessToken)
                    ]);

                    // 转换账户状态
                    const accountStatus = this.convertAccountStatus(accountInfo.account_status);

                    return {
                        accountId: accountInfo.id,
                        accountName: accountInfo.name,
                        accountStatus,
                        spend: insightsData.spend,
                        impressions: insightsData.impressions,
                        clicks: insightsData.clicks,
                        ctr: insightsData.ctr,
                        cpm: insightsData.cpm,
                        cpc: insightsData.cpc,
                        currency: accountInfo.currency,
                        lastUpdated: new Date().toISOString()
                    };
                } catch (error) {
                    console.error(`获取账户 ${accountOAuth.accountId} 数据失败:`, error);

                    // 返回错误状态的账户数据
                    return {
                        accountId: accountOAuth.accountId,
                        accountName: '获取失败',
                        accountStatus: 'DISABLED' as const,
                        spend: 0,
                        impressions: 0,
                        clicks: 0,
                        ctr: 0,
                        cpm: 0,
                        cpc: 0,
                        currency: 'USD',
                        lastUpdated: new Date().toISOString()
                    };
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);

            // 添加延时避免API限制
            if (i + batchSize < accountOAuths.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        return results;
    }

    // 转换Facebook账户状态
    private convertAccountStatus(status: number): FacebookAccountData['accountStatus'] {
        // Facebook账户状态映射
        switch (status) {
            case 1: return 'ACTIVE';
            case 2: return 'DISABLED';
            case 3: return 'PAUSED';
            case 7: return 'PENDING_REVIEW';
            case 8: return 'DISAPPROVED';
            default: return 'DISABLED';
        }
    }

    // 验证访问令牌是否有效
    async validateAccessToken(accessToken: string): Promise<boolean> {
        try {
            const response = await axios.get(
                `${this.API_BASE}/me`,
                {
                    params: { access_token: accessToken }
                }
            );
            return response.status === 200;
        } catch {
            return false;
        }
    }

    // 获取用户有权限的广告账户列表
    async getUserAdAccounts(accessToken: string): Promise<Array<{
        id: string;
        name: string;
        account_status: number;
    }>> {
        try {
            const response = await axios.get(
                `${this.API_BASE}/me/adaccounts`,
                {
                    params: {
                        fields: 'id,name,account_status',
                        access_token: accessToken
                    }
                }
            );
            return response.data.data || [];
        } catch (error) {
            console.error('获取Facebook广告账户列表失败:', error);
            throw new Error(`获取广告账户列表失败: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    async createAdSet(payload: any, accessToken: string): Promise<{ id: string }> {
        // 组装参数，调用 Facebook Marketing API
        // 参考官方文档：https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/
        // 需要 adAccountId 字段
        const adAccountId = payload.account_id || payload.adAccountId || payload.adAccountID || payload.ad_account_id || payload.ad_accountId || payload.adAccountId;
        if (!adAccountId) {
            throw new Error('adAccountId/account_id 不能为空');
        }
        const url = `https://graph.facebook.com/v23.0/act_${adAccountId}/adsets`;
        const postData = { ...payload, access_token: accessToken };
        try {
            const res = await axiosProxyPost(url, postData);
            return { id: res.data.id };
        } catch (e) {
            console.error('Facebook API 创建广告组失败:', e?.response?.data || e);
            throw e;
        }
    }

    async updateAdSet(adsetId: string, payload: any, accessToken: string): Promise<void> {
        // POST https://graph.facebook.com/v23.0/{adset_id}
        // await axios.post(`https://graph.facebook.com/v23.0/${adsetId}`, { ...payload, access_token: accessToken });
    }

    async deleteAdSet(adsetId: string, accessToken: string): Promise<void> {
        // DELETE https://graph.facebook.com/v23.0/${adsetId}
        // await axios.delete(`https://graph.facebook.com/v23.0/${adsetId}`, { params: { access_token: accessToken } });
    }

    // 1. 国家/地区列表
    async getCountries(): Promise<OptionDTO[]> {
        return FACEBOOK_COUNTRIES;
    }

    // 2. 语言ID列表
    async getLocales(): Promise<OptionDTO[]> {
        return FACEBOOK_LOCALES;
    }

    // 3. 兴趣选项（Facebook API）
    async getInterests(query: string, accessToken: string): Promise<OptionDTO[]> {
        if (!accessToken) throw new Error('accessToken required');
        const url = `https://graph.facebook.com/v23.0/search?type=adinterest&q=[\"${encodeURIComponent(query || '')}\"]&access_token=${accessToken}`;
        const res = await axiosProxyGet(url);
        return (res.data.data || []).map((item: any) => ({ id: item.id, name: item.name }));
    }

    // 4. 行为选项（Facebook API）
    async getBehaviors(query: string, accessToken: string): Promise<OptionDTO[]> {
        if (!accessToken) throw new Error('accessToken required');
        const url = `https://graph.facebook.com/v22.0/search?type=adinterest&q=[\"${encodeURIComponent(query || '')}\"]&access_token=${accessToken}`;
        const res = await axiosProxyGet(url);
        const behaviors = (res.data.data || []).filter((item: any) => item.topic === 'Behavior');
        console.log('FB行为接口返回（仅Behavior）:', url, JSON.stringify(behaviors, null, 2));
        return behaviors.map((item: any) => ({ id: item.id, name: item.name }));
    }

    // 5. 自定义受众（Facebook API）
    async getCustomAudiences(adAccountId: string, accessToken: string): Promise<OptionDTO[]> {
        if (!accessToken) throw new Error('accessToken required');
        if (!adAccountId) throw new Error('adAccountId required');
        const url = `https://graph.facebook.com/v23.0/act_${adAccountId}/customaudiences?access_token=${accessToken}`;
        const res = await axiosProxyGet(url);
        return (res.data.data || []).map((item: any) => ({ id: item.id, name: item.name }));
    }

    async createAdRule(payload: any, adAccountId: string, accessToken: string): Promise<{ id: string }> {
        const url = `https://graph.facebook.com/v20.0/act_${adAccountId}/adrules_library`;
        let lastError;
        // 1. 默认传filters
        try {
            let payload = {
                "name": "账户级自动暂停规则",
                "evaluation_spec": {
                    "evaluation_type": "TRIGGER",
                    "trigger": {
                        "type": "STATS_CHANGE",
                        "field": "CLICKS",
                        "operator": "GREATER_THAN",
                        "value": 1
                    },
                    "filters": [
                        {
                            "field": "entity_type",
                            "value": "AD",
                            "operator": "EQUAL"
                        },
                        {
                            "field": "time_preset",
                            "value": "TODAY",
                            "operator": "EQUAL"
                        }
                    ]
                },
                "execution_spec": {
                    "execution_type": "PAUSE"
                },
                "status": "ENABLED",
                "account_id": "****************",
                "access_token": "你的有效access_token"
            }
            console.log('[createAdRule] 首次尝试 payload:', JSON.stringify(payload));
            const res = await axiosProxyPost(url, { ...payload, access_token: accessToken });
            return { id: res.data.id };
        } catch (error) {
            console.error(error.response, "error");
            const msg = error?.response?.data?.error?.message || '';
            lastError = error;
            console.error('[createAdRule] 首次失败:', msg);
            // 2. 如果报not supported in trigger based rules，则去掉filters重试
            if (msg.includes('not supported in trigger based rules')) {
                const payloadNoFilters = { ...payload };
                if (payloadNoFilters.evaluation_spec) {
                    delete payloadNoFilters.evaluation_spec.filters;
                }
                try {
                    console.log('[createAdRule] 去掉filters重试 payload:', JSON.stringify(payloadNoFilters));
                    const res = await axiosProxyPost(url, { ...payloadNoFilters, access_token: accessToken });
                    return { id: res.data.id };
                } catch (e2) {
                    lastError = e2;
                    console.error('[createAdRule] 去掉filters重试失败:', e2?.response?.data?.error?.message || e2.message);
                }
            }
            // 3. 如果报filters is required，则自动补上filters重试
            if (msg.includes('evaluation_spec[filters] is required')) {
                // 自动补上filters（如无则用ad.id/adset.id/campaign.id）
                if (!payload.evaluation_spec) payload.evaluation_spec = {};
                // 总是补充filters字段
                const bizId = payload.ad_id || payload.adset_id || payload.campaign_id;
                let field = '';
                if (payload.ad_id) field = 'ad.id';
                else if (payload.adset_id) field = 'adset.id';
                else if (payload.campaign_id) field = 'campaign.id';
                if (field && bizId) {
                    payload.evaluation_spec.filters = [{ field, operator: 'IN', value: [String(bizId)] }];
                }
                try {
                    console.log('[createAdRule] 自动补上filters重试 payload:', JSON.stringify(payload));
                    const res = await axiosProxyPost(url, { ...payload, access_token: accessToken });
                    return { id: res.data.id };
                } catch (e3) {
                    lastError = e3;
                    console.error('[createAdRule] 自动补上filters重试失败:', e3?.response?.data?.error?.message || e3.message);
                }
            }
            // 4. 其他错误正常抛出
            console.error('Facebook API创建自动规则失败:', lastError?.response?.data || lastError);
            throw new Error('Facebook API创建自动规则失败: ' + (lastError?.response?.data?.error?.message || lastError.message));
        }
    }
} 