import request from '@/utils/request';
import { MaterialSyncParams, ListParams } from '../types/api';

export function syncMaterials({ tenantId, materialList }: MaterialSyncParams) {
  return request.post('/ad-platform/material/batch', { tenantId, materialList });
}

export function fetchMaterialList({ tenantId, page, pageSize }: ListParams) {
  return request.get('/ad-platform/material', { params: { tenantId, page, pageSize } });
}
