import { useTranslation } from 'react-i18next';
export const COUNTRY_OPTIONS = [
    { label: 'pages.key_42', value: 'CN' },
    { label: 'pages.key_1882', value: 'US' },
    { label: 'pages.key_1929', value: 'GB' },
    { label: 'pages.key_1192', value: 'JP' },
    { label: 'pages.key_898', value: 'DE' },
    { label: 'pages.key_1491', value: 'FR' },
    { label: 'pages.key_325', value: 'CA' },
    { label: 'pages.key_1580', value: 'AU' },
    { label: 'pages.key_1134', value: 'SG' },
    { label: 'pages.key_388', value: 'IN' },
    { label: 'pages.key_752', value: 'BR' },
    { label: 'pages.key_130', value: 'RU' },
    { label: 'pages.key_933', value: 'IT' },
    { label: 'pages.key_2041', value: 'ES' },
    { label: 'pages.key_564', value: 'MX' },
    { label: 'pages.key_2538', value: 'KR' },
    { label: 'pages.key_535', value: 'TR' },
    { label: 'pages.key_1932', value: 'NL' },
    { label: 'pages.key_1617', value: 'CH' },
    { label: 'pages.key_1615', value: 'SE' },
    { label: 'pages.key_1473', value: 'BE' },
    { label: 'pages.key_1003', value: 'NO' },
    { label: 'pages.key_46', value: 'DK' },
    { label: 'pages.key_1925', value: 'FI' },
    { label: 'pages.key_1493', value: 'PL' },
    { label: 'pages.key_601', value: 'AT' },
    { label: 'pages.key_1592', value: 'IE' },
    { label: 'pages.key_1169', value: 'NZ' },
    { label: 'pages.key_365', value: 'ZA' },
    { label: 'pages.key_2524', value: 'AR' },
    { label: 'pages.key_1476', value: 'SA' },
    { label: 'pages.key_2525', value: 'AE' },
    { label: 'pages.key_94', value: 'IL' },
    { label: 'pages.key_1504', value: 'TH' },
    { label: 'pages.key_2593', value: 'MY' },
    { label: 'pages.key_389', value: 'ID' },
    { label: 'pages.key_2007', value: 'PH' },
    { label: 'pages.key_2341', value: 'VN' },
    { label: 'pages.key_549', value: 'EG' },
    { label: 'pages.key_719', value: 'NG' },
    { label: 'pages.key_751', value: 'PK' },
    { label: 'pages.key_644', value: 'BD' },
    { label: 'pages.key_58', value: 'UA' },
    { label: 'pages.key_1005', value: 'CZ' },
    { label: 'pages.key_357', value: 'HU' },
    { label: 'pages.key_759', value: 'GR' },
    { label: 'pages.key_2016', value: 'PT' },
    { label: 'pages.key_1879', value: 'RO' },
    { label: 'pages.key_513', value: 'CO' },
    { label: 'pages.key_1722', value: 'PE' },
    { label: 'pages.key_623', value: 'VE' },
    { label: 'pages.key_1057', value: 'MA' },
    { label: 'pages.key_1899', value: 'KE' },
    { label: 'pages.key_550', value: 'ET' },
    { label: 'pages.key_2521', value: 'DZ' },
    { label: 'pages.key_370', value: 'QA' },
    { label: 'pages.key_1716', value: 'KW' },
    { label: 'pages.key_2523', value: 'OM' },
    { label: 'pages.key_385', value: 'LU' },
    { label: 'pages.key_205', value: 'IS' },
    { label: 'pages.key_1127', value: 'SK' },
    { label: 'pages.key_1129', value: 'SI' },
    { label: 'pages.key_173', value: 'HR' },
    { label: 'pages.key_132', value: 'BG' },
    { label: 'pages.key_558', value: 'RS' },
    { label: 'pages.key_1593', value: 'EE' },
    { label: 'pages.key_993', value: 'LV' },
    { label: 'pages.key_1743', value: 'LT' },
    { label: 'pages.key_1657', value: 'BY' },
    { label: 'pages.key_1395', value: 'GE' },
    { label: 'pages.key_65', value: 'AM' },
    { label: 'pages.key_2519', value: 'AZ' },
    { label: 'pages.key_507', value: 'KZ' },
    { label: 'pages.key_60', value: 'UZ' },
    { label: 'pages.key_2018', value: 'MN' },
    { label: 'pages.key_1369', value: 'KH' },
    { label: 'pages.key_1891', value: 'LA' },
    { label: 'pages.key_1851', value: 'MM' },
    { label: 'pages.key_1131', value: 'LK' },
    { label: 'pages.key_721', value: 'NP' },
    { label: 'pages.key_2520', value: 'AF' },
    { label: 'pages.key_97', value: 'IR' },
    { label: 'pages.key_96', value: 'IQ' },
    { label: 'pages.key_426', value: 'SY' },
    { label: 'pages.key_1794', value: 'JO' },
    { label: 'pages.key_2616', value: 'LB' },
    { label: 'pages.key_63', value: 'YE' },
    { label: 'pages.key_1928', value: 'SD' },
    { label: 'pages.key_307', value: 'LY' },
    { label: 'pages.key_1739', value: 'TN' },
    { label: 'pages.key_326', value: 'GH' },
    { label: 'pages.key_1719', value: 'CI' },
    { label: 'pages.key_557', value: 'SN' },
    { label: 'pages.key_515', value: 'CM' },
    { label: 'pages.key_648', value: 'AO' },
    { label: 'pages.key_1934', value: 'MZ' },
    { label: 'pages.key_2337', value: 'ZM' },
    { label: 'pages.key_1506', value: 'ZW' },
    { label: 'pages.key_366', value: 'BW' },
    { label: 'pages.key_1796', value: 'NA' },
    { label: 'pages.key_2595', value: 'MG' },
    { label: 'pages.key_547', value: 'TZ' },
    { label: 'pages.key_61', value: 'UG' },
    { label: 'pages.key_384', value: 'RW' },
    { label: 'pages.key_757', value: 'BI' },
    { label: 'pages.key_2592', value: 'MW' },
    { label: 'pages.key_1791', value: 'SO' },
    { label: 'pages.key_234', value: 'CG' },
    { label: '刚果（金）', value: 'CD' },
    { label: 'pages.key_44', value: 'CF' },
    { label: 'pages.key_62', value: 'TD' },
    { label: 'pages.key_720', value: 'NE' },
    { label: 'pages.key_2596', value: 'ML' },
    { label: 'pages.key_756', value: 'BF' },
    { label: 'pages.key_2318', value: 'BJ' },
    { label: 'pages.key_594', value: 'TG' },
    { label: 'pages.key_560', value: 'SL' },
    { label: 'pages.key_308', value: 'LR' },
    { label: 'pages.key_203', value: 'GM' },
    { label: 'pages.key_215', value: 'GN' },
    { label: 'pages.key_216', value: 'GW' },
    { label: 'pages.key_2338', value: 'GQ' },
    { label: 'pages.key_327', value: 'GA' },
    { label: 'pages.key_109', value: 'CV' },
    { label: 'pages.key_1474', value: 'MR' },
    { label: 'pages.key_392', value: 'ER' },
    { label: 'pages.key_461', value: 'DJ' },
    { label: 'pages.key_561', value: 'SC' },
    { label: 'pages.key_1718', value: 'KM' },
    { label: 'pages.key_537', value: 'ST' },
];

export const LANGUAGE_OPTIONS = [
    { label: '英文（美国）', value: 6 },
    { label: '英文（英国）', value: 24 },
    { label: 'pages.key_43', value: 11 },
    { label: 'pages.key_1193', value: 23 },
    { label: 'pages.key_2539', value: 19 },
    { label: 'pages.key_2042', value: 10 },
    { label: 'pages.key_1492', value: 9 },
    { label: 'pages.key_899', value: 16 },
    { label: 'pages.key_2017', value: 12 },
    { label: 'pages.key_131', value: 20 },
    { label: 'pages.key_2522', value: 22 },
    { label: 'pages.key_934', value: 15 },
    { label: 'pages.key_1933', value: 17 },
    { label: 'pages.key_536', value: 21 },
    { label: 'pages.key_2342', value: 29 },
    { label: 'pages.key_1505', value: 25 },
    { label: 'pages.key_387', value: 26 },
    { label: 'pages.key_2594', value: 28 },
    { label: 'pages.key_1494', value: 27 },
    { label: 'pages.key_1616', value: 31 },
    { label: 'pages.key_47', value: 8 },
    { label: 'pages.key_1926', value: 32 },
    { label: 'pages.key_1004', value: 30 },
    { label: 'pages.key_760', value: 7 },
    { label: 'pages.key_358', value: 34 },
    { label: 'pages.key_1006', value: 35 },
    { label: 'pages.key_1880', value: 36 },
    { label: 'pages.key_133', value: 37 },
    { label: 'pages.key_174', value: 38 },
    { label: 'pages.key_1128', value: 39 },
    { label: 'pages.key_59', value: 40 },
    { label: 'pages.key_758', value: 41 },
    { label: 'pages.key_386', value: 42 },
    { label: 'pages.key_2008', value: 43 },
    { label: 'pages.key_2591', value: 44 },
    { label: 'pages.key_559', value: 45 },
    { label: 'pages.key_1130', value: 46 },
    { label: 'pages.key_1594', value: 47 },
    { label: 'pages.key_994', value: 48 },
    { label: 'pages.key_1744', value: 49 },
    { label: 'pages.key_133', value: 50 },
];

export const GENDER_OPTIONS = [
    { label: 'pages.key_20', value: null },
    { label: '男', value: 1 },
    { label: '女', value: 2 },
];

export const PLATFORM_OPTIONS = [
    { label: 'Facebook', value: 'facebook' },
    { label: 'Instagram', value: 'instagram' },
    { label: 'Audience Network', value: 'audience_network' },
    { label: 'Messenger', value: 'messenger' },
];

export const AGE_RANGE_OPTIONS = [
    { label: '13-17', value: [13, 17] },
    { label: '18-24', value: [18, 24] },
    { label: '25-34', value: [25, 34] },
    { label: '35-44', value: [35, 44] },
    { label: '45-54', value: [45, 54] },
    { label: '55-64', value: [55, 64] },
    { label: '65+', value: [65, 100] },
];

// value→label Map 构建
export const GENDER_MAP = new Map(GENDER_OPTIONS.map(opt => [opt.value, opt.label]));
export const LANGUAGE_MAP = new Map(LANGUAGE_OPTIONS.map(opt => [opt.value, opt.label]));
export const COUNTRY_MAP = new Map(COUNTRY_OPTIONS.map(opt => [opt.value, opt.label]));
export const PLATFORM_MAP = new Map(PLATFORM_OPTIONS.map(opt => [opt.value, opt.label]));
export const AGE_RANGE_MAP = new Map(AGE_RANGE_OPTIONS.map(opt => [JSON.stringify(opt.value), opt.label]));

// 通用 value→label 工具函数
export function getLabelByValue(
    map: Map<any, string>,
    value: any,
    defaultLabel: string = '-'
): string {
    if (value === undefined || value === null) return defaultLabel;
    // 处理数组（如 locales、countries）
    if (Array.isArray(value)) {
        return value.map((v: any) => map.get(v) || v).join('、');
    }
    // 处理对象数组（如 interests/behaviors）
    if (Array.isArray(value) && value.length && typeof value[0] === 'object' && value[0].id) {
        return value.map((v: any) => map.get(v.id) || v.id).join('、');
    }
    // 处理普通值
    return map.get(value) || defaultLabel;
}

export const EVENT_TYPE_OPTIONS = [
    { label: '购买 (Purchase)', value: 'PURCHASE' },
    { label: '注册 (CompleteRegistration)', value: 'COMPLETE_REGISTRATION' },
    { label: '加购 (AddToCart)', value: 'ADD_TO_CART' },
    { label: '发起结账 (InitiateCheckout)', value: 'INITIATE_CHECKOUT' },
    { label: '潜在客户 (Lead)', value: 'LEAD' },
    { label: '页面浏览 (ViewContent)', value: 'VIEW_CONTENT' },
    // ...可补充其它官方支持的事件
]; 