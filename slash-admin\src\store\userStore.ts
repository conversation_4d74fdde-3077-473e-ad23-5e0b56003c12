import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import userService, { type SignInReq } from "@/api/services/userService";

import { toast } from "sonner";
import { StorageEnum } from "#/enum";
import { UserStatus } from "@/pages/management/system/account/types";
import { useTenant } from '@/hooks/useTenant';

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

// 用户信息类型
export interface AppUserInfo {
	id: string;
	username: string;
	email: string;
	avatar?: string;
	fullName?: string;
	phone?: string;
	address?: string;
	status: UserStatus;
	roles?: {
		id: string;
		name: string;
		routeIds: string[];
		routeList: {
			id: string;
			parentId: string | null;
			name: string;
			path: string;
			component: string | null;
			icon: string | null;
			order: number;
			type: 'menu' | 'page';
			status: 'enabled' | 'disabled';
			isHidden: boolean;
			description: string;
			children?: Array<{
				id: string;
				parentId: string;
				name: string;
				path: string;
				component: string | null;
				icon: string | null;
				order: number;
				type: 'menu' | 'page';
				status: 'enabled' | 'disabled';
				isHidden: boolean;
				description: string;
			}>;
		}[];
	}[];
	groups?: { id: string; name: string }[];
	createdAt: string;
	updatedAt: string;
}

// 用户令牌类型
export interface AppUserToken {
	accessToken?: string;
	refreshToken?: string;
}

type UserStore = {
	userInfo: Partial<AppUserInfo>;
	userToken: AppUserToken;
	// 使用 actions 命名空间来存放所有的 action
	actions: {
		setUserInfo: (userInfo: Partial<AppUserInfo>) => void;
		setUserToken: (token: AppUserToken) => void;
		clearUserInfoAndToken: () => void;
	};
};

// 获取当前租户ID
function getCurrentTenantId() {
	return localStorage.getItem('current_tenant_id') || '';
}

// 保存 token 到多租户分区
const saveTokenToLocalStorage = (token: string) => {
	const tenantId = getCurrentTenantId();
	let allTokens: Record<string, any> = {};
	try {
		allTokens = JSON.parse(localStorage.getItem('tenant_tokens') || '{}');
	} catch { }
	allTokens[tenantId] = token;
	localStorage.setItem('tenant_tokens', JSON.stringify(allTokens));
	// 兼容 Apollo 认证
	localStorage.setItem('auth_token', token);
};

// 保存 userInfo 到多租户分区
const saveUserInfoToLocalStorage = (userInfo: any) => {
	const tenantId = getCurrentTenantId();
	let allUserInfo: Record<string, any> = {};
	try {
		allUserInfo = JSON.parse(localStorage.getItem('tenant_userInfo') || '{}');
	} catch { }
	allUserInfo[tenantId] = userInfo;
	localStorage.setItem('tenant_userInfo', JSON.stringify(allUserInfo));
};

const useUserStore = create<UserStore>()(
	persist(
		(set) => ({
			userInfo: {},
			userToken: {},
			actions: {
				setUserInfo: (userInfo) => {
					set({ userInfo });
					saveUserInfoToLocalStorage(userInfo);
				},
				setUserToken: (userToken) => {
					set({ userToken });
					if (userToken?.accessToken) {
						saveTokenToLocalStorage(userToken.accessToken);
					}
				},
				clearUserInfoAndToken() {
					set({ userInfo: {}, userToken: {} });
				},
			},
		}),
		{
			name: "userStore",
			storage: createJSONStorage(() => localStorage),
			partialize: (state) => {
				return {
					[StorageEnum.UserInfo]: state.userInfo,
					[StorageEnum.UserToken]: state.userToken,
				};
			},
		},
	),
);

export const useUserInfo = () => useUserStore((state) => state.userInfo);
export const useUserToken = () => useUserStore((state) => state.userToken);
export const useUserPermissions = () => {
	const userInfo = useUserStore((state) => state.userInfo);
	return userInfo?.roles?.map((role) => role.name) || [];
};
// 为了保持向后兼容性，提供旧的名称导出
export const useUserPermission = useUserPermissions;
export const useUserActions = () => useUserStore((state) => state.actions);

export const useSignIn = () => {
	const navigatge = useNavigate();
	const { setUserToken, setUserInfo } = useUserActions();

	const signInMutation = useMutation({
		mutationFn: userService.signin,
	});

	const signIn = async (data: SignInReq) => {
		try {
			const res = await signInMutation.mutateAsync(data);
			const { user, accessToken, refreshToken } = res;
			setUserToken({ accessToken, refreshToken });
			setUserInfo(user as any);
			navigatge(HOMEPAGE, { replace: true });
			toast.success("Sign in success!", {
				closeButton: true,
			});
		} catch (err) {
			toast.error(err.message, {
				position: "top-center",
			});
		}
	};

	return signIn;
};

export default useUserStore;
