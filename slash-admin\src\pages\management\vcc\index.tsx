import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Modal, message, Table } from 'antd';
import { useQuery } from '@apollo/client';
import { GET_VCC_LIST } from '@/api/vcc.graphql';
import { useTenant } from '@/hooks/useTenant';
import { syncVccs } from '@/api/vcc';
import type { VccSyncResponse } from '@/types/api';

export default function VccManagement() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [syncResult, setSyncResult] = useState<VccSyncResponse | null>(null);
  const tenantId = useTenant();
  const { data, refetch } = useQuery(GET_VCC_LIST, {
    variables: {
      filter: { tenantId },
      paging: { limit: 20, offset: 0 },
      sorting: [{ field: 'createdAt', direction: 'DESC' }]
    }
  });

  const handleSync = async () => {
    setLoading(true);
    try {
      const vccList = data?.vccs?.nodes || [];
      const res = await syncVccs({ tenantId: tenantId || '', vccList });
      const syncData = res.data as VccSyncResponse;
      setSyncResult(syncData);
      Modal.info({
        title: t('pages.key_480'),
        content: (
          <div>
            <div>同步成功的记录：{syncData.success || 0}</div>
            <div>同步失败的记录：{syncData.failed || 0}</div>
            {syncData.errors && syncData.errors.length > 0 && (
              <div>
                <div>错误信息：</div>
                <pre>{JSON.stringify(syncData.errors, null, 2)}</pre>
              </div>
            )}
          </div>
        ),
      });
      message.success(t('pages.key_474'));
      refetch();
    } catch (e: any) {
      message.error("同步失败：" + (e?.message || t('pages.key_1312')));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Table
      rowKey="id"
      columns={[
        { title: t('pages.key_367'), dataIndex: "cardNumber" },
        { title: t('pages.key_995'), dataIndex: "cardHolder" },
        { title: t('pages.key_1296'), dataIndex: "expiry" },
        { title: "CVV", dataIndex: "cvv" },
        { title: t('pages.key_1838'), dataIndex: "bindAccount" },
        { title: t('pages.key_1602'), dataIndex: "status" },
      ]}
      dataSource={data?.vccs?.nodes || []}
      loading={loading}
      pagination={{ pageSize: 20 }}
    />
  );
}
