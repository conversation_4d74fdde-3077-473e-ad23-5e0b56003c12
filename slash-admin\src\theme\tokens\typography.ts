export const FontFamilyPreset = {
	openSans: "Open Sans Variable",
	inter: "Inter Variable",
};

export const typographyTokens = {
	fontFamily: {
		openSans: FontFamilyPreset.openSans,
		inter: FontFamilyPreset.inter,
	},
	fontSize: {
		xs: "12",
		sm: "14",
		default: "16",
		lg: "18",
		xl: "20",
	},
	fontWeight: {
		light: "300",
		normal: "400",
		medium: "500",
		semibold: "600",
		bold: "700",
	},
	lineHeight: {
		none: "1",
		tight: "1.25",
		normal: "1.375",
		relaxed: "1.5",
	},
};
