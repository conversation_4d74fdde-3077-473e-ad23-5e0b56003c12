/*
 * @Author: 潘孝权
 * @Date: 2025-05-31 01:36:32
 * @Description: 
 * @FilePath: \sking_frontend\slash-admin\src\api\graphql\materialManagement.graphql.ts
 */
import { gql } from '@apollo/client';
export const CREATE_MATERIALMANAGEMENT_MUTATION = gql`
mutation CreateOneMaterialManagement($input: CreateOneMaterialManagementInput!) {
  createOneMaterialManagement(input: $input) {
      id
      tenantId
      fileName
      tags
      notes
      file
  }
}
`;

export const CREATE_MATERIACREATE_MUTATION = gql`
mutation CreateOneMateriaCreate($input: CreateOneMateriaCreateInput!) {
  createOneMateriaCreate(input: $input) {
        id
        name
        materials
        pageLading
        slogan
        account
        pageId
        createId
  }
}
`;

export const DELETE_MATERIACREATE = gql`
  mutation deleteOneMateriaCreate($input: DeleteOneMateriaCreateInput!) {
    deleteOneMateriaCreate(input: $input) {
      id
    }
  }
`;

export const GET_MATERIALMANAGEMENT = gql`
query GetmaterialManagement(
  $paging: OffsetPaging
  $filter: materialManagementFilter
  $sorting: [materialManagementSort!]
) {
  materialManagements(
    paging: $paging
    filter: $filter
    sorting: $sorting
  ) {
    nodes {
      id
      fileName
      tags
      group
      adCount
      activeAdCount
      bannedCount
      spend
      conversion
      commission
      file
      notes
      facebookImageId
      facebookVedioId
      tenantId
      updatedAt
    }
    totalCount  
  }
}
`;

export const DELETE_MATERIALMANAGEMENT = gql`
  mutation deleteOneMaterialManagement($input: DeleteOneMaterialManagementInput!) {
    deleteOneMaterialManagement(input: $input) {
      id
    }
  }
`;

export const CREATE_MANY_MATERIALMANAGEMENT_MUTATION = gql`
  mutation CreateManyMaterialManagements($input: CreateManyMaterialManagementsInput!) {
    createManyMaterialManagements(input: $input) {
      id
      tenantId
      fileName
      tags
      notes
      file
    }
  }
`;