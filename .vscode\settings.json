{"deepscan.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "typescript"], "eslint.workingDirectories": [{"mode": "auto"}], "cSpell.words": ["codegen", "cqrs", "microservices", "<PERSON><PERSON><PERSON>", "nestjsx", "postgres", "QLISO", "typeorm"], "files.exclude": {"**/dist": true, "**/.build": true}, "git.ignoreLimitWarning": true, "i18n-ally.localesPaths": ["slash-admin/src/locales", "slash-admin/src/locales/lang"]}