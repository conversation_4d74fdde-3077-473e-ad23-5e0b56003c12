import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';

// 代理地址可根据实际情况修改
const proxyUrl = process.env.AXIOS_PROXY;
const isDev = process.env.NODE_ENV === 'development';

const httpsAgent = isDev ? new HttpsProxyAgent(proxyUrl) : undefined;

export async function axiosProxy<T = any>(
    config: AxiosRequestConfig
): Promise<AxiosResponse<T>> {
    // 仅开发环境合并 httpsAgent
    const mergedConfig: AxiosRequestConfig = isDev
        ? { ...config, httpsAgent }
        : { ...config };
    return axios(mergedConfig);
}

// 便捷方法
export const axiosProxyGet = <T = any>(url: string, config?: AxiosRequestConfig) =>
    axiosProxy<T>({ ...config, url, method: 'get' });

export const axiosProxyPost = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    axiosProxy<T>({ ...config, url, data, method: 'post' });

export const axiosProxyPut = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    axiosProxy<T>({ ...config, url, data, method: 'put' });

export const axiosProxyDelete = <T = any>(url: string, config?: AxiosRequestConfig) =>
    axiosProxy<T>({ ...config, url, method: 'delete' }); 