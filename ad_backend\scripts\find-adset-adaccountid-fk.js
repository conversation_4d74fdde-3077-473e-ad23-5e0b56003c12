const { Client } = require('pg');

const client = new Client({
  host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
  port: 5432,
  user: 'yeeu',
  password: 'Yy&********',
  database: 'ad_auto',
});

async function main() {
  await client.connect();

  // 查找 ad_set.adAccountId 所有关联的外键及其目标表和字段
  const result = await client.query(`
    SELECT
      conname,
      confrelid::regclass AS referenced_table,
      a.attname AS referenced_column,
      pg_get_constraintdef(pg_constraint.oid) AS constraint_def
    FROM
      pg_constraint
      JOIN pg_class ON conrelid = pg_class.oid
      JOIN pg_attribute a ON a.attrelid = confrelid AND a.attnum = ANY(confkey)
    WHERE
      conrelid = 'ad_set'::regclass
      AND contype = 'f'
      AND pg_get_constraintdef(pg_constraint.oid) LIKE '%adAccountId%';
  `);
  console.log('ad_set.adAccountId 相关外键信息:');
  result.rows.forEach(row => console.log(row));

  await client.end();
}

main().catch(e => {
  console.error(e);
  process.exit(1);
}); 