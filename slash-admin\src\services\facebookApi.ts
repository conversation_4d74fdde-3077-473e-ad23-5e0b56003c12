import { useTranslation } from 'react-i18next';
// Facebook Marketing API服务 - 通过后端API调用
export interface FacebookAccountData {
    accountId: string;
    accountName: string;
    accountStatus: 'ACTIVE' | 'DISABLED' | 'PAUSED' | 'PENDING_REVIEW' | 'DISAPPROVED';
    spend: number;
    impressions: number;
    clicks: number;
    ctr: number;
    cpm: number;
    cpc: number;
    currency: string;
    lastUpdated: string;
}

export interface FacebookInsightsData {
    spend: number;
    impressions: number;
    clicks: number;
    ctr: number;
    cpm: number;
    cpc: number;
    date_start: string;
    date_stop: string;
}

// 广告账户OAuth信息接口
export interface AdAccountOAuth {
    accountId: string;
    accessToken: string;
}

export class FacebookApiService {
    private readonly BACKEND_API_BASE = '/api/ad-platform/facebook';

    constructor() {
        // 使用后端API，不需要直接调用Facebook API
    }

    // 获取多个广告账户的完整数据（通过后端API调用）
    async getBatchAccountData(accountOAuths: AdAccountOAuth[]): Promise<FacebookAccountData[]> {
        const { t } = useTranslation();
        try {
            console.log('📡 调用后端API获取Facebook数据...');
            console.log('请求参数:', { accountOAuths });

            const response = await fetch(`${this.BACKEND_API_BASE}/vcc/sync-single`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ accountOAuths })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('后端API响应错误:', errorData);
                throw new Error(`后端API错误: ${response.status} - ${errorData.message || 'Unknown error'}`);
            }

            const result = await response.json();
            console.log('✅ 后端API响应成功:', result);

            if (!result.success) {
                throw new Error(result.message || t('pages.key_488'));
            }

            // 返回详细数据
            return result.data.detailData || [];

        } catch (error: any) {
            console.error('❌ 调用后端Facebook API失败:', error);

            // 根据错误类型提供更详细的错误信息
            if (error.message.includes('Failed to fetch')) {
                throw new Error('网络连接失败，请检查后端服务是否正常运行');
            } else if (error.message.includes('access_token')) {
                throw new Error('Facebook访问令牌无效，请重新授权广告账户');
            } else if (error.message.includes('permission')) {
                throw new Error(t('pages.key_1321'));
            } else if (error.message.includes('rate limit')) {
                throw new Error('API调用频率过高，请稍后再试');
            } else {
                throw new Error(`Facebook数据同步失败: ${error.message}`);
            }
        }
    }

    // 批量获取VCC卡片的Facebook数据
    async getBatchVccData(vccCards: Array<{
        vccCardId: string;
        accountOAuths: AdAccountOAuth[];
    }>): Promise<Array<{
        vccCardId: string;
        success: boolean;
        data?: any;
        error?: string;
    }>> {
        const { t } = useTranslation();
        try {
            console.log('📡 调用后端API批量获取VCC Facebook数据...');
            console.log('请求参数:', { vccCards });

            const response = await fetch(`${this.BACKEND_API_BASE}/vcc/sync-batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ vccCards })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('后端API响应错误:', errorData);
                throw new Error(`后端API错误: ${response.status} - ${errorData.message || 'Unknown error'}`);
            }

            const result = await response.json();
            console.log('✅ 后端API批量响应成功:', result);

            if (!result.success) {
                throw new Error(result.message || t('pages.key_488'));
            }

            return result.results || [];

        } catch (error: any) {
            console.error('❌ 调用后端Facebook批量API失败:', error);
            throw new Error(`批量Facebook数据同步失败: ${error.message}`);
        }
    }

    // 验证访问令牌是否有效（通过后端验证）
    async validateAccessToken(accessToken: string): Promise<boolean> {
        const { t } = useTranslation();
        try {
            // 暂时使用简单的验证逻辑，实际应该调用后端API
            return !!(accessToken && accessToken.length > 0);
        } catch {
            return false;
        }
    }

    // 获取用户有权限的广告账户列表（通过后端调用）
    async getUserAdAccounts(accessToken: string): Promise<Array<{
        id: string;
        name: string;
        account_status: number;
    }>> {
        const { t } = useTranslation();
        try {
            const response = await fetch(`${this.BACKEND_API_BASE}/accounts?accessToken=${encodeURIComponent(accessToken)}`);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`获取广告账户列表失败: ${errorData.message || 'Unknown error'}`);
            }

            const result = await response.json();
            return result.accounts || [];
        } catch (error: any) {
            console.error('获取Facebook广告账户列表失败:', error);
            throw new Error(`获取广告账户列表失败: ${error.message}`);
        }
    }

    // 获取聚合数据的便捷方法
    async getAggregatedData(accountOAuths: AdAccountOAuth[]): Promise<{
        totalSpend: number;
        totalImpressions: number;
        totalClicks: number;
        activeAccounts: number;
        totalAccounts: number;
        accountStatuses: { [key: string]: string };
        detailData: FacebookAccountData[];
    }> {
        const { t } = useTranslation();
        try {
            console.log('📊 获取聚合Facebook数据...');

            const response = await fetch(`${this.BACKEND_API_BASE}/vcc/sync-single`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ accountOAuths })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`后端API错误: ${response.status} - ${errorData.message || 'Unknown error'}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || t('pages.key_1996'));
            }

            console.log('✅ 获取聚合数据成功:', result.data);
            return result.data;

        } catch (error: any) {
            console.error('❌ 获取聚合Facebook数据失败:', error);
            throw error;
        }
    }
} 