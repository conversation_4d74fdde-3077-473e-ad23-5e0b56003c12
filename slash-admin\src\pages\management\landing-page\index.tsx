import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from "react";
import { Card, Tabs, Table, Button, Input, Space, Popconfirm, Tag, message, Avatar, Modal, Form, Select, Switch, Tooltip } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, CopyOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import LandingPageModal, { LandingPageFormValues } from "./LandingPageModal";
import { useQuery, useMutation } from '@apollo/client';
import { GET_LANDING_PAGES, CREATE_LANDING_PAGE, UPDATE_LANDING_PAGE, DELETE_LANDING_PAGE, GET_PROMOTIONS, CREATE_PROMOTION, UPDATE_PROMOTION, DELETE_PROMOTION, GET_DOMAINS, ADD_DOMAIN, DELETE_DOMAIN, REFRESH_DOMAIN } from './graphql';
import { GET_ANTIBLOCKS } from '@/api/antiBlock';
import axios from "axios"
interface LandingPageItem extends LandingPageFormValues {
  id: string;
  code: string;
  status: "active" | "inactive";
  createdAt: string;
  creator: string;
  domain: string;
  url: string;
  tenantId: string;
}

// 推广数据类型
interface PromotionItem {
  id: string;
  code: string;
  platform: string;
  landingPageName: string;
  landingPageUrl: string;
  status: "enabled" | "disabled";
  createdAt: string;
  creator: string;
  tenantId: string;
}

const promotionData: PromotionItem[] = [
  {
    id: "p1",
    code: "CH001",
    platform: "Facebook",
    landingPageName: '渠道A',
    landingPageUrl: "https://promo.example.com",
    status: "enabled",
    createdAt: "2025-06-01",
    creator: '张三',
    tenantId: "tenant1",
  },
  {
    id: "p2",
    code: "CH002",
    platform: "Google",
    landingPageName: '渠道B',
    landingPageUrl: "https://new.example.com",
    status: "disabled",
    createdAt: "2025-06-02",
    creator: '李四',
    tenantId: "tenant1",
  },
];

function PromotionModal({ open, loading, initialValues, onCancel, onOk, landingPages, domains }: any) {
  const [form] = Form.useForm();
  const [platform, setPlatform] = useState<string | undefined>(initialValues?.platform);
  const [selectedLandingCode, setSelectedLandingCode] = useState<string | undefined>(initialValues?.landingPageCode);
  const [selectedDomain, setSelectedDomain] = useState<string | undefined>(initialValues?.appDomain);
  const { t } = useTranslation();
  // 自动拼接落地页地址
  const updateLandingPageUrl = () => {
    const values = form.getFieldsValue();
    const lp = landingPages.find((l: any) => l.code === values.landingPageCode);
    if (!lp) {
      form.setFieldsValue({ landingPageUrl: '' });
      return;
    }
    let params: Record<string, string> = {};
    if (values.platform) params.platform = values.platform;
    if (values.platformParams?.access_token) params.access_token = values.platformParams.access_token;
    // pixel_id/conversion_id
    if (values.platform === 'Google' && values.platformParams?.conversion_id) {
      params.pixel_id = values.platformParams.conversion_id;
    } else if ((values.platform === 'Facebook' || values.platform === 'Instagram' || values.platform === 'TikTok') && values.platformParams?.pixel_id) {
      params.pixel_id = values.platformParams.pixel_id;
    }
    // 新增：渠道 code 参数
    if (values.code) params.code = values.code;
    // 新增：tenantId 参数
    if (lp.tenantId) params.tenantId = lp.tenantId;
    // 新增：app_domain 参数
    if (values.appDomain) params.app_domain = values.appDomain;
    const paramStr = Object.keys(params).map(k => `${k}=${encodeURIComponent(params[k])}`).join('&');
    const url = paramStr ? `${lp.url}?${paramStr}` : lp.url;
    form.setFieldsValue({ landingPageUrl: "https://" + url });
  };

  useEffect(() => {
    if (open) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue(initialValues);
        setPlatform(initialValues.platform);
        setSelectedLandingCode(initialValues.landingPageCode);
        setSelectedDomain(initialValues.appDomain);
      } else {
        setPlatform(undefined);
        setSelectedLandingCode(undefined);
        setSelectedDomain(undefined);
        form.setFieldsValue({ status: "enabled" });
      }
    }
  }, [open, initialValues, form]);

  // 仅用onValuesChange监听表单字段变化

  const handleLandingChange = (code: string) => {
    setSelectedLandingCode(code);
    updateLandingPageUrl();
  };
  const handlePlatformChange = (val: string) => {
    setPlatform(val);
    updateLandingPageUrl();
  };
  const handleDomainChange = (val: string) => {
    setSelectedDomain(val);
    updateLandingPageUrl();
  };
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const lp = landingPages.find((l: any) => l.code === values.landingPageCode);
      onOk({ ...values, landingPageName: lp?.title || "", landingPageUrl: values.landingPageUrl });
    } catch { }
  };
  // 平台初始化参数渲染
  const renderPlatformFields = () => {
    if (platform === "Facebook" || platform === "Instagram") {
      return <>
        <Form.Item name={["platformParams", "access_token"]} label={t('pages.key_1859')} rules={[{ required: true, message: `请输入${platform} access_token` }]}><Input placeholder={`请输入${platform} access_token`} /></Form.Item>
        <Form.Item name={["platformParams", "pixel_id"]} label={t('pages.key_1150')} rules={[{ required: true, message: `请输入${platform} Pixel ID` }]}><Input placeholder={`请输入${platform} Pixel ID`} /></Form.Item>
      </>;
    }
    if (platform === "TikTok") {
      return <>
        <Form.Item name={["platformParams", "access_token"]} label={t('pages.key_1327')} rules={[{ required: true, message: "请输入 TikTok access_token" }]}><Input placeholder="请输入 TikTok access_token" /></Form.Item>
        <Form.Item name={["platformParams", "pixel_id"]} label={t('pages.key_1327')} rules={[{ required: true, message: "请输入 TikTok Pixel ID" }]}><Input placeholder="请输入 TikTok Pixel ID" /></Form.Item>
      </>;
    }
    if (platform === "Google") {
      return <Form.Item name={["platformParams", "conversion_id"]} label={t('pages.key_1327')} rules={[{ required: true, message: "请输入 Google Conversion ID" }]}><Input placeholder="请输入 Google Conversion ID" /></Form.Item>;
    }
    return null;
  };
  return (
    <Modal
      title={initialValues?.id ? t('pages.key_1859') : t('pages.key_1150')}
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      confirmLoading={loading}
      destroyOnHidden
      maskClosable={false}
      width={500}
    >
      <Form form={form} layout="vertical" initialValues={initialValues} onValuesChange={updateLandingPageUrl}>
        <Form.Item name="code" label="渠道编码" rules={[{ required: true, message: "请输入渠道编码" }]}><Input placeholder="请输入渠道编码" /></Form.Item>
        <Form.Item name="appDomain" label="推广站点" rules={[{ required: true, message: "请选择推广站点" }]}>
          <Select
            showSearch
            placeholder="请选择推广站点"
            optionFilterProp="children"
            onChange={handleDomainChange}
            filterOption={(input, option) => {
              const child = option?.children;
              if (typeof child === 'string') {
                return (child as string).toLowerCase().includes(input.toLowerCase());
              }
              return false;
            }}
          >
            {domains.map((d: any) => <Select.Option key={d} value={d}>{d}</Select.Option>)}
          </Select>
        </Form.Item>
        <Form.Item name="landingPageCode" label="落地页" rules={[{ required: true, message: "请选择落地页" }]}>
          <Select
            showSearch
            placeholder={t('pages.key_2277')}
            optionFilterProp="children"
            onChange={handleLandingChange}
            filterOption={(input, option) => {
              const child = option?.children;
              if (typeof child === 'string') {
                return (child as string).toLowerCase().includes(input.toLowerCase());
              }
              return false;
            }}
          >
            {landingPages.map((lp: any) => <Select.Option key={lp.code} value={lp.code}>{lp.title}</Select.Option>)}
          </Select>
        </Form.Item>
        <Form.Item name="platform" label={t('pages.key_1024')} rules={[{ required: true, message: t('pages.key_2269') }]}>
          <Select options={[
            { label: "Facebook", value: "Facebook" },
            { label: "Google", value: "Google" },
            { label: "Instagram", value: "Instagram" },
            { label: "TikTok", value: "TikTok" },
          ]} placeholder={t('pages.key_2269')} onChange={handlePlatformChange} />
        </Form.Item>
        {renderPlatformFields()}
        <Form.Item name="landingPageUrl" label={t('pages.key_2013')}>
          <Input disabled placeholder={t('pages.key_1905')} />
        </Form.Item>
        <Form.Item name="status" label={t('pages.key_1203')} valuePropName="checked" style={{ marginTop: 24 }}>
          <Switch checkedChildren={t('pages.key_496')} unCheckedChildren={t('pages.key_153')} defaultChecked={initialValues?.status !== "disabled"} />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default function LandingPageManagement() {
  const { t } = useTranslation();
  const [tabKey, setTabKey] = useState("landing");
  const [modalVisible, setModalVisible] = useState(false);
  const [editing, setEditing] = useState<LandingPageItem | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [deleteLoadingId, setDeleteLoadingId] = useState<string | null>(null);

  // GraphQL hooks
  const { data, loading: queryLoading, refetch } = useQuery<{ getLandingPages: LandingPageItem[] }>(GET_LANDING_PAGES, { context: { operationName: 'getLandingPages' } });
  const [createLandingPage] = useMutation(CREATE_LANDING_PAGE, { context: { operationName: 'createLandingPage' } });
  const [updateLandingPage] = useMutation(UPDATE_LANDING_PAGE, { context: { operationName: 'updateLandingPage' } });
  const [deleteLandingPage] = useMutation(DELETE_LANDING_PAGE, { context: { operationName: 'deleteLandingPage' } });

  const landingPages = data?.getLandingPages || [];
  const filteredData = landingPages.filter(item =>
    item.title.includes(searchText) || item.url.includes(searchText)
  );

  const { data: domainsData } = useQuery(GET_DOMAINS);
  const activeDomains = (domainsData?.myDomains || []).filter((d: any) => d.sslStatus === 'active');

  // 新增/编辑弹窗
  const showModal = (record?: LandingPageItem) => {
    setEditing(record);
    setModalVisible(true);
  };

  // 删除
  const handleDelete = async (id: string) => {
    setDeleteLoadingId(id);
    try {
      await deleteLandingPage({ variables: { id } });
      message.success(t('pages.key_294'));
      refetch();
    } finally {
      setDeleteLoadingId(null);
    }
  };

  // 处理图片字段为url数组
  const extractUrls = (fileList: any[] = []) => fileList.map(f => f.url || f.response?.url || f.thumbUrl || f);

  // 新增/编辑提交
  const handleModalOk = async (values: LandingPageFormValues) => {
    setLoading(true);
    const safeArray = (arr: unknown): string[] => Array.isArray(arr) ? arr.map(String).filter(Boolean) : [];
    const input = {
      ...values,
      appIcon: safeArray(extractUrls(values.appIcon)),
      background: safeArray(extractUrls(values.background)),
      promoImages: safeArray(extractUrls(values.promoImages)),
    };
    try {
      if (editing) {
        await updateLandingPage({ variables: { id: editing.id, input } });
        message.success(t('pages.key_1858'));
      } else {
        await createLandingPage({ variables: input });
        message.success(t('pages.key_1149'));
      }
      setModalVisible(false);
      setEditing(undefined);
      refetch();
    } finally {
      setLoading(false);
    }
  };

  // 表格列
  const columns: ColumnsType<LandingPageItem> = [
    {
      title: t('pages.key_1852'),
      dataIndex: "code",
      key: "code",
      render: (text) => <span style={{ fontWeight: 500 }}>{text}</span>,
    },
    {
      title: t('pages.key_483'),
      dataIndex: "title",
      key: "title",
      render: (text, record) => <span style={{ fontWeight: 500 }}>{text}</span>,
    },
    {
      title: t('pages.key_2014'),
      dataIndex: "url",
      key: "url",
      render: (url: string, record) => <a href={url} target="_blank" rel="noopener noreferrer">{url}</a>,
    },
    {
      title: "App图标",
      dataIndex: "appIcon",
      key: "appIcon",
      render: (iconArr: any[]) => iconArr?.[0] ? <Avatar shape="square" size={40} src={iconArr[0]} /> : "-",
    },
    {
      title: t('pages.key_1026'),
      dataIndex: "slogan",
      key: "slogan",
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: t('pages.key_250'),
      dataIndex: "createdAt",
      key: "createdAt",
    },
    {
      title: t('pages.key_243'),
      dataIndex: "creator",
      key: "creator",
    },
    {
      title: t('pages.key_1058'),
      key: "action",
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EditOutlined />} onClick={() => showModal(record)}>{t('pages.key_1853')}</Button>
          <Popconfirm title="确定要删除吗？" onConfirm={() => handleDelete(record.id!)} okButtonProps={{ loading: deleteLoadingId === record.id }}>
            <Button type="link" danger icon={<DeleteOutlined />} disabled={deleteLoadingId === record.id}>{t('pages.key_287')}</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Tabs
        activeKey={tabKey}
        onChange={setTabKey}
        items={[
          {
            key: "promotion",
            label: t('pages.key_1019'),
            children: (
              <PromotionTab />
            ),
          },
          {
            key: "landing",
            label: t('pages.key_2009'),
            children: (
              <>
                <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between" }}>
                  <Button type="primary" icon={<PlusOutlined />} onClick={() => showModal()}>{t('pages.key_1154')}</Button>
                  <Input
                    style={{ width: 240 }}
                    allowClear
                    prefix={<SearchOutlined />}
                    placeholder={t('pages.key_1046')}
                    value={searchText}
                    onChange={e => setSearchText(e.target.value)}
                  />
                </div>
                <Table
                  rowKey="id"
                  columns={columns}
                  dataSource={filteredData}
                  loading={queryLoading}
                  pagination={{ pageSize: 10 }}
                />
                <LandingPageModal
                  open={modalVisible}
                  loading={loading}
                  initialValues={editing}
                  onCancel={() => { setModalVisible(false); setEditing(undefined); }}
                  onOk={handleModalOk}
                  activeDomains={activeDomains}
                />
              </>
            ),
          },
          {
            key: "domain",
            label: t('pages.key_555'),
            children: <DomainResolveTab />,
          },
        ]}
      />
    </Card>
  );
}

function PromotionTab() {
  const { t } = useTranslation();
  const { data: gqlData, loading: queryLoading, refetch } = useQuery(GET_PROMOTIONS, { context: { operationName: 'getPromotions' } });
  const landingPages = useQuery(GET_LANDING_PAGES, { context: { operationName: 'getLandingPages' } }).data?.getLandingPages || [];
  const [createPromotion] = useMutation(CREATE_PROMOTION, { context: { operationName: 'createPromotion' } });
  const [updatePromotion] = useMutation(UPDATE_PROMOTION, { context: { operationName: 'updatePromotion' } });
  const [deletePromotion] = useMutation(DELETE_PROMOTION, { context: { operationName: 'deletePromotion' } });
  const [modalVisible, setModalVisible] = useState(false);
  const [editing, setEditing] = useState<PromotionItem | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [deleteLoadingId, setDeleteLoadingId] = useState<string | null>(null);

  const { data: antiBlockData } = useQuery(GET_ANTIBLOCKS, {
    variables: { paging: { limit: 50, offset: 0 }, sorting: [{ field: 'createdAt', direction: 'DESC' }] },
  });
  const domains = Array.from(new Set((antiBlockData?.antiBlocks?.nodes || []).map((item: any) => item.relatedDomain).filter(Boolean)));

  const showModal = (record?: PromotionItem) => {
    setEditing(record);
    setModalVisible(true);
  };
  const handleDelete = async (id: string) => {
    setDeleteLoadingId(id);
    try {
      await deletePromotion({ variables: { id } });
      message.success(t('pages.key_294'));
      refetch();
    } finally {
      setDeleteLoadingId(null);
    }
  };
  const handleModalOk = async (values: any) => {
    setLoading(true);
    try {
      const input = {
        ...values,
        landingPageCode: values.landingPageCode,
        landingPageUrl: values.landingPageUrl,
        platform: values.platform,
        code: values.code,
        status: values.status,
        platformParams: values.platformParams,
      };
      delete input.landingPageName;
      if (editing) {
        await updatePromotion({ variables: { id: editing.id, input } });
        message.success(t('pages.key_1858'));
      } else {
        await createPromotion({ variables: { input } });
        message.success(t('pages.key_1149'));
      }
      setModalVisible(false);
      setEditing(undefined);
      refetch();
    } finally {
      setLoading(false);
    }
  };
  const columns: ColumnsType<PromotionItem> = [
    { title: t('pages.key_1565'), dataIndex: 'code', key: 'code' },
    { title: t('pages.key_1024'), dataIndex: 'platform', key: 'platform' },
    { title: t('pages.key_2012'), dataIndex: 'landingPageName', key: 'landingPageName' },
    {
      title: t('pages.key_2013'),
      dataIndex: 'landingPageUrl',
      key: 'landingPageUrl',
      render: (url: string) => {
        const short = url.length > 32 ? url.slice(0, 32) + '...' : url;
        return (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title={url} placement="topLeft">
              <a href={url} target="_blank" rel="noopener noreferrer" style={{ maxWidth: 220, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', display: 'inline-block', verticalAlign: 'middle' }}>{short}</a>
            </Tooltip>
            <Tooltip title={t('pages.key_590')}>
              <Button
                type="link"
                size="small"
                icon={<CopyOutlined />}
                style={{ marginLeft: 4, padding: 0 }}
                onClick={() => { navigator.clipboard.writeText(url); message.success(t('pages.key_738')); }}
              />
            </Tooltip>
          </span>
        );
      },
    },
    { title: t('pages.key_1602'), dataIndex: 'status', key: 'status', render: (status: string) => <Tag color={status === 'enabled' ? 'green' : 'gray'}>{status === 'enabled' ? t('pages.key_496') : t('pages.key_153')}</Tag> },
    { title: t('pages.key_250'), dataIndex: 'createdAt', key: 'createdAt' },
    { title: t('pages.key_243'), dataIndex: 'creator', key: 'creator' },
    {
      title: t('pages.key_1058'),
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EditOutlined />} onClick={() => showModal(record)}>{t('pages.key_1853')}</Button>
          <Popconfirm title="确定要删除吗？" onConfirm={() => handleDelete(record.id!)} okButtonProps={{ loading: deleteLoadingId === record.id }}>
            <Button type="link" danger icon={<DeleteOutlined />} disabled={deleteLoadingId === record.id}>{t('pages.key_287')}</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  return (
    <Card>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => showModal()}>{t('pages.key_1150')}</Button>
      </div>
      <Table rowKey="id" columns={columns} dataSource={gqlData?.getPromotions || []} loading={queryLoading} pagination={{ pageSize: 10 }} />
      <PromotionModal
        open={modalVisible}
        loading={loading}
        initialValues={editing}
        onCancel={() => { setModalVisible(false); setEditing(undefined); }}
        onOk={handleModalOk}
        landingPages={landingPages}
        domains={domains}
      />
    </Card>
  );
}

function DomainResolveTab() {
  const { t } = useTranslation();
  const { data, loading, refetch } = useQuery(GET_DOMAINS);
  const [addDomain] = useMutation(ADD_DOMAIN);
  const [deleteDomain] = useMutation(DELETE_DOMAIN);
  const [refreshDomain] = useMutation(REFRESH_DOMAIN);

  const [input, setInput] = React.useState('');
  const [adding, setAdding] = React.useState(false);

  const handleAdd = async () => {
    if (!input.trim()) return;
    setAdding(true);
    try {
      await addDomain({ variables: { name: input.trim() } });
      setInput('');
      refetch();
    } finally {
      setAdding(false);
    }
  };

  const handleDelete = async (id: string) => {
    await deleteDomain({ variables: { id } });
    refetch();
  };

  const handleRefresh = async (id: string) => {
    await refreshDomain({ variables: { id } });
    refetch();
  };

  const domains = data?.myDomains || [];

  const columns = [
    { title: t('pages.key_551'), dataIndex: 'name', key: 'name' },
    { title: 'CNAME目标', dataIndex: 'cnameTarget', key: 'cnameTarget', render: (text: string) => <span style={{ fontFamily: 'monospace' }}>{text}</span> },
    { title: t('pages.key_1602'), dataIndex: 'status', key: 'status', render: (text: string) => text === 'active' ? <Tag color="green">{t('pages.key_746')}</Tag> : <Tag color="orange">{t('pages.key_895')}</Tag> },
    { title: 'SSL证书', dataIndex: 'sslStatus', key: 'sslStatus', render: (text: string) => text === 'active' ? <Tag color="green">{t('pages.key_748')}</Tag> : <Tag color="orange">{t('pages.key_1760')}</Tag> },
    { title: t('pages.key_250'), dataIndex: 'createdAt', key: 'createdAt' },
    {
      title: t('pages.key_1058'), key: 'action', render: (_: any, record: any) => (
        <Space>
          <Button size="small" onClick={() => handleRefresh(record.id)} loading={loading}>{t('pages.key_310')}</Button>
          <Popconfirm title="确定要删除该域名吗？" onConfirm={() => handleDelete(record.id)}>
            <Button size="small" danger>{t('pages.key_287')}</Button>
          </Popconfirm>
        </Space>
      )
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center', gap: 12 }}>
        <Input
          style={{ width: 260 }}
          placeholder={t('pages.key_2231')}
          value={input}
          onChange={e => setInput(e.target.value)}
          onPressEnter={handleAdd}
          disabled={adding}
        />
        <Button type="primary" onClick={handleAdd} loading={adding}>添加域名</Button>
        <span style={{ color: '#888', marginLeft: 24 }}>
          请将您的域名 <b>CNAME</b> 到 <span style={{ fontFamily: 'monospace', color: '#555' }}>pwa.fb333.com</span>
        </span>
      </div>
      <Table
        rowKey="id"
        columns={columns}
        dataSource={domains}
        loading={loading}
        pagination={false}
        bordered
      />
      <div style={{ marginTop: 24, color: '#888', fontSize: 13 }}>
        <b>说明：</b> 域名添加后请在域名服务商处设置CNAME解析到 < span style={{ fontFamily: 'monospace', color: '#555' }
        }> pwa.fb333.com</span >，SSL证书会自动签发。生效后访问该域名将自动跳转到主站点。
      </div >
    </div >
  );
}
