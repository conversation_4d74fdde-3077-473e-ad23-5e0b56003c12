import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';
import { AdAccount } from './ad-account.entity';
import { ObjectType, Field, ID } from '@nestjs/graphql';

@Entity('pixel')
export class Pixel {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '像素ID（Facebook Pixel ID）' })
  @Index()
  @Column({ length: 64 })
  pixelId: string;

  @ApiProperty({ description: '像素名称' })
  @Column({ length: 128 })
  name: string;

  @ApiProperty({ description: '所属广告账户' })
  @ManyToOne(() => AdAccount, { nullable: false })
  adAccount: AdAccount;

  @ApiProperty({ description: '租户ID' })
  @Index()
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始像素数据（JSON）' })
  @Column({ type: 'jsonb', nullable: true })
  raw: any;

  @ApiProperty({ description: '像素状态' })
  @Column({ length: 32, default: 'active' })
  status: string;
}

@ObjectType('Pixel')
export class PixelDTO {
  @Field(() => ID)
  id: string;
  @Field()
  pixelId: string;
  @Field()
  name: string;
}
