import request from '@/utils/request';

/**
 * 获取广告系列下广告账户的 Pixel 列表
 * @param campaignId 广告系列ID
 * @returns [{ label, value }]
 */
export async function getPixelsByAccount({ campaignId }: { campaignId: string }) {
    if (!campaignId) return [];
    const res = await request.get('/ad-platform/facebook/pixels', {
        params: { campaignId }
    });
    // 返回格式：{ pixels: [{ id, name, ... }] }
    return (res.data.pixels || []).map((p: any) => ({
        label: p.name,
        value: p.id,
    }));
} 