{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    // "noEmit": false,
    "incremental": true,
    "tsBuildInfoFile": "./tsconfig.tsbuildinfo",
    "allowJs": false,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "strict": false,
    "strictPropertyInitialization": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": true
  },
  "exclude": ["node_modules", "dist", ".build"],
  "paths": {
    "@db-config/*": [
      "src/config/*"
    ],
    "@modules/*":[
      "src/modules/*"
    ],
    "@entities/*": [
      "src/entity/*"
    ]
  }
}
