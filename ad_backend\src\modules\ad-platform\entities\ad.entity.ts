import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';
import { AdAccount } from './ad-account.entity';
import { Campaign } from './campaign.entity';
import { AdCreative } from './ad-creative.entity';

@Entity('ad')
export class Ad {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '平台类型（facebook/google/tiktok/adjust/af等）' })
  @Column({ length: 32 })
  platform: string;

  @ApiProperty({ description: '广告平台Ad ID' })
  @Index()
  @Column({ length: 64 })
  adId: string;

  @ApiProperty({ description: '广告名称' })
  @Column({ length: 128 })
  name: string;

  @ApiProperty({ description: '所属广告账户' })
  @ManyToOne(() => AdAccount, { nullable: false })
  adAccount: AdAccount;

  @ApiProperty({ description: '所属广告系列' })
  @ManyToOne(() => Campaign, { nullable: false })
  adCampaign: Campaign;

  @ApiProperty({ description: '所属创意' })
  @ManyToOne(() => AdCreative, { nullable: true })
  adCreative?: AdCreative;

  @ApiProperty({ description: '租户ID' })
  @Index()
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始ad数据（JSON）' })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;

  @ApiProperty({ description: '广告状态' })
  @Column({ length: 32, default: 'active' })
  status: string;

  @ApiProperty({ description: '广告洞察（JSON）', required: false })
  @Column({ type: 'jsonb', nullable: true })
  insight?: any;

  @ApiProperty({ description: '广告备注', required: false })
  @Column({ type: 'text', nullable: true })
  remark?: string;

  @Column({ type: 'float', nullable: true })
  bid_amount?: number;

  @Column({ type: 'varchar', length: 32, nullable: true })
  effective_status?: string;

  @Column({ type: 'varchar', length: 32, nullable: true })
  configured_status?: string;

  @Column({ type: 'timestamp', nullable: true })
  updated_time?: Date;

  @ApiProperty({ description: '所属广告组AdSet ID' })
  @Column({ name: 'adset_id', length: 64, nullable: true })
  adsetId?: string;
}
