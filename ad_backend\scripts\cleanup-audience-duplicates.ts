import { AppDataSource } from '../src/config/data-source';
import { Audience } from '../src/modules/ad-platform/entities/audience.entity';

async function truncateAudienceTable() {
    await AppDataSource.initialize();
    const repo = AppDataSource.getRepository(Audience);
    // TRUNCATE TABLE audience
    await repo.query('TRUNCATE TABLE audience RESTART IDENTITY CASCADE;');
    console.log('Audience table has been truncated (all data deleted).');
    await AppDataSource.destroy();
}

truncateAudienceTable().catch(e => {
    console.error(e);
    process.exit(1);
}); 