import { useCallback, useEffect } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { Navigate } from "react-router";

import { useUserToken } from "@/store/userStore";

import PageError from "@/pages/sys/error/PageError";
import { useRouter } from "../hooks";

type Props = {
	children: React.ReactNode;
};
export default function ProtectedRoute({ children }: Props) {
	const router = useRouter();
	const { accessToken } = useUserToken();

	if (!accessToken) {
		return <Navigate to="/login" replace />;
	}

	return <ErrorBoundary FallbackComponent={PageError}>{children}</ErrorBoundary>;
}
