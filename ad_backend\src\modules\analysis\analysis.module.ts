import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalysisService } from './analysis.service';
import { AnalysisResolver } from './analysis.resolver';
import { AnalysisController } from './analysis.controller';
import { Analysis } from './analysis.entity';
import { Report } from '../ad-platform/report/report.entity';
import { AdAccount } from '../ad-platform/entities/ad-account.entity';
import { FacebookApiService } from '../ad-platform/services/facebook-api.service';
import { materialManagement } from '../material-management/materialManagement.entity';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    TypeOrmModule.forFeature([Analysis, Report, AdAccount, materialManagement]),
    AuthModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET') || 'defaultSecretKey',
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN') || '24h',
        },
      }),
    }),
  ],
  controllers: [AnalysisController],
  providers: [AnalysisService, AnalysisResolver, FacebookApiService],
  exports: [AnalysisService],
})
export class AnalysisModule { } 