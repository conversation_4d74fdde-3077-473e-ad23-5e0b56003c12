import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStartTimeEndTimeToAdSet20240613000300 implements MigrationInterface {
    name = 'AddStartTimeEndTimeToAdSet20240613000300'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set"
            ADD COLUMN "start_time" timestamp NOT NULL DEFAULT now(),
            ADD COLUMN "end_time" timestamp NOT NULL DEFAULT now()
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set"
            DROP COLUMN "start_time",
            DROP COLUMN "end_time"
        `);
    }
} 