import { MigrationInterface, QueryRunner } from "typeorm";

export class FixInterestsBehaviorsJsonb20240611190000 implements MigrationInterface {
    name = 'FixInterestsBehaviorsJsonb20240611190000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "interests" TYPE jsonb USING to_jsonb(interests)`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "behaviors" TYPE jsonb USING to_jsonb(behaviors)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "interests" TYPE text[] USING interests::text[]`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "behaviors" TYPE text[] USING behaviors::text[]`);
    }
} 