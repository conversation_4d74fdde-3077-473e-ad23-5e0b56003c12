import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMissingFieldsToAdAccount20240529000500 implements MigrationInterface {
    name = 'AddMissingFieldsToAdAccount20240529000500'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "tag" character varying(128);`);
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "channel" character varying(128);`);
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "holder" character varying(128);`);
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "adNumber" character varying(128);`);
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "remark" text;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "tag";`);
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "channel";`);
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "holder";`);
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "adNumber";`);
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "remark";`);
    }
} 