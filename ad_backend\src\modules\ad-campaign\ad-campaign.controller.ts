import { BadRequestException, Req } from '@nestjs/common';
import { Controller } from '@nestjs/common';
import { AdCampaignService } from './ad-campaign.service';

@Controller('api/ad-campaigns')
export class AdCampaignController {
    constructor(private readonly service: AdCampaignService) { }
    async getMany(@Req() req) {
        if (!req.query.tenantId) throw new BadRequestException('tenantId required');
        return this.service.find({ where: { tenant: { id: req.query.tenantId } } });
    }
} 