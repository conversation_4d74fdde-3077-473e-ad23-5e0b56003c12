import request from '@/utils/request';
import { ReportParams, SaasReportParams, ExportReportParams } from '../types/api';

export function fetchTenantReport({ tenantId, type, date, page, pageSize }: ReportParams) {
  return request.get('/ad-platform/report/tenant', { params: { tenantId, type, date, page, pageSize } });
}

export function fetchSaasReport({ type, date, page, pageSize }: SaasReportParams) {
  return request.get('/ad-platform/report/saas', { params: { type, date, page, pageSize } });
}

export function exportReport({ tenantId, type, date, limit }: ExportReportParams) {
  return request.get('/ad-platform/report/export', { params: { tenantId, type, date, limit } });
}
