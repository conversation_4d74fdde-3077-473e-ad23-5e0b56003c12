import { APP_GUARD } from '@nestjs/core';
import { NestModule, MiddlewareConsumer, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthMiddleware } from './auth.middleware';
import { AuthGuard } from './auth.guard';
import { TenantApiKeyService } from './tenant-api-key.service';
import { TenantApiKey } from './../../entity';
import { UserModule } from '../user/user.module';
import { AuthService } from './auth.service';
import { AuthResolver } from './auth.resolver';
import { JwtAuthGuard } from './jwt-auth.guard';
import { Reflector } from '@nestjs/core';

@Module({
	imports: [
		TypeOrmModule.forFeature([TenantApiKey]),
		JwtModule.registerAsync({
			imports: [ConfigModule],
			inject: [ConfigService],
			useFactory: async (configService: ConfigService) => ({
				secret: configService.get('JWT_SECRET') || 'defaultSecretKey',
				signOptions: {
					expiresIn: configService.get('JWT_EXPIRES_IN') || '24h',
				},
			}),
		}),
		UserModule,
	],
	controllers: [],
	providers: [
		AuthMiddleware,
		TenantApiKeyService,
		AuthService,
		AuthResolver,
		{
			provide: JwtAuthGuard,
			useFactory: (jwtService: JwtService, reflector: Reflector, configService: ConfigService) => {
				return new JwtAuthGuard(jwtService, reflector, configService);
			},
			inject: [JwtService, Reflector, ConfigService]
		},
		// This will lock all routes and make them accessible by authenticated users only.
		{
			provide: APP_GUARD,
			useClass: AuthGuard,
		},
	],
	exports: [TypeOrmModule, TenantApiKeyService, AuthService, JwtModule, JwtAuthGuard],
})
export class AuthModule implements NestModule {
	configure(consumer: MiddlewareConsumer): void {
		consumer.apply(AuthMiddleware).forRoutes('*');
	}
}
