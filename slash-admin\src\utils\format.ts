/**
 * 日期格式化工具函数
 */

/**
 * 格式化日期为字符串
 * @param dateStr 日期字符串或Date对象
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateStr?: string | Date | null, format = 'YYYY-MM-DD HH:mm:ss'): string {
    if (!dateStr) return '-';

    const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
        return '-';
    }

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();

    // 补零函数
    const pad = (num: number): string => (num < 10 ? `0${num}` : `${num}`);

    return format
        .replace('YYYY', `${year}`)
        .replace('MM', pad(month))
        .replace('DD', pad(day))
        .replace('HH', pad(hours))
        .replace('mm', pad(minutes))
        .replace('ss', pad(seconds));
}

/**
 * 格式化相对时间（如：几分钟前，几小时前等）
 * @param dateStr 日期字符串或Date对象
 * @returns 相对时间字符串
 */
export function formatRelativeTime(dateStr?: string | Date | null): string {
    if (!dateStr) return '-';

    const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
        return '-';
    }

    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // 转换为秒
    const seconds = Math.floor(diff / 1000);

    // 小于1分钟
    if (seconds < 60) {
        return `${seconds}秒前`;
    }

    // 小于1小时
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) {
        return `${minutes}分钟前`;
    }

    // 小于1天
    const hours = Math.floor(minutes / 60);
    if (hours < 24) {
        return `${hours}小时前`;
    }

    // 小于30天
    const days = Math.floor(hours / 24);
    if (days < 30) {
        return `${days}天前`;
    }

    // 小于12个月
    const months = Math.floor(days / 30);
    if (months < 12) {
        return `${months}个月前`;
    }

    // 大于等于12个月
    const years = Math.floor(months / 12);
    return `${years}年前`;
} 