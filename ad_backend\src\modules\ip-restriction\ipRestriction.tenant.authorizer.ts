import { Authorizer, AuthorizerContext } from '@ptc-org/nestjs-query-graphql';
import { Injectable } from '@nestjs/common';
import { ipRestrictionDTO } from './dto/ipRestriction.dto';

@Injectable()
export class IpRestrictionTenantAuthorizer implements Authorizer<ipRestrictionDTO> {
    authorize(): Promise<any> { return Promise.resolve(true); }
    authorizeRelation(): Promise<any> { return Promise.resolve(true); }
    async authorizeFilter(context: AuthorizerContext<ipRestrictionDTO>): Promise<any> {
        const req = (context as any).req;
        const user = req?.user;
        const tenantId = req?.tenantId;
        const isSuperAdmin = req?.isSuperAdmin;
        if (!user) return { id: null };
        if (isSuperAdmin) return {};
        return { tenantId };
    }
} 