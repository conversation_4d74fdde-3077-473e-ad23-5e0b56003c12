import { MigrationInterface, QueryRunner } from "typeorm";

export class DropAudienceTable20240608161000 implements MigrationInterface {
    name = 'DropAudienceTable20240608161000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS "audience" CASCADE;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 不做回滚，需用建表 migration 重新建表
    }
} 