name: Build and Push Docker Image to Aliyun Container Registry

on:
  push:
    branches:
      - dev

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    permissions:
      contents: read

    steps:
      # 检出代码
      - name: Checkout code
        uses: actions/checkout@v3

      # 登录阿里云镜像仓库
      - name: Log in to Aliyun Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_USERNAME }}
          password: ${{ secrets.ALIYUN_PASSWORD }}

      # 构建并推送 Docker 镜像到阿里云
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: ./ad_backend
          file: ./ad_backend/Dockerfile
          push: true
          tags: |
            ${{ secrets.ALIYUN_REGISTRY }}/${{ secrets.ALIYUN_NAMESPACE }}/ad-backend:${{ github.sha }}