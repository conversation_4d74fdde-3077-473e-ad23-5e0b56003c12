import { gql, useQuery, useMutation } from '@apollo/client';

// 受众列表查询（游标分页）
export const GET_AUDIENCE_LIST = gql`
  query GetAudienceList($filter: AudienceCustomFilter) {
    audiences(filter: $filter) {
      id
      name
      geo_locations { countries regions { id name } cities { id name } }
      excluded_geo_locations { countries regions { id name } cities { id name } }
      locales
      gender
      age_min
      age_max
      interests { id name }
      behaviors { id name }
      notes
    }
  }
`;

// 新增受众
export const CREATE_AUDIENCE = gql`
  mutation CreateAudience($input: CreateAudience!) {
    createAudience(input: $input) {
      id
      name
      geo_locations { countries regions { id name } cities { id name } }
      excluded_geo_locations { countries regions { id name } cities { id name } }
      locales
      gender
      age_min
      age_max
      interests { id name }
      behaviors { id name }
      notes
    }
  }
`;

// 编辑受众
export const UPDATE_AUDIENCE = gql`
  mutation UpdateAudience($id: ID!, $input: CreateAudience!) {
    updateAudience(id: $id, input: $input) {
      id
      name
      geo_locations { countries regions { id name } cities { id name } }
      excluded_geo_locations { countries regions { id name } cities { id name } }
      locales
      gender
      age_min
      age_max
      interests { id name }
      behaviors { id name }
      notes
    }
  }
`;

// 删除受众
export const DELETE_AUDIENCE = gql`
  mutation DeleteAudience($id: ID!) {
    deleteAudience(id: $id)
  }
`;

// 通用受众列表 hooks，支持 platform 参数
export function useAudienceList(rawVariables: Record<string, any> & { platform?: string }) {
  const variables: Record<string, any> = {};
  if (rawVariables) {
    // 允许 filter 透传所有字段
    if (rawVariables.filter) {
      variables.filter = rawVariables.filter;
    }
    // 自动注入 platform 过滤（如有需要可扩展）
    // if (rawVariables.platform) {
    //   variables.filter = { ...variables.filter, platform: { eq: rawVariables.platform } };
    // }
    if (rawVariables.paging && (rawVariables.paging.first || rawVariables.paging.after)) {
      variables.paging = rawVariables.paging;
    }
    if (rawVariables.sorting && rawVariables.sorting.length) {
      variables.sorting = rawVariables.sorting;
    }
  }
  return useQuery(GET_AUDIENCE_LIST, {
    variables,
    fetchPolicy: 'network-only',
  });
}

// 封装hooks
export function useCreateAudience() {
  return useMutation(CREATE_AUDIENCE);
}
export function useUpdateAudience() {
  return useMutation(UPDATE_AUDIENCE);
}
export function useDeleteAudience() {
  return useMutation(DELETE_AUDIENCE);
}

// 兴趣选项
export const GET_AUDIENCE_INTEREST_OPTIONS = gql`
  query AudienceInterestOptions {
    audienceInterestOptions { id name }
  }
`;
export function useAudienceInterestOptions() {
  return useQuery(GET_AUDIENCE_INTEREST_OPTIONS, { fetchPolicy: 'network-only' });
}

// 行为选项
export const GET_AUDIENCE_BEHAVIOR_OPTIONS = gql`
  query AudienceBehaviorOptions {
    audienceBehaviorOptions { id name }
  }
`;
export function useAudienceBehaviorOptions() {
  return useQuery(GET_AUDIENCE_BEHAVIOR_OPTIONS, { fetchPolicy: 'network-only' });
}

// 自定义受众选项
export const GET_AUDIENCE_CUSTOM_AUDIENCE_OPTIONS = gql`
  query AudienceCustomAudienceOptions {
    audienceCustomAudienceOptions { id name }
  }
`;
export function useAudienceCustomAudienceOptions() {
  return useQuery(GET_AUDIENCE_CUSTOM_AUDIENCE_OPTIONS, { fetchPolicy: 'network-only' });
}
