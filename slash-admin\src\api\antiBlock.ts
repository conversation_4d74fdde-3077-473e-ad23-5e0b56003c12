/*
 * @Author: 潘孝权
 * @Date: 2025-06-03 21:29:51
 * @Description: 
 * @FilePath: \sking_frontend\slash-admin\src\api\antiBlock.ts
 */
import { gql, useQuery, useMutation } from '@apollo/client';

// export const GET_ANTIBLOCKS = gql`
// query GetAntiBlocks(
//   $paging: CursorPaging
//   $filter: AntiBlockFilter
//   $sorting: [AntiBlockSort!]!
// ) {
//   antiBlocks(
//     paging: $paging
//     filter: $filter
//     sorting: $sorting
//   ) {
//     edges {
//       node {
//         id
//         url
//         status
//         relatedDomain
//         createdAt
//       }
//     }
//     pageInfo {
//       hasNextPage
//       startCursor
//       endCursor
//     }
//     totalCount
//   }
// }
// `;

export const GET_ANTIBLOCKS = gql`
query GetAntiBlocks(
  $paging: OffsetPaging
  $filter: AntiBlockFilter
  $sorting: [AntiBlockSort!]
) {
  antiBlocks(
    paging: $paging
    filter: $filter
    sorting: $sorting
  ) {
    nodes {
      id
      url
      userId
      status
      relatedDomain
      createdAt
    }
  }
}
`;

// export const CREATE_ANTIBLOCK_MUTATION = gql`
// mutation CreateOneAntiBlock($input: CreateOneAntiBlockInput!) {
//   createOneAntiBlock(input: $input) {
//     id
//     url
//     status
//     relatedDomain
//   }
// }
// `;

export const CREATE_MANY_ANTIBLOCKS_MUTATION = gql`
  mutation CreateManyAntiBlocks($antiBlocks: [AntiBlockInput!]!) {
    createManyAntiBlocks(antiBlocks: $antiBlocks) {
      id
      userId
      url
      status
      name
      relatedDomain
      tenantId
      createdAt
      updatedAt
    }
  }
`;

export const DELETE_ANTI_BLOCKS = gql`
  mutation DeleteAntiBlock($input: DeleteOneAntiBlockInput!) {
    deleteOneAntiBlock(input: $input) {
      id
    }
  }
`;

