import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from './role.entity';
import { Route } from '../route/route.entity';

@Injectable()
export class RoleSeedService {
    constructor(
        @InjectRepository(Role)
        private readonly roleRepository: Repository<Role>,
        @InjectRepository(Route)
        private readonly routeRepository: Repository<Route>
    ) { }

    async seed() {
        const count = await this.roleRepository.count();

        if (count === 0) {
            console.log('📦 开始初始化角色数据...');

            // 获取所有路由
            const allRoutes = await this.routeRepository.find();
            // 只分配系统设置及其子菜单
            const systemMenu = allRoutes.find(route => route.path === '/system');
            const systemSubMenus = allRoutes.filter(route => route.parentId === systemMenu?.id);
            const systemSubSubMenus = allRoutes.filter(route => systemSubMenus.map(m => m.id).includes(route.parentId));
            const adminRoutes = [
                ...(systemMenu ? [systemMenu] : []),
                ...systemSubMenus,
                ...systemSubSubMenus
            ];

            // 创建超级管理员角色
            const adminRole = this.roleRepository.create({
                name: '超级管理员',
                description: '仅拥有系统设置权限',
                status: 'enabled',
                order: 1,
                routes: adminRoutes
            });

            await this.roleRepository.save([adminRole]);
            console.log('✅ 角色数据初始化完成');
        } else {
            console.log('📦 角色数据已存在，跳过初始化');
        }
    }
} 