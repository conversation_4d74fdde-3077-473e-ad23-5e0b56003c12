# 组件路径 /pages/management/campaign/index

## 前端

- **实际路径**：slash-admin/src/pages/management/campaign/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，推广任务管理表单与列表，含增删改查、分页、搜索、弹窗、自动规则等）
- **主要依赖API定义**：
  - audience.graphql.ts（受众）
  - material.graphql.ts（素材）
  - adAccount.graphql.ts（广告账户）
  - route.graphql.ts（路由）
  - ad.graphql.ts（广告）
  - adSet.graphql.ts（广告组）

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取广告列表    | query    | adList                 | filter, paging | id, name, status, ... | 支持分页、搜索、过滤 |
| 新增广告        | mutation | createAd               | input: AdInput | id, name, ... | 新建广告，id为UUID |
| 编辑广告        | mutation | updateAd               | id, input: AdInput | id, name, ... | 编辑指定广告 |
| 删除广告        | mutation | deleteAd               | id: string | id | 删除指定广告 |
| 获取广告组列表  | query    | adSetList              | filter, paging | id, name, ... | 获取广告组 |
| 获取受众列表    | query    | audienceList           | filter, paging | id, name, ... | 获取受众 |
| 获取素材列表    | query    | materialList           | filter, paging | id, name, ... | 获取素材 |
| 获取路由列表    | query    | routeList              | filter, paging | id, name, ... | 获取落地页路由 |

#### 主要参数结构说明

- **AdInput**（用于新增/编辑广告）  
  ```ts
  {
    name: string; // 广告名称
    adSetId: string; // 广告组ID
    creativeId: string; // 创意ID
    audienceId: string; // 受众ID
    materialId: string; // 素材ID
    routeId: string; // 路由ID
    status: string; // 状态
    budget: number; // 预算
    bid: number; // 出价
    schedule: { start: string, end: string }; // 投放时间
    ... // 其他业务字段
  }
  ```

- **自动规则结构**
  ```ts
  {
    id: string;
    name: string;
    status: 'ENABLED' | 'DISABLED';
    conditions: Array<{ field: string; operator: string; value: string; unit?: string }>;
    scheduleType: string;
    timeRange: string;
    action: string;
    object: string;
  }
  ```

- **广告任务/推广任务结构**
  ```ts
  {
    id: string;
    name: string;
    autoOptimize: boolean;
    adCount: number;
    status: string;
    arrears: number;
    rejected: number;
    cost: number;
    register: number;
    download: number;
    paid: number;
    commission: number;
    remark?: string;
  }
  ```

### 前端表单/组件说明

- 支持推广任务的增删改查，表单字段与上方AdInput结构一致
- 支持自动规则配置（如消耗超限自动暂停等）
- 支持广告、广告组、受众、素材、路由等多维度管理
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（ad, ad_set, campaign等，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| name        | varchar     | 名称                       | NOT NULL     |
| adSetId     | uuid        | 广告组ID                   | 外键         |
| creativeId  | uuid        | 创意ID                     | 外键         |
| audienceId  | uuid        | 受众ID                     | 外键         |
| materialId  | uuid        | 素材ID                     | 外键         |
| routeId     | uuid        | 路由ID                     | 外键         |
| status      | varchar     | 状态                       | NOT NULL     |
| budget      | numeric     | 预算                       | 可空         |
| bid         | numeric     | 出价                       | 可空         |
| schedule    | jsonb       | 投放时间                   | 可空         |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| updatedAt   | timestamp   | 更新时间                   | 自动生成     |
| ...         | ...         | 其他业务字段               | ...          |

- **自动规则表结构**（如有）：
  - id, name, status, conditions(jsonb), scheduleType, timeRange, action, object, createdAt, updatedAt

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query adList / mutation createAd(input) / ...
    后端->>数据库: 操作 ad, ad_set, campaign 等表
    数据库-->>后端: 返回广告/任务数据
    后端-->>前端: 返回广告/任务数据
```

---

## 变更历史/注意事项

- 推广任务/广告/广告组/创意/受众/素材/路由等需保持ID唯一性
- 自动规则配置需与业务流程严格匹配
- 删除任务时需校验是否被广告组/广告等引用
- 预算、出价、投放时间等字段需与平台实际规则一致
- 任务状态需与广告投放平台同步
- 复杂业务字段建议在表结构中用jsonb扩展