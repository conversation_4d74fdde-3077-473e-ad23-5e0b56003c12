import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Domain } from '../../entity/domain.entity';
import { DomainResolver } from './domain.resolver';
import { CloudflareService } from './cloudflare.service';
import { AuthModule } from '../auth/auth.module';
import { Rule } from './rule.entity';
import { RuleService } from './rule.service';
import { RuleResolver } from './rule.resolver';
// import { CloudflareService } from './cloudflare.service'; // 预留

@Module({
    imports: [
        TypeOrmModule.forFeature([Domain]),
        TypeOrmModule.forFeature([Rule]),
        AuthModule,
    ],
    providers: [DomainResolver, CloudflareService, RuleService, RuleResolver],
    exports: [RuleService],
})
export class DomainModule { } 