import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export class materialManagement {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ length: 255, nullable: true })
  fileName: string;

  @Column({ length: 255, nullable: true })
  tags: string;

  @Column({ length: 255, nullable: true })
  group?: string;

  @Column({ length: 255, nullable: true })
  facebookImageId?: string;

  @Column({ length: 255, nullable: true })
  facebookVedioId?: string;

  @Column({ default: 0, nullable: true })
  adCount?: number;

  @Column({ default: 0, nullable: true })
  activeAdCount?: number;

  @Column({ default: 0, nullable: true })
  bannedCount?: number;

  @Column({ type: 'decimal', default: 0, nullable: true })
  spend?: number;

  @Column({ type: 'decimal', default: 0, nullable: true })
  conversion?: number;

  @Column({ type: 'decimal', default: 0, nullable: true })
  commission?: number;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'text', nullable: true })
  file: string;

  @Column({ length: 255 })
  tenantId: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt?: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;
} 