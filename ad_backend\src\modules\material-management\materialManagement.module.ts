import { ConfigModule } from '@nestjs/config';
import { NestjsQueryGraphQLModule, PagingStrategies } from '@ptc-org/nestjs-query-graphql';
import { NestjsQueryTypeOrmModule } from '@ptc-org/nestjs-query-typeorm';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { materialManagementDTO } from './dto/materialManagement.dto';
import { materialManagement } from './materialManagement.entity';
import { materialManagementController } from './materialManagement.controller';
import { materialManagementService } from './materialManagement.service';
import { materialManagementResolver } from './resolvers/materialManagement.resolver'
import config from '../../config/config';
import { Group } from '../group/group.entity';
import { AdPlatformModule } from '../ad-platform/ad-platform.module';
import { User } from '../user/user.entity';
import { Role } from '../role/role.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
	imports: [
		ConfigModule.forRoot({
			load: [config],
		}),
		TypeOrmModule.forFeature([materialManagement, Group, User, Role]),
		AdPlatformModule,
		AuthModule,
		NestjsQueryGraphQLModule.forFeature({
			imports: [NestjsQueryTypeOrmModule.forFeature([materialManagement])],
			resolvers: [
				{
					DTOClass: materialManagementDTO,
					EntityClass: materialManagement,
					enableSubscriptions: true,
					delete: {
						disabled: false,
					},
					read: {
						maxResultsSize: 1000
					},
					pagingStrategy: PagingStrategies.OFFSET
				},
			],
		}),
	],
	controllers: [materialManagementController],
	providers: [materialManagementService, materialManagementResolver],
	exports: [materialManagementService],
})
export class materialManagementModule { }
