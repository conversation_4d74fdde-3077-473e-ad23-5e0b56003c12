import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';

@Entity('risk')
export class Risk {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '风险类型（账户/广告/支付等）' })
  @Column({ length: 32 })
  type: string;

  @ApiProperty({ description: '关联对象ID' })
  @Column({ length: 64 })
  objectId: string;

  @ApiProperty({ description: '风险等级' })
  @Column({ length: 16 })
  level: string;

  @ApiProperty({ description: '风险描述' })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({ description: '状态' })
  @Column({ length: 32, default: 'active' })
  status: string;

  @ApiProperty({ description: '租户' })
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;

  @ApiProperty({ description: '原始数据', required: false })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;
}
