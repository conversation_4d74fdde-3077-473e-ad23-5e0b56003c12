# 文档同步机制

## 快速开始

### 1. 安装配置
```bash
# 安装 Git Hooks
npm run doc:setup

# 安装依赖
npm install
npm install -g glob
```

### 2. 基本使用
```bash
# 检测代码变更
npm run doc:detect

# 更新文档
npm run doc:update

# 验证文档一致性
npm run doc:validate

# 一键同步（检测 + 更新）
npm run doc:sync
```

## 文件说明

### 核心脚本
- `detect-changes.js` - 代码变更检测脚本
- `update-docs.js` - 文档自动更新工具
- `validate-docs.js` - 文档验证脚本
- `setup-hooks.sh` - Git Hooks 安装脚本

### 配置文件
- `../.github/workflows/doc-sync.yml` - CI/CD 配置
- `../package.json` - NPM 脚本配置
- `../docs/文档同步机制说明.md` - 详细说明文档

### 生成文件
- `changes.json` - 检测到的代码变更
- `update-report.md` - 文档更新报告
- `validation-report.md` - 文档验证报告

## 工作流程

```mermaid
graph LR
    A[代码变更] --> B[检测变更]
    B --> C{有变更?}
    C -->|是| D[更新文档]
    C -->|否| E[无需操作]
    D --> F[验证文档]
    F --> G{验证通过?}
    G -->|是| H[完成]
    G -->|否| I[人工审查]
    I --> J[修正文档]
    J --> F
```

## 支持的变更类型

### Entity 变更
- ✅ 字段新增/删除
- ✅ 字段类型变更
- ✅ 约束条件变更
- ✅ 关联关系变更

### GraphQL 变更
- ✅ Query/Mutation 新增/删除
- ✅ 参数变更
- ✅ 返回字段变更

### 组件变更
- ✅ 路径变更检测
- ✅ API 调用变更

## 自动化集成

### Git Hooks
- **pre-commit**: 提交前检测变更
- **post-commit**: 提交后自动更新
- **pre-push**: 推送前验证一致性

### CI/CD
- **GitHub Actions**: 自动化检查和更新
- **PR 评论**: 自动评论检查结果
- **报告上传**: 保存验证报告

## 配置说明

### 模块映射
在 `update-docs.js` 中配置模块到文档的映射：

```javascript
this.moduleMapping = {
  'ad-platform': ['z 广告账户.md', 'z 广告系列.md'],
  'vcc': ['z 卡片管理.md'],
  // ...
};
```

### 类型映射
在 `validate-docs.js` 中配置类型映射：

```javascript
const typeMapping = {
  'string': 'varchar',
  'number': 'int',
  'Date': 'timestamp with time zone',
  // ...
};
```

## 故障排除

### 常见错误

1. **Node.js 版本问题**
   ```bash
   # 确保使用 Node.js 18+
   node --version
   ```

2. **依赖缺失**
   ```bash
   npm install -g glob
   ```

3. **权限问题**
   ```bash
   chmod +x scripts/doc-sync/setup-hooks.sh
   ```

### 调试方法

1. **查看详细日志**
   ```bash
   node scripts/doc-sync/detect-changes.js
   cat scripts/doc-sync/changes.json
   ```

2. **单步执行**
   ```bash
   npm run doc:detect
   npm run doc:update
   npm run doc:validate
   ```

3. **检查生成的报告**
   ```bash
   cat scripts/doc-sync/update-report.md
   cat scripts/doc-sync/validation-report.md
   ```

## 最佳实践

### 开发流程
1. 修改代码后运行 `npm run doc:sync`
2. 检查生成的文档变更
3. 必要时手动调整文档
4. 提交代码（自动触发 hooks）

### 团队协作
1. 新成员先运行 `npm run doc:setup`
2. 代码审查时检查文档变更
3. 定期运行 `npm run doc:validate`

### 质量保证
1. 重大变更后手动验证文档
2. 发布前确保文档同步
3. 定期更新映射配置

## 扩展开发

### 添加新的检测类型
在 `detect-changes.js` 中添加新的检测方法：

```javascript
detectNewType(changedFiles) {
  // 实现新的检测逻辑
}
```

### 添加新的更新规则
在 `update-docs.js` 中添加新的更新方法：

```javascript
updateNewSection(content, data) {
  // 实现新的更新逻辑
}
```

### 添加新的验证规则
在 `validate-docs.js` 中添加新的验证方法：

```javascript
validateNewAspect() {
  // 实现新的验证逻辑
}
```

## 版本历史

- **v1.0.0** - 初始版本，支持 Entity 和 GraphQL 检测
- **v1.1.0** - 添加 CI/CD 集成
- **v1.2.0** - 完善验证机制
- **v1.3.0** - 添加人工审查流程

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 创建 Pull Request
5. 等待审查

## 许可证

MIT License
