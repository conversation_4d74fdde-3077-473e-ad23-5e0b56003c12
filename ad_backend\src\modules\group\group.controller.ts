import { Controller, BadRequestException, Req, UseGuards } from '@nestjs/common';
import { <PERSON><PERSON>, CrudController } from '@dataui/crud';
import { Group } from './group.entity';
import { GroupService } from './group.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Crud({
    model: {
        type: Group,
    },
    params: {
        id: {
            field: 'id',
            primary: true,
            type: 'uuid',
        },
    },
})
@Controller('api/groups')
export class GroupController implements CrudController<Group> {
    constructor(public readonly service: GroupService) { }

    // 拦截所有 GET 查询，强制校验 tenantId
    async getMany(@Req() req) {
        const tenantId = req.tenantId;
        if (!tenantId) throw new BadRequestException('tenantId required');
        return this.service.find({ where: { tenantId } });
    }
} 