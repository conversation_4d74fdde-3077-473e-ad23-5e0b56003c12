import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveExclusionsColumn20240609180000 implements MigrationInterface {
    name = 'RemoveExclusionsColumn20240609180000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "exclusions"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN "exclusions" jsonb`);
    }
} 