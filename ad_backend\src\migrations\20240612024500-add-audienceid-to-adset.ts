import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAudienceIdToAdSet20240612024500 implements MigrationInterface {
    name = 'AddAudienceIdToAdSet20240612024500'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. ad_set.audienceId 字段补充为 uuid（如不存在）
        await queryRunner.query(`DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name='ad_set' AND column_name='audienceId'
            ) THEN
                ALTER TABLE "ad_set" ADD COLUMN "audienceId" uuid;
            END IF;
        END$$;`);
        // 2. 可选：加外键约束
        // await queryRunner.query(`ALTER TABLE "ad_set" ADD CONSTRAINT "FK_adset_audience" FOREIGN KEY ("audienceId") REFERENCES "audience"("id")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回退：删除 audienceId 字段
        await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN "audienceId"`);
    }
} 