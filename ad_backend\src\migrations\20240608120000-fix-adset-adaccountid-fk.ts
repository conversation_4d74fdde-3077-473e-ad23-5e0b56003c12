import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAdSetAdAccountIdFk20240608120000 implements MigrationInterface {
    name = 'FixAdSetAdAccountIdFk20240608120000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 删除原有外键（如果存在）
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "FK_dcb3ef81acf5eae82b9cdb2358f"`);
        // 2. 删除原有 adAccountId 字段（如果类型不对）
        // await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN IF EXISTS "adAccountId"`);
        // 3. 新建 adAccountId 字段（varchar 64）
        await queryRunner.query(`ALTER TABLE "ad_set" ADD COLUMN IF NOT EXISTS "adAccountId" varchar(64)`);
        // 4. 将原有数据迁移到新字段（如有需要，可自定义）
        // 5. 添加外键约束
        await queryRunner.query(`ALTER TABLE "ad_set" ADD CONSTRAINT "FK_adset_adaccount_accountid" FOREIGN KEY ("adAccountId") REFERENCES "ad_account"("accountId") ON DELETE RESTRICT`);
        // 6. 给 accountId 添加唯一索引（如果还没有）
        await queryRunner.query(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_ad_account_accountId_unique" ON "ad_account" ("accountId")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" DROP CONSTRAINT IF EXISTS "FK_adset_adaccount_accountid"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_ad_account_accountId_unique"`);
        // 可选：保留 adAccountId 字段
        // await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN IF EXISTS "adAccountId"`);
    }
} 