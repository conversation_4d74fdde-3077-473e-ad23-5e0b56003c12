import {
	Connection,
	getConnection,
	createConnection,
	DataSourceOptions,
	getRepository,
} from 'typeorm';
import { Injectable } from '@nestjs/common';
import { Entities, TypeOrmConfigService } from '../../config/database';
import { createEmployees } from '../employee/employee.seed';
import { createAntiBlock } from '../anti-block/antiBlock.seed';
import { createGroups } from '../group/group.seed';
import { ConfigurationService } from '../../config/config';
import { EmployeeService } from '../employee/employee.service';
import { AntiBlockService } from '../anti-block/antiBlock.service';
import { RouteService } from '../route/route.service';
import { GroupService } from '../group/group.service';
import { ipRestrictionService } from '../ip-restriction/ipRestriction.service';
import { RoleSeedService } from '../role/role.seed';
import { UserSeedService } from '../user/user.seed';
import { VccCardSeed } from '../vcc/vcc-card.seed';

@Injectable()
export class SeedDataService {
	constructor(
		private readonly typeOrmConfigService: TypeOrmConfigService,
		private readonly configService: ConfigurationService,
		private readonly employeeService: EmployeeService,
		private readonly antiBlockService: AntiBlockService,
		private readonly routeService: RouteService,
		private readonly groupService: GroupService,
		private readonly ipRestrictionService: ipRestrictionService,
		private readonly roleSeedService: RoleSeedService,
		private readonly userSeedService: UserSeedService,
		private readonly vccCardSeed: VccCardSeed
	) { }

	public async executeSeed(isDefault: boolean): Promise<void> {
		try {
			await this.createConnection();

			const count = await this.employeeService.count();

			if (count === 0) {
				await this.resetDatabase();
				await this.seedData(isDefault);

				console.log('Seed Completed');
			} else {
				console.log(`DB Already Seeded`);
			}
		} catch (error) {
			await this.handleError(error);
		}
	}

	public async executeUpdate(isDefault: boolean): Promise<void> {
		try {
			await this.createConnection();

			const count = await this.employeeService.count();

			if (count === 0) {
				console.log(`Start seed...`);

				await this.resetDatabase();
				await this.seedData(isDefault);

				console.log('Seed Completed');
			} else {
				console.log(`Update DB...`);
				await this.seedData(isDefault);
				console.log('Update Completed');
			}
		} catch (error) {
			await this.handleError(error);
		}
	}

	private connection: Connection;

	private tryExecute<T>(
		name: string,
		p: Promise<T>
	): Promise<T> | Promise<void> {
		console.log(`Execute Seed for ${name}`);

		return (p as any).then(
			(x: T) => x,
			(error: Error) => {
				console.error(error);
			}
		);
	}

	private async createConnection() {
		try {
			this.connection = getConnection();
		} catch (error) {
			console.log('DB not found. Creating new DB');
		}

		if (!this.connection || !this.connection.isConnected) {
			try {
				console.log(`Checking connection to DB ...`);

				const dbConnectOptions =
					await this.typeOrmConfigService.createTypeOrmOptions();

				const overrideDbConfig = {
					logging: true,
					logger: 'file',
				};

				this.connection = await createConnection({
					...dbConnectOptions,
					...overrideDbConfig,
					entities: Entities,
				} as DataSourceOptions);
			} catch (error) {
				await this.handleError(error, 'Cannot connect to DB!');
			}
		}
	}

	private async seedData(isDefault: boolean): Promise<void> {
		try {
			await this.tryExecute(
				'Employees',
				createEmployees(this.connection)
			);

			// 注释这行代码，因为routeService没有seed方法
			// await this.tryExecute(
			// 	'Routes',
			// 	this.routeService.seed()
			// );

			await this.tryExecute(
				'AntiBlock',
				createAntiBlock(this.connection)
			);

			await this.tryExecute(
				'Groups',
				createGroups(this.connection)
			);

			// 添加角色数据初始化
			await this.tryExecute(
				'Roles',
				this.roleSeedService.seed()
			);

			// 添加用户数据初始化
			await this.tryExecute(
				'Users',
				this.userSeedService.seed()
			);

			// 添加VCC卡片数据初始化
			await this.tryExecute(
				'VccCards',
				this.vccCardSeed.seed()
			);

			console.log(`Seed Completed`);
		} catch (error) {
			await this.handleError(error);
		}
	}

	/**
	 * Retrieve entities metadata
	 */
	private async getEntities() {
		const entities: any = [];
		try {
			this.connection.entityMetadatas.forEach((entity) =>
				entities.push({
					name: entity.name,
					tableName: entity.tableName,
				})
			);
			return entities;
		} catch (error) {
			await this.handleError(error, 'Cannot read metadata from entities');
		}
	}

	private async cleanAll(entities: any) {
		try {
			for (const entity of entities) {
				const repository = getRepository(entity.name);
				const truncateSql =
					this.configService.dbType() === 'sqlite'
						? `DELETE FROM  "${entity.tableName}";`
						: `TRUNCATE  "${entity.tableName}" RESTART IDENTITY CASCADE;`;
				await repository.query(truncateSql);
			}
		} catch (error) {
			await this.handleError(error, 'Clean database failed');
		}
	}

	private async resetDatabase() {
		const entities = await this.getEntities();
		await this.cleanAll(entities);
		console.log(`DB Reset`);
	}

	private async handleError(error: Error, message?: string) {
		console.error(error, message);
		throw error;
	}

	public async run() {
		console.log('🔥 开始数据初始化...');

		// 初始化角色数据
		console.log('📦 初始化角色数据...');
		try {
			await this.roleSeedService.seed();
			console.log('✅ 角色数据初始化完成');
		} catch (error) {
			console.error('❌ 角色数据初始化失败', error);
		}

		// 初始化用户数据
		console.log('📦 初始化用户数据...');
		try {
			await this.userSeedService.seed();
			console.log('✅ 用户数据初始化完成');
		} catch (error) {
			console.error('❌ 用户数据初始化失败', error);
		}

		console.log('✅ 所有数据初始化完成');
	}
}
