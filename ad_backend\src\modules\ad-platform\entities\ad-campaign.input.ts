import { InputType, Field, ID } from '@nestjs/graphql';

@InputType()
export class AdCampaignFilterInput {
    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    platform?: string;

    // 可根据实际 filter 字段继续补充
}

@InputType()
export class RelateAdAccountInput {
    @Field(() => ID)
    id: string;
}

@InputType()
export class CreateAdCampaignInput {
    @Field()
    name: string;

    @Field()
    platform: string;

    @Field()
    budget: number;

    @Field()
    status: string;

    @Field()
    startDate: Date;

    @Field()
    endDate: Date;

    @Field(() => [String], { nullable: true })
    tags?: string[];

    @Field({ nullable: true })
    description?: string;

    @Field(() => RelateAdAccountInput)
    adAccount: RelateAdAccountInput;
}

@InputType()
export class CreateOneAdCampaignInput {
    @Field(() => CreateAdCampaignInput)
    adCampaign: CreateAdCampaignInput;
} 