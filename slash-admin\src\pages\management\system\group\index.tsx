import { useTranslation } from 'react-i18next';
import { useState, useEffect, useCallback, useRef } from "react";
import { Table, Button, Tag, Input, Space, Modal, Form, Select, Tree, message } from "antd";
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table/interface";
import type { DataNode } from "antd/es/tree";
import {
  useGroups,
  useCreateGroup,
  useUpdateGroup,
  useDeleteGroup
} from "@/api/services/groupService";
import { useTenant } from '@/hooks/useTenant';
import { useApolloClient } from '@apollo/client';
import { UserService } from '@/api/services/user.service';
import type { UserItem } from '@/pages/management/system/account/types';

interface GroupData {
  id: string;
  name: string;
  description: string;
  belongTo: string;
  contactInfo: string;
  memberCount: number;
  createTime: string;
  status: string;
  permissions?: any;
}

export default function GroupManagement() {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editing, setEditing] = useState<GroupData | null>(null);
  const [form] = Form.useForm();
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const searchTimerRef = useRef<NodeJS.Timeout | null>(null);
  const tenantId = useTenant();
  const client = useApolloClient();
  const userService = new UserService(client);
  const [userOptions, setUserOptions] = useState<UserItem[]>([]);
  const [userLoading, setUserLoading] = useState(false);
  const [deleteLoadingId, setDeleteLoadingId] = useState<string | null>(null);

  const permissionTreeData: DataNode[] = [
    {
      title: t('pages.key_1778'),
      key: 'system',
      children: [
        {
          title: t('pages.key_1641'),
          key: 'system:user',
        },
        {
          title: t('pages.key_2082'),
          key: 'system:role',
        },
        {
          title: t('pages.key_1889'),
          key: 'system:group',
        }
      ],
    },
    {
      title: t('pages.key_804'),
      key: 'ad',
      children: [
        {
          title: t('pages.key_819'),
          key: 'ad:account',
        },
        {
          title: t('pages.key_809'),
          key: 'ad:material',
        },
        {
          title: t('pages.key_796'),
          key: 'ad:campaign',
        }
      ],
    },
  ];

  // GraphQL钩子
  const filter: any = {
    ...(searchText ? {
      or: [
        { name: { like: `%${searchText}%` } },
        { description: { like: `%${searchText}%` } }
      ]
    } : {})
  };
  if (typeof tenantId === 'string' && tenantId) {
    filter.tenantId = { eq: tenantId };
  }
  const { data: groupsData, loading: groupsLoading, refetch, error } = useGroups({
    paging: { limit: pageSize, offset: (currentPage - 1) * pageSize },
    filter,
    sorting: [{ field: "createTime", direction: "DESC" }]
  });

  const [createGroup, { loading: createLoading }] = useCreateGroup();
  const [updateGroup, { loading: updateLoading }] = useUpdateGroup();
  const [deleteGroup, { loading: deleteLoading }] = useDeleteGroup();

  // 自定义防抖搜索函数
  const handleSearch = useCallback((value: string) => {
    // 清除之前的定时器
    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }

    // 设置新的定时器
    searchTimerRef.current = setTimeout(() => {
      setSearchText(value);
      setCurrentPage(1); // 重置到第一页
      refetch(); // 重新获取数据
    }, 500); // 500ms延迟
  }, [refetch]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }
    };
  }, []);

  // 如果有错误，记录到控制台以便调试
  useEffect(() => {
    if (error) {
      console.error("GraphQL error:", error);
    }
  }, [error]);

  // 拉取当前租户下所有账户
  useEffect(() => {
    const fetchUsers = async () => {
      if (!tenantId) return;
      setUserLoading(true);
      try {
        const res = await userService.getUsers({ filter: {}, page: 1, limit: 1000 });
        setUserOptions(res?.nodes || []);
      } catch (e) {
        setUserOptions([]);
      } finally {
        setUserLoading(false);
      }
    };
    if (isModalOpen) fetchUsers();
  }, [isModalOpen, tenantId]);

  // 处理权限树选择逻辑
  const getPermissionsFromKeys = (keys: React.Key[]) => {
    const permissions = {
      systems: keys.filter(key => String(key).startsWith('system:')),
      ads: keys.filter(key => String(key).startsWith('ad:'))
    };
    return permissions;
  };

  const getKeysFromPermissions = (permissions: any) => {
    if (!permissions) return [];
    const { systems = [], ads = [] } = permissions;
    return [...systems, ...ads];
  };

  const columns: ColumnsType<GroupData> = [
    {
      title: t('pages.key_844'),
      dataIndex: "id",
      key: "id",
      width: 80,
      render: (_, __, index) => (currentPage - 1) * pageSize + index + 1,
    },
    {
      title: t('pages.key_1886'),
      dataIndex: "name",
      key: "name",
      width: 120,
    },
    {
      title: t('pages.key_874'),
      dataIndex: "belongTo",
      key: "belongTo",
      width: 120,
    },
    {
      title: t('pages.key_1896'),
      dataIndex: "contactInfo",
      key: "contactInfo",
      width: 120,
    },
    {
      title: t('pages.key_1027'),
      dataIndex: "description",
      key: "description",
      width: 200,
    },
    {
      title: t('pages.key_1602'),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        const color = status === t('pages.key_1465') ? "green" : "red";
        return <Tag color={color}>{status}</Tag>;
      }
    },
    {
      title: t('pages.key_1058'),
      key: "actions",
      width: 160,
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EditOutlined />} size="small" onClick={() => showEditModal(record)}>{t('pages.key_1853')}</Button>
          <Button type="link" danger icon={<DeleteOutlined />} size="small" onClick={() => handleDelete(record)} loading={deleteLoadingId === record.id} disabled={deleteLoadingId === record.id}>{t('pages.key_287')}</Button>
        </Space>
      )
    }
  ];

  const showModal = () => {
    setEditing(null);
    setIsModalOpen(true);
    setCheckedKeys([]);
    form.resetFields();
  };

  const showEditModal = (record: GroupData) => {
    setEditing(record);
    setIsModalOpen(true);
    form.setFieldsValue({
      name: record.name,
      description: record.description,
      belongTo: record.belongTo,
      contactInfo: record.contactInfo,
      status: record.status
    });

    // 设置已有权限
    if (record.permissions) {
      setCheckedKeys(getKeysFromPermissions(record.permissions));
    } else {
      setCheckedKeys([]);
    }
  };

  const handleDelete = async (record: GroupData) => {
    setDeleteLoadingId(record.id);
    try {
      await deleteGroup({
        variables: {
          input: { id: record.id }
        }
      });
      message.success(t('pages.key_294'));
      refetch();
    } catch (error) {
      console.error("删除失败:", error);
      message.error(t('pages.key_293'));
    } finally {
      setDeleteLoadingId(null);
    }
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      // 修正：tenantId 为空字符串时传 null
      const safeTenantId = tenantId && tenantId !== '' ? tenantId : null;
      if (editing) {
        // 更新
        await updateGroup({
          variables: {
            input: {
              id: editing.id,
              update: {
                ...values,
                tenantId: safeTenantId,
                permissions: { systems: [], ads: [] }
              }
            }
          }
        });
        message.success(t('pages.key_1252'));
      } else {
        // 创建
        await createGroup({
          variables: {
            input: {
              group: {
                ...values,
                tenantId: safeTenantId,
                permissions: { systems: [], ads: [] }
              }
            }
          }
        });
        message.success(t('pages.key_246'));
      }
      setIsModalOpen(false);
      form.resetFields();
      refetch(); // 刷新列表
    } catch (error) {
      console.error("操作失败:", error);
      message.error(t('pages.key_1060'));
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  const onCheck = (checkedKeysValue: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    if (Array.isArray(checkedKeysValue)) {
      setCheckedKeys(checkedKeysValue);
    } else {
      setCheckedKeys(checkedKeysValue.checked);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{t('pages.key_1889')}</h1>
        <Button type="primary" onClick={showModal} icon={<PlusOutlined />}>{t('pages.key_1136')}</Button>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <div className="mb-4">
          <Space>
            <Input
              placeholder={t('pages.key_1052')}
              prefix={<SearchOutlined />}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: 200 }}
            />
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={groupsData?.groups?.nodes || []}
          rowKey="id"
          loading={groupsLoading || deleteLoadingId !== null}
          pagination={{
            pageSize,
            current: currentPage,
            total: groupsData?.groups?.nodes?.length === pageSize ? (currentPage + 1) * pageSize : currentPage * pageSize,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(Math.min(pageSize, 50)); // 限制最大分页大小为50
            },
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '30', '50'] // 限制分页选项
          }}
        />
      </div>

      <Modal
        title={editing ? t('pages.key_1864') : t('pages.key_1153')}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={createLoading || updateLoading}
        width={700}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label={t('pages.key_1886')}
            rules={[{ required: true, message: t('pages.key_2225') }]}
          >
            <Input placeholder={t('pages.key_2225')} />
          </Form.Item>

          <Form.Item
            name="belongTo"
            label={t('pages.key_874')}
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (!value) return Promise.reject(t('pages.key_2217'));
                  if (!userOptions.find(u => u.username === value)) return Promise.reject(t('pages.key_875'));
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input placeholder={t('pages.key_2217')} />
          </Form.Item>

          <Form.Item
            name="contactInfo"
            label={t('pages.key_1896')}
            rules={[{ required: true, message: t('pages.key_2228') }, { pattern: /^\d{11}$/, message: t('pages.key_2169') }]}
          >
            <Input placeholder={t('pages.key_2228')} />
          </Form.Item>

          <Form.Item
            name="description"
            label={t('pages.key_1027')}
          >
            <Input.TextArea rows={2} placeholder={t('pages.key_2226')} />
          </Form.Item >

          <Form.Item
            name="status"
            label={t('pages.key_1602')}
            initialValue={t('pages.key_1465')}
            rules={[{ required: true, message: t('pages.key_2273') }]}
          >
            <Select>
              <Select.Option value={t('pages.key_1465')}>{t('pages.key_1465')}</Select.Option>
              <Select.Option value={t('pages.key_1714')}>{t('pages.key_1714')}</Select.Option>
            </Select>
          </Form.Item >

          {/* <Form.Item
            label=t('pages.key_1324')
            name="permissions"
          >
            <Tree
              checkable
              onCheck={onCheck}
              checkedKeys={checkedKeys}
              treeData={permissionTreeData}
            />
          </Form.Item> */}
        </Form >
      </Modal >
    </div >
  );
}
