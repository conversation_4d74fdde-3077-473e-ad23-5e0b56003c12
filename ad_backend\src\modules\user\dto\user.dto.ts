import { <PERSON><PERSON>ptional, <PERSON><PERSON>tring, IsEmail, IsUUID, IsEnum, IsArray, IsDate } from 'class-validator';
import {
    Field,
    ID,
    ObjectType,
    GraphQLISODateTime,
    InputType,
    registerEnumType
} from '@nestjs/graphql';
import { FilterableField } from '@ptc-org/nestjs-query-graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserStatus } from '../user.entity';
import { Role } from '../../role/dto/role.dto';
import { GroupDTO } from '../../group/dto/group.dto';
import { Tenant } from '../../../entity/tenant.entity';

registerEnumType(UserStatus, {
    name: 'UserStatus',
});

@ObjectType('User')
export class UserDTO {
    @FilterableField(() => ID, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsOptional()
    @IsUUID()
    id?: string;

    @FilterableField(() => String)
    @ApiProperty({ type: String })
    @IsString()
    username: string;

    @FilterableField(() => String)
    @ApiProperty({ type: String })
    @IsEmail()
    email: string;

    @Field({ nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsString()
    @IsOptional()
    password?: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsString()
    @IsOptional()
    avatar?: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsString()
    @IsOptional()
    fullName?: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsString()
    @IsOptional()
    phone?: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsString()
    @IsOptional()
    address?: string;

    @FilterableField(() => String, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsString()
    @IsOptional()
    registerIp?: string;

    @FilterableField(() => UserStatus)
    @ApiProperty({ enum: UserStatus, default: UserStatus.ACTIVE })
    @IsEnum(UserStatus)
    status: UserStatus;

    @Field(() => [Role], { nullable: true })
    @ApiPropertyOptional({ type: () => [Role] })
    @IsArray()
    @IsOptional()
    roles?: Role[];

    @Field(() => [GroupDTO], { nullable: true })
    @ApiPropertyOptional({ type: () => [GroupDTO] })
    @IsArray()
    @IsOptional()
    groups?: GroupDTO[];

    @Field(() => [ID], { nullable: true })
    @ApiPropertyOptional({ type: () => [String] })
    @IsArray()
    @IsOptional()
    @IsUUID("4", { each: true })
    roleIds?: string[];

    @Field(() => [ID], { nullable: true })
    @ApiPropertyOptional({ type: () => [String] })
    @IsArray()
    @IsOptional()
    @IsUUID("4", { each: true })
    groupIds?: string[];

    @FilterableField(() => GraphQLISODateTime, { nullable: true })
    @ApiPropertyOptional({
        type: 'string',
        format: 'date-time',
        example: '2023-01-01T00:00:00.000Z',
    })
    @IsOptional()
    @IsDate()
    createdAt?: Date;

    @FilterableField(() => GraphQLISODateTime, { nullable: true })
    @ApiPropertyOptional({
        type: 'string',
        format: 'date-time',
        example: '2023-01-01T00:00:00.000Z',
    })
    @IsOptional()
    @IsDate()
    updatedAt?: Date;

    @Field(() => String, { nullable: true })
    tenantId?: string;

    @Field(() => Tenant, { nullable: true })
    tenant: Tenant;

    @Field({ nullable: true })
    get tenantName(): string | undefined {
        // @ts-ignore
        return this.tenant && typeof this.tenant === 'object' ? this.tenant.name : undefined;
    }
} 