# Facebook数据同步功能 - 开发进度

## ✅ 已完成的功能

### 1. Facebook API服务重构
- ✅ 移除全局Token依赖，改为使用每个广告账户的OAuth信息
- ✅ 修改`FacebookApiService`支持`AdAccountOAuth`接口
- ✅ 更新所有API方法接受`accessToken`参数
- ✅ 保持批量处理和错误处理逻辑

### 2. VCC组件OAuth集成
- ✅ 移除Facebook Token设置相关组件和状态
- ✅ 删除`FacebookTokenModal.tsx`组件
- ✅ 修改同步逻辑从`adAccountData`获取OAuth信息
- ✅ 重新实现单个卡片同步和批量同步功能
- ✅ 添加OAuth信息验证和错误处理

### 3. 用户体验优化
- ✅ 简化操作流程，无需手动设置Token
- ✅ 自动使用系统中已授权的广告账户
- ✅ 保持原有的同步状态显示和错误提示
- ✅ 维持批量同步和单个同步功能

## 🔄 当前实现逻辑

### OAuth信息获取流程：
1. 从VCC卡片的`boundAdAccountIds`获取绑定的广告账户ID
2. 在`adAccountData.adAccounts`中查找对应的广告账户
3. 提取每个账户的`oauth`字段作为访问令牌
4. 构建`AdAccountOAuth[]`数组传递给Facebook API

### 同步数据处理：
```typescript
// 过滤有效OAuth账户
const accountOAuths: AdAccountOAuth[] = boundAdAccounts
  .filter((account: AdAccount) => account.oauth && account.oauth.trim() !== '')
  .map((account: AdAccount) => ({
    accountId: account.accountId,
    accessToken: account.oauth!
  }));

// 调用Facebook API
const facebookApiService = new FacebookApiService();
const facebookAccountData = await facebookApiService.getBatchAccountData(accountOAuths);
```

## 📋 待后端支持的功能

### 1. 数据持久化
- [ ] 实现VCC卡片Facebook数据更新API
- [ ] 支持以下字段的更新：
  - `fbRealTimeSpend` - Facebook实时消费
  - `fbSyncStatus` - 同步状态
  - `fbLastSyncTime` - 最后同步时间
  - `fbAccountStatuses` - 各账户状态映射

### 2. 批量更新接口
- [ ] 支持批量更新多个VCC卡片的Facebook数据
- [ ] 优化数据库写入性能

## ⚠️ 依赖条件

### 1. 广告账户OAuth数据
- 广告账户表必须包含有效的`oauth`字段
- OAuth令牌必须具有`ads_read`权限
- OAuth令牌必须在有效期内

### 2. 数据一致性
- VCC卡片的`boundAdAccountIds`必须与广告账户的ID匹配
- 广告账户的`accountId`必须是有效的Facebook广告账户ID

## 🧪 测试场景

### 1. 成功同步场景
- [x] 有效OAuth + 有效广告账户ID
- [x] 多个广告账户聚合数据
- [x] 批量同步多个VCC卡片

### 2. 错误处理场景
- [x] 无OAuth信息的广告账户
- [x] 过期的OAuth令牌
- [x] 无效的广告账户ID
- [x] Facebook API限制

### 3. 边界情况
- [x] 未绑定广告账户的VCC卡片
- [x] 空的广告账户列表
- [x] 网络错误和超时

## 📊 数据映射说明

### Facebook API 数据 → VCC 字段映射：
- `spend` → `consumption` (消费)
- `clicks` → `transactionCount` (交易数)
- `account_status` → `adAccountStatus` (活跃账户数)
- `impressions` → 仅用于展示，不存储

### 聚合逻辑：
- 消费金额：所有绑定账户的spend总和
- 交易数：所有绑定账户的clicks总和  
- 活跃账户数：状态为'ACTIVE'的账户数量
- 账户状态：各账户ID与状态的映射对象

## 🔗 相关文件

### 核心文件：
- `src/services/facebookApi.ts` - Facebook API服务
- `src/pages/management/system/organization/VCCListTab.tsx` - VCC列表组件

### 已删除：
- `src/pages/management/system/organization/FacebookTokenModal.tsx` - Token设置弹窗

### 接口定义：
```typescript
interface AdAccountOAuth {
  accountId: string;
  accessToken: string;
}

interface AdAccount {
  id: string;           // 内部UUID
  accountId: string;    // Facebook广告账户ID
  account: string;      // 账户名称
  oauth?: string;       // OAuth访问令牌
  // 其他字段...
}
```

## 🚀 下一步开发

1. **后端API实现**：实现VCC卡片Facebook数据更新接口
2. **实时数据同步**：考虑WebSocket或定时同步机制
3. **数据缓存**：减少Facebook API调用频率
4. **权限管理**：OAuth令牌的刷新和管理机制

## 📋 功能概述

VCC卡片与Facebook广告账号数据同步功能，用于获取实时的消费数据、交易数和广告账号存活状态。

## 🎯 当前状态

- ✅ 前端UI已完成（消费对比、同步状态显示、操作按钮）
- ✅ 前端逻辑已完成（模拟同步功能）
- ❌ 后端GraphQL接口待实现
- ❌ Facebook Marketing API集成待实现

## 🔧 后端需要实现的接口

### 1. GraphQL Schema扩展

```graphql
# VCC卡片类型扩展
type VccCard {
  # ... 现有字段 ...
  fbSyncStatus: String      # 同步状态: syncing|success|error|pending
  fbLastSyncTime: DateTime  # 最后同步时间
  fbRealTimeSpend: Float    # Facebook实时消费
  fbAccountStatuses: JSON   # 各账户状态映射
}

# Facebook账户统计数据
type FacebookAccountStats {
  accountId: String!
  spend: Float!             # 消费金额
  impressions: Int!         # 展示次数
  clicks: Int!              # 点击次数
  accountStatus: String!    # 账户状态 (ACTIVE, DISABLED, PAUSED)
  lastUpdated: DateTime!
}

# 输入类型
input SyncFacebookDataInput {
  vccCardId: ID!
}

input BatchSyncFacebookDataInput {
  vccCardIds: [ID!]!
}

# Mutation扩展
extend type Mutation {
  syncFacebookData(input: SyncFacebookDataInput!): VccCard!
  batchSyncFacebookData(input: BatchSyncFacebookDataInput!): [VccCard!]!
}

# Query扩展
extend type Query {
  facebookAccountStats(accountIds: [String!]!): [FacebookAccountStats!]!
}
```

### 2. Facebook Marketing API集成

```typescript
// 需要的Facebook API端点
const FB_API_BASE = 'https://graph.facebook.com/v18.0';

const API_ENDPOINTS = {
  // 获取广告账户列表
  AD_ACCOUNTS: '/me/adaccounts',
  
  // 获取账户消费数据  
  ACCOUNT_INSIGHTS: '/{ad-account-id}/insights',
  
  // 获取账户状态
  ACCOUNT_INFO: '/{ad-account-id}',
  
  // 获取广告活动
  CAMPAIGNS: '/{ad-account-id}/campaigns',
  
  // 获取广告组
  AD_SETS: '/{ad-account-id}/adsets',
  
  // 获取广告
  ADS: '/{ad-account-id}/ads'
};

// 需要的权限
const REQUIRED_PERMISSIONS = [
  'ads_read',
  'ads_management', 
  'business_management'
];
```

### 3. 数据同步逻辑

```typescript
// 同步服务示例
class FacebookSyncService {
  
  // 单个VCC卡片同步
  async syncVccCardData(vccCardId: string): Promise<VccCard> {
    // 1. 获取VCC卡片及绑定的广告账户ID
    // 2. 调用Facebook API获取账户数据
    // 3. 聚合消费数据、状态数据
    // 4. 更新VCC卡片的Facebook字段
    // 5. 返回更新后的VCC卡片
  }
  
  // 批量同步
  async batchSyncVccCards(vccCardIds: string[]): Promise<VccCard[]> {
    // 并发调用单个同步，控制并发数量
  }
  
  // 获取Facebook账户统计
  async getFacebookAccountStats(accountIds: string[]): Promise<FacebookAccountStats[]> {
    // 调用Facebook Insights API
  }
}
```

## 📊 Facebook API调用示例

### 获取账户消费数据
```bash
curl -G \
  -d "access_token=YOUR_ACCESS_TOKEN" \
  -d "fields=spend,impressions,clicks" \
  -d "time_range={'since':'2024-01-01','until':'2024-01-31'}" \
  "https://graph.facebook.com/v18.0/act_ACCOUNT_ID/insights"
```

### 获取账户状态
```bash
curl -G \
  -d "access_token=YOUR_ACCESS_TOKEN" \
  -d "fields=account_status,name,account_id" \
  "https://graph.facebook.com/v18.0/act_ACCOUNT_ID"
```

## 🔐 安全考虑

1. **Access Token管理**
   - 使用长期Token或Business Manager Token
   - 定期刷新Token
   - 安全存储Token

2. **API限制**
   - 遵守Facebook API调用限制
   - 实现重试机制
   - 使用批量API减少调用次数

3. **权限控制**
   - 验证用户对广告账户的访问权限
   - 只同步用户有权限的账户

## 📈 性能优化

1. **缓存策略**
   - 缓存Facebook数据，避免频繁API调用
   - 设置合理的缓存过期时间

2. **异步处理**
   - 大批量同步使用队列处理
   - 实现同步状态实时推送

3. **数据聚合**
   - 按时间维度聚合消费数据
   - 预计算常用统计指标

## 🐛 错误处理

1. **API错误**
   - Facebook API限制
   - 账户权限不足
   - Token过期

2. **数据错误**
   - 账户ID不存在
   - 数据格式异常
   - 网络超时

3. **业务错误**
   - VCC卡片未绑定广告账户
   - 同步频率过高

## 🚀 实现步骤

1. **Phase 1: 基础接口**
   - 实现GraphQL schema扩展
   - 实现基础的同步接口

2. **Phase 2: Facebook API集成**
   - 集成Facebook Marketing API
   - 实现Token管理

3. **Phase 3: 数据同步**
   - 实现数据获取和聚合逻辑
   - 实现缓存和错误处理

4. **Phase 4: 优化**
   - 性能优化
   - 监控和告警

## 📝 测试建议

1. **单元测试**
   - Facebook API调用
   - 数据聚合逻辑
   - 错误处理

2. **集成测试**
   - 端到端同步流程
   - 批量同步性能

3. **压力测试**
   - 大量VCC卡片同步
   - API限制测试

## 📚 参考文档

- [Facebook Marketing API文档](https://developers.facebook.com/docs/marketing-api/)
- [Facebook Insights API](https://developers.facebook.com/docs/marketing-api/insights/)
- [GraphQL最佳实践](https://graphql.org/learn/best-practices/) 