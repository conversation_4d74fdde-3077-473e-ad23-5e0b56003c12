import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDailyBudgetToAdSet1748520756887 implements MigrationInterface {
    name = 'AddDailyBudgetToAdSet1748520756887'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set" ADD COLUMN "dailyBudget" float DEFAULT 0;
        `);
        await queryRunner.query(`
            UPDATE "ad_set" SET "dailyBudget" = 0 WHERE "dailyBudget" IS NULL;
        `);
        await queryRunner.query(`
            ALTER TABLE "ad_set" ALTER COLUMN "dailyBudget" SET NOT NULL;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set" DROP COLUMN "dailyBudget";
        `);
    }
} 