// 用户状态枚举
export enum UserStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    BLOCKED = 'BLOCKED'
}

// 用户项
export interface UserItem {
    id: string;
    username: string;
    email: string;
    avatar?: string;
    fullName?: string;
    phone?: string;
    address?: string;
    registerIp?: string;
    status: UserStatus;
    roles?: {
        id: string;
        name: string;
    }[];
    groups?: {
        id: string;
        name: string;
    }[];
    createdAt: string;
    updatedAt: string;
}

// 分页信息
export interface PageInfo {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
}

// 用户列表响应
export interface UserConnection {
    nodes: UserItem[];
    totalCount: number;
    pageInfo: PageInfo;
}

// 创建用户参数
export interface CreateUserParams {
    username: string;
    email: string;
    password: string;
    avatar?: string;
    fullName?: string;
    phone?: string;
    address?: string;
    registerIp?: string;
    status?: UserStatus;
    roleIds?: string[];
    groupIds?: string[];
}

// 更新用户参数
export interface UpdateUserParams {
    id: string;
    username?: string;
    email?: string;
    password?: string;
    avatar?: string;
    fullName?: string;
    phone?: string;
    address?: string;
    registerIp?: string;
    status?: UserStatus;
    roleIds?: string[];
    groupIds?: string[];
}

// 用户查询参数
export interface UserQueryParams {
    filter?: {
        username?: string;
        email?: string;
        fullName?: string;
        phone?: string;
        status?: UserStatus;
        roleIds?: string[];
        groupIds?: string[];
    };
    page?: number;
    limit?: number;
    sorting?: {
        field: string;
        direction: 'ASC' | 'DESC';
    }[];
} 