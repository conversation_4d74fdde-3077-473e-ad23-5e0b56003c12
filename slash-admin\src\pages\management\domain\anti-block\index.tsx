import { useTranslation } from 'react-i18next';
/*
 * @Author: 潘孝权
 * @Date: 2025-06-03 21:29:51
 * @Description: 
 * @FilePath: \sking_frontend\slash-admin\src\pages\management\domain\anti-block\index.tsx
 */
import { useEffect, useState } from "react";
import { Table, Button, Tag, Input, Space, Modal, Form, Select } from "antd";
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table/interface";
import { useLazyQuery, useMutation } from '@apollo/client';
import { toast } from 'sonner'
import {
  GET_ANTIBLOCKS, CREATE_MANY_ANTIBLOCKS_MUTATION,
  DELETE_ANTI_BLOCKS
} from "@/api/antiBlock"
import { useSetState } from "ahooks";
import { AntiBlockInput } from "./type"
import { TablePaginationConfig } from "antd/lib";
import { useLocalStorage } from "react-use";
import antiBlockService from "@/api/services/antiBlockService";
interface DomainData {
  id: string;
  url: string;
  status: string;
  relatedDomain: string;
  updateTime: string;
}

const mockData: DomainData[] = [
  {
    id: "1",
    url: "https://example1.com",
    status: 'key_1465',
    relatedDomain: "example-main.com",
    updateTime: "2025-05-07 14:30:22"
  },
  {
    id: "2",
    url: "https://example2.com",
    status: 'key_863',
    relatedDomain: "example-main.com",
    updateTime: "2025-05-06 09:15:43"
  },
  {
    id: "3",
    url: "https://example3.com",
    status: 'key_893',
    relatedDomain: "example-secondary.com",
    updateTime: "2025-05-08 10:22:15"
  }
];

export default function AntiBlockDomainManagement() {
  const { t } = useTranslation();
  const [res, setData] = useState<AntiBlockInput[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [loadData, { refetch }] = useLazyQuery(GET_ANTIBLOCKS, { fetchPolicy: 'network-only' });
  const [createMany] = useMutation(CREATE_MANY_ANTIBLOCKS_MUTATION);
  const [deleteAntiBlock] = useMutation(DELETE_ANTI_BLOCKS, {
    refetchQueries: [{ query: GET_ANTIBLOCKS }],
  });
  const [userInfo] = useLocalStorage<any>('userStore')
  const [paginationProps, setPaginationProps] = useSetState({
    total: 0, // 数据总数
    pageSize: 10, // 每页条数
    current: 1, // 当前页码
  })
  const userId = userInfo?.state?.userInfo?.id;
  const columns: ColumnsType<AntiBlockInput> = [
    {
      title: t('pages.key_844'),
      dataIndex: "id",
      key: "id",
      width: 80,
      render: (_, record, index) => {
        return (paginationProps.current - 1) * paginationProps.pageSize + index + 1;
      },

    },
    {
      title: t('pages.key_544'),
      dataIndex: "url",
      key: "url",
      width: 250,
    },
    {
      title: t('pages.key_1602'),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        let color = "green";
        if (status === 'key_863') {
          color = "red";
        } else if (status === 'key_893') {
          color = "gold";
        }
        return <Tag color={color}>{t(`pages.${status}`)}</Tag>;
      }
    },
    {
      title: t('pages.key_873'),
      dataIndex: "relatedDomain",
      key: "relatedDomain",
      width: 180,
    },
    {
      title: t('pages.key_250'),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 180,
    },
    {
      title: t('pages.key_1058'),
      key: "actions",
      width: 160,
      render: (_: any, record: AntiBlockInput) => (
        <Space>
          <Button type="link" danger icon={<DeleteOutlined />} size="small" onClick={() => handleDelete(record)}>{t('pages.key_287')}</Button>
        </Space>
      )
    }
  ];

  // const handleSearch = (value: string) => {
  //   setSearchText(value);
  //   const filteredData = mockData.filter(item =>
  //     item.url.toLowerCase().includes(value.toLowerCase()) ||
  //     item.relatedDomain.toLowerCase().includes(value.toLowerCase())
  //   );
  //   setData(filteredData);
  // };

  const showModal = async () => {
    setIsModalOpen(true);
  };
  const initData = async () => {
    const data = await antiBlockService.getAntiBlockList({
      paging: {
        limit: paginationProps.pageSize,
        offset: (paginationProps.current - 1) * paginationProps.pageSize,
      },
      filter: {},
      sorting: [
        { "field": "createdAt", "direction": "DESC" }
      ]
    }) as any
    let arr = [] as AntiBlockInput[]
    setPaginationProps({ total: data?.nodes?.length || 0 })
    data?.nodes?.forEach((item: AntiBlockInput) => {
      arr.push({ ...item, createdAt: item.createdAt?.slice(0, 19).replace('T', ' ') })
    }) as AntiBlockInput[]
    setData(arr)
  }

  const handleDelete = async (record: AntiBlockInput) => {
    await deleteAntiBlock({
      variables: {
        input: { id: record.id },
        sorting: [{ field: "id", direction: "ASC" }]  // 添加默认排序
      }
    });
    toast.success(t('pages.key_294'));
    setPaginationProps({ current: 1 })
    initData();
  };

  const extractMainDomain = (url: string) => {
    // 移除协议和路径
    let domain = url.replace(/^(https?:\/\/)?(www\.)?/, '').split('/')[0];
    // 获取主域名（简单处理）
    let parts = domain.split('.');
    if (parts.length <= 2) return domain;
    return parts.slice(-2).join('.');
  }
  // 校验是否为合法 URL
  const isValidURL = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };
  const handleOk = () => {
    form.validateFields().then(async values => {
      if (values.domains.split('\n').every((item: string) => isValidURL(item))) {
        const data = await createMany({
          variables: {
            antiBlocks: values.domains.split('\n').map((subItem: string) => ({
              relatedDomain: extractMainDomain(subItem),
              url: subItem,
              status: 'key_1465',
              userId
            }))
          }
        });
        if (data?.data?.createManyAntiBlocks?.length) {
          toast.success(t('pages.key_1149'));
          initData();
        }
        setIsModalOpen(false);
        form.resetFields();
      }
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  // 批量导入模板表格
  const templateColumns: ColumnsType<any> = [
    {
      title: t('pages.key_844'),
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: t('pages.key_544'),
      dataIndex: "url",
      key: "url",
      width: 250,
    },
    {
      title: t('pages.key_1602'),
      dataIndex: "status",
      key: "status",
      width: 100,
    }
  ];
  const handleStandardTableChange = (val: TablePaginationConfig) => {
    setPaginationProps({
      current: val.current as number,
      pageSize: val.pageSize as number,
    })

  }
  useEffect(() => {
    initData()
  }, [paginationProps.current, paginationProps.pageSize])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{t('pages.key_2515')}</h1>
        <Button type="primary" onClick={showModal} icon={<PlusOutlined />}>{t('pages.key_981')}</Button>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">


        <Table
          onChange={handleStandardTableChange}
          columns={columns}
          dataSource={res}
          rowKey="id"
          pagination={paginationProps}
        />
      </div>

      <Modal
        title={t('pages.key_982')}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
      >
        <div className="mb-4">
          <h3 className="text-lg font-medium mb-2">{t('pages.key_552')}</h3>
          <Table
            columns={templateColumns}
            dataSource={mockData}
            rowKey="id"
            pagination={false}
            bordered
          />
        </div>
        <Form form={form} layout="vertical">
          <Form.Item
            name="domains"
            label={t('pages.key_680')}
            rules={[{ required: true, message: t('pages.key_2188') }, () => ({
              validator(_, value) {
                if (!value) return Promise.resolve(); // 空值由required控制
                const urls = value.split('\n').map((url: string) => url.trim()).filter(Boolean);
                const invalidUrls = urls.filter((url: string) => !isValidURL(url));
                if (invalidUrls.length === 0) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(`以下URL格式不正确：\n${invalidUrls.join('\n')}`));
              },
            }),]}
          >
            <Input.TextArea rows={4} placeholder={t('pages.key_2189')} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
