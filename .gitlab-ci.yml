image: bitnami/git:latest

stages:
  - sync

sync_to_github:
  stage: sync
  tags:
    - global-runner
  script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "C<PERSON> Bot"
    - git remote remove github || true
    - git remote add github https://$<EMAIL>/bauer37/sking_frontend.git
    - git push github HEAD:$CI_COMMIT_REF_NAME --force
  rules:
    - if: '$CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != "test"'
      when: always
