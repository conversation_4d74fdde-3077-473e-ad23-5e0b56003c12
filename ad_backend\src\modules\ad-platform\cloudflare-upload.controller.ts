import { Controller, Post, UseInterceptors, UploadedFile, Body, BadRequestException, Query, Get } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { cfClient, cloudflareConfig, uploadToCf } from '../../utils/utils';
import { ListObjectsV2Command } from '@aws-sdk/client-s3';
// import { Public } from '../auth/public.decorator';

@Controller('cloudflare/upload')
export class CloudflareUploadController {
    /**
     * 处理文件上传到Cloudflare R2的请求
     * @param file 上传的文件
     * @param userId 用户ID (从请求体中获取)
     * @param source 来源 (从请求体中获取，默认为'default')
     * @returns 上传成功的信息，包含文件URL
     */
    @Post()
    // @Public() // 如果需要公开访问，添加此装饰器
    @UseInterceptors(FileInterceptor('file'))
    async uploadFile(
        @UploadedFile() file: Express.Multer.File,
        @Body('userId') userId: string,
        @Body('source') source = 'default',
    ) {
        if (!file) {
            throw new BadRequestException('No file uploaded');
        }

        if (!userId) {
            throw new BadRequestException('User ID is required');
        }

        try {
            // 调用uploadToCf函数上传文件
            const fileKey = await uploadToCf(userId, {
                buffer: file.buffer,
                originalname: file.originalname,
            }, source);

            // 返回成功响应
            return {
                success: true,
                fileUrl: `https://r2-ad.yeeu.net/${fileKey}`,
                fileKey,
                originalName: file.originalname,
                size: file.size,
                mimeType: file.mimetype,
            };
        } catch (error) {
            console.error('Upload error:', error);
            throw new BadRequestException(`File upload failed: ${error.message}`);
        }
    }
    @Get('list')
    async getFileList(
        @Query('userId') userId: string,
        @Query('source') source = 'default'
    ) {
        console.log('Getting file list for userId:', userId);

        if (!userId) {
            throw new BadRequestException('用户ID是必需的');
        }

        try {
            // 创建ListObjectsV2命令，查询指定用户和来源前缀的所有文件
            const command = new ListObjectsV2Command({
                Bucket: cloudflareConfig.CF_BUCKET_NAME,
                Prefix: `${userId}/${source}/`, // 使用用户ID和source作为前缀
            });

            // 执行命令获取文件列表
            const response = await cfClient.send(command);

            // 如果没有文件，返回空数组
            if (!response.Contents || response.Contents.length === 0) {
                return {
                    success: true,
                    totalFiles: 0,
                    files: [],
                };
            }

            // 处理文件列表，提取文件名和URL
            const files = response.Contents.map(item => {
                // 从完整路径中提取文件名（去除用户ID和source前缀）
                const fileKey = item.Key;
                const originalName = fileKey.replace(`${userId}/${source}/`, '');

                return {
                    fileUrl: `https://r2-ad.yeeu.net/${fileKey}`,
                    fileKey,
                    originalName,
                    size: item.Size,
                    lastModified: item.LastModified,
                };
            });

            // 返回文件列表
            return {
                success: true,
                totalFiles: files.length,
                files,
            };
        } catch (error) {
            console.error('获取文件列表失败:', error);
            throw new BadRequestException(`获取文件列表失败: ${error.message}`);
        }
    }
}