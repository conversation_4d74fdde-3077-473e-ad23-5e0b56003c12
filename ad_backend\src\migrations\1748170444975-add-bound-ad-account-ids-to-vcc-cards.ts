import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBoundAdAccountIdsToVccCards1748170444975 implements MigrationInterface {
    name = 'AddBoundAdAccountIdsToVccCards1748170444975'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "vcc_cards" ADD "boundAdAccountIds" json`);
        await queryRunner.query(`COMMENT ON COLUMN "vcc_cards"."boundAdAccountIds" IS '绑定的广告账户ID数组'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`COMMENT ON COLUMN "vcc_cards"."boundAdAccountIds" IS '绑定的广告账户ID数组'`);
        await queryRunner.query(`ALTER TABLE "vcc_cards" DROP COLUMN "boundAdAccountIds"`);
    }

}
