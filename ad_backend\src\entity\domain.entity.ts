import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@ObjectType()
@Entity('domain')
export class Domain {
    @Field(() => ID)
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Field()
    @Column()
    name: string; // 用户自定义域名

    @Field()
    @Column()
    tenantId: string; // 归属租户

    @Field()
    @Column({ default: 'pending' })
    status: string;

    @Field()
    @Column({ default: 'pending' })
    sslStatus: string;

    @Field()
    @Column({ default: 'pwa.yeeu.net' })
    cnameTarget: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    cfId: string; // Cloudflare custom hostname id

    @Field({ nullable: true })
    @Column({ nullable: true })
    error: string;

    @Field()
    @CreateDateColumn()
    createdAt: Date;

    @Field()
    @UpdateDateColumn()
    updatedAt: Date;
} 