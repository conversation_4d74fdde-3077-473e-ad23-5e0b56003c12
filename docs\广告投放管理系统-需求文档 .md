

**文档版本：** V1.3

**更新日期：** 2025/4/21

**作者：** SsoJoo

  

**目录**

- [1. 项目技术架构图](#1-项目技术架构图)

- [2. 数据流图](#2-数据流图)

- [3. 各项功能模块说明](#3-各项功能模块说明)

- [4. 接口设计与接口文档](#4-graphql接口设计与接口文档)

- [5. 系统部署与扩展性](#5-系统部署与扩展性)

- [6. 多租户SaaS架构说明](#6-多租户saas架构说明)

  

## 1. 项目技术架构图

  

本系统采用基于微服务的多租户SaaS架构，主要包含以下核心模块：

- 物料管理服务（图片、视频、文案管理）

- 账号管理服务（Facebook BM授权、账户同步）

- VCC管理服务（支付方式导入、绑定、余额监控）

- 流量分发与落地页服务（斗篷规则、入口URL、重定向）

- 广告发布服务（批量广告生成与发布）

- 自动优化服务（规则引擎、自动调整预算与创意）

- 数据集成与分析服务（数据同步、报表、转化分析）

- 系统风险控制服务（权限管理、风险预警）

- 用户认证与权限服务

  

技术栈说明：

- 后端：Node.js + Express + GraphQL（Apollo Server）

- 数据库：PostgreSQL（支持多租户架构，含分库分表）

- 前端：React + Ant Design + Apollo Client

- 斗篷服务：高性能Node.js微服务，支持水平扩展，保障高并发

- API网关：用于请求路由、负载均衡、身份验证、监控

- 第三方集成：Facebook Ads API，IP地理库服务

- 高可用组件（可按需配置）：

* Redis集群：会话存储、缓存、分布式锁

* 消息中间件：异步通信、事件驱动架构

* 计划任务集群：定时同步、监控与预警

* 内容分发网络(CDN)：静态资源加速、全球流量分发

  

### 1.1 技术架构图

  

### 1.1 系统架构图

  

```mermaid

flowchart TB

subgraph 客户端层

Browser[Web浏览器]

Mobile[移动应用]

end

subgraph 接入层

LB[负载均衡器]

CDN[内容分发网络]

LB --> APIGW

end

subgraph 应用服务层

APIGW[API网关]

Auth[认证授权服务]

APIGW --> Auth

APIGW --> CoreAPI

subgraph 核心服务

CoreAPI[GraphQL API服务]

MM[物料管理服务]

AM[账号管理服务]

VCC[VCC管理服务]

LP[流量分发服务]

AD[广告发布服务]

OPT[自动优化服务]

DA[数据分析服务]

RISK[风险控制服务]

end

subgraph 基础服务

MQ[消息队列]

CRON[计划任务]

CACHE[缓存集群]

end

end

subgraph 数据层

PG[(PostgreSQL集群)]

CoreAPI --> PG

CoreAPI --> CACHE

CoreAPI --> MQ

end

subgraph 外部系统

FB[Facebook Ads API]

IPDB[IP地理库]

MAIL[邮件服务]

end

Browser --> CDN

Browser --> LB

Mobile --> LB

AM <--> FB

VCC <--> FB

LP --> IPDB

RISK --> MAIL

CRON --> CoreAPI

```

  

## 2. 数据流图

  

```mermaid

flowchart TD

U1(用户上传物料) --> MM1[物料管理服务]

U2(用户导入VCC) --> VCC1[VCC管理服务]

U3(用户绑定VCC与账户) --> VCC2[VCC管理服务]

VCC2 -->|API调用| FB1[Facebook Ads API]

U4(广告账户数据同步) --> AM1[账号管理服务]

AM1 -->|API调用| FB1

U5(配置斗篷规则/落地页) --> LP1[流量分发服务]

U6(广告点击流量) --> LP2[斗篷服务入口URL]

LP2 -->|规则判断| LP3[302重定向]

LP3 -->|到主落地页/安全页| OUT1(访客)

U7(自动优化规则触发) --> OPT1[自动优化服务]

OPT1 -->|调整预算/暂停广告/通知| AM1

U8(数据分析) --> DA1[数据集成与分析服务]

DA1 -->|报表/看板| U8

RISK1(系统风险监控) --> RISK2[风险控制服务]

RISK2 -->|预警通知| U9(用户)

```

  

### 2.1 用户角色与权限控制

  

```mermaid

flowchart TD

classDef role fill:#f9f9f9,stroke:#333,stroke-width:1px

classDef module fill:#e3f2fd,stroke:#1976d2,stroke-width:2px

SysAdmin[系统管理员]:::role

TenantAdmin[租户管理员]:::role

TeamLead[团队负责人]:::role

Optimizer[优化师]:::role

Advertiser[投手]:::role

SYS[系统配置]:::module

TENANT[租户管理]:::module

MM[物料管理]:::module

AM[账号管理]:::module

VCC[VCC管理]:::module

LP[流量分发与LP]:::module

AD[广告发布]:::module

OPT[自动优化]:::module

DA[数据分析]:::module

RISK[风险控制]:::module

AUTH[权限管理]:::module

SysAdmin -->|完全控制| SYS

SysAdmin -->|创建管理| TENANT

TenantAdmin -->|完全控制| AUTH

TenantAdmin -->|完全控制| RISK

TenantAdmin -->|查看所有| DA

TeamLead -->|管理| AUTH

TeamLead -->|管理| AM

TeamLead -->|查看全部| DA

TeamLead -->|查看| RISK

Optimizer -->|完全控制| OPT

Optimizer -->|管理| LP

Optimizer -->|查看全部| DA

Advertiser -->|使用| MM

Advertiser -->|使用| AM

Advertiser -->|查看| VCC

Advertiser -->|基础使用| LP

Advertiser -->|使用| AD

Advertiser -->|基础使用| OPT

Advertiser -->|个人数据| DA

Advertiser -->|查看| RISK

```

  

## 3. 各项功能模块说明

  

### 3.1 物料管理

- 支持批量上传图片、视频，自动检测Facebook尺寸要求

- 文案编辑器支持多语言及敏感词检测

- 落地页URL库管理主落地页和安全页，支持标签分类

- 创意单元组合管理，关联历史投放数据

- Facebook广告预览功能

- 广告像素、受众人群管理功能

- 域名管理，支持防封域名批量上传

  

### 3.2 账号管理

- OAuth批量授权连接Facebook BM，实时同步账户信息

- 主页、像素关联管理，受众跨账户共享

- 账户风险评估与预警，支持账户结构克隆与分组管理

  

### 3.3 VCC管理

- 支持批量导入VCC卡信息，包括卡号、余额、有效期、CVV等、如有条件，可使用三方服务一键批量生成vcc

- VCC列表展示，显示卡号（部分隐藏）、有效期、当前余额（同步自Facebook）、状态、标签、绑定账户

- 余额预警阈值设置及预警通知（支持邮件、系统内通知、Webhook）

- 绑定和消费历史记录，支持批量状态更新

- 不支持平台内充值、支付，仅做数据同步与风险监控

- VCC数据安全考量：

* 所有展示的卡号都经过部分掩码处理（仅显示前6后4位）

* 系统不存储CVV和完整卡号，仅记录掩码后的卡号和过期时间

* 所有API请求都通过HTTPS加密传输，符合PCI DSS安全标准

* 数据库中敏感字段采用字段级加密

  

### 3.4 流量分发与Landing Page

- Landing Page配置，关联主落地页和安全页URL

- 斗篷规则配置，支持多维度访客信息判断（IP、UA、Referer、语言等）

- IP黑名单库管理，入口URL生成与流量重定向

- 斗篷日志记录与规则测试工具

- 流量分发机制支持多主落地页/安全页

  

### 3.5 一键发布海量广告

- 广告活动结构模板管理

- 批量组合生成广告变体，发布前预览

- 批量选择账户、主页、像素及VCC绑定投放

- 多变量A/B测试支持

- 发布过程实时监控与快速复制广告结构

- 发布前内容风险提示

  

### 3.6 自动优化

- 灵活规则引擎支持多指标和逻辑运算

- 规则触发动作包括暂停/启动、预算调整、创意更换、通知发送

- 规则应用于活动、广告组、广告层级

- 预算自动管理与创意轮换规则

- 规则执行日志与优先级设置

- 异常检测规则及基于VCC余额和Landing Page表现的自动化规则

  

### 3.7 数据集成与分析

- 自动同步Facebook广告数据及第三方转化数据

- 多维度自定义报表和可视化看板

- 创意性能分析、历史趋势、漏斗分析

- Gaming行业关键指标计算展示

- 数据导出功能

- Landing Page及斗篷流量分析报告

- VCC消耗报告

  

### 3.8 系统风险控制

- 重要操作风险提示，明确Facebook政策风险

- 斗篷配置权限独立控制

- VCC敏感数据保护，符合PCI DSS标准

- 账户状态联动暂停自动化规则和发布任务

- 异常活动监控与预警

  

## 4. GraphQL接口设计与接口文档

  

本系统所有核心API均采用GraphQL接口，便于前后端联调和灵活扩展。通过统一的GraphQL Schema实现强类型API，并支持字段级别的权限控制。以下为主要类型定义与典型查询/变更示例。

  

### 4.1 物料管理 GraphQL Schema

  

```graphql

type Material {

id: ID!

type: String! # image/video/text

url: String!

tags: [String!]

createdAt: String!

meta: MaterialMeta

}

  

type MaterialMeta {

width: Int

height: Int

duration: Int

language: String

riskLevel: String

}

  

input MaterialUploadInput {

files: [Upload!]!

type: String!

tags: [String!]

}

  

type MaterialUploadPayload {

materialIds: [ID!]!

warnings: [String!]

}

  

type Query {

materials(type: String, tags: [String!], page: Int, size: Int): [Material!]!

}

  

type Mutation {

uploadMaterials(input: MaterialUploadInput!): MaterialUploadPayload!

}

```

  

### 4.2 账号管理 GraphQL Schema

  

```graphql

type Account {

id: ID!

name: String!

status: String!

balance: Float

currency: String

vccs: [VCC!]

riskLevel: String

}

  

type Query {

accounts(page: Int, size: Int): [Account!]!

}

  

type Mutation {

oauthCallback(code: String!): [Account!]!

}

```

  

### 4.3 VCC管理 GraphQL Schema

  

```graphql

type VCC {

id: ID!

cardNoMasked: String!

expire: String!

balance: Float

status: String!

tags: [String!]

bindAccounts: [Account!]

warning: Boolean

lastSyncTime: String

historyData: [VCCHistory!]

}

  

type VCCHistory {

id: ID!

timestamp: String!

action: String! # BIND/UNBIND/BALANCE_UPDATE

accountId: String

oldBalance: Float

newBalance: Float

note: String

}

  

type Query {

vccList(filters: VCCFilterInput, page: Int, size: Int): [VCC!]!

vccDetails(id: ID!): VCC

vccWarnings: [VCC!]!

}

  

input VCCFilterInput {

status: String

tags: [String!]

warningOnly: Boolean

accountId: ID

}

  

type Mutation {

setVCCWarningThreshold(vccId: ID!, thresholdType: String!, thresholdValue: Float!): Boolean!

updateVCCTags(vccId: ID!, tags: [String!]!): Boolean!

syncVCCDataFromFacebook(accountIds: [ID!]): SyncStatusPayload!

}

  

type SyncStatusPayload {

success: Boolean!

message: String

syncedCount: Int

failedCount: Int

}

```

  

### 4.4 流量分发与Landing Page GraphQL Schema

  

```graphql

type CloakRule {

id: ID!

name: String!

conditions: [String!]!

action: String!

priority: Int!

}

  

type LandingPage {

id: ID!

entryUrl: String!

mainUrls: [String!]!

safeUrls: [String!]!

cloakRules: [CloakRule!]

}

  

type CloakLog {

id: ID!

time: String!

ip: String!

ua: String!

result: String!

}

  

type Query {

landingPage(id: ID!): LandingPage

cloakLogs(timeRange: String, filters: String): [CloakLog!]

}

  

type Mutation {

createCloakRule(name: String!, conditions: [String!]!, action: String!, priority: Int!): CloakRule!

}

```

  

### 4.5 广告发布 GraphQL Schema

  

```graphql

type AdVariant {

id: ID!

previewUrl: String!

status: String!

}

  

type Mutation {

createCampaignTemplate(name: String!, structure: String!, defaultSettings: String): ID!

generateAdVariants(materialIds: [ID!]!, landingPageConfigId: ID!, templateId: ID!): [AdVariant!]!

publishAds(variantIds: [ID!]!, accountIds: [ID!]!, vccBindingSettings: String): Boolean!

}

```

  

### 4.6 自动优化 GraphQL Schema

  

```graphql

type AutoOptRule {

id: ID!

name: String!

conditions: String!

actions: String!

targetLevel: String!

priority: Int!

}

  

type AutoOptLog {

id: ID!

ruleId: ID!

time: String!

result: String!

}

  

type Query {

autoOptLogs(timeRange: String, ruleId: ID): [AutoOptLog!]

}

  

type Mutation {

createAutoOptRule(name: String!, conditions: String!, actions: String!, targetLevel: String!, priority: Int!): AutoOptRule!

}

```

  

### 4.7 数据集成与分析 GraphQL Schema

  

```graphql

type PerformanceData {

id: ID!

accountId: ID!

metrics: String!

}

  

type TrafficReport {

id: ID!

landingPageId: ID!

stats: String!

}

  

type Query {

performanceData(filters: String): [PerformanceData!]

cloakTrafficReport(filters: String): [TrafficReport!]

exportData(format: String!, filters: String): String! # 返回下载URL

}

```

  

### 4.8 多租户用户与权限管理 GraphQL Schema

  

```graphql

# 租户相关

type Tenant {

id: ID!

name: String!

plan: String! # BASIC/STANDARD/ENTERPRISE

status: String!

createdAt: String!

adminUsers: [User!]

features: TenantFeatures!

usageStatistics: TenantUsage

}

  

type TenantFeatures {

maxUsers: Int!

maxAccounts: Int!

enabledModules: [String!]!

advancedAnalytics: Boolean!

apiAccess: Boolean!

}

  

type TenantUsage {

currentUsers: Int!

currentAccounts: Int!

activeAds: Int!

storageUsed: Float!

}

  

# 用户相关

type User {

id: ID!

email: String!

fullName: String!

role: String!

status: String!

tenantId: ID!

teams: [Team!]

permissions: [Permission!]

lastLogin: String

}

  

type Team {

id: ID!

name: String!

members: [User!]

managedResources: [Resource!]

}

  

type Resource {

id: ID!

type: String! # ACCOUNT/CAMPAIGN/LANDING_PAGE等

name: String!

}

  

type Permission {

resourceType: String!

resourceId: ID

actions: [String!]! # VIEW/EDIT/DELETE/MANAGE等

}

  

# 风险监控

type RiskAlert {

id: ID!

tenantId: ID!

resourceType: String!

resourceId: ID

message: String!

level: String!

timestamp: String!

status: String! # NEW/ACKNOWLEDGED/RESOLVED

}

  

# 查询和变更

type Query {

# 租户管理

tenants(page: Int, size: Int): [Tenant!]! # 系统管理员可查

tenantDetails(id: ID!): Tenant

# 用户管理

users(tenantId: ID, teamId: ID, role: String, page: Int, size: Int): [User!]!

userDetails(id: ID!): User

currentUser: User!

# 团队管理

teams(tenantId: ID): [Team!]!

teamDetails(id: ID!): Team

# 风险监控

riskAlerts(tenantId: ID, level: String, status: String, page: Int, size: Int): [RiskAlert!]!

}

  

type Mutation {

# 租户管理

createTenant(name: String!, plan: String!, adminEmail: String!): Tenant!

updateTenant(id: ID!, name: String, plan: String): Tenant!

# 用户管理

createUser(email: String!, fullName: String!, role: String!, tenantId: ID!): User!

updateUser(id: ID!, fullName: String, role: String): User!

# 权限管理

assignPermission(userId: ID!, resourceType: String!, resourceId: ID, actions: [String!]!): Boolean!

revokePermission(userId: ID!, resourceType: String!, resourceId: ID): Boolean!

# 团队管理

createTeam(name: String!, tenantId: ID!, memberIds: [ID!]): Team!

assignResourceToTeam(teamId: ID!, resourceType: String!, resourceId: ID!): Boolean!

# 风险管理

updateRiskAlertStatus(id: ID!, status: String!): RiskAlert!

}

```

  

## 5. 系统部署与扩展性

  

### 5.1 基础架构部署

  

系统采用容器化部署方案，基于Kubernetes编排，支持多种云环境（AWS、Azure、阿里云等）或混合云架构：

  

```mermaid

flowchart TB

subgraph 云基础设施

K8S[Kubernetes集群]

DBaaS[数据库托管服务]

S3[对象存储]

CDN[内容分发网络]

Route53[DNS服务]

end

subgraph 监控与DevOps

Prometheus[监控系统]

Logger[日志系统]

Jenkins[CI/CD流水线]

end

subgraph 扩展性

HPA[水平Pod自动扩容]

VPA[垂直Pod自动扩容]

DBScaling[数据库自动扩容]

end

K8S --- HPA & VPA

DBaaS --- DBScaling

Route53 --> CDN --> K8S

Prometheus & Logger --> K8S

Jenkins --> K8S

K8S --> S3

K8S --> DBaaS

```

  

### 5.2 高可用与灾备方案

  

系统设计满足以下可用性目标：

- 生产环境目标SLA: 99.95%（月度维护窗口外）

- 多可用区部署，任一区域故障系统仍可运行

- 数据库采用主从架构，支持自动故障转移

- 核心数据每日备份，保留30天备份历史

- 支持跨区域灾备，RTO < 4小时，RPO < 15分钟

  

### 5.3 多租户数据隔离

  

系统采用混合隔离策略，平衡成本和安全：

- 数据库层：租户ID列模式（逻辑隔离）+ 关键租户独立Schema（物理隔离）

- 应用层：租户上下文传递，所有查询强制带租户过滤条件

- 存储层：对象存储按租户ID隔离存储路径

- 缓存层：缓存键前缀包含租户ID

  

## 6. 多租户SaaS架构说明

  

### 6.1 SaaS计划与功能矩阵

  

系统支持三级租户方案，满足不同规模客户需求：

  

| 功能/计划 | 基础版 | 标准版 | 企业版 |

|---------|-------|-------|-------|

| 用户数限制 | 5 | 20 | 无限 |

| 广告账户数 | 10 | 50 | 无限 |

| 广告发布数量 | 有限 | 大量 | 无限 |

| API访问 | 不支持 | 基础 | 高级 |

| 数据留存期 | 30天 | 90天 | 1年+ |

| 高级分析报表 | 基础 | 标准 | 全部 |

| 自动优化规则 | 有限 | 标准 | 高级 |

| 斗篷功能 | 基础 | 标准 | 高级 |

| 专属支持 | 邮件 | 邮件+聊天 | 专属客户经理 |

| 高级权限控制 | 不支持 | 基础 | 完整 |

| 自定义集成 | 不支持 | 不支持 | 支持 |

  

### 6.2 多租户数据模型

  

所有数据表都包含tenant_id字段，确保查询时正确过滤：

  

```mermaid

erDiagram

TENANT ||--o{ USER : "包含"

TENANT ||--o{ TEAM : "组织"

TENANT ||--o{ PERMISSION : "控制"

TENANT ||--o{ FB_ACCOUNT : "拥有"

TEAM ||--o{ USER : "包含"

USER ||--o{ PERMISSION : "具有"

PERMISSION ||--o{ RESOURCE : "应用于"

FB_ACCOUNT ||--o{ MATERIAL : "包含"

FB_ACCOUNT ||--o{ VCC : "绑定"

FB_ACCOUNT ||--o{ AD_CAMPAIGN : "包含"

TENANT {

uuid id PK

string name

string plan

datetime created_at

json features

}

USER {

uuid id PK

uuid tenant_id FK

string email

string full_name

string role

string status

}

FB_ACCOUNT {

uuid id PK

uuid tenant_id FK

string fb_account_id

string name

string status

}

VCC {

uuid id PK

uuid tenant_id FK

string card_no_masked

float balance

datetime expire

}

```

  
