import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, Upload, Button, message } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { UserItem, UserStatus, CreateUserParams, UpdateUserParams } from './types';
import { useApolloClient, useQuery, gql } from '@apollo/client';
import { RoleService } from '@/api/services/role.service';
import { GroupService } from '@/api/services/group.service';
import { useUserInfo } from '@/store/userStore';

interface UserModalProps {
    visible: boolean;
    user?: UserItem;
    onCancel: () => void;
    onSubmit: (values: CreateUserParams | UpdateUserParams) => Promise<void>;
    confirmLoading?: boolean;
}

const TENANT_LIST = gql`
  query Tenants($page: Int, $limit: Int) {
    tenants(page: $page, limit: $limit) {
      nodes { id name }
    }
  }
`;

const UserModal: React.FC<UserModalProps> = ({ visible, user, onCancel, onSubmit, confirmLoading }) => {
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const client = useApolloClient();
    const roleService = new RoleService(client);
    const groupService = new GroupService(client);
    const currentUserInfo = useUserInfo();
    const [roles, setRoles] = useState<any[]>([]);
    const [groups, setGroups] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const { data: tenantData, loading: tenantLoading } = useQuery(TENANT_LIST, { variables: { page: 1, limit: 100 } });
    const tenants = tenantData?.tenants?.nodes || [];
    const [showTenant, setShowTenant] = useState(false);
    const [showGroup, setShowGroup] = useState(false);

    // 判断是否为系统管理员（兼容多种写法）
    const isSuperAdmin = currentUserInfo?.roles?.some(
        role => [t('pages.key_1779'), t('pages.key_2340'), 'superadmin'].includes(role.name?.trim().toLowerCase())
    );
    // 过滤下拉角色（兼容多种写法）
    const filteredRoles = isSuperAdmin
        ? roles
        : roles.filter(
            role => ![t('pages.key_1779'), t('pages.key_2340'), 'superadmin'].includes(role.name?.trim().toLowerCase())
        );
    // 调试输出
    console.log('当前登录用户:', currentUserInfo);
    console.log('isSuperAdmin:', isSuperAdmin);
    console.log('下拉所有角色:', roles);
    console.log('过滤后角色:', filteredRoles);

    // 加载角色和群组数据
    useEffect(() => {
        const loadData = async () => {
            try {
                setLoading(true);
                console.log('开始加载角色和群组数据...');

                // 分开获取角色和群组，便于分别处理异常
                try {
                    console.log('正在加载角色数据...');
                    const rolesData = await roleService.getEnabledRoles();
                    console.log('角色数据原始响应:', rolesData);

                    const roleItems = rolesData?.items || [];
                    setRoles(roleItems);
                    console.log('已加载启用状态的角色:', roleItems.length, '个');

                    if (roleItems.length === 0) {
                        console.warn(t('pages.key_1482'));
                    }
                } catch (roleError) {
                    console.error('加载角色数据失败:', roleError);
                    message.error('无法加载启用状态的角色：' + (roleError as Error).message);
                }

                try {
                    console.log('正在加载群组数据...');
                    const groupsData = await groupService.getActiveGroups(t('pages.key_1465'));
                    console.log('群组数据原始响应:', groupsData);

                    const groupItems = groupsData?.items || [];
                    setGroups(groupItems);
                    console.log('已加载活跃状态的群组:', groupItems.length, '个');

                    if (groupItems.length === 0) {
                        console.warn(t('pages.key_1485'));
                    }
                } catch (groupError) {
                    console.error('加载群组数据失败:', groupError);
                    message.error('无法加载活跃状态的群组：' + (groupError as Error).message);
                }
            } catch (error) {
                console.error('加载角色和群组数据总体失败:', error);
                message.error('数据加载失败：' + (error as Error).message);
            } finally {
                setLoading(false);
            }
        };

        if (visible) {
            loadData();
        }
    }, [visible]);

    // 当编辑用户时，填充表单
    useEffect(() => {
        if (visible && user) {
            const formData = {
                ...user,
                roleIds: user.roles && user.roles.length > 0 ? user.roles[0].id : undefined,
                groupIds: user.groups && user.groups.length > 0 ? user.groups[0].id : undefined
            };
            console.log('设置编辑表单初始值:', formData);
            form.setFieldsValue(formData);
        } else if (visible) {
            form.resetFields();
            form.setFieldsValue({
                status: UserStatus.ACTIVE,
                roleIds: undefined,
                groupIds: undefined
            });
        }
    }, [visible, user, form]);

    // 角色选择变化时，判断是否为系统管理员和租户管理员
    const handleRoleChange = (roleId: string) => {
        const selectedRole = roles.find(role => role.id === roleId);
        const isSuperRole = selectedRole && [t('pages.key_1779'), t('pages.key_2340'), 'superadmin'].includes(selectedRole.name?.trim().toLowerCase());
        const isTenantAdmin = selectedRole && [t('pages.key_1732'), 'tenantadmin'].includes(selectedRole.name?.trim().toLowerCase());
        setShowTenant(!isSuperRole && isTenantAdmin);
        setShowGroup(isTenantAdmin);
    };

    // 初始化时，判断是否为租户管理员，决定是否显示群组
    useEffect(() => {
        if (visible) {
            let isTenantAdmin = false;
            if (user && user.roles && user.roles.length > 0) {
                isTenantAdmin = [t('pages.key_1732'), 'tenantadmin'].includes(user.roles[0].name?.trim().toLowerCase());
            }
            setShowGroup(isTenantAdmin);
        }
    }, [visible, user, roles]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();

            // 确保roleIds和groupIds为正确格式
            const formData = {
                ...values,
                roleIds: values.roleIds ? [values.roleIds] : [],
                groupIds: values.groupIds ? [values.groupIds] : []
            };

            // 如果是编辑模式且密码为空，则从提交数据中删除密码字段
            if (user && (!formData.password || formData.password.trim() === '')) {
                delete formData.password;
            }

            console.log('提交表单数据:', formData);

            await onSubmit(formData);
            console.log(t('pages.key_2027'));
            form.resetFields();
        } catch (error) {
            console.error('表单验证或提交失败:', error);

            if (error instanceof Error) {
                message.error(`提交失败: ${error.message}`);
            } else {
                message.error(t('pages.key_1030'));
            }
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal
            title={user ? t('pages.key_1862') : t('pages.key_251')}
            open={visible}
            onOk={handleOk}
            onCancel={handleCancel}
            width={600}
            confirmLoading={confirmLoading || loading}
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={{ status: UserStatus.ACTIVE }}
            >
                {user && (
                    <Form.Item name="id" hidden>
                        <Input />
                    </Form.Item>
                )}

                <Form.Item
                    name="username"
                    label={t('pages.key_1627')}
                    rules={[{ required: true, message: t('pages.key_2216') }]}
                >
                    <Input prefix={<UserOutlined />} placeholder={t('pages.key_2216')} />
                </Form.Item>

                <Form.Item
                    name="email"
                    label={t('pages.key_2471')}
                    rules={[
                        { required: true, message: t('pages.key_2239') },
                        { type: 'email', message: t('pages.key_2209') }
                    ]}
                >
                    <Input placeholder={t('pages.key_2239')} />
                </Form.Item>

                <Form.Item
                    name="password"
                    label={t('pages.key_674')}
                    rules={[
                        { required: !user, message: t('pages.key_2193') },
                        { min: 6, message: t('pages.key_676') }
                    ]}
                >
                    <Input.Password placeholder={user ? t('pages.key_11') : t('pages.key_2193')} />
                </Form.Item>

                <Form.Item
                    name="fullName"
                    label={t('pages.key_622')}
                >
                    <Input placeholder={t('pages.key_2192')} />
                </Form.Item>

                <Form.Item
                    name="phone"
                    label={t('pages.key_1647')}
                >
                    <Input placeholder={t('pages.key_2218')} />
                </Form.Item>

                <Form.Item
                    name="status"
                    label={t('pages.key_1602')}
                    rules={[{ required: true, message: t('pages.key_2273') }]}
                >
                    <Select>
                        <Select.Option value={UserStatus.ACTIVE}>{t('pages.key_1465')}</Select.Option>
                        <Select.Option value={UserStatus.INACTIVE}>{t('pages.key_1310')}</Select.Option>
                        <Select.Option value={UserStatus.BLOCKED}>{t('pages.key_1714')}</Select.Option>
                    </Select>
                </Form.Item>

                <Form.Item
                    name="roleIds"
                    label={t('pages.key_2067')}
                    extra={t('pages.key_429')}
                    rules={[{ required: true, message: t('pages.key_2285') }]}
                >
                    <Select
                        placeholder={t('pages.key_2285')}
                        loading={loading}
                        notFoundContent={roles.length === 0 ? t('pages.key_1480') : undefined}
                        style={{ width: '100%' }}
                        optionFilterProp="children"
                        allowClear
                        onChange={handleRoleChange}
                    >
                        {filteredRoles.map(role => (
                            <Select.Option key={role.id} value={role.id}>
                                {role.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>

                {showTenant && (
                    <Form.Item
                        name="tenantId"
                        label={t('pages.key_1723')}
                        rules={[{ required: true, message: t('pages.key_2274') }]}
                    >
                        <Select placeholder={t('pages.key_2274')} loading={tenantLoading} allowClear showSearch optionFilterProp="children">
                            {tenants.map((tenant: any) => (
                                <Select.Option key={tenant.id} value={tenant.id}>{tenant.name}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                )}

                {showGroup && (
                    <Form.Item
                        name="groupIds"
                        label={t('pages.key_950')}
                        extra={t('pages.key_430')}
                        rules={[{ required: true, message: t('pages.key_2267') }]}
                    >
                        <Select
                            placeholder={t('pages.key_2267')}
                            loading={loading}
                            notFoundContent={groups.length === 0 ? t('pages.key_1481') : undefined}
                            style={{ width: '100%' }}
                            optionFilterProp="children"
                            allowClear
                        >
                            {groups.map(group => (
                                <Select.Option key={group.id} value={group.id}>
                                    {group.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                )}
            </Form>
        </Modal>
    );
};

export default UserModal; 