import { gql } from '@apollo/client';

// 获取群组列表
export const GET_GROUPS = gql`
  query GetGroups($filter: GroupFilter, $paging: OffsetPaging, $sorting: [GroupSort!]) {
    groups(filter: $filter, paging: $paging, sorting: $sorting) {
      nodes {
        id
        name
        description
        belongTo
        contactInfo
        memberCount
        status
        createTime
        updateTime
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
      }
    }
  }
`;

// 获取单个群组
export const GET_GROUP = gql`
  query GetGroup($id: ID!) {
    group(id: $id) {
      id
      name
      description
      belongTo
      contactInfo
      memberCount
      status
      createTime
      updateTime
    }
  }
`;

// 创建群组
export const CREATE_GROUP = gql`
  mutation CreateGroup($input: CreateOneGroupInput!) {
    createOneGroup(input: $input) {
      id
      name
      description
      belongTo
      contactInfo
      memberCount
      status
      createTime
    }
  }
`;

// 更新群组
export const UPDATE_GROUP = gql`
  mutation UpdateGroup($input: UpdateOneGroupInput!) {
    updateOneGroup(input: $input) {
      id
      name
      description
      belongTo
      contactInfo
      memberCount
      status
      updateTime
    }
  }
`;

// 删除群组
export const DELETE_GROUP = gql`
  mutation DeleteGroup($input: DeleteOneGroupInput!) {
    deleteOneGroup(input: $input) {
      id
      name
    }
  }
`;

// 获取群组统计信息
export const GET_GROUPS_AGGREGATE = gql`
  query GroupsAggregate($filter: GroupAggregateFilter) {
    groupsAggregate(filter: $filter) {
      count {
        total
        status
      }
      sum {
        memberCount
      }
      avg {
        memberCount
      }
    }
  }
`; 