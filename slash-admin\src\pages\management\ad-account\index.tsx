import { useTranslation } from 'react-i18next';
import { useState } from "react";
import { Tabs, TabsProps } from "antd";
import AccountListTab from "./tabs/account-list-tab.tsx";
import BatchUploadTab from "./tabs/batch-upload-tab.tsx";
import OAuthTab from "./tabs/oauth-tab.tsx";
import GroupManageTab from "./tabs/group-manage-tab.tsx";

// 广告账户管理主页面
export default function AdAccountManagement() {
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState("account-list");
  const items: TabsProps["items"] = [
    {
      key: "account-list",
      label: t('pages.key_2324'),
      children: <AccountListTab />,
    },
    {
      key: "batch-upload",
      label: t('pages.key_969'),
      children: <BatchUploadTab />,
    },
    {
      key: "oauth",
      label: "OAuth授权",
      children: <OAuthTab />,
    },
    {
      key: "group-manage",
      label: t('pages.key_225'),
      children: <GroupManageTab />,
    },
  ];
  return (
    <Tabs
      items={items}
      activeKey={activeKey}
      onChange={setActiveKey}
      destroyOnHidden
    />
  );
}
