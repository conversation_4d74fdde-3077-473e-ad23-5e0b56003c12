import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    Request,
    HttpCode,
    HttpStatus,
    Header
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { VccTransactionService } from '../services/vcc-transaction.service';
import {
    CreateVccTransactionInputDTO,
    UpdateVccTransactionInputDTO,
    VccTransactionFilterInputDTO,
    VccTransactionPaginationInputDTO,
    SyncFacebookTransactionsInputDTO
} from '../dto/vcc-transaction.dto';

@ApiTags('VCC 交易记录')
@Controller('api/vcc/transactions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class VccTransactionController {
    constructor(private readonly vccTransactionService: VccTransactionService) { }

    /**
     * 查询交易记录列表（支持前端所有搜索条件）
     */
    @Get()
    @ApiOperation({ summary: '查询VCC交易记录列表' })
    @ApiResponse({ status: 200, description: '查询成功' })
    async getTransactions(
        @Query() filter: VccTransactionFilterInputDTO,
        @Query() pagination: VccTransactionPaginationInputDTO,
        @Request() req: any
    ) {
        const tenantId = req.user.tenantId;
        return await this.vccTransactionService.findTransactions(filter, pagination, tenantId);
    }

    /**
     * 根据ID查询单个交易记录
     */
    @Get(':id')
    @ApiOperation({ summary: '根据ID查询单个VCC交易记录' })
    @ApiResponse({ status: 200, description: '查询成功' })
    @ApiResponse({ status: 404, description: '交易记录不存在' })
    async getTransaction(@Param('id') id: string, @Request() req: any) {
        const tenantId = req.user.tenantId;
        return await this.vccTransactionService.findTransactionById(id, tenantId);
    }

    /**
     * 创建交易记录
     */
    @Post()
    @ApiOperation({ summary: '创建VCC交易记录' })
    @ApiResponse({ status: 201, description: '创建成功' })
    @ApiResponse({ status: 400, description: '参数错误' })
    @HttpCode(HttpStatus.CREATED)
    async createTransaction(
        @Body() input: CreateVccTransactionInputDTO,
        @Request() req: any
    ) {
        const tenantId = req.user.tenantId;
        return await this.vccTransactionService.createTransaction(input, tenantId);
    }

    /**
     * 更新交易记录
     */
    @Put(':id')
    @ApiOperation({ summary: '更新VCC交易记录' })
    @ApiResponse({ status: 200, description: '更新成功' })
    @ApiResponse({ status: 404, description: '交易记录不存在' })
    async updateTransaction(
        @Param('id') id: string,
        @Body() input: UpdateVccTransactionInputDTO,
        @Request() req: any
    ) {
        const tenantId = req.user.tenantId;
        return await this.vccTransactionService.updateTransaction(id, input, tenantId);
    }

    /**
     * 删除交易记录
     */
    @Delete(':id')
    @ApiOperation({ summary: '删除VCC交易记录' })
    @ApiResponse({ status: 200, description: '删除成功' })
    @ApiResponse({ status: 404, description: '交易记录不存在' })
    async deleteTransaction(@Param('id') id: string, @Request() req: any) {
        const tenantId = req.user.tenantId;
        return await this.vccTransactionService.deleteTransaction(id, tenantId);
    }

    /**
     * 获取交易统计数据
     */
    @Get('stats/summary')
    @ApiOperation({ summary: '获取VCC交易统计数据' })
    @ApiResponse({ status: 200, description: '查询成功' })
    async getTransactionStats(
        @Query() filter: VccTransactionFilterInputDTO,
        @Request() req: any
    ) {
        const tenantId = req.user.tenantId;
        return await this.vccTransactionService.getTransactionStats(filter, tenantId);
    }

    /**
     * 导出交易记录（对应前端导出功能）
     */
    @Get('export/:format')
    @ApiOperation({ summary: '导出VCC交易记录' })
    @ApiResponse({ status: 200, description: '导出成功' })
    async exportTransactions(
        @Param('format') format: 'csv' | 'json',
        @Query() filter: VccTransactionFilterInputDTO,
        @Request() req: any
    ) {
        const tenantId = req.user.tenantId;
        const data = await this.vccTransactionService.exportTransactions(filter, tenantId, format);

        if (format === 'csv') {
            return {
                data,
                filename: `vcc-transactions-${new Date().toISOString().split('T')[0]}.csv`,
                contentType: 'text/csv'
            };
        } else {
            return {
                data,
                filename: `vcc-transactions-${new Date().toISOString().split('T')[0]}.json`,
                contentType: 'application/json'
            };
        }
    }

    /**
     * 🚀 从Facebook同步交易记录 - 核心功能
     */
    @Post('sync/facebook')
    @ApiOperation({ summary: '从Facebook API同步VCC卡片的广告消费记录' })
    @ApiResponse({ status: 200, description: '同步成功' })
    @ApiResponse({ status: 400, description: '同步失败' })
    async syncFromFacebook(
        @Body() input: SyncFacebookTransactionsInputDTO,
        @Request() req: any
    ) {
        const tenantId = req.user.tenantId;
        return await this.vccTransactionService.syncFacebookTransactions(input, tenantId);
    }

    /**
     * 手动添加充值记录
     */
    @Post('recharge')
    @ApiOperation({ summary: '手动添加VCC充值记录' })
    @ApiResponse({ status: 201, description: '添加成功' })
    @HttpCode(HttpStatus.CREATED)
    async addRechargeTransaction(
        @Body() body: { cardId: string; amount: number; description?: string },
        @Request() req: any
    ) {
        const tenantId = req.user.tenantId;

        const input: CreateVccTransactionInputDTO = {
            cardId: body.cardId,
            amount: body.amount,
            merchant: '手动充值',
            transactionTime: new Date().toISOString(),
            status: 'success' as any,
            type: 'deposit' as any,
            description: body.description || `手动充值 $${body.amount}`
        };

        return await this.vccTransactionService.createTransaction(input, tenantId);
    }
} 