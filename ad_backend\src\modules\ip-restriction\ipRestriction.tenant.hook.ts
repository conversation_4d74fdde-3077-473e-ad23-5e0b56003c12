import { Injectable } from '@nestjs/common';
import { ipRestrictionDTO } from './dto/ipRestriction.dto';

@Injectable()
export class IpRestrictionTenantHook {
    async run(instance: any, context: any): Promise<any> {
        const req = (context as any).req;
        const user = req?.user;
        const tenantId = req?.tenantId;
        const isSuperAdmin = req?.isSuperAdmin;
        console.log('IpRestrictionTenantHook before create:', JSON.stringify(instance.input.ipRestriction), 'tenantId:', tenantId, 'isSuperAdmin:', isSuperAdmin);
        if (!user) throw new Error('未登录');
        if (!isSuperAdmin) {
            instance.input.ipRestriction.tenantId = tenantId;
        }
        // 超级管理员可传任意 tenantId
        console.log('IpRestrictionTenantHook after patch:', JSON.stringify(instance.input.ipRestriction));
        return instance;
    }
} 