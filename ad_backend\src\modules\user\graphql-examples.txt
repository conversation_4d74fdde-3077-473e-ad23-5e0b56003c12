# 用户登录 (不需要身份验证)
mutation Login {
  login(input: { username: "admin", password: "Admin@123" }) {
    userId
    accessToken
    username
    roles
  }
}

# 用户注册 (不需要身份验证)
mutation Register {
  register(input: {
    username: "newuser",
    email: "<EMAIL>",
    password: "Password@123",
    fullName: "新用户"
  }) {
    userId
    accessToken
    username
  }
}

# 获取当前用户信息 (需要身份验证)
query GetCurrentUser {
  me {
    user {
      id
      username
      email
      fullName
      phone
      avatar
      status
      roles {
        id
        name
      }
      groups {
        id
        name
      }
      createdAt
    }
    accessToken
  }
}

# 获取用户列表 (需要身份验证)
query GetUsers {
  users(input: {
    page: 1,
    limit: 10,
    filter: {
      status: ACTIVE
    }
  }, sorting: [{ field: "createdAt", direction: "DESC" }]) {
    nodes {
      id
      username
      email
      fullName
      phone
      status
      registerIp
      roles {
        id
        name
      }
      groups {
        id
        name
      }
      createdAt
    }
    totalCount
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
  }
}

# 根据ID获取用户 (需要身份验证)
query GetUser {
  user(id: "用户ID") {
    id
    username
    email
    fullName
    phone
    avatar
    address
    registerIp
    status
    roles {
      id
      name
    }
    groups {
      id
      name
    }
    createdAt
    updatedAt
  }
}

# 创建用户 (需要身份验证)
mutation CreateUser {
  createUser(input: {
    username: "testuser",
    email: "<EMAIL>",
    password: "Password@123",
    fullName: "测试用户",
    phone: "13800138000",
    roleIds: ["角色ID1", "角色ID2"],
    groupIds: ["群组ID1", "群组ID2"]
  }) {
    id
    username
    email
    status
    roles {
      id
      name
    }
    groups {
      id
      name
    }
  }
}

# 更新用户 (需要身份验证)
mutation UpdateUser {
  updateUser(input: {
    id: "用户ID",
    fullName: "更新的名称",
    phone: "13900000000",
    roleIds: ["角色ID1", "角色ID2"],
    groupIds: ["群组ID1", "群组ID2"]
  }) {
    id
    username
    fullName
    phone
    roles {
      id
      name
    }
    groups {
      id
      name
    }
    updatedAt
  }
}

# 删除用户 (需要身份验证)
mutation DeleteUser {
  deleteUser(id: "用户ID") {
    id
    username
  }
}

# 修改用户状态 (需要身份验证)
mutation ChangeUserStatus {
  changeUserStatus(id: "用户ID", status: BLOCKED) {
    id
    username
    status
    updatedAt
  }
} 