import { ConfigModule } from '@nestjs/config';
import { NestjsQueryGraphQLModule, PagingStrategies } from '@ptc-org/nestjs-query-graphql';
import { NestjsQueryTypeOrmModule } from '@ptc-org/nestjs-query-typeorm';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { materiaCreateDTO } from './dto/materiaCreate.dto';
import { materiaCreate } from './materiaCreate.entity';
import { materiaCreateController } from './materiaCreate.controller';
import { materiaCreateService } from './materiaCreate.service';
import { materiaCreateResolver } from './resolvers/materiaCreate.resolver'
import config from '../../config/config';
import { Group } from '../group/group.entity';
import { User } from '../user/user.entity';
import { AdPlatformModule } from '../ad-platform/ad-platform.module';
import { Role } from '../role/role.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
	imports: [
		ConfigModule.forRoot({
			load: [config],
		}),
		TypeOrmModule.forFeature([materiaCreate, Group, User, Role]),
		AdPlatformModule,
		AuthModule,
		NestjsQueryGraphQLModule.forFeature({
			imports: [NestjsQueryTypeOrmModule.forFeature([materiaCreate])],
			resolvers: [
				{
					DTOClass: materiaCreateDTO,
					EntityClass: materiaCreate,
					enableSubscriptions: true,
					delete: {
						disabled: false,
					},
					read: {
						maxResultsSize: 1000
					},
					pagingStrategy: PagingStrategies.OFFSET
				},
			],
		}),
	],
	controllers: [materiaCreateController],
	providers: [materiaCreateService, materiaCreateResolver],
	exports: [materiaCreateService],
})
export class materiaCreateModule { }
