import { Module, DynamicModule, Global } from '@nestjs/common';
import { FacebookService } from './facebook/facebook.service';
import { FacebookController } from './facebook/facebook.controller';
import { FacebookAdService } from './services/facebook-ad.service';
import { FacebookAdController } from './facebook/facebook-ad.controller';
import { FacebookPixelService } from './facebook/facebook-pixel.service';
import { FacebookPixelController } from './facebook/facebook-pixel.controller';
import { CloudflareUploadController } from './cloudflare-upload.controller';
// 预留：GoogleService, TikTokService, AdjustService, AppsFlyerService
import { ReportService } from './report/report.service';
import { ReportController } from './report/report.controller';
import { TenantSyncController } from './tenant-sync.controller';
import { FacebookSyncController } from './facebook/facebook-sync.controller';
import { FacebookSyncService } from './facebook/facebook-sync.service';
import { TenantResolver } from './tenant.resolver';
import { AudienceResolver } from './audience.resolver';
import { AdAccountService } from "./services/ad-account.service"

import { TypeOrmModule } from '@nestjs/typeorm';
import { NestjsQueryGraphQLModule } from '@ptc-org/nestjs-query-graphql';
import { NestjsQueryTypeOrmModule } from '@ptc-org/nestjs-query-typeorm';
import { AdAccount } from './entities/ad-account.entity';
import { AdCampaign } from './entities/ad-campaign.entity';
import { AdCreative } from './entities/ad-creative.entity';
import { Ad } from './entities/ad.entity';
import { AdSet } from './entities/adset.entity';
import { Pixel } from './entities/pixel.entity';
import { Audience } from './entities/audience.entity';
import { Material } from './entities/material.entity';
import { Vcc } from './entities/vcc.entity';
import { Tenant } from '../../entity/tenant.entity';
import { LandingPage } from '../landing-page/landing-page.entity';
import { Campaign } from './entities/campaign.entity';
import { FacebookService as FacebookBasicService } from './services/facebook.service';
import { AdDTO } from './entities/ad.dto';
import { AdAccountDTO } from './entities/ad-account.dto';
import { AdCampaignDTO } from './entities/ad-campaign.dto';
import { AdCreativeDTO } from './entities/ad-creative.dto';
import { AdSetDTO } from './entities/adset.dto';
import { AudienceDTO } from './entities/audience.dto';
import { MaterialDTO } from './entities/material.dto';
import { VccDTO } from './entities/vcc.dto';
import { AuthModule } from '../auth/auth.module';
import { TenantGuard } from '../../guards/tenant.guard';
import { AdAccountResolver } from './resolvers/ad-account.resolver';
import { AdCampaignResolver } from './resolvers/ad-campaign.resolver';
import { AdSetResolver } from './resolvers/adset.resolver';
import { PromotionResolver } from './resolvers/promotion.resolver';
import { PromotionService } from './promotion.service';
import { Promotion } from './entities/promotion.entity';
import { FacebookApiService } from './services/facebook-api.service';
import { GoogleApiService } from './services/google-api.service';
import { TiktokApiService } from './services/tiktok-api.service';
import { AdAccountController } from './ad-account.controller';
import { CampaignResolver } from './resolvers/campaign.resolver';
import { FacebookOptionResolver } from './resolvers/promotion.resolver';
import { PixelResolver } from './resolvers/pixel.resolver';
import { PixelService } from './services/pixel.service';
import { AdResolver } from './resolvers/ad.resolver';
import { UserModule } from '../user/user.module';
import { User } from '../user/user.entity';
import { Group } from '../group/group.entity';
import { Report } from './report/report.entity';
import { DomainModule } from '../domain/domain.module';
import { AdMetricsResolver } from './ad-metrics.resolver';
import { CampaignOverviewResolver } from './campaign-overview.resolver';
import { AutoOptimizationResolver } from './auto-optimization.resolver';
import { CreativeAnalysisResolver } from './creative-analysis.resolver';
import { BudgetUtilizationResolver } from './budget-utilization.resolver';
import { AdGroupsOverviewResolver } from './ad-groups-overview.resolver';
import { AdGroupOverviewItemDTO, AdGroupsOverviewDTO } from './entities/ad-groups-overview.dto';
import { materiaCreate } from '../material-create/materiaCreate.entity';
import { AdPlatformDistributionResolver } from './ad-platform-distribution.resolver';
import { AudienceInsightsResolver } from './audience-insights.resolver';
import { LandingPageModule } from '../landing-page/landing-page.module';
import { PwaDecodeController } from './pwa-decode.controller';
import { MaterialController } from './material.controller';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AdAccount,
      AdCampaign,
      AdCreative,
      Ad,
      AdSet,
      Pixel,
      Audience,
      Tenant,
      Promotion,
      LandingPage,
      Campaign,
      Report,
      User,
      Group,
      materiaCreate,
    ]),
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([
          AdAccount,
          AdCampaign,
          AdCreative,
          Ad,
          AdSet,
          Audience,
          Material,
          Vcc,
        ]),
      ],
      resolvers: [
        {
          DTOClass: AdDTO,
          EntityClass: Ad,
          enableSubscriptions: true,
          enableAggregate: false,
          create: { disabled: true },
          update: { disabled: true },
          delete: { disabled: true },
        },
        {
          DTOClass: AdCampaignDTO,
          EntityClass: AdCampaign,
          enableSubscriptions: true,
          enableAggregate: false,
          create: { disabled: true },
          update: { disabled: true },
          delete: { disabled: true },
        },
        {
          DTOClass: AdCreativeDTO,
          EntityClass: AdCreative,
          enableSubscriptions: true,
          enableAggregate: false,
          create: { disabled: true },
          update: { disabled: true },
          delete: { disabled: true },
        },
        {
          DTOClass: AdSetDTO,
          EntityClass: AdSet,
          enableSubscriptions: true,
          enableAggregate: false,
          create: { disabled: true },
          update: { disabled: true },
          delete: { disabled: true },
        },
        {
          DTOClass: AudienceDTO,
          EntityClass: Audience,
          enableSubscriptions: true,
          enableAggregate: false,
          create: { disabled: true },
        },
        {
          DTOClass: MaterialDTO,
          EntityClass: Material,
          enableSubscriptions: true,
          enableAggregate: false,
          create: { disabled: true },
          update: { disabled: true },
          delete: { disabled: true },
        },
        {
          DTOClass: VccDTO,
          EntityClass: Vcc,
          enableSubscriptions: true,
          enableAggregate: false,
          create: { disabled: true },
          update: { disabled: true },
          delete: { disabled: true },
        },
      ],
    }),
    AuthModule,
    UserModule,
    DomainModule,
    LandingPageModule,
  ],
  providers: [
    FacebookService,
    FacebookAdService,
    FacebookPixelService,
    AdAccountService,
    ReportService,
    FacebookSyncService,
    TenantResolver,
    AudienceResolver,
    TenantGuard,
    AdAccountResolver,
    AdCampaignResolver,
    AdSetResolver,
    PromotionResolver,
    PromotionService,
    FacebookApiService,
    GoogleApiService,
    TiktokApiService,
    CampaignResolver,
    FacebookBasicService,
    FacebookOptionResolver,
    PixelResolver,
    PixelService,
    AdResolver,
    AdMetricsResolver,
    CampaignOverviewResolver,
    AutoOptimizationResolver,
    CreativeAnalysisResolver,
    BudgetUtilizationResolver,
    AdGroupsOverviewResolver,
    AdPlatformDistributionResolver,
    AudienceInsightsResolver,
  ],
  controllers: [
    FacebookController,
    FacebookAdController,
    FacebookPixelController,
    CloudflareUploadController,
    ReportController,
    TenantSyncController,
    FacebookSyncController,
    AdAccountController,
    MaterialController,
    PwaDecodeController,
  ],
  exports: [FacebookService, FacebookAdService, FacebookPixelService, ReportService, FacebookSyncService, FacebookApiService, GoogleApiService, TiktokApiService, PixelService, AdAccountService]
})
export class AdPlatformModule {
  static register(): DynamicModule {
    return {
      module: AdPlatformModule,
      providers: [FacebookService],
      controllers: [FacebookController],
      exports: [FacebookService],
    };
  }
}
