import { Resolver, Query, Args, ObjectType, Field, Int, Float, Context } from '@nestjs/graphql';
import { AnalysisService } from './analysis.service';
import { TimeRangeEnum, ChannelTimeRangeEnum } from './dto/analysis.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

// GraphQL 类型定义
@ObjectType()
class MetricData {
  @Field(() => Int)
  value: number;

  @Field(() => Float)
  percentage: number;
}

@ObjectType()
class OverviewCardsResponse {
  @Field(() => MetricData)
  impressions: MetricData;

  @Field(() => MetricData)
  clicks: MetricData;

  @Field(() => MetricData)
  conversions: MetricData;

  @Field(() => MetricData)
  spend: MetricData;
}

@ObjectType()
class BarData {
  @Field(() => [Int])
  impressions: number[];

  @Field(() => [Int])
  clicks: number[];

  @Field(() => [Int])
  conversions: number[];
}

@ObjectType()
class LineData {
  @Field(() => [Float])
  ctr: number[];

  @Field(() => [Float])
  cvr: number[];
}

@ObjectType()
class AdPerformanceAnalysisResponse {
  @Field(() => BarData)
  barData: BarData;

  @Field(() => LineData)
  lineData: LineData;

  @Field(() => [String])
  timeLabels: string[];
}

@ObjectType()
class AdTrafficAnalysisResponse {
  @Field(() => [Float])
  spend: number[];

  @Field(() => [Int])
  conversions: number[];

  @Field(() => [Int])
  clicks: number[];

  @Field(() => [Int])
  impressions: number[];

  @Field(() => [String])
  timeLabels: string[];
}

@ObjectType()
class CreativeItem {
  @Field(() => Int)
  id: number;

  @Field(() => String)
  name: string;

  @Field(() => Int)
  impressions: number;

  @Field(() => Int)
  clicks: number;

  @Field(() => Float)
  ctr: number;

  @Field(() => Int)
  conversions: number;

  @Field(() => Float)
  cvr: number;
}

@ObjectType()
class CreativePerformanceResponse {
  @Field(() => [CreativeItem])
  images: CreativeItem[];

  @Field(() => [CreativeItem])
  videos: CreativeItem[];

  @Field(() => [CreativeItem])
  carousels: CreativeItem[];
}

@ObjectType()
class ChannelMetric {
  @Field(() => String)
  channel: string;

  @Field(() => Int)
  impressions: number;

  @Field(() => Int)
  clicks: number;

  @Field(() => Int)
  conversions: number;

  @Field(() => Float)
  cost: number;

  @Field(() => Float)
  ctr: number;

  @Field(() => Float)
  cvr: number;

  @Field(() => Float)
  cpc: number;

  @Field(() => Float)
  roi: number;
}

@ObjectType()
class ChannelAnalysisResponse {
  @Field(() => [ChannelMetric])
  channels: ChannelMetric[];

  @Field(() => [String])
  channelNames: string[];

  @Field(() => [Int])
  impressionsData: number[];

  @Field(() => [Float])
  roiData: number[];
}

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver()
export class AnalysisResolver {
  constructor(private readonly analysisService: AnalysisService) { }

  @Query(() => OverviewCardsResponse)
  async getOverviewCards(@Context() ctx) {
    const { tenantId, isSuperAdmin } = ctx;
    return this.analysisService.getOverviewCards(isSuperAdmin ? undefined : tenantId);
  }

  @Query(() => AdPerformanceAnalysisResponse)
  async getAdPerformanceAnalysis(
    @Args('timeRange', { type: () => String }) timeRange: TimeRangeEnum,
    @Context() ctx
  ) {
    const { tenantId, isSuperAdmin } = ctx;
    return this.analysisService.getAdPerformanceAnalysis(timeRange, isSuperAdmin ? undefined : tenantId);
  }

  @Query(() => AdTrafficAnalysisResponse)
  async getAdTrafficAnalysis(
    @Args('timeRange', { type: () => String }) timeRange: TimeRangeEnum,
    @Context() ctx
  ) {
    const { tenantId, isSuperAdmin } = ctx;
    return this.analysisService.getAdTrafficAnalysis(timeRange, isSuperAdmin ? undefined : tenantId);
  }

  @Query(() => CreativePerformanceResponse)
  async getCreativePerformance(@Context() ctx) {
    const { tenantId, isSuperAdmin } = ctx;
    return this.analysisService.getCreativePerformance(isSuperAdmin ? undefined : tenantId);
  }

  @Query(() => ChannelAnalysisResponse)
  async getChannelAnalysis(
    @Args('timeRange', { type: () => String }) timeRange: ChannelTimeRangeEnum,
    @Context() ctx
  ) {
    const { tenantId, isSuperAdmin } = ctx;
    return this.analysisService.getChannelAnalysis(timeRange);
  }
} 