import { useTranslation } from 'react-i18next';
import { useState } from "react";
import { Card, Tabs, Table, Button, Tag, Space, Modal, Input, Form, Select, Switch, Statistic, Row, Col, Tooltip, Badge, Divider, Alert, Progress, Timeline, Descriptions, message } from "antd";
import { syncRisks, exportRisks } from '@/api/risk';
import { useTenant } from '@/hooks/useTenant';
import { fetchTenantReport, exportReport } from '@/api/report';
import { ExclamationCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, WarningOutlined, BellOutlined, SafetyCertificateOutlined, SecurityScanOutlined, SettingOutlined, SyncOutlined, EyeOutlined, FilterOutlined, SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, LineChartOutlined } from "@ant-design/icons";
import type { TabsProps } from "antd";
import type { RiskItem } from "@/types/ad";

const { t } = useTranslation();

// 模拟数据 - 风险告警数据
const mockAlerts = [
  {
    id: "alert1",
    type: "account",
    level: "high",
    title: t('pages.key_2327'),
    description: t('pages.key_1421'),
    source: "login-service",
    timestamp: "2025-05-07 15:23:45",
    status: "active",
    affectedUsers: 2,
    ipAddress: "************",
  },
  {
    id: "alert2",
    type: "payment",
    level: "critical",
    title: t('pages.key_1070'),
    description: t('pages.key_1420'),
    source: "payment-gateway",
    timestamp: "2025-05-07 14:35:12",
    status: "investigating",
    affectedUsers: 15,
    transactionIds: ["tx-45678", "tx-45679", "tx-45680"],
  },
  {
    id: "alert3",
    type: "api",
    level: "medium",
    title: "API请求频率异常",
    description: t('pages.key_1600'),
    source: "api-gateway",
    timestamp: "2025-05-07 13:15:30",
    status: "resolved",
    ipAddress: "**************",
    requestCount: 1500,
  },
  {
    id: "alert4",
    type: "content",
    level: "low",
    title: t('pages.key_199'),
    description: t('pages.key_1620'),
    source: "content-scanner",
    timestamp: "2025-05-07 10:45:22",
    status: "reviewing",
    contentId: "content-78945",
    userId: "user-12345",
  },
];

// 模拟数据 - 风控规则
const mockRules = [
  {
    id: "rule1",
    name: t('pages.key_1653'),
    type: "account",
    description: t('pages.key_1424'),
    conditions: [t('pages.key_1686'), t('pages.key_12')],
    actions: [t('pages.key_2495'), t('pages.key_405')],
    severity: "high",
    status: "active",
    createdAt: "2025-04-15",
    updatedAt: "2025-05-01",
  },
  {
    id: "rule2",
    name: t('pages.key_1068'),
    type: "payment",
    description: t('pages.key_1422'),
    conditions: [t('pages.key_1687'), t('pages.key_1170')],
    actions: [t('pages.key_1223'), t('pages.key_79')],
    severity: "critical",
    status: "active",
    createdAt: "2025-03-20",
    updatedAt: "2025-04-25",
  },
  {
    id: "rule3",
    name: "API访问限制规则",
    type: "api",
    description: t('pages.key_2528'),
    conditions: [t('pages.key_361')],
    actions: [t('pages.key_2533'), "暂时封禁IP"],
    severity: "medium",
    status: "active",
    createdAt: "2025-02-10",
    updatedAt: "2025-04-10",
  },
  {
    id: "rule4",
    name: t('pages.key_200'),
    type: "content",
    description: t('pages.key_1423'),
    conditions: [t('pages.key_185'), t('pages.key_527')],
    actions: [t('pages.key_1906'), t('pages.key_1376')],
    severity: "low",
    status: "inactive",
    createdAt: "2025-01-05",
    updatedAt: "2025-03-15",
  },
];

// 模拟数据 - 系统健康状态
const mockSystemHealth = {
  overall: {
    status: "normal",
    score: 92,
    lastUpdated: "2025-05-07 16:30:00",
  },
  components: [
    {
      name: t('pages.key_1642'),
      status: "normal",
      score: 95,
      metrics: {
        availability: "99.9%",
        responseTime: "120ms",
        errorRate: "0.01%",
      },
    },
    {
      name: t('pages.key_1069'),
      status: "warning",
      score: 85,
      metrics: {
        availability: "99.7%",
        responseTime: "180ms",
        errorRate: "0.5%",
      },
    },
    {
      name: "API网关",
      status: "normal",
      score: 98,
      metrics: {
        availability: "99.95%",
        responseTime: "85ms",
        errorRate: "0.005%",
      },
    },
    {
      name: t('pages.key_201'),
      status: "normal",
      score: 90,
      metrics: {
        availability: "99.8%",
        responseTime: "150ms",
        errorRate: "0.1%",
      },
    },
  ],
};

export default function RiskControlManagement() {
  const tenantId = useTenant();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [isRuleModalVisible, setIsRuleModalVisible] = useState(false);
  const [isAlertModalVisible, setIsAlertModalVisible] = useState(false);
  const [form] = Form.useForm();

  const handleSync = async () => {
    try {
      const riskList: RiskItem[] = [];
      const { data: res } = await syncRisks({ tenantId: tenantId || '', riskList });
      Modal.info({
        title: t('pages.key_480'),
        width: 600,
        content: (
          <div>
            <div>插入{res.inserted}，更新{res.updated}，跳过{res.skipped}</div>
            {res.errors && res.errors.length > 0 && (
              <div style={{ marginTop: 12 }}>
                <b>错误日志：</b>
                <pre style={{ maxHeight: 200, overflow: 'auto', background: '#f8f8f8' }}>{JSON.stringify(res.errors, null, 2)}</pre>
              </div>
            )}
          </div>
        ),
      });
      // TODO: 刷新表格
    } catch (e) {
      message.error("同步失败：" + (e?.message || t('pages.key_1312')));
    }
  };

  const handleExport = async () => {
    try {
      const type = "risk";
      const limit = 1000;
      const { data: res } = await exportRisks({ tenantId: tenantId || '', type, limit });
      Modal.info({
        title: t('pages.key_693'),
        width: 600,
        content: (
          <div>
            <div>导出成功，文件地址：{res.url || '无'}</div>
            {res.logs && res.logs.length > 0 && (
              <div style={{ marginTop: 12 }}>
                <b>导出日志：</b>
                <pre style={{ maxHeight: 200, overflow: 'auto', background: '#f8f8f8' }}>{JSON.stringify(res.logs, null, 2)}</pre>
              </div>
            )}
          </div>
        ),
      });
    } catch (e) {
      message.error("导出失败：" + (e?.message || t('pages.key_1312')));
    }
  };

  // 状态颜色映射
  const statusColors = {
    active: "green",
    investigating: "orange",
    reviewing: "blue",
    resolved: "gray",
    normal: "green",
    warning: "orange",
    error: "red",
  };

  // 级别颜色映射
  const levelColors = {
    low: "blue",
    medium: "orange",
    high: "red",
    critical: "purple",
  };

  // 类型图标映射
  const typeIcons = {
    account: <SafetyCertificateOutlined />,
    payment: <SecurityScanOutlined />,
    api: <SettingOutlined />,
    content: <EyeOutlined />,
  };

  // 统计数据
  const stats = {
    totalAlerts: mockAlerts.length,
    activeAlerts: mockAlerts.filter(a => a.status === "active" || a.status === "investigating").length,
    resolvedAlerts: mockAlerts.filter(a => a.status === "resolved").length,
    criticalAlerts: mockAlerts.filter(a => a.level === "critical").length,
    highAlerts: mockAlerts.filter(a => a.level === "high").length,
    totalRules: mockRules.length,
    activeRules: mockRules.filter(r => r.status === "active").length,
  };

  // 表格列 - 风险告警
  const alertColumns = [
    {
      title: t('pages.key_501'),
      key: "info",
      render: (_: any, record: any) => (
        <div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Tag color={levelColors[record.level as keyof typeof levelColors]} style={{ marginRight: 8 }}>
              {record.level === "critical" ? t('pages.key_1792') :
                record.level === "high" ? t('pages.key_2615') :
                  record.level === "medium" ? t('pages.key_45') : t('pages.key_104')}
            </Tag>
            <span style={{ fontWeight: "bold" }}>{record.title}</span>
          </div>
          <div style={{ margin: "4px 0" }}>
            {typeIcons[record.type as keyof typeof typeIcons]} {record.type === "account" ? t('pages.key_2326') :
              record.type === "payment" ? t('pages.key_1067') :
                record.type === "api" ? "API安全" : t('pages.key_198')}
          </div>
          <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: t('pages.key_1335'),
      dataIndex: "source",
      key: "source",
      width: 150,
    },
    {
      title: t('pages.key_1195'),
      dataIndex: "timestamp",
      key: "timestamp",
      width: 180,
      sorter: (a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    },
    {
      title: t('pages.key_1602'),
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status: string) => (
        <Tag color={
          status === "active" ? "red" :
            status === "investigating" ? "orange" :
              status === "reviewing" ? "blue" :
                "green"
        }>
          {status === "active" ? t('pages.key_1507') :
            status === "investigating" ? t('pages.key_2298') :
              status === "reviewing" ? t('pages.key_667') :
                t('pages.key_750')}
        </Tag>
      ),
      filters: [
        { text: t('pages.key_1507'), value: "active" },
        { text: t('pages.key_2298'), value: "investigating" },
        { text: t('pages.key_667'), value: "reviewing" },
        { text: t('pages.key_750'), value: "resolved" },
      ],
      onFilter: (value: any, record: any) => record.status === value,
    },
    {
      title: t('pages.key_1058'),
      key: "action",
      width: 150,
      render: (_: any, record: any) => (
        <Space size="small">
          <Button type="link" size="small">{t('pages.key_2136')}</Button>
          {record.status !== "resolved" && (
            <Button type="link" size="small" style={{ color: "#52c41a" }}>{t('pages.key_565')}</Button>
          )}
        </Space>
      ),
    },
  ];

  // 表格列 - 风控规则
  const ruleColumns = [
    {
      title: t('pages.key_2047'),
      key: "info",
      render: (_: any, record: any) => (
        <div>
          <div style={{ fontWeight: "bold" }}>{record.name}</div>
          <div style={{ margin: "4px 0" }}>
            {typeIcons[record.type as keyof typeof typeIcons]} {record.type === "account" ? t('pages.key_2326') :
              record.type === "payment" ? t('pages.key_1067') :
                record.type === "api" ? "API安全" : t('pages.key_198')}
            <Tag color={record.severity === "critical" ? "purple" :
              record.severity === "high" ? "red" :
                record.severity === "medium" ? "orange" : "blue"}
              style={{ marginLeft: 8 }}>
              {record.severity === "critical" ? t('pages.key_1792') :
                record.severity === "high" ? t('pages.key_2615') :
                  record.severity === "medium" ? t('pages.key_45') : t('pages.key_104')}
            </Tag>
          </div>
          <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: t('pages.key_1329'),
      dataIndex: "conditions",
      key: "conditions",
      width: 250,
      render: (conditions: string[]) => (
        <ul style={{ paddingLeft: 16, margin: 0 }}>
          {conditions.map((condition, index) => (
            <li key={index}>{condition}</li>
          ))}
        </ul>
      ),
    },
    {
      title: t('pages.key_348'),
      dataIndex: "actions",
      key: "actions",
      width: 200,
      render: (actions: string[]) => (
        <ul style={{ paddingLeft: 16, margin: 0 }}>
          {actions.map((action, index) => (
            <li key={index}>{action}</li>
          ))}
        </ul>
      ),
    },
    {
      title: t('pages.key_1602'),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => (
        <Switch checked={status === "active"} disabled />
      ),
      filters: [
        { text: t('pages.key_1507'), value: "active" },
        { text: t('pages.key_153'), value: "inactive" },
      ],
      onFilter: (value: any, record: any) => record.status === value,
    },
    {
      title: t('pages.key_1058'),
      key: "action",
      width: 150,
      render: (_: any, record: any) => (
        <Space size="small">
          <Button type="link" size="small" icon={<EditOutlined />}>{t('pages.key_1853')}</Button>
          <Button type="link" size="small" danger icon={<DeleteOutlined />}>{t('pages.key_287')}</Button>
        </Space>
      ),
    },
  ];

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'dashboard',
      label: t('pages.key_2571'),
      children: (
        <div>
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Alert
                message={t('pages.key_1776')}
                description={
                  <div style={{ marginTop: 8 }}>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                      <Progress
                        type="circle"
                        percent={mockSystemHealth.overall.score}
                        width={80}
                        status={mockSystemHealth.overall.status === "normal" ? "success" : "exception"}
                      />
                      <div style={{ marginLeft: 16 }}>
                        <div style={{ fontSize: 16, fontWeight: 'bold' }}>
                          总体健康分数: {mockSystemHealth.overall.score}/100
                        </div>
                        <div>
                          状态: {mockSystemHealth.overall.status === "normal" ? t('pages.key_1465') : t('pages.key_863')}
                        </div>
                        <div>
                          最后更新: {mockSystemHealth.overall.lastUpdated}
                        </div>
                      </div>
                    </div>
                    <Divider style={{ margin: '8px 0 16px' }} />
                    <Row gutter={16}>
                      {mockSystemHealth.components.map(component => (
                        <Col span={6} key={component.name}>
                          <Card size="small" title={component.name} bordered={false}>
                            <div>
                              <Tag color={component.status === "normal" ? "green" : "orange"}>
                                {component.status === "normal" ? t('pages.key_1465') : t('pages.key_2099')}
                              </Tag>
                              <div style={{ margin: '8px 0' }}>当前分数: {component.score}/100</div>
                              <div>可用性: {component.metrics.availability}</div>
                              <div>响应时间: {component.metrics.responseTime}</div>
                              <div>错误率: {component.metrics.errorRate}</div>
                            </div>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </div>
                }
                type="info"
                showIcon
              />
            </Col>

            <Col span={6}>
              <Card>
                <Statistic
                  title={t('pages.key_913')}
                  value={stats.totalAlerts}
                  prefix={<BellOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title={t('pages.key_1509')}
                  value={stats.activeAlerts}
                  valueStyle={{ color: '#ff4d4f' }}
                  prefix={<ExclamationCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title={t('pages.key_1793')}
                  value={stats.criticalAlerts}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<WarningOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title={t('pages.key_1512')}
                  value={stats.activeRules}
                  valueStyle={{ color: '#1677ff' }}
                  prefix={<SafetyCertificateOutlined />}
                />
              </Card>
            </Col>
          </Row>

          <Card title={t('pages.key_1287')} style={{ marginTop: 24 }}>
            <Table
              dataSource={mockAlerts}
              columns={alertColumns}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </div>
      ),
    },
    {
      key: 'alerts',
      label: t('pages.key_2580'),
      children: (
        <div>
          <Card
            bordered={false}
            extra={(
              <Space>
                <Input.Search placeholder={t('pages.key_1047')} style={{ width: 250 }} />
                <Button icon={<FilterOutlined />}>{t('pages.key_1759')}</Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsAlertModalVisible(true)}>{t('pages.key_1531')}</Button>
              </Space>
            )}
          >
            <Table
              dataSource={mockAlerts}
              columns={alertColumns}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </Card>
        </div>
      ),
    },
    {
      key: 'rules',
      label: t('pages.key_2577'),
      children: (
        <div>
          <Card
            bordered={false}
            extra={(
              <Space>
                <Input.Search placeholder={t('pages.key_1056')} style={{ width: 250 }} />
                <Button icon={<FilterOutlined />}>{t('pages.key_1759')}</Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsRuleModalVisible(true)}>{t('pages.key_1541')}</Button>
              </Space>
            )}
          >
            <Table
              dataSource={mockRules}
              columns={ruleColumns}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </Card>
        </div>
      ),
    },
  ];

  // 添加规则模态框
  const ruleModalContent = (
    <Form
      form={form}
      layout="vertical"
      initialValues={{ type: 'account', severity: 'medium', status: 'active' }}
    >
      <Row gutter={16}>
        <Col span={16}>
          <Form.Item
            name="name"
            label={t('pages.key_2049')}
            rules={[{ required: true, message: t('pages.key_2232') }]}
          >
            <Input placeholder={t('pages.key_2232')} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="type"
            label={t('pages.key_2060')}
            rules={[{ required: true, message: t('pages.key_2284') }]}
          >
            <Select>
              <Select.Option value="account">{t('pages.key_2326')}</Select.Option>
              <Select.Option value="payment">{t('pages.key_1067')}</Select.Option>
              <Select.Option value="api">API安全</Select.Option>
              <Select.Option value="content">{t('pages.key_198')}</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="description"
        label={t('pages.key_2053')}
        rules={[{ required: true, message: t('pages.key_2233') }]}
      >
        <Input.TextArea rows={2} placeholder={t('pages.key_2233')} />
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="severity"
            label={t('pages.key_2589')}
            rules={[{ required: true, message: t('pages.key_2297') }]}
          >
            <Select>
              <Select.Option value="critical">{t('pages.key_1792')}</Select.Option>
              <Select.Option value="high">{t('pages.key_2615')}</Select.Option>
              <Select.Option value="medium">{t('pages.key_45')}</Select.Option>
              <Select.Option value="low">{t('pages.key_104')}</Select.Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="status"
            label={t('pages.key_1602')}
            valuePropName="checked"
          >
            <Switch checkedChildren={t('pages.key_1507')} unCheckedChildren={t('pages.key_153')} />
          </Form.Item>
        </Col>
      </Row>

      <Form.List name="conditions">
        {(fields, { add, remove }) => (
          <div>
            <Form.Item label={t('pages.key_2098')}>
              <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>{t('pages.key_1538')}</Button>
            </Form.Item>
            {fields.map(({ key, name, ...restField }) => (
              <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                <Form.Item
                  {...restField}
                  name={name}
                  rules={[{ required: true, message: t('pages.key_2211') }]}
                >
                  <Input placeholder={t('pages.key_2211')} style={{ width: 400 }} />
                </Form.Item>
                <Button danger onClick={() => remove(name)}>{t('pages.key_287')}</Button>
              </Space>
            ))}
          </div>
        )}
      </Form.List>

      <Form.List name="actions">
        {(fields, { add, remove }) => (
          <div>
            <Form.Item label={t('pages.key_508')}>
              <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>{t('pages.key_1527')}</Button>
            </Form.Item>
            {fields.map(({ key, name, ...restField }) => (
              <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                <Form.Item
                  {...restField}
                  name={name}
                  rules={[{ required: true, message: t('pages.key_2178') }]}
                >
                  <Input placeholder={t('pages.key_2178')} style={{ width: 400 }} />
                </Form.Item>
                <Button danger onClick={() => remove(name)}>{t('pages.key_287')}</Button>
              </Space>
            ))}
          </div>
        )}
      </Form.List>
    </Form>
  );

  return (
    <div className="risk-control-container">
      <Card bordered={false}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>

      <Modal
        title={t('pages.key_1544')}
        open={isRuleModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            setIsRuleModalVisible(false);
            form.resetFields();
          });
        }}
        onCancel={() => {
          setIsRuleModalVisible(false);
          form.resetFields();
        }}
        width={700}
        okText={t('pages.key_1523')}
        cancelText={t('pages.key_407')}
      >
        {ruleModalContent}
      </Modal>

      <Modal
        title={t('pages.key_1545')}
        open={isAlertModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            setIsAlertModalVisible(false);
            form.resetFields();
          });
        }}
        onCancel={() => {
          setIsAlertModalVisible(false);
          form.resetFields();
        }}
        width={700}
        okText={t('pages.key_1523')}
        cancelText={t('pages.key_407')}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ type: 'account', level: 'medium', status: 'active' }}
        >
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="title"
                label={t('pages.key_503')}
                rules={[{ required: true, message: t('pages.key_2184') }]}
              >
                <Input placeholder={t('pages.key_2184')} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="type"
                label={t('pages.key_504')}
                rules={[{ required: true, message: t('pages.key_2253') }]}
              >
                <Select>
                  <Select.Option value="account">{t('pages.key_2326')}</Select.Option>
                  <Select.Option value="payment">{t('pages.key_1067')}</Select.Option>
                  <Select.Option value="api">API安全</Select.Option>
                  <Select.Option value="content">{t('pages.key_198')}</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label={t('pages.key_502')}
            rules={[{ required: true, message: t('pages.key_2182') }]}
          >
            <Input.TextArea rows={2} placeholder={t('pages.key_2182')} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="level"
                label={t('pages.key_2589')}
                rules={[{ required: true, message: t('pages.key_2297') }]}
              >
                <Select>
                  <Select.Option value="critical">{t('pages.key_1792')}</Select.Option>
                  <Select.Option value="high">{t('pages.key_2615')}</Select.Option>
                  <Select.Option value="medium">{t('pages.key_45')}</Select.Option>
                  <Select.Option value="low">{t('pages.key_104')}</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="source"
                label={t('pages.key_1335')}
                rules={[{ required: true, message: t('pages.key_2183') }]}
              >
                <Input placeholder={t('pages.key_2183')} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label={t('pages.key_1602')}
                rules={[{ required: true, message: t('pages.key_2273') }]}
              >
                <Select>
                  <Select.Option value="active">{t('pages.key_1507')}</Select.Option>
                  <Select.Option value="investigating">{t('pages.key_2298')}</Select.Option>
                  <Select.Option value="reviewing">{t('pages.key_667')}</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ipAddress"
                label="IP地址"
              >
                <Input placeholder={t('pages.key_2219')} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="affectedUsers"
                label={t('pages.key_421')}
              >
                <Input type="number" min={0} placeholder={t('pages.key_2180')} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="additionalInfo"
            label={t('pages.key_194')}
          >
            <Input.TextArea rows={3} placeholder={t('pages.key_2176')} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
