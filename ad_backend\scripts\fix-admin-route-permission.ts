import 'dotenv/config';
import { AppDataSource } from '../src/config/data-source';
import { Role } from '../src/modules/role/role.entity';
import { Route } from '../src/modules/route/route.entity';

async function main() {
    await AppDataSource.initialize();
    const roleRepo = AppDataSource.getRepository(Role);
    const routeRepo = AppDataSource.getRepository(Route);

    // 查找超级管理员
    const adminRole = await roleRepo.findOne({ where: { name: '超级管理员' }, relations: ['routes'] });
    if (!adminRole) {
        console.error('未找到超级管理员角色');
        process.exit(1);
    }

    // 查找所有路由
    const allRoutes = await routeRepo.find();
    if (allRoutes.length === 0) {
        console.error('未找到任何路由');
        process.exit(1);
    }

    // 分配所有路由
    adminRole.routes = allRoutes;
    await roleRepo.save(adminRole);
    console.log(`已将 ${allRoutes.length} 条路由分配给超级管理员`);
    await AppDataSource.destroy();
}

main().catch(err => {
    console.error(err);
    process.exit(1);
}); 