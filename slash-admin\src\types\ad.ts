// 广告账户类型
export interface AdAccountItem {
  id: string;
  name: string;
  status: string;
  accountId: string;
}

// 素材类型
export interface MaterialItem {
  id: string;
  name: string;
  type: string;
  status: string;
  url: string;
  content: string;
}

// VCC类型
export interface VccItem {
  id: string;
  cardNumber: string;
  cardHolder: string;
  expiry: string;
  cvv: string;
  bindAccount: string;
  status: string;
}

// 风控类型
export interface RiskItem {
  id: string;
  type: string;
  level: string;
  description: string;
  status: string;
}

// 同步/导出结果类型
export interface SyncResult {
  success: boolean;
  url?: string;
  logs?: string[];
}

// 风控同步响应
export interface SyncRisksResponse {
  inserted: number;
  updated?: number;
  skipped?: number;
  errors?: any[];
}

// 风控导出响应
export interface ExportRisksResponse {
  url?: string;
  logs?: any[];
}
