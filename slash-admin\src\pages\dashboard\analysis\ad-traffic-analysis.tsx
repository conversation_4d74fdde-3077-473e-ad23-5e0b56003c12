import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Icon } from "@/components/icon";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/ui/tabs";

// 数据类型定义
interface AdTrafficData {
  spend: number[];
  conversions: number[];
  clicks: number[];
  impressions: number[];
  timeLabels: string[];
}

interface AdTrafficAnalysisProps {
  data: AdTrafficData | null;
  timeRange: 'day' | 'week' | 'month';
  onTimeRangeChange: (timeRange: 'day' | 'week' | 'month') => void;
}

const AdTrafficAnalysis = ({ data, timeRange, onTimeRangeChange }: AdTrafficAnalysisProps) => {
  const { t } = useTranslation();

  // 如果没有数据，显示加载状态
  if (!data) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-base font-medium">{t('pages.key_840')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 计算统计数据
  const totalImpressions = data.impressions.reduce((sum: number, val: number) => sum + val, 0);
  const totalClicks = data.clicks.reduce((sum: number, val: number) => sum + val, 0);
  const totalConversions = data.conversions.reduce((sum: number, val: number) => sum + val, 0);
  const totalSpend = data.spend.reduce((sum: number, val: number) => sum + val, 0);

  const avgCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
  const avgCVR = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;
  const avgCPC = totalClicks > 0 ? totalSpend / totalClicks : 0;

  // 图表配置
  const chartOptions = useChart({
    chart: {
      height: 350,
      type: 'line',
    },
    stroke: {
      width: [3, 3, 3, 3],
      curve: 'smooth',
    },
    xaxis: {
      categories: data.timeLabels,
    },
    yaxis: [
      {
        title: {
          text: t('pages.key_1116'),
        },
        labels: {
          formatter: (value: number) => {
            if (value >= 1000000) {
              return `${(value / 1000000).toFixed(1)}M`;
            } else if (value >= 1000) {
              return `${(value / 1000).toFixed(0)}K`;
            }
            return Math.round(value).toString();
          },
        },
      },
      {
        opposite: true,
        title: {
          text: '花费 (¥)',
        },
        labels: {
          formatter: (value: number) => `¥${Math.round(value).toLocaleString()}`,
        },
      }
    ],
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: (value: number, { seriesIndex }: { seriesIndex: number }) => {
          if (seriesIndex === 0) return `${value.toLocaleString()} 展示`;
          if (seriesIndex === 1) return `${value.toLocaleString()} 点击`;
          if (seriesIndex === 2) return `${value.toLocaleString()} 转化`;
          return `¥${value.toLocaleString()}`;
        },
      },
    },
    colors: ['#3b82f6', '#4ade80', '#f59e0b', '#a855f7'],
    legend: {
      position: 'top',
      horizontalAlign: 'right',
    },
  });

  // 图表数据
  const series = [
    {
      name: t('pages.key_725'),
      type: 'line',
      data: data.impressions,
      yAxisIndex: 0,
    },
    {
      name: t('pages.key_1590'),
      type: 'line',
      data: data.clicks,
      yAxisIndex: 0,
    },
    {
      name: t('pages.key_2362'),
      type: 'line',
      data: data.conversions,
      yAxisIndex: 0,
    },
    {
      name: t('pages.key_1927'),
      type: 'line',
      data: data.spend,
      yAxisIndex: 1,
    }
  ];

  // 漏斗图配置
  const funnelChartOptions = useChart({
    chart: {
      type: 'bar',
      height: 400,
    },
    plotOptions: {
      bar: {
        borderRadius: 0,
        horizontal: true,
        barHeight: '80%',
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number, opt: any) => {
        return opt.w.globals.labels[opt.dataPointIndex] + ': ' + val.toLocaleString();
      },
      dropShadow: {
        enabled: true,
      },
    },
    colors: ['#3b82f6', '#4ade80', '#f59e0b'],
    xaxis: {
      categories: [t('pages.key_725'), t('pages.key_1590'), t('pages.key_2362')],
    },
    legend: {
      show: false,
    },
  });

  // 漏斗图数据
  const funnelSeries = [
    {
      name: t('pages.key_1116'),
      data: [
        totalImpressions,
        totalClicks,
        totalConversions
      ],
    },
  ];

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-base font-medium">{t('pages.key_840')}</CardTitle>
        <div className="flex space-x-2">
          <button
            onClick={() => onTimeRangeChange('day')}
            className={`px-3 py-1 text-sm rounded-md ${timeRange === 'day' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >
            日
          </button>
          <button
            onClick={() => onTimeRangeChange('week')}
            className={`px-3 py-1 text-sm rounded-md ${timeRange === 'week' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >
            周
          </button>
          <button
            onClick={() => onTimeRangeChange('month')}
            className={`px-3 py-1 text-sm rounded-md ${timeRange === 'month' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >
            月
          </button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="trend">
          <TabsList className="mb-4">
            <TabsTrigger value="trend">{t('pages.key_2343')}</TabsTrigger>
            <TabsTrigger value="funnel">{t('pages.key_1572')}</TabsTrigger>
          </TabsList>

          <TabsContent value="trend" className="space-y-4">
            <div className="h-[350px]">
              <Chart
                type="line"
                series={series}
                options={chartOptions}
                height={350}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2">
              <div className="bg-muted/50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均点击率 (CTR)</p>
                    <p className="text-2xl font-semibold">{avgCTR.toFixed(2)}%</p>
                  </div>
                  <div className="p-2 rounded-full bg-blue-100 text-blue-700">
                    <Icon icon="solar:mouse-bold-duotone" size={24} />
                  </div>
                </div>
              </div>

              <div className="bg-muted/50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均转化率 (CVR)</p>
                    <p className="text-2xl font-semibold">{avgCVR.toFixed(2)}%</p>
                  </div>
                  <div className="p-2 rounded-full bg-green-100 text-green-700">
                    <Icon icon="solar:flag-bold-duotone" size={24} />
                  </div>
                </div>
              </div>

              <div className="bg-muted/50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均点击成本 (CPC)</p>
                    <p className="text-2xl font-semibold">¥{avgCPC.toFixed(2)}</p>
                  </div>
                  <div className="p-2 rounded-full bg-amber-100 text-amber-700">
                    <Icon icon="solar:dollar-minimalistic-bold-duotone" size={24} />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="funnel" className="h-[400px]">
            <Chart
              type="bar"
              series={funnelSeries}
              options={funnelChartOptions}
              height={400}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AdTrafficAnalysis;
