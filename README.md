# 广告管理系统项目进度文档

## 一、项目概述
本项目为广告管理系统，包含广告组、广告、受众等核心模块，支持多平台（如Facebook、Google等）广告数据的管理、同步与风险控制。采用前后端分离架构，前端基于 React + Apollo Client，后端基于 NestJS + TypeORM + GraphQL（nestjs-query）。

## 二、核心模块与进度
- **广告组管理**：已完成 GraphQL 化，支持增删查改、分页、搜索。
- **广告管理**：已完成 GraphQL 化，支持增删查改、分页、搜索。
- **受众管理**：已完成 GraphQL 化，支持分页、搜索。
- **风险控制**：已集成至侧边栏，支持权限与模块模式切换。
- **同步服务**：Facebook 数据同步服务已接入。

## 三、前后端联调情况
- 后端 GraphQL schema 已自动生成，接口类型与 DTO/Entity 完全一致。
- 前端所有管理页面已切换为通过 Apollo Client 调用 GraphQL 查询与 mutation。
- 已完成广告组、广告、受众等主要实体的 CRUD 联调。

## 四、后续计划
- 优化接口文档与开发者文档
- 增加更多平台广告同步能力
- 完善权限与风控体系

---

# 贡献说明
- 代码风格统一，Entity/DTO 字段类型保持严格一致。
- 建议开发前先阅读下方后端、前端及接口文档。
