import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, DatePicker, InputNumber, message, Tag, AutoComplete } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useQuery, useLazyQuery } from '@apollo/client';
import { GET_CAMPAIGN_LIST } from '@/api/campaign.graphql';
import { useAudienceList } from '@/api/audience.graphql';
import { useCreateAdSet, useUpdateAdSet, useDeleteAdSet, GET_AD_SET_LIST } from '@/api/adSet.graphql';
import { useQuery as useApolloQuery, useLazyQuery as useApolloLazyQuery, gql as gql2 } from '@apollo/client';
import { COUNTRY_MAP, LANGUAGE_MAP, GENDER_MAP, getLabelByValue } from '@/constants/facebook';
import { gql } from '@apollo/client';
import { GET_AD_ACCOUNT_LIST } from '@/api/adAccount.graphql';
import PixelSelect from '@/components/PixelSelect';

// Pixel 查询
const GET_PIXELS_BY_CAMPAIGN = gql`
  query GetPixelsByCampaign($campaignId: String!) {
    pixelsByCampaign(campaignId: $campaignId) {
      id
      name
    }
  }
`;
// Facebook 用户 App 查询
const GET_FACEBOOK_USER_APPS_BY_AD_ACCOUNT = gql`
  query FacebookUserAppsByAdAccountByCampaignId($campaignId: ID!) {
    facebookUserAppsByAdAccountByCampaignId(campaignId: $campaignId) {
      id
      name
      link
    }
  }
`;
// businessId 查询
const BUSINESS_ID_BY_CAMPAIGN_ID = gql`
  query BusinessIdByCampaignId($campaignId: String!) {
    businessIdByCampaignId(campaignId: $campaignId)
  }
`;

export default function AdSetManagement() {
    const { t } = useTranslation();
    const [data, setData] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editing, setEditing] = useState<any>(null);
    const [deleteId, setDeleteId] = useState<string | null>(null);
    const [form] = Form.useForm();
    const [optimizationGoal, setOptimizationGoal] = useState<string | undefined>();
    const [getPixels, { data: pixelData, loading: pixelLoading }] = useLazyQuery(GET_PIXELS_BY_CAMPAIGN);

    // 获取广告系列列表
    const { data: campaignData } = useQuery(GET_CAMPAIGN_LIST);
    const campaigns = campaignData?.campaigns || [];
    const campaignOptions = campaigns.map((c: any) => ({ label: c.name, value: c.id }));

    // 获取受众列表
    const { data: audienceData } = useAudienceList({ paging: { first: 50 } });
    const audiences = (audienceData?.audiences || audienceData?.audienceDTOS?.edges?.map((edge: any) => edge.node) || []);
    // 构造受众下拉 label，包含主要字段
    const audienceOptions = audiences.map((a: any) => {
        // 展示国家、年龄、性别等主要信息
        const countries = getLabelByValue(COUNTRY_MAP, a.geo_locations?.countries || []);
        const age = a.age_min && a.age_max ? `${a.age_min}-${a.age_max}` : '';
        const gender = getLabelByValue(GENDER_MAP, a.gender);
        const label = `${a.name}${countries ? '｜' + countries : ''}${age ? '｜' + age : ''}${gender ? '｜' + gender : ''}`;
        return { label, value: a.id };
    });

    // 广告组列表
    const { data: adSetData, loading: adSetLoading, refetch } = useApolloQuery(GET_AD_SET_LIST, { variables: { filter: {} } });
    const adSets = adSetData?.adSetsCustom || [];

    // 新增/编辑/删除 mutation
    const [createAdSet] = useCreateAdSet();
    const [updateAdSet] = useUpdateAdSet();
    const [deleteAdSet] = useDeleteAdSet();

    // 获取广告账户列表（含 accessToken）
    const { data: adAccountData } = useQuery(GET_AD_ACCOUNT_LIST);
    const adAccounts = adAccountData?.adAccounts || [];

    // 新增 Facebook 用户 App Promotion 应用查询（用 campaignId 让后端自动查账户和 accessToken）
    const [getFacebookUserAppsByAdAccount, { data: facebookUserAppsByAdAccountData, loading: facebookUserAppsByAdAccountLoading }] = useLazyQuery(GET_FACEBOOK_USER_APPS_BY_AD_ACCOUNT);

    const [appOptions, setAppOptions] = useState<{ label: string, value: string, storeUrl: string }[]>([]);
    const [selectedCampaignObjective, setSelectedCampaignObjective] = useState<string | undefined>();
    const [showAppFields, setShowAppFields] = useState(false);
    const [businessId, setBusinessId] = useState<string>('');
    const [getBusinessIdByCampaignId] = useApolloLazyQuery(BUSINESS_ID_BY_CAMPAIGN_ID);

    // 在组件状态中增加 devicePlatform
    const [devicePlatform, setDevicePlatform] = useState<string | undefined>();

    // 监听广告系列变化，自动获取广告账户ID和accessToken，并拉取 Facebook 用户 App
    const handleCampaignChange = async (campaignId: string) => {
        getPixels({ variables: { campaignId } });
        getFacebookUserAppsByAdAccount({ variables: { campaignId } });
        form.setFieldsValue({ pixelId: undefined, customEventType: undefined, applicationId: undefined, objectStoreUrl: undefined, appEventType: undefined });
        // 取出所选 campaign 的 objective
        const campaign = campaigns.find((c: any) => c.id === campaignId);
        setSelectedCampaignObjective(campaign?.objective);
        setShowAppFields(campaign?.objective === 'OUTCOME_APP_PROMOTION');
        // 获取 businessId
        console.log('handleCampaignChange: campaignId', campaignId);
        const { data } = await getBusinessIdByCampaignId({ variables: { campaignId } });
        console.log('handleCampaignChange: businessIdByCampaignId result', data);
        setBusinessId(data?.businessIdByCampaignId || '');
    };

    useEffect(() => {
        if (facebookUserAppsByAdAccountData?.facebookUserAppsByAdAccountByCampaignId) {
            setAppOptions(
                facebookUserAppsByAdAccountData.facebookUserAppsByAdAccountByCampaignId.map((app: any) => ({
                    label: app.name,
                    value: app.id,
                    storeUrl: app.link || '',
                }))
            );
        }
    }, [facebookUserAppsByAdAccountData]);

    const handleOptimizationGoalChange = (val: string) => {
        setOptimizationGoal(val);
        // 联动计费方式
        setBillingEventOptions(OPTIMIZATION_BILLING_MAP[val] || []);
        // 联动转化事件
        setShowEventType(['OFFSITE_CONVERSIONS', 'APP_INSTALLS'].includes(val));
        // 清空相关字段
        form.setFieldsValue({ billingEvent: undefined, customEventType: undefined });
    };

    // 新建/编辑弹窗
    const openModal = (record?: any) => {
        setEditing(record || null);
        setModalVisible(true);
        if (record) {
            form.setFieldsValue({
                ...record,
                campaignId: record.adCampaign?.id,
                audienceId: record.audience?.id,
                startTime: record.startTime ? dayjs(record.startTime) : undefined,
                endTime: record.endTime ? dayjs(record.endTime) : undefined,
                bidAmount: record.bid_amount ? Number(record.bid_amount) : undefined,
            });
        } else {
            form.resetFields();
        }
    };

    // 计费方式联动逻辑
    const [billingEventOptions, setBillingEventOptions] = useState<{ label: string, value: string }[]>([]);
    const [showEventType, setShowEventType] = useState(false);

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            console.log('表单 values', values);
            const bid_amount = values.bidAmount !== undefined ? Number(values.bidAmount) : undefined;
            const input: any = {
                ...values,
                bid_amount,
                startTime: values.startTime ? values.startTime.toISOString() : undefined,
                endTime: values.endTime ? values.endTime.toISOString() : undefined,
            };
            delete input.bidAmount;
            // 只要表单填写了 App 和 App Store链接就组装
            if (values.applicationId && values.objectStoreUrl) {
                input.promoted_object = {
                    application_id: values.applicationId,
                    object_store_url: values.objectStoreUrl,
                    custom_event_type: values.appEventType,
                };
                // 新增：将 device_platforms 传递给后端
                input.device_platforms = values.device_platforms ? [values.device_platforms] : undefined;
                input.user_os = values.user_os ? [values.user_os] : undefined;
            } else if (values.optimizationGoal === 'OFFSITE_CONVERSIONS') {
                input.promoted_object = {
                    pixel_id: values.pixelId,
                    custom_event_type: values.customEventType,
                };
            }
            if (editing) {
                await updateAdSet({ variables: { input: { ...input, id: editing.id } } });
                message.success(t('pages.key_145'));
            } else {
                await createAdSet({ variables: { ...input } });
                message.success(t('pages.key_1149'));
            }
            setModalVisible(false);
            setEditing(null);
            refetch();
        } catch { }
    };

    const handleDelete = (record: any) => setDeleteId(record.id);
    const handleDeleteOk = async () => {
        if (!deleteId) return;
        await deleteAdSet({ variables: { id: deleteId } });
        setDeleteId(null);
        message.success(t('pages.key_294'));
        refetch();
    };
    const handleDeleteCancel = () => setDeleteId(null);

    // 在组件顶部 useEffect 追踪 PixelSelect props
    useEffect(() => {
        if (optimizationGoal === 'OFFSITE_CONVERSIONS') {
            const campaignId = form.getFieldValue('campaignId');
            const pixelId = form.getFieldValue('pixelId');
            const campaign = campaigns.find((c: any) => c.id === campaignId);
            const adAccountId = campaign?.adAccount?.accountId || '';
            const accessToken = campaign?.adAccount?.accessToken || '';
            console.log('PixelSelect props', {
                campaignId,
                businessId,
                adAccountId,
                accessToken,
                value: pixelId,
                disabled: !businessId,
            });
        }
    }, [optimizationGoal, businessId, campaigns, form]);

    const columns = [
        { title: 'ID', dataIndex: 'id', key: 'id', render: (id: string) => <span title={id}>{id.length > 10 ? id.slice(0, 8) + '...' : id}</span> },
        { title: t('pages.key_483'), dataIndex: 'name', key: 'name' },
        { title: t('pages.key_949'), dataIndex: ['adCampaign', 'name'], key: 'campaignName' },
        { title: t('pages.key_408'), dataIndex: ['audience', 'name'], key: 'audienceName' },
        { title: t('pages.key_1602'), dataIndex: 'status', key: 'status', render: (v: string) => STATUS_OPTIONS.find(o => o.value === v)?.label || v },
        { title: t('pages.key_1194'), dataIndex: 'dailyBudget', key: 'dailyBudget', render: (v: number) => v ? <Tag color="blue">¥{v}</Tag> : '-' },
        { title: t('pages.key_858'), dataIndex: 'startTime', key: 'startTime' },
        { title: t('pages.key_1841'), dataIndex: 'endTime', key: 'endTime' },
        { title: t('pages.key_101'), dataIndex: 'optimizationGoal', key: 'optimizationGoal', render: (v: string) => OPTIMIZATION_GOAL_OPTIONS.find(o => o.value === v)?.label || v },
        { title: t('pages.key_2107'), dataIndex: 'billingEvent', key: 'billingEvent', render: (v: string) => BILLING_EVENT_OPTIONS.find(o => o.value === v)?.label || v },
        {
            title: t('pages.key_1058'),
            key: 'actions',
            render: (_: any, record: any) => (
                <Space>
                    <Button size="small" icon={<EditOutlined />} onClick={() => openModal(record)}>{t('pages.key_1853')}</Button>
                    <Button size="small" icon={<DeleteOutlined />} danger onClick={() => handleDelete(record)}>{t('pages.key_287')}</Button>
                </Space>
            ),
        },
    ];

    const STATUS_OPTIONS = [
        { label: t('pages.key_496'), value: 'ACTIVE' },
        { label: t('pages.key_1221'), value: 'PAUSED' },
    ];
    const OPTIMIZATION_GOAL_OPTIONS = [
        { label: t('pages.key_2357'), value: 'OFFSITE_CONVERSIONS' },
        { label: t('pages.key_1513'), value: 'LINK_CLICKS' },
        { label: t('pages.key_722'), value: 'IMPRESSIONS' },
        { label: t('pages.key_2043'), value: 'REACH' },
        { label: t('pages.key_2029'), value: 'LEAD_GENERATION' },
        { label: t('pages.key_845'), value: 'APP_INSTALLS' },
        { label: t('pages.key_2542'), value: 'PAGE_LIKES' },
        { label: t('pages.key_2064'), value: 'THRUPLAY' },
    ];
    const BILLING_EVENT_OPTIONS = [
        { label: t('pages.key_1470'), value: 'IMPRESSIONS' },
        { label: t('pages.key_1471'), value: 'CLICKS' },
    ];
    const OPTIMIZATION_BILLING_MAP: Record<string, { label: string, value: string }[]> = {
        APP_INSTALLS: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        AD_RECALL_LIFT: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        ENGAGED_USERS: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        EVENT_RESPONSES: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        IMPRESSIONS: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        LEAD_GENERATION: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        LINK_CLICKS: [
            { label: t('pages.key_1471'), value: 'LINK_CLICKS' },
            { label: t('pages.key_1470'), value: 'IMPRESSIONS' },
        ],
        OFFSITE_CONVERSIONS: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        PAGE_LIKES: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        POST_ENGAGEMENT: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        REACH: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        REPLIES: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        SOCIAL_IMPRESSIONS: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        THRUPLAY: [
            { label: t('pages.key_1470'), value: 'IMPRESSIONS' },
            { label: t('pages.key_2065'), value: 'THRUPLAY' },
        ],
        TWO_SECOND_CONTINUOUS_VIDEO_VIEWS: [
            { label: t('pages.key_1470'), value: 'IMPRESSIONS' },
            { label: '2秒连续视频播放', value: 'TWO_SECOND_CONTINUOUS_VIDEO_VIEWS' },
        ],
        VALUE: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
        LANDING_PAGE_VIEWS: [{ label: t('pages.key_1470'), value: 'IMPRESSIONS' }],
    };
    const EVENT_TYPE_OPTIONS = [
        { label: '购买 (PURCHASE)', value: 'PURCHASE' },
        { label: '注册 (COMPLETE_REGISTRATION)', value: 'COMPLETE_REGISTRATION' },
        { label: '加购 (ADD_TO_CART)', value: 'ADD_TO_CART' },
        { label: '安装 (INSTALL)', value: 'INSTALL' },
        { label: '启动 (ACTIVATE_APP)', value: 'ACTIVATE_APP' },
        { label: '发起结账 (INITIATED_CHECKOUT)', value: 'INITIATED_CHECKOUT' },
        { label: '搜索 (SEARCH)', value: 'SEARCH' },
        { label: '自定义事件 (CUSTOMIZE_PRODUCT)', value: 'CUSTOMIZE_PRODUCT' },
    ];
    const DEVICE_PLATFORM_OPTIONS = [
        { label: '移动端 (mobile)', value: 'mobile' },
        { label: '桌面端 (desktop)', value: 'desktop' },
        { label: '电视 (connected_tv)', value: 'connected_tv' },
        { label: 'Messenger', value: 'messenger' },
    ];
    const USER_OS_OPTIONS = [
        { label: 'iOS', value: 'iOS' },
        { label: 'Android', value: 'Android' },
    ];

    return (
        <div>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
                <Button type="primary" icon={<PlusOutlined />} onClick={() => openModal()}>{t('pages.key_1148')}</Button>
            </div>
            <Table
                rowKey="id"
                columns={columns}
                dataSource={adSets}
                loading={adSetLoading}
                pagination={{ pageSize: 10 }}
            />
            <Modal
                open={modalVisible}
                title={editing ? t('pages.key_1857') : t('pages.key_1148')}
                onOk={handleModalOk}
                onCancel={() => setModalVisible(false)}
                okText={t('pages.key_1693')}
                cancelText={t('pages.key_407')}
                destroyOnClose
            >
                <Form form={form} layout="vertical" preserve={false}>
                    <Form.Item name="name" label={t('pages.key_483')} rules={[{ required: true, message: t('pages.key_2196') }]}><Input placeholder={t('pages.key_2181')} /></Form.Item>
                    <Form.Item name="campaignId" label={t('pages.key_948')} rules={[{ required: true, message: t('pages.key_2259') }]}><Select options={campaignOptions} placeholder={t('pages.key_2259')} onChange={handleCampaignChange} /></Form.Item>
                    <Form.Item name="audienceId" label={t('pages.key_408')} rules={[{ required: true, message: t('pages.key_2251') }]}><Select options={audienceOptions} placeholder={t('pages.key_2251')} /></Form.Item>
                    <Form.Item name="dailyBudget" label={t('pages.key_1194')} rules={[{ required: true, message: t('pages.key_2204') }]}><InputNumber min={1} style={{ width: '100%' }} placeholder={t('pages.key_2204')} /></Form.Item>
                    <Form.Item name="startTime" label={t('pages.key_858')} rules={[{ required: true, message: t('pages.key_2262') }]}><DatePicker style={{ width: '100%' }} /></Form.Item>
                    <Form.Item name="endTime" label={t('pages.key_1841')}
                        rules={[{
                            validator: (_, value) => {
                                const start = form.getFieldValue('startTime');
                                if (value && start) {
                                    const startDay = dayjs(start);
                                    const endDay = dayjs(value);
                                    if (!endDay.isAfter(startDay, 'day')) {
                                        return Promise.reject(new Error(t('pages.key_1842')));
                                    }
                                }
                                return Promise.resolve();
                            }
                        }]}
                    >
                        <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item name="optimizationGoal" label={t('pages.key_101')} rules={[{ required: true, message: t('pages.key_2249') }]}><Select options={OPTIMIZATION_GOAL_OPTIONS} placeholder={t('pages.key_2249')} onChange={handleOptimizationGoalChange} /></Form.Item>
                    {optimizationGoal === 'OFFSITE_CONVERSIONS' && (
                        <Form.Item
                            label="Pixel"
                            name="pixelId"
                            rules={[{ required: true, message: '请选择或新建 Pixel' }]}
                        >
                            <PixelSelect
                                key={businessId + form.getFieldValue('campaignId')}
                                campaignId={form.getFieldValue('campaignId')}
                                businessId={businessId}
                                value={form.getFieldValue('pixelId')}
                                onChange={val => form.setFieldsValue({ pixelId: val })}
                            />
                        </Form.Item>
                    )}
                    <Form.Item name="billingEvent" label={t('pages.key_2107')} rules={[{ required: true, message: t('pages.key_2286') }]}><Select options={billingEventOptions} placeholder={t('pages.key_2286')} /></Form.Item>
                    {showEventType && (
                        <Form.Item name="customEventType" label={t('pages.key_2358')} rules={[{ required: true, message: t('pages.key_2292') }]}><Select options={EVENT_TYPE_OPTIONS} placeholder={t('pages.key_2292')} showSearch optionFilterProp="label" /></Form.Item>
                    )}
                    <Form.Item name="status" label={t('pages.key_1602')} rules={[{ required: true, message: t('pages.key_2273') }]}><Select options={STATUS_OPTIONS} placeholder={t('pages.key_2273')} /></Form.Item>
                    <Form.Item name="bidAmount" label={t('pages.key_1748')} rules={[{ required: true, message: t('pages.key_2221') }]}><InputNumber min={0.01} step={0.01} style={{ width: '100%' }} placeholder={t('pages.key_2221')} /></Form.Item>
                    {showAppFields && (
                        <>
                            <Form.Item name="device_platforms" label={t('pages.key_2111')} rules={[{ required: true, message: t('pages.key_2287') }]}><Select options={DEVICE_PLATFORM_OPTIONS} placeholder={t('pages.key_2287')} /></Form.Item>
                            <Form.Item name="user_os" label={t('pages.key_1064')} rules={[{ required: true, message: t('pages.key_2270') }]}><Select options={USER_OS_OPTIONS} placeholder={t('pages.key_2270')} /></Form.Item>
                            <Form.Item name="applicationId" label="App" rules={[{ required: true, message: '请选择或输入 App' }]}><AutoComplete options={appOptions} style={{ width: 300 }} placeholder="请选择或输入 App 名称或ID" filterOption={(inputValue, option) => (option?.label ?? '').toLowerCase().includes(inputValue.toLowerCase()) || (option?.value ?? '').toLowerCase().includes(inputValue.toLowerCase())} onSelect={(value, option) => { if (option && option.storeUrl) { form.setFieldsValue({ objectStoreUrl: option.storeUrl }); } }} /></Form.Item>
                            <Form.Item name="objectStoreUrl" label="App Store链接" rules={[{ required: true, message: t('pages.key_2168') }, { type: 'url', message: t('pages.key_2210') }]}><Input placeholder={t('pages.key_2168')} /></Form.Item>
                        </>
                    )}
                </Form>
            </Modal>
            <Modal
                open={!!deleteId}
                title="确认删除该广告组？"
                onOk={handleDeleteOk}
                onCancel={handleDeleteCancel}
                okText={t('pages.key_287')}
                okType="danger"
                cancelText={t('pages.key_407')}
                destroyOnClose
            >
                <div>ID：{deleteId}</div>
            </Modal>
        </div>
    );
} 