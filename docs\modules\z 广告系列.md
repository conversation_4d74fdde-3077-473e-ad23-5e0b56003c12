# 组件路径 /pages/management/campaign/index

## 前端

- **实际路径**：slash-admin/src/pages/management/campaign/index.tsx
- **主要页面/组件**：

  - index.tsx（主入口，广告系列管理表单与列表，含增删改查、分页、搜索、弹窗等）
  - AdSetManagement（广告组管理 Tab）

- **主要依赖 API 定义**：slash-admin/src/api/campaign.graphql.ts

### 重要说明：两种广告系列实体

本系统包含两种不同的广告系列实体：

1. **Campaign** - 简化的 Facebook 广告系列实体，主要用于 Facebook 平台同步
2. **AdCampaign** - 完整的多平台广告系列实体，包含预算、时间等完整信息

### API 调用列表与详细说明

#### Campaign 相关接口（Facebook 专用）

| API 名称           | 方法     | GraphQL 名/路径             | 参数类型/说明                        | 返回字段                                                                                                                     | 说明/示例                |
| ------------------ | -------- | --------------------------- | ------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------- | ------------------------ |
| 获取广告系列列表   | query    | campaigns                   | 无                                   | id, name, objective, status, specialAdCategories, accountId, facebookCampaignId, syncStatus, syncError, createdAt, updatedAt | Facebook 广告系列列表    |
| 新增广告系列       | mutation | createCampaign              | input: CreateCampaignInputX          | Campaign 完整字段                                                                                                            | 创建 Facebook 广告系列   |
| 编辑广告系列       | mutation | updateCampaign              | id: ID!, input: CreateCampaignInputX | Campaign 完整字段                                                                                                            | 编辑 Facebook 广告系列   |
| 删除广告系列       | mutation | deleteCampaign              | id: ID!                              | Boolean                                                                                                                      | 删除 Facebook 广告系列   |
| 刷新 Facebook 状态 | mutation | refreshCampaignFromFacebook | id: ID!                              | Campaign 完整字段                                                                                                            | 从 Facebook 同步最新状态 |

#### AdCampaign 相关接口（多平台通用）

| API 名称         | 方法     | GraphQL 名/路径     | 参数类型/说明                                                                      | 返回字段                                                                                                      | 说明/示例          |
| ---------------- | -------- | ------------------- | ---------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------- | ------------------ |
| 获取广告系列列表 | query    | adCampaigns         | filter: AdCampaignFilterInput                                                      | id, platform, campaignId, name, adAccount, tenant, raw, status, budget, startDate, endDate, tags, description | 多平台广告系列列表 |
| 新增广告系列     | mutation | createOneAdCampaign | name, platform, budget, status, startDate, endDate, tags, description, adAccountId | AdCampaign 完整字段                                                                                           | 创建多平台广告系列 |

#### 主要参数结构说明

##### Campaign 相关参数

- **CreateCampaignInputX**（用于 Facebook 广告系列新增/编辑）

  ```ts
  {
    name: string; // 广告系列名称
    objective: string; // 广告目标，枚举值（见下）
    status: string; // 状态，枚举值（ACTIVE/PAUSED等）
    specialAdCategories: string[]; // 特殊广告类别（如HOUSING、EMPLOYMENT等）
    accountId: string; // 投放账户ID
  }
  ```

  - **objective 枚举值**：
    - OUTCOME_LEADS（线索收集）
    - OUTCOME_SALES（销售转化）
    - OUTCOME_ENGAGEMENT（互动）
    - OUTCOME_AWARENESS（品牌认知）
    - OUTCOME_TRAFFIC（流量）
    - OUTCOME_APP_PROMOTION（应用推广）
  - **status 枚举值**：
    - ACTIVE（启用）
    - PAUSED（暂停）
  - **specialAdCategories 枚举值**：
    - HOUSING（住房）
    - EMPLOYMENT（就业）
    - CREDIT（信贷）
    - ISSUES_ELECTIONS_POLITICS（政治/选举/社会议题）

##### AdCampaign 相关参数

- **createOneAdCampaign 参数**（用于多平台广告系列创建）

  ```ts
  {
    name: string; // 广告系列名称
    platform: string; // 平台类型（facebook/google/tiktok等）
    budget: number; // 预算
    status: string; // 状态
    startDate: Date; // 开始时间
    endDate: Date; // 结束时间
    tags?: string[]; // 标签数组
    description?: string; // 描述
    adAccountId: string; // 广告账户ID
  }
  ```

- **AdCampaignFilterInput**（用于查询过滤）
  ```ts
  {
    name?: string; // 名称过滤
    platform?: string; // 平台过滤
  }
  ```

#### 返回字段说明

##### Campaign 字段（Facebook 专用）

| 字段名              | 类型      | 说明                     |
| ------------------- | --------- | ------------------------ |
| id                  | string    | 主键，UUID               |
| name                | string    | 广告系列名称             |
| objective           | string    | 广告目标                 |
| status              | string    | 状态                     |
| specialAdCategories | string[]  | 特殊广告类别             |
| accountId           | string    | 投放账户 ID              |
| facebookCampaignId  | string    | Facebook 平台广告系列 ID |
| syncStatus          | string    | 同步状态                 |
| syncError           | string    | 同步错误信息             |
| createdAt           | Date      | 创建时间                 |
| updatedAt           | Date      | 更新时间                 |
| adAccount           | AdAccount | 关联的广告账户对象       |

##### AdCampaign 字段（多平台通用）

| 字段名      | 类型      | 说明                  |
| ----------- | --------- | --------------------- |
| id          | string    | 主键，UUID            |
| platform    | string    | 平台类型              |
| campaignId  | string    | 平台广告系列 ID       |
| name        | string    | 广告系列名称          |
| adAccount   | AdAccount | 关联的广告账户对象    |
| tenant      | Tenant    | 关联的租户对象        |
| raw         | any       | 原始数据（JSON 格式） |
| status      | string    | 状态                  |
| budget      | number    | 预算                  |
| startDate   | Date      | 开始时间              |
| endDate     | Date      | 结束时间              |
| tags        | string[]  | 标签数组              |
| description | string    | 描述                  |

### 前端表单/组件说明

- 支持广告系列的增删改查，表单字段与上方 CreateCampaignInputX 结构一致
- 支持广告目标、状态、特殊广告类别的下拉选择
- 支持投放账户的下拉选择（自动加载账户列表）
- 支持分页、模糊搜索
- 支持批量删除（如有实现）
- Tab 页集成广告组管理（AdSetManagement）

---

## 后端

- **主要接口**：GraphQL 接口与前端一致，详见上表

### 数据库表结构

#### Campaign 表（Facebook 专用）

详见 ad_backend/src/modules/ad-platform/entities/campaign.entity.ts

| 字段名              | 类型         | 说明                     | 约束        |
| ------------------- | ------------ | ------------------------ | ----------- |
| id                  | uuid         | 主键，广告系列唯一标识   | PRIMARY KEY |
| name                | varchar(128) | 广告系列名称             | NOT NULL    |
| objective           | varchar(64)  | 广告目标                 | NOT NULL    |
| status              | varchar(32)  | 状态                     | NOT NULL    |
| specialAdCategories | text[]       | 特殊广告类别             | 默认'{}'    |
| facebookCampaignId  | varchar(64)  | Facebook 平台广告系列 ID | 可空        |
| createdAt           | timestamp    | 创建时间                 | 自动生成    |
| updatedAt           | timestamp    | 更新时间                 | 自动生成    |
| accountId           | varchar(64)  | 投放账户 ID（私有字段）  | NOT NULL    |
| syncStatus          | varchar      | 同步状态                 | 可空        |
| syncError           | varchar      | 同步错误信息             | 可空        |
| adAccount           | 关联关系     | 关联广告账户（外键）     | NOT NULL    |

#### AdCampaign 表（多平台通用）

详见 ad_backend/src/modules/ad-platform/entities/ad-campaign.entity.ts

| 字段名      | 类型         | 说明                   | 约束            |
| ----------- | ------------ | ---------------------- | --------------- |
| id          | uuid         | 主键，广告系列唯一标识 | PRIMARY KEY     |
| platform    | varchar(32)  | 平台类型               | NOT NULL        |
| campaignId  | varchar(64)  | 广告平台 Campaign ID   | NOT NULL, INDEX |
| name        | varchar(128) | 广告系列名称           | NOT NULL        |
| adAccount   | 关联关系     | 所属广告账户（外键）   | NOT NULL        |
| tenant      | 关联关系     | 租户 ID（外键）        | NOT NULL, INDEX |
| raw         | jsonb        | 原始 campaign 数据     | 可空            |
| status      | varchar(32)  | 系列状态               | 默认'active'    |
| budget      | float        | 预算                   | NOT NULL        |
| startDate   | timestamp    | 开始时间               | NOT NULL        |
| endDate     | timestamp    | 结束时间               | NOT NULL        |
| tags        | text[]       | 标签数组               | 可空            |
| description | text         | 描述                   | 可空            |

- **外键说明**
  - Campaign.adAccount 关联 ad_account 表（通过 accountId 字段）
  - AdCampaign.adAccount 关联 ad_account 表
  - AdCampaign.tenant 关联 tenant 表

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query campaigns / mutation createCampaign(input) / ...
    后端->>数据库: 操作 campaign 表
    数据库-->>后端: 返回广告系列数据
    后端-->>前端: 返回广告系列数据
```

---

## 变更历史/注意事项

### Campaign（Facebook 专用）注意事项

- facebookCampaignId 字段用于与 Facebook 平台同步，需保持唯一性
- objective/status/specialAdCategories 字段需严格按枚举值传递
- 删除广告系列时需校验是否被广告组等引用
- 投放账户(accountId)必须为已存在的 ad_account
- 同步状态(syncStatus)和错误(syncError)仅用于平台同步追踪
- 创建时会自动调用 Facebook API，失败时不会本地落库

### AdCampaign（多平台通用）注意事项

- platform 字段支持多平台扩展（facebook/google/tiktok 等）
- campaignId 为平台广告系列 ID，需保持在同一平台内唯一
- budget、startDate、endDate 为必填字段
- tags 和 description 为可选字段，支持灵活扩展
- 支持多租户隔离，所有操作需校验租户权限

### 两种实体的使用场景

- **Campaign**：主要用于 Facebook 广告系列的创建和同步，包含 Facebook 特有的字段
- **AdCampaign**：用于多平台广告系列管理，包含完整的预算、时间等信息
- 前端可根据业务需求选择使用对应的实体和接口
