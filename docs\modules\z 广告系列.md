# 组件路径 /pages/management/facebook/campaign/index	

## 前端

- **实际路径**：slash-admin/src/pages/management/facebook/campaign/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，广告系列管理表单与列表，含增删改查、分页、搜索、弹窗等）
  - AdSetManagement（广告组管理Tab）

- **主要依赖API定义**：slash-admin/src/api/campaign.graphql.ts

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取广告系列列表 | query    | campaigns              | 无           | id, name, objective, status, specialAdCategories, accountId, facebookCampaignId, syncStatus, syncError, createdAt, updatedAt | 支持分页、搜索 |
| 新增广告系列     | mutation | createCampaign         | input: CreateCampaignInputX | id | 新建广告系列，id为UUID |
| 编辑广告系列     | mutation | updateCampaign         | id: string, input: CreateCampaignInputX | id | 编辑指定id的广告系列 |
| 删除广告系列     | mutation | deleteCampaign         | id: string   | id       | 删除指定id的广告系列 |

#### 主要参数结构说明

- **CreateCampaignInputX**（用于新增/编辑）  
  ```ts
  {
    name: string; // 广告系列名称
    objective: string; // 广告目标，枚举值（见下）
    status: string; // 状态，枚举值（ACTIVE/PAUSED等）
    specialAdCategories: string[]; // 特殊广告类别（如HOUSING、EMPLOYMENT等）
    accountId: string; // 投放账户ID
  }
  ```
  - **objective 枚举值**：
    - OUTCOME_LEADS（线索收集）
    - OUTCOME_SALES（销售转化）
    - OUTCOME_ENGAGEMENT（互动）
    - OUTCOME_AWARENESS（品牌认知）
    - OUTCOME_TRAFFIC（流量）
    - OUTCOME_APP_PROMOTION（应用推广）
  - **status 枚举值**：
    - ACTIVE（启用）
    - PAUSED（暂停）
  - **specialAdCategories 枚举值**：
    - HOUSING（住房）
    - EMPLOYMENT（就业）
    - CREDIT（信贷）
    - ISSUES_ELECTIONS_POLITICS（政治/选举/社会议题）

- **返回字段说明**  
  | 字段名              | 类型      | 说明                       |
  |---------------------|-----------|----------------------------|
  | id                  | string    | 主键，UUID                 |
  | name                | string    | 广告系列名称               |
  | objective           | string    | 广告目标                   |
  | status              | string    | 状态                       |
  | specialAdCategories | string[]  | 特殊广告类别               |
  | accountId           | string    | 投放账户ID                 |
  | facebookCampaignId  | string    | Facebook平台广告系列ID     |
  | syncStatus          | string    | 同步状态                   |
  | syncError           | string    | 同步错误信息               |
  | createdAt           | Date      | 创建时间                   |
  | updatedAt           | Date      | 更新时间                   |

### 前端表单/组件说明

- 支持广告系列的增删改查，表单字段与上方CreateCampaignInputX结构一致
- 支持广告目标、状态、特殊广告类别的下拉选择
- 支持投放账户的下拉选择（自动加载账户列表）
- 支持分页、模糊搜索
- 支持批量删除（如有实现）
- Tab页集成广告组管理（AdSetManagement）

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（campaign，详见 ad_backend/src/modules/ad-platform/entities/campaign.entity.ts）：

| 字段名              | 类型         | 说明                       | 约束         |
|---------------------|-------------|----------------------------|--------------|
| id                  | uuid        | 主键，广告系列唯一标识     | PRIMARY KEY  |
| name                | varchar(128)| 广告系列名称               | NOT NULL     |
| objective           | varchar(64) | 广告目标                   | NOT NULL     |
| status              | varchar(32) | 状态                       | NOT NULL     |
| specialAdCategories | text[]      | 特殊广告类别               | 默认空数组   |
| facebookCampaignId  | varchar(64) | Facebook平台广告系列ID     | 可空         |
| createdAt           | timestamp   | 创建时间                   | 自动生成     |
| updatedAt           | timestamp   | 更新时间                   | 自动生成     |
| accountId           | varchar(64) | 投放账户ID                 | NOT NULL     |
| syncStatus          | varchar     | 同步状态                   | 可空         |
| syncError           | varchar     | 同步错误信息               | 可空         |

- **外键说明**  
  - accountId 关联 ad_account 表的 accountId 字段

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query campaigns / mutation createCampaign(input) / ...
    后端->>数据库: 操作 campaign 表
    数据库-->>后端: 返回广告系列数据
    后端-->>前端: 返回广告系列数据
```

---

## 变更历史/注意事项

- facebookCampaignId 字段用于与Facebook平台同步，需保持唯一性
- objective/status/specialAdCategories 字段需严格按枚举值传递
- 删除广告系列时需校验是否被广告组等引用
- 投放账户(accountId)必须为已存在的ad_account
- 同步状态(syncStatus)和错误(syncError)仅用于平台同步追踪	