import { gql, useQuery, useMutation } from '@apollo/client';

export const GET_MATERIAL_LIST = gql`
  query GetMaterials($filter: MaterialFilter, $paging: CursorPaging, $sorting: [MaterialSort!]) {
    materials(filter: $filter, paging: $paging, sorting: $sorting) {
      edges {
        node {
          id
          name
          type
          url
          status
          tenant { id }
        }
      }
    }
  }
`;
