import { gql } from '@apollo/client';

export const GET_CAMPAIGN_OVERVIEW = gql`
  query CampaignOverview($startDate: String!, $endDate: String!) {
    campaignOverview(startDate: $startDate, endDate: $endDate) {
      activeCount
      totalSpend
      totalConversions
      avgRoi
      recentCampaigns {
        name
        status
        budget
        spent
        progress
        impressions
        clicks
        conversions
      }
    }
  }
`; 