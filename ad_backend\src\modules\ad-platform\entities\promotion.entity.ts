import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn } from 'typeorm';

@Entity('promotion')
export class Promotion {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ length: 32 })
    code: string;

    @Column({ length: 32 })
    platform: string;

    @Column({ length: 128, nullable: true })
    landingPageName?: string;

    @Column({ length: 256 })
    landingPageUrl: string;

    @Column({ length: 32 })
    landingPageCode: string;

    @Column({ length: 16, default: 'enabled' })
    status: string;

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    @Column({ length: 64 })
    creator: string;

    @Column({ type: 'jsonb', nullable: true })
    platformParams: any;

    @Column({ length: 64 })
    tenantId: string;

    @Column({ length: 128, nullable: true })
    appDomain?: string;
} 