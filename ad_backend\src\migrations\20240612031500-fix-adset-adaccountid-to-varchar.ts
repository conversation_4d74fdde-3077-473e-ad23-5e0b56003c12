import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAdSetAdAccountIdToVarchar20240612031500 implements MigrationInterface {
    name = 'FixAdSetAdAccountIdToVarchar20240612031500'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set"
            ALTER COLUMN "adAccountId" TYPE varchar(64)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set"
            ALTER COLUMN "adAccountId" TYPE uuid
            USING "adAccountId"::uuid
        `);
    }
} 