import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Card, List, Avatar, Button, Input, Select, Tag, Tabs, Modal, Form, message } from 'antd';
import { UserOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';

const { t } = useTranslation();

// 模拟连接数据
const mockConnections = {
  colleagues: [
    { id: 'c1', name: t('pages.key_1327'), department: t('pages.key_791'), position: t('pages.key_2613'), avatar: 'https://via.placeholder.com/40' },
    { id: 'c2', name: t('pages.key_1612'), department: t('pages.key_279'), position: t('pages.key_2127'), avatar: 'https://via.placeholder.com/40' },
    { id: 'c3', name: t('pages.key_2339'), department: t('pages.key_1093'), position: t('pages.key_1092'), avatar: 'https://via.placeholder.com/40' },
    { id: 'c4', name: t('pages.key_2490'), department: t('pages.key_755'), position: t('pages.key_754'), avatar: 'https://via.placeholder.com/40' },
    { id: 'c5', name: t('pages.key_643'), department: t('pages.key_77'), position: t('pages.key_76'), avatar: 'https://via.placeholder.com/40' },
  ],
  clients: [
    { id: 'cl1', name: t('pages.key_866'), company: 'ABC公司', industry: t('pages.key_1644'), avatar: 'https://via.placeholder.com/40' },
    { id: 'cl2', name: t('pages.key_1328'), company: 'XYZ科技', industry: t('pages.key_1717'), avatar: 'https://via.placeholder.com/40' },
    { id: 'cl3', name: t('pages.key_1613'), company: '123教育', industry: t('pages.key_1084'), avatar: 'https://via.placeholder.com/40' },
  ],
  partners: [
    { id: 'p1', name: t('pages.key_233'), company: t('pages.key_626'), type: t('pages.key_625'), avatar: 'https://via.placeholder.com/40' },
    { id: 'p2', name: t('pages.key_2526'), company: t('pages.key_270'), type: t('pages.key_276'), avatar: 'https://via.placeholder.com/40' },
  ],
};

const ConnectionsTab = () => {
  const [connections, setConnections] = useState(mockConnections);
  const [searchText, setSearchText] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [connectionType, setConnectionType] = useState('colleagues');
  const [form] = Form.useForm();

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const showAddModal = (type: string) => {
    setConnectionType(type);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleAddConnection = (values: any) => {
    const newConnection = {
      id: Date.now().toString(),
      avatar: 'https://via.placeholder.com/40',
      ...values,
    };

    if (connectionType === 'colleagues') {
      setConnections({
        ...connections,
        colleagues: [...connections.colleagues, newConnection],
      });
    } else if (connectionType === 'clients') {
      setConnections({
        ...connections,
        clients: [...connections.clients, newConnection],
      });
    } else if (connectionType === 'partners') {
      setConnections({
        ...connections,
        partners: [...connections.partners, newConnection],
      });
    }

    setIsModalVisible(false);
    message.success(t('pages.key_1895'));
  };

  const filterConnections = (list: any[], searchText: string) => {
    if (!searchText) return list;
    return list.filter(item =>
      item.name.toLowerCase().includes(searchText.toLowerCase()) ||
      (item.department && item.department.toLowerCase().includes(searchText.toLowerCase())) ||
      (item.company && item.company.toLowerCase().includes(searchText.toLowerCase()))
    );
  };

  const renderColleaguesList = () => {
    const filteredList = filterConnections(connections.colleagues, searchText);
    return (
      <List
        itemLayout="horizontal"
        dataSource={filteredList}
        renderItem={item => (
          <List.Item
            actions={[
              <Button type="link">{t('pages.key_403')}</Button>,
              <Button type="link">{t('pages.key_1349')}</Button>,
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar src={item.avatar} icon={<UserOutlined />} />}
              title={item.name}
              description={
                <div>
                  <div>{item.department} - {item.position}</div>
                </div>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  const renderClientsList = () => {
    const filteredList = filterConnections(connections.clients, searchText);
    return (
      <List
        itemLayout="horizontal"
        dataSource={filteredList}
        renderItem={item => (
          <List.Item
            actions={[
              <Button type="link">{t('pages.key_403')}</Button>,
              <Button type="link">{t('pages.key_1349')}</Button>,
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar src={item.avatar} icon={<UserOutlined />} />}
              title={item.name}
              description={
                <div>
                  <div>{item.company}</div>
                  <Tag color="blue">{item.industry}</Tag>
                </div>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  const renderPartnersList = () => {
    const filteredList = filterConnections(connections.partners, searchText);
    return (
      <List
        itemLayout="horizontal"
        dataSource={filteredList}
        renderItem={item => (
          <List.Item
            actions={[
              <Button type="link">{t('pages.key_403')}</Button>,
              <Button type="link">{t('pages.key_1349')}</Button>,
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar src={item.avatar} icon={<UserOutlined />} />}
              title={item.name}
              description={
                <div>
                  <div>{item.company}</div>
                  <Tag color="green">{item.type}</Tag>
                </div>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  // 定义标签页内容，移除不支持的extra属性
  const items: TabsProps['items'] = [
    {
      key: 'colleagues',
      label: `同事 (${connections.colleagues.length})`,
      children: (
        <div>
          <div style={{ marginBottom: 16, textAlign: 'right' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => showAddModal('colleagues')}>{t('pages.key_1530')}</Button>
          </div>
          {renderColleaguesList()}
        </div>
      ),
    },
    {
      key: 'clients',
      label: `客户 (${connections.clients.length})`,
      children: (
        <div>
          <div style={{ marginBottom: 16, textAlign: 'right' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => showAddModal('clients')}>{t('pages.key_1533')}</Button>
          </div>
          {renderClientsList()}
        </div>
      ),
    },
    {
      key: 'partners',
      label: `合作伙伴 (${connections.partners.length})`,
      children: (
        <div>
          <div style={{ marginBottom: 16, textAlign: 'right' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => showAddModal('partners')}>{t('pages.key_1529')}</Button>
          </div>
          {renderPartnersList()}
        </div>
      ),
    },
  ];

  const renderModalContent = () => {
    if (connectionType === 'colleagues') {
      return (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddConnection}
        >
          <Form.Item
            name="name"
            label={t('pages.key_622')}
            rules={[{ required: true, message: t('pages.key_2192') }]}
          >
            <Input placeholder={t('pages.key_2192')} />
          </Form.Item>
          <Form.Item
            name="department"
            label={t('pages.key_2475')}
            rules={[{ required: true, message: t('pages.key_2241') }]}
          >
            <Input placeholder={t('pages.key_2241')} />
          </Form.Item>
          <Form.Item
            name="position"
            label={t('pages.key_1892')}
            rules={[{ required: true, message: t('pages.key_2227') }]}
          >
            <Input placeholder={t('pages.key_2227')} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>{t('pages.key_1523')}</Button>
            <Button onClick={() => setIsModalVisible(false)}>{t('pages.key_407')}</Button>
          </Form.Item>
        </Form>
      );
    } else if (connectionType === 'clients') {
      return (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddConnection}
        >
          <Form.Item
            name="name"
            label={t('pages.key_622')}
            rules={[{ required: true, message: t('pages.key_2192') }]}
          >
            <Input placeholder={t('pages.key_2192')} />
          </Form.Item>
          <Form.Item
            name="company"
            label={t('pages.key_184')}
            rules={[{ required: true, message: t('pages.key_2173') }]}
          >
            <Input placeholder={t('pages.key_2173')} />
          </Form.Item>
          <Form.Item
            name="industry"
            label={t('pages.key_2020')}
            rules={[{ required: true, message: t('pages.key_2279') }]}
          >
            <Select placeholder={t('pages.key_2279')}>
              <Select.Option value={t('pages.key_1644')}>{t('pages.key_1644')}</Select.Option>
              <Select.Option value={t('pages.key_1717')}>{t('pages.key_1717')}</Select.Option>
              <Select.Option value={t('pages.key_1084')}>{t('pages.key_1084')}</Select.Option>
              <Select.Option value={t('pages.key_2487')}>{t('pages.key_2487')}</Select.Option>
              <Select.Option value={t('pages.key_359')}>{t('pages.key_359')}</Select.Option>
              <Select.Option value={t('pages.key_193')}>{t('pages.key_193')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>{t('pages.key_1523')}</Button>
            <Button onClick={() => setIsModalVisible(false)}>{t('pages.key_407')}</Button>
          </Form.Item>
        </Form>
      );
    } else {
      return (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddConnection}
        >
          <Form.Item
            name="name"
            label={t('pages.key_622')}
            rules={[{ required: true, message: t('pages.key_2192') }]}
          >
            <Input placeholder={t('pages.key_2192')} />
          </Form.Item>
          <Form.Item
            name="company"
            label={t('pages.key_184')}
            rules={[{ required: true, message: t('pages.key_2173') }]}
          >
            <Input placeholder={t('pages.key_2173')} />
          </Form.Item>
          <Form.Item
            name="type"
            label={t('pages.key_460')}
            rules={[{ required: true, message: t('pages.key_2252') }]}
          >
            <Select placeholder={t('pages.key_2252')}>
              <Select.Option value={t('pages.key_625')}>{t('pages.key_625')}</Select.Option>
              <Select.Option value={t('pages.key_276')}>{t('pages.key_276')}</Select.Option>
              <Select.Option value={t('pages.key_987')}>{t('pages.key_987')}</Select.Option>
              <Select.Option value={t('pages.key_753')}>{t('pages.key_753')}</Select.Option>
              <Select.Option value={t('pages.key_193')}>{t('pages.key_193')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>{t('pages.key_1523')}</Button>
            <Button onClick={() => setIsModalVisible(false)}>{t('pages.key_407')}</Button>
          </Form.Item>
        </Form>
      );
    }
  };

  return (
    <div className="connections-tab">
      <Card bordered={false}>
        <div className="search-bar" style={{ marginBottom: 16 }}>
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('pages.key_1053')}
            onChange={e => handleSearch(e.target.value)}
            style={{ width: 300 }}
          />
        </div>

        <Tabs
          defaultActiveKey="colleagues"
          items={items ? items.map(item => ({
            ...item,
            label: (
              <span>
                {item.label}
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  style={{ marginLeft: 8 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    showAddModal(item.key as string);
                  }}
                >{t('pages.key_1523')}</Button>
              </span>
            ),
          })) : []}
        />
      </Card>

      <Modal
        title={`添加${connectionType === 'colleagues' ? t('pages.key_462') : connectionType === 'clients' ? t('pages.key_668') : t('pages.key_459')}`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
      >
        {renderModalContent()}
      </Modal>
    </div>
  );
};

export default ConnectionsTab;
