import axios from 'axios';
const cloudflareUrl = "https://pub-7d41dc42509b4dc596d7748dc3a2058e.r2.dev/"
/**
 * 上传文件到 Cloudflare（后端接口）
 * @param file File对象
 * @param userId 用户ID
 * @param source 来源
 * @returns Promise<any> 后端响应
 */
export async function uploadToCloudflare(file: File, userId: string, source: string = 'default'): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('userId', userId);
    formData.append('source', source);

    const response = await axios.post('http://localhost:3005/cloudflare/upload', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    response.data.fileUrl = cloudflareUrl + response.data.fileKey
    return response.data;
} 