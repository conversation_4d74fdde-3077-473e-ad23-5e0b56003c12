import { ObjectType, Field, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import GraphQLJSON from 'graphql-type-json';
import { TenantDTO } from '../../../entity/tenant.dto';
import { AdAccountDTO } from './ad-account.dto';
import { CampaignDTO } from './campaign.dto';
import { AudienceDTO } from './audience.dto';
import { Type } from 'class-transformer';

@ObjectType('AdSet')
export class AdSetDTO {
  @IDField(() => ID)
  id: string;

  @FilterableField()
  platform: string;

  @FilterableField()
  adsetId: string;

  @FilterableField()
  name: string;

  @Type(() => AdAccountDTO)
  @Field(() => AdAccountDTO)
  adAccount: AdAccountDTO;

  @Type(() => CampaignDTO)
  @Field(() => CampaignDTO)
  adCampaign: CampaignDTO;

  @Type(() => TenantDTO)
  @Field(() => TenantDTO)
  tenant: TenantDTO;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  raw?: any;

  @FilterableField()
  status: string;

  @Type(() => AudienceDTO)
  @Field(() => AudienceDTO)
  audience: AudienceDTO;

  @Field(() => Number)
  dailyBudget: number;

  @Field(() => GraphQLISODateTime)
  startTime: Date;

  @Field(() => GraphQLISODateTime, { nullable: true })
  endTime?: Date;

  @Field()
  optimizationGoal: string;

  @Field()
  billingEvent: string;
}
