#!/usr/bin/env node

/**
 * 文档同步 - 代码变更检测脚本
 * 检测 GraphQL schema、Entity、API 等关键代码的变更
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const glob = require('glob');

class ChangeDetector {
  constructor() {
    this.projectRoot = process.cwd();
    this.backendPath = path.join(this.projectRoot, 'ad_backend');
    this.frontendPath = path.join(this.projectRoot, 'slash-admin');
    this.docsPath = path.join(this.projectRoot, 'docs/modules');
    
    this.changes = {
      entities: [],
      graphqlSchemas: [],
      resolvers: [],
      frontendApis: [],
      components: []
    };
  }

  /**
   * 获取 Git 变更的文件列表
   */
  getChangedFiles() {
    try {
      // 获取暂存区的变更文件
      const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' })
        .split('\n')
        .filter(file => file.trim());
      
      // 获取工作区的变更文件
      const workingFiles = execSync('git diff --name-only', { encoding: 'utf8' })
        .split('\n')
        .filter(file => file.trim());
      
      return [...new Set([...stagedFiles, ...workingFiles])];
    } catch (error) {
      console.warn('无法获取 Git 变更文件，使用全量检测模式');
      return [];
    }
  }

  /**
   * 检测 Entity 变更
   */
  detectEntityChanges(changedFiles = []) {
    const entityPattern = path.join(this.backendPath, 'src/modules/**/entities/*.entity.ts');
    const entityFiles = glob.sync(entityPattern);
    
    const relevantChanges = changedFiles.length > 0 
      ? entityFiles.filter(file => changedFiles.some(changed => file.includes(changed)))
      : entityFiles;

    relevantChanges.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const entityInfo = this.parseEntityFile(file, content);
      if (entityInfo) {
        this.changes.entities.push(entityInfo);
      }
    });
  }

  /**
   * 解析 Entity 文件
   */
  parseEntityFile(filePath, content) {
    const fileName = path.basename(filePath, '.entity.ts');
    const moduleName = this.extractModuleName(filePath);
    
    // 提取实体名称
    const entityMatch = content.match(/@Entity\(['"`]?([^'"`\)]+)['"`]?\)/);
    const tableName = entityMatch ? entityMatch[1] : fileName;
    
    // 提取字段信息
    const fields = this.extractEntityFields(content);
    
    // 提取关联关系
    const relations = this.extractEntityRelations(content);
    
    return {
      filePath,
      fileName,
      moduleName,
      tableName,
      fields,
      relations,
      lastModified: fs.statSync(filePath).mtime
    };
  }

  /**
   * 提取实体字段
   */
  extractEntityFields(content) {
    const fields = [];
    const fieldRegex = /@Column\((.*?)\)\s*(\w+):\s*([^;]+);/gs;
    let match;
    
    while ((match = fieldRegex.exec(content)) !== null) {
      const [, columnOptions, fieldName, fieldType] = match;
      
      // 解析列选项
      const options = this.parseColumnOptions(columnOptions);
      
      fields.push({
        name: fieldName,
        type: fieldType.trim(),
        options
      });
    }
    
    // 提取主键字段
    const primaryKeyRegex = /@PrimaryGeneratedColumn\((.*?)\)\s*(\w+):\s*([^;]+);/gs;
    while ((match = primaryKeyRegex.exec(content)) !== null) {
      const [, options, fieldName, fieldType] = match;
      fields.push({
        name: fieldName,
        type: fieldType.trim(),
        options: { primary: true, generated: true, ...this.parseColumnOptions(options) }
      });
    }
    
    return fields;
  }

  /**
   * 解析列选项
   */
  parseColumnOptions(optionsStr) {
    const options = {};
    
    // 解析常见选项
    if (optionsStr.includes('nullable: false') || optionsStr.includes('NOT NULL')) {
      options.nullable = false;
    }
    if (optionsStr.includes('unique: true')) {
      options.unique = true;
    }
    if (optionsStr.includes('default:')) {
      const defaultMatch = optionsStr.match(/default:\s*['"`]?([^,'"`\}]+)['"`]?/);
      if (defaultMatch) {
        options.default = defaultMatch[1];
      }
    }
    
    // 解析长度
    const lengthMatch = optionsStr.match(/length:\s*(\d+)/);
    if (lengthMatch) {
      options.length = parseInt(lengthMatch[1]);
    }
    
    // 解析类型
    const typeMatch = optionsStr.match(/type:\s*['"`]([^'"`]+)['"`]/);
    if (typeMatch) {
      options.type = typeMatch[1];
    }
    
    return options;
  }

  /**
   * 提取实体关联关系
   */
  extractEntityRelations(content) {
    const relations = [];
    const relationRegex = /@(ManyToOne|OneToMany|OneToOne|ManyToMany)\((.*?)\)\s*(\w+):/gs;
    let match;
    
    while ((match = relationRegex.exec(content)) !== null) {
      const [, relationType, options, fieldName] = match;
      relations.push({
        type: relationType,
        field: fieldName,
        options: options.trim()
      });
    }
    
    return relations;
  }

  /**
   * 检测 GraphQL Schema 变更
   */
  detectGraphQLChanges(changedFiles = []) {
    const graphqlPattern = path.join(this.frontendPath, 'src/api/**/*.graphql.ts');
    const graphqlFiles = glob.sync(graphqlPattern);
    
    const relevantChanges = changedFiles.length > 0 
      ? graphqlFiles.filter(file => changedFiles.some(changed => file.includes(changed)))
      : graphqlFiles;

    relevantChanges.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const schemaInfo = this.parseGraphQLFile(file, content);
      if (schemaInfo) {
        this.changes.graphqlSchemas.push(schemaInfo);
      }
    });
  }

  /**
   * 解析 GraphQL 文件
   */
  parseGraphQLFile(filePath, content) {
    const fileName = path.basename(filePath, '.graphql.ts');
    
    // 提取查询
    const queries = this.extractGraphQLOperations(content, 'query');
    
    // 提取变更
    const mutations = this.extractGraphQLOperations(content, 'mutation');
    
    // 提取订阅
    const subscriptions = this.extractGraphQLOperations(content, 'subscription');
    
    return {
      filePath,
      fileName,
      queries,
      mutations,
      subscriptions,
      lastModified: fs.statSync(filePath).mtime
    };
  }

  /**
   * 提取 GraphQL 操作
   */
  extractGraphQLOperations(content, operationType) {
    const operations = [];
    const operationRegex = new RegExp(`export const (\\w+) = gql\`\\s*(${operationType})\\s+(\\w+)\\s*\\((.*?)\\)\\s*\\{([^}]+)\\}`, 'gis');
    let match;
    
    while ((match = operationRegex.exec(content)) !== null) {
      const [, constName, , operationName, params, fields] = match;
      
      operations.push({
        constName,
        operationName,
        params: params.trim(),
        fields: fields.trim(),
        type: operationType
      });
    }
    
    return operations;
  }

  /**
   * 检测 Resolver 变更
   */
  detectResolverChanges(changedFiles = []) {
    const resolverPattern = path.join(this.backendPath, 'src/modules/**/resolvers/*.resolver.ts');
    const resolverFiles = glob.sync(resolverPattern);
    
    const relevantChanges = changedFiles.length > 0 
      ? resolverFiles.filter(file => changedFiles.some(changed => file.includes(changed)))
      : resolverFiles;

    relevantChanges.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const resolverInfo = this.parseResolverFile(file, content);
      if (resolverInfo) {
        this.changes.resolvers.push(resolverInfo);
      }
    });
  }

  /**
   * 解析 Resolver 文件
   */
  parseResolverFile(filePath, content) {
    const fileName = path.basename(filePath, '.resolver.ts');
    const moduleName = this.extractModuleName(filePath);
    
    // 提取查询方法
    const queries = this.extractResolverMethods(content, '@Query');
    
    // 提取变更方法
    const mutations = this.extractResolverMethods(content, '@Mutation');
    
    return {
      filePath,
      fileName,
      moduleName,
      queries,
      mutations,
      lastModified: fs.statSync(filePath).mtime
    };
  }

  /**
   * 提取 Resolver 方法
   */
  extractResolverMethods(content, decorator) {
    const methods = [];
    const methodRegex = new RegExp(`${decorator}\\((.*?)\\)\\s*async\\s+(\\w+)\\s*\\((.*?)\\)\\s*:\\s*([^{]+)\\s*\\{`, 'gs');
    let match;
    
    while ((match = methodRegex.exec(content)) !== null) {
      const [, decoratorOptions, methodName, params, returnType] = match;
      
      methods.push({
        name: methodName,
        params: params.trim(),
        returnType: returnType.trim(),
        options: decoratorOptions.trim()
      });
    }
    
    return methods;
  }

  /**
   * 提取模块名称
   */
  extractModuleName(filePath) {
    const parts = filePath.split(path.sep);
    const modulesIndex = parts.findIndex(part => part === 'modules');
    return modulesIndex !== -1 && parts[modulesIndex + 1] ? parts[modulesIndex + 1] : 'unknown';
  }

  /**
   * 运行检测
   */
  async detect() {
    console.log('🔍 开始检测代码变更...');
    
    const changedFiles = this.getChangedFiles();
    console.log(`📁 检测到 ${changedFiles.length} 个变更文件`);
    
    // 检测各类变更
    this.detectEntityChanges(changedFiles);
    this.detectGraphQLChanges(changedFiles);
    this.detectResolverChanges(changedFiles);
    
    console.log('✅ 变更检测完成');
    console.log(`📊 检测结果:`);
    console.log(`  - Entity 变更: ${this.changes.entities.length}`);
    console.log(`  - GraphQL 变更: ${this.changes.graphqlSchemas.length}`);
    console.log(`  - Resolver 变更: ${this.changes.resolvers.length}`);
    
    return this.changes;
  }

  /**
   * 保存检测结果
   */
  saveResults(outputPath = 'scripts/doc-sync/changes.json') {
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, JSON.stringify(this.changes, null, 2));
    console.log(`💾 检测结果已保存到: ${outputPath}`);
  }
}

// 主函数
async function main() {
  const detector = new ChangeDetector();
  const changes = await detector.detect();
  detector.saveResults();
  
  // 如果有变更，退出码为 1，触发后续流程
  const hasChanges = Object.values(changes).some(arr => arr.length > 0);
  process.exit(hasChanges ? 1 : 0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = ChangeDetector;
