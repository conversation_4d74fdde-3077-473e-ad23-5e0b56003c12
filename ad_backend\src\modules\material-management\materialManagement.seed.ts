import { Connection } from 'typeorm';
import { materialManagement } from './materialManagement.entity';

export const creatematerialManagement = async (
	connection: Connection
): Promise<materialManagement[]> => {
	const employees = new Array<materialManagement>();

	const employee1 = new materialManagement();
	employees.push(employee1);

	const employee2 = new materialManagement();
	employees.push(employee2);

	return await insertmaterialManagement(connection, employees);
};

const insertmaterialManagement = async (
	connection: Connection,
	employees: materialManagement[]
) => {
	const repo = connection.getRepository(materialManagement);

	// await Promise.all(
	// 	employees.map(async (emp) => {
	// 		const existed = await repo.findOne({
	// 			where: {
	// 				url: emp.url,
	// 			},
	// 		});

	// 		if (existed) {
	// 			emp.id = existed.id;
	// 		}
	// 	})
	// );

	return await repo.save(employees);
};
