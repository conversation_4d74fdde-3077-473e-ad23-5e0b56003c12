import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, And } from 'typeorm';
import { AdAccount } from '../entities/ad-account.entity';

@Injectable()
export class AdAccountService {
  constructor(
    @InjectRepository(AdAccount)
    private readonly adAccountRepo: Repository<AdAccount>,
  ) { }

  async findByFbId(fbId: string) {
    return this.adAccountRepo.findOne({
      where: { accountId: fbId },
      select: ['id', 'accessToken', 'accountId']
    });
  }

  async findAllWithAccessToken(tenantId: string) {
    if (!tenantId) {
        throw new BadRequestException('tenantId is required');
    }
    return this.adAccountRepo.find({ 
        where: { 
            tenant: { id: tenantId },
            accessToken: Not('')
        }
    });
  }
}