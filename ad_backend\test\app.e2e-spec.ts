import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from './../src/app.module';

describe('AppController (e2e)', () => {
	let app: INestApplication;

	beforeAll(async () => {
		const moduleFixture = await Test.createTestingModule({
			imports: [AppModule],
		}).compile();

		app = moduleFixture.createNestApplication();
		await app.init();
	});

	afterAll(async () => {
		await app.close();
	});

	it('/ (GET)', () => {
		return request(app.getHttpServer())
			.get('/')
			.expect(404);
	});

	// 权限自动化测试
	it('超级管理员只能看到系统设置菜单', async () => {
		// 假设有超级管理员账号：admin/Admin@123
		const loginRes = await request(app.getHttpServer())
			.post('/graphql')
			.send({
				query: `mutation { login(input: { username: "admin", password: "Admin@123" }) { accessToken } }`
			});
		const token = loginRes.body.data.login.accessToken;
		const meRes = await request(app.getHttpServer())
			.post('/graphql')
			.set('Authorization', `Bearer ${token}`)
			.send({ query: `query { me { user { roles { routeList { path children { path } } } } } }` });
		const routes = meRes.body.data.me.user.roles[0].routeList;
		expect(routes.length).toBe(1);
		expect(routes[0].path).toBe('/system');
		// 检查子菜单
		expect(Array.isArray(routes[0].children)).toBe(true);
	});

	it('非超级管理员不能创建超级管理员账号', async () => {
		// 先用超级管理员创建一个普通账号
		const loginRes = await request(app.getHttpServer())
			.post('/graphql')
			.send({
				query: `mutation { login(input: { username: "admin", password: "Admin@123" }) { accessToken } }`
			});
		const token = loginRes.body.data.login.accessToken;
		// 创建普通账号
		const createUserRes = await request(app.getHttpServer())
			.post('/graphql')
			.set('Authorization', `Bearer ${token}`)
			.send({
				query: `mutation { createUser(input: { username: "testuser", email: "<EMAIL>", password: "Test@123" }) { id username } }`
			});
		expect(createUserRes.body.data.createUser.username).toBe('testuser');
		// 用普通账号登录
		const loginTestRes = await request(app.getHttpServer())
			.post('/graphql')
			.send({
				query: `mutation { login(input: { username: "testuser", password: "Test@123" }) { accessToken } }`
			});
		const testToken = loginTestRes.body.data.login.accessToken;
		// 普通账号尝试创建超级管理员账号
		const getSuperAdminRoleRes = await request(app.getHttpServer())
			.post('/graphql')
			.set('Authorization', `Bearer ${token}`)
			.send({ query: `query { roles(input: { page: 1, limit: 10 }) { items { id name } } }` });
		const superAdminRole = getSuperAdminRoleRes.body.data.roles.items.find((r: any) => r.name === '超级管理员');
		const res = await request(app.getHttpServer())
			.post('/graphql')
			.set('Authorization', `Bearer ${testToken}`)
			.send({
				query: `mutation { createUser(input: { username: "hacker", email: "<EMAIL>", password: "Hacker@123", roleIds: ["${superAdminRole.id}"] }) { id username } }`
			});
		expect(res.body.errors).toBeDefined();
		expect(res.body.errors[0].message).toContain('只有超级管理员可以创建超级管理员账号');
	});
});
