const { Client } = require('pg');

async function main() {
    const client = new Client({
        host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
        port: 5432,
        user: 'yeeu',
        password: 'Yy&********',
        database: 'ad_auto',
    });
    await client.connect();
    // 查询将要删除的数据，字段加双引号
    const res = await client.query(`SELECT id, "accountId" FROM ad_account WHERE "accountId" LIKE 'act_%'`);
    console.log(`将要删除 ${res.rows.length} 条历史数据：`);
    res.rows.forEach(row => console.log(row.accountId));
    if (res.rows.length > 0) {
        await client.query(`DELETE FROM ad_account WHERE "accountId" LIKE 'act_%'`);
        console.log('已删除历史数据。');
    } else {
        console.log('没有需要删除的数据。');
    }
    await client.end();
}

main().catch(err => {
    console.error('清理失败:', err);
    process.exit(1);
}); 