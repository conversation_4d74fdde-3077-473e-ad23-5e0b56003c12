const { Client } = require('pg');

async function main() {
    const client = new Client({
        host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
        port: 5432,
        user: 'yeeu',
        password: 'Yy&********',
        database: 'ad_auto',
    });
    await client.connect();
    const res = await client.query('SELECT * FROM ad_account');
    console.log('ad_account 表所有用户字段和值:');
    res.rows.forEach(row => {
        console.log(row);
    });
    await client.end();
}

main().catch(err => {
    console.error('查询失败:', err);
    process.exit(1);
});