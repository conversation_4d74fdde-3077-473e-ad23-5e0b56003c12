import { MigrationInterface, QueryRunner } from "typeorm";

export class DropAudienceTable20250525112652 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS "audience"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 这里无法自动恢复表结构，如需恢复请手动补充
        // await queryRunner.query(`CREATE TABLE "audience" (...原表结构...)`);
    }
} 