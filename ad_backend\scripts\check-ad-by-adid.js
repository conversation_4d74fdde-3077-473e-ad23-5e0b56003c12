// Node.js 脚本：根据 adId 查询广告是否存在
const { Client } = require('pg');

// 请根据实际情况修改数据库连接配置
const client = new Client({
    host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
    port: 5432,
    user: 'yeeu',
    password: 'Yy&20240505',
    database: 'ad_auto',
});

const adId = process.argv[2];
if (!adId) {
    console.error('用法: node check-ad-by-adid.js <adId>');
    process.exit(1);
}

async function main() {
    await client.connect();
    const res = await client.query('SELECT id, "adId", name FROM ad WHERE "adId" = $1', [adId]);
    if (res.rows.length === 0) {
        console.log(`未找到 ad_id = ${adId} 的广告`);
    } else {
        console.log('找到的广告:');
        console.table(res.rows);
    }
    await client.end();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 