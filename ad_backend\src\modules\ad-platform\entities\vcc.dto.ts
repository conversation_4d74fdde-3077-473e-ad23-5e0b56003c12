import { ObjectType, Field, ID } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import GraphQLJSON from 'graphql-type-json';
import { TenantDTO } from '../../../entity/tenant.dto';

@ObjectType('Vcc')
export class VccDTO {
  @IDField(() => ID)
  id: string;

  @FilterableField()
  cardNumber: string;

  @FilterableField()
  cardHolder: string;

  @FilterableField()
  expiry: string;

  @FilterableField()
  cvv: string;

  @FilterableField({ nullable: true })
  bindAccount?: string;

  @FilterableField()
  status: string;

  @Field(() => TenantDTO)
  tenant: TenantDTO;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  raw?: any;
}
