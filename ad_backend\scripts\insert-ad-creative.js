const { Client } = require('pg');
const { v4: uuidv4 } = require('uuid');

// 请根据实际情况修改数据库连接配置
const client = new Client({
  host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
  port: 5432,
  user: 'yeeu',
  password: 'Yy&********',
  database: 'ad_auto',
});

async function main() {
  await client.connect();

  // 请替换为你实际存在的外键ID
  const adAccountId = '2c3991d6-d6fb-4e00-9a2f-979e4c53b020'
  const adCampaignId = '726e6166-395a-4479-9156-2f29e57ad421'
  const tenantId = '3f04876f-6742-49ed-aa6c-05fa9b3fa426'

  const id = uuidv4();
  const creativeId = 'test_creative_' + Math.floor(Math.random() * 10000);
  const name = '测试创意_' + Math.floor(Math.random() * 10000);
  const status = 'active';

  const sql = `
    INSERT INTO ad_creative (
      id, platform, "creativeId", name, "adAccountId", "adCampaignId", "tenantId", raw, status
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9
    )
  `;

  await client.query(sql, [
    id,
    'facebook',
    creativeId,
    name,
    adAccountId,
    adCampaignId,
    tenantId,
    JSON.stringify({ test: true }),
    status,
  ]);

  console.log('已插入测试创意:', { id, creativeId, name, status });
  await client.end();
}

main().catch(e => {
  console.error(e);
  process.exit(1);
}); 