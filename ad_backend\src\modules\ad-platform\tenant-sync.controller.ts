import { Controller, Get, Put, Body, Param, Query, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tenant } from '../../entity/tenant.entity';

@Controller('ad-platform/tenant/sync')
export class TenantSyncController {
  constructor(
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
  ) {}

  // 查询指定租户的同步配置
  @Get(':tenantId')
  async getSyncConfig(@Param('tenantId') tenantId: string) {
    const tenant = await this.tenantRepository.findOne({ where: { id: tenantId } });
    if (!tenant) throw new NotFoundException('Tenant not found');
    return { sync_config: tenant.sync_config || {} };
  }

  // 更新指定租户的同步配置
  @Put(':tenantId')
  async updateSyncConfig(@Param('tenantId') tenantId: string, @Body() body: any) {
    const tenant = await this.tenantRepository.findOne({ where: { id: tenantId } });
    if (!tenant) throw new NotFoundException('Tenant not found');
    tenant.sync_config = body.sync_config;
    await this.tenantRepository.save(tenant);
    return { success: true };
  }

  // 查询所有租户的同步配置（可分页）
  @Get()
  async listSyncConfigs(@Query('page') page = 1, @Query('pageSize') pageSize = 20) {
    const [tenants, total] = await this.tenantRepository.findAndCount({
      skip: (page - 1) * pageSize,
      take: pageSize,
      select: ['id', 'name', 'type', 'sync_config'],
      order: { name: 'ASC' },
    });
    return { total, tenants };
  }
}
