# VCC卡片交易历史管理系统

## 📋 项目概述

本模块为VCC（虚拟信用卡）卡片管理系统提供完整的交易历史记录功能，支持与Facebook广告消费记录的自动同步。通过后端代理的方式调用Facebook Marketing API，获取绑定广告账户的消费数据并转换为交易记录。

## 🎯 核心功能

### **1. 交易记录管理**
- ✅ 交易记录的增删改查（CRUD）
- ✅ 分页查询，支持多条件过滤
- ✅ 交易统计和数据分析
- ✅ CSV/JSON格式数据导出

### **2. Facebook广告消费同步**
- 🚀 **Facebook API代理**：后端代理调用Facebook Marketing API
- 🔄 **自动数据同步**：将广告消费转换为交易记录  
- 📊 **批量账户支持**：同时处理多个绑定的Facebook广告账户
- 🛡️ **重复检测**：避免重复同步相同的消费记录

### **3. 前端兼容性**
- 🎨 **接口对齐**：与前端TransactionRecord接口完全对应
- 🔍 **搜索过滤**：支持卡号、商户、状态、类型、时间范围等搜索
- 📄 **分页排序**：完整的分页和排序功能
- 📊 **实时统计**：交易总数、金额、成功率等统计

## 🏗️ 系统架构

### **数据层**
```
VccTransaction (实体)
├── 基础字段：id, cardId, cardNo, amount, merchant, transactionTime
├── 状态字段：status, type  
├── Facebook字段：facebookAccountId, facebookTransactionId, campaignName
└── 扩展字段：description, tenantId, createdAt, updatedAt
```

### **服务层**
```
VccTransactionService (核心服务)
├── 基础CRUD操作
├── 高级查询和过滤
├── 数据统计和分析
└── 数据导出功能

FacebookTransactionProxyService (Facebook代理)
├── Facebook API调用
├── 数据格式转换
├── 批量账户处理
└── 访问令牌验证
```

### **接口层**
```
GraphQL Resolver
├── 查询：vccTransactions, vccTransaction, vccTransactionStats
├── 变更：createVccTransaction, updateVccTransaction, deleteVccTransaction
└── 同步：syncVccTransactionsFromFacebook

REST Controller
├── GET    /api/vcc/transactions (查询列表)
├── POST   /api/vcc/transactions (创建记录)
├── PUT    /api/vcc/transactions/:id (更新记录)
├── DELETE /api/vcc/transactions/:id (删除记录)
└── POST   /api/vcc/transactions/sync/facebook (Facebook同步)
```

## 🔧 接口规范

### **查询交易记录列表**

**GraphQL Query：**
```graphql
query GetVccTransactions(
  $filter: VccTransactionFilterInputDTO
  $pagination: VccTransactionPaginationInputDTO
) {
  vccTransactions(filter: $filter, pagination: $pagination) {
    data {
      id
      cardId
      cardNo
      amount
      merchant
      transactionTime
      status
      type
      facebookAccountId
      campaignName
      description
    }
    total
    page
    limit
    totalPages
  }
}
```

**过滤条件：**
```typescript
interface VccTransactionFilter {
  cardId?: string;           // VCC卡片ID
  cardNo?: string;           // 卡号模糊搜索
  merchant?: string;         // 商户模糊搜索
  status?: 'success' | 'pending' | 'failed';  // 交易状态
  type?: 'payment' | 'deposit' | 'refund';   // 交易类型
  startTime?: string;        // 开始时间
  endTime?: string;          // 结束时间
  minAmount?: number;        // 最小金额
  maxAmount?: number;        // 最大金额
}
```

### **Facebook同步操作**

**GraphQL Mutation：**
```graphql
mutation SyncVccTransactionsFromFacebook($input: SyncFacebookTransactionsInputDTO!) {
  syncVccTransactionsFromFacebook(input: $input) {
    id
    cardId
    cardNo
    amount
    merchant
    transactionTime
    status
    type
    facebookAccountId
    campaignName
  }
}
```

**输入参数：**
```typescript
interface SyncFacebookTransactionsInput {
  cardId: string;           // VCC卡片ID
  startDate?: string;       // 同步开始日期（可选）
  endDate?: string;         // 同步结束日期（可选）
  forceOverwrite?: boolean; // 是否强制覆盖已有记录（默认false）
}
```

## 🚀 Facebook API集成

### **同步流程**
1. **验证VCC卡片**：检查卡片是否存在且属于当前租户
2. **获取绑定账户**：查询卡片绑定的Facebook广告账户列表
3. **Token验证**：验证广告账户的OAuth访问令牌有效性
4. **API调用**：并行调用Facebook Marketing API获取消费数据
5. **数据转换**：将Facebook Insights数据转换为交易记录格式
6. **去重保存**：检查重复记录，可选择覆盖或跳过

### **Facebook API调用详情**
```typescript
// 获取账户信息
GET /v18.0/act_{account_id}?fields=id,name,account_status,currency

// 获取消费洞察
GET /v18.0/act_{account_id}/insights?fields=account_id,account_name,spend,clicks,impressions,campaign_name&time_range={"since":"2024-01-01","until":"2024-01-31"}&time_increment=1&level=campaign
```

### **数据映射关系**
```
Facebook Insights → VCC Transaction
├── account_id → facebookAccountId
├── spend → amount  
├── account_name → merchant (固定为"Facebook Ads")
├── date_start → transactionTime
├── campaign_name → campaignName
├── status → TransactionStatus.SUCCESS
└── type → TransactionType.PAYMENT
```

## 🔒 安全考虑

### **租户隔离**
- 所有操作都基于当前用户的`tenantId`进行隔离
- 确保用户只能访问自己租户的数据

### **权限控制**
- 使用JWT认证保护所有API端点
- GraphQL解析器和REST控制器都添加了`@UseGuards(JwtAuthGuard)`

### **数据脱敏**
- 卡号在存储和显示时都经过脱敏处理
- 保留前4位和后4位，中间用`*`替换

### **Facebook Token安全**
- OAuth访问令牌存储在数据库中加密字段
- API调用前验证令牌有效性
- 失败的API调用不会暴露敏感信息

## 📊 数据统计

系统提供丰富的交易统计数据：

```typescript
interface VccTransactionStats {
  totalCount: number;      // 总交易数
  totalAmount: number;     // 总交易金额  
  averageAmount: number;   // 平均交易金额
  successCount: number;    // 成功交易数
  pendingCount: number;    // 待处理交易数
  failedCount: number;     // 失败交易数
  successRate: number;     // 成功率（%）
}
```

## 🛠️ 部署说明

### **环境依赖**
```json
{
  "@nestjs/axios": "^3.0.0",
  "@nestjs/graphql": "^12.0.0", 
  "@nestjs/typeorm": "^10.0.0",
  "typeorm": "^0.3.0"
}
```

### **数据库迁移**
系统会自动创建`vcc_transactions`表，包含以下索引：
- `IDX_cardId_transactionTime`：按卡片ID和交易时间
- `IDX_tenantId_transactionTime`：按租户ID和交易时间  
- `IDX_status_type`：按状态和类型
- `IDX_merchant`：按商户名称

### **配置项**
```typescript
// Facebook API配置
FACEBOOK_API_BASE_URL=https://graph.facebook.com/v18.0
FACEBOOK_API_TIMEOUT=30000

// 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ad_platform
DB_USERNAME=postgres
DB_PASSWORD=password
```

## 🧪 测试建议

### **单元测试覆盖**
- ✅ VccTransactionService的所有方法
- ✅ FacebookTransactionProxyService的API调用
- ✅ 数据转换和格式化函数
- ✅ 过滤查询构建器逻辑

### **集成测试重点**
- 🔄 Facebook API的真实调用和数据同步
- 📊 分页查询在大数据量下的性能
- 🔒 租户隔离和权限控制
- 💾 数据库事务的正确性

## 🐛 故障排查

### **常见问题**

**1. Facebook API调用失败**
```bash
# 检查访问令牌
curl -X GET "https://graph.facebook.com/v18.0/me?access_token=YOUR_TOKEN"

# 检查账户权限
curl -X GET "https://graph.facebook.com/v18.0/act_ACCOUNT_ID?access_token=YOUR_TOKEN"
```

**2. 数据同步不完整**
- 检查时间范围设置是否正确
- 验证Facebook账户是否有消费数据
- 查看后端日志中的错误信息

**3. 分页查询性能问题**
- 确保数据库索引已正确创建
- 考虑增加查询缓存
- 限制单次查询的数据量

## 📈 性能优化

### **数据库优化**
- 使用复合索引提升查询性能
- 定期清理过期的交易记录
- 考虑读写分离和分库分表

### **API优化**  
- Facebook API调用增加重试机制
- 实现批量操作减少网络开销
- 添加缓存层减少重复查询

### **前端优化**
- 虚拟滚动处理大量交易记录
- 搜索防抖减少API调用频率
- 分页加载提升用户体验

---

## 📞 技术支持

如有问题或需要技术支持，请联系开发团队或查看相关文档。

**最后更新时间：** 2024年12月 