import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

@Injectable()
export class TenantGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        let req, ctx;
        if (context.getType() === 'http') {
            req = context.switchToHttp().getRequest();
            ctx = req;
        } else {
            ctx = GqlExecutionContext.create(context).getContext();
            req = ctx.req;
        }
        const user = req?.user || ctx.user;
        const tenantId = user?.tenantId;
        const roles: string[] = user?.roles || user?.roleIds || (user?.role ? [user.role] : []);
        // 支持角色ID或角色名称
        const SUPER_ADMIN_ROLE_ID = '0a48e13b-2cd7-46de-b926-9f84ebeb88c8';
        const SUPER_ADMIN_ROLE_NAME = '超级管理员';
        const isSuperAdmin = Array.isArray(roles) && (roles.includes(SUPER_ADMIN_ROLE_ID) || roles.includes(SUPER_ADMIN_ROLE_NAME));
        if (isSuperAdmin) {
            ctx.tenantId = undefined;
            ctx.isSuperAdmin = true;
            req.tenantId = undefined;
            req.isSuperAdmin = true;
            return true;
        }
        if (!tenantId) {
            throw new Error('No tenantId in user context');
        }
        ctx.tenantId = tenantId;
        ctx.isSuperAdmin = false;
        req.tenantId = tenantId;
        req.isSuperAdmin = false;
        return true;
    }
} 