import { Controller, Get, Query, BadRequestException, UseGuards, Req } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Report } from './report.entity';
import { Tenant } from '../../../entity/tenant.entity';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Controller('ad-platform/report')
export class ReportController {
  constructor(
    @InjectRepository(Report) private readonly reportRepo: Repository<Report>,
    @InjectRepository(Tenant) private readonly tenantRepo: Repository<Tenant>,
  ) { }

  // 租户层广告投放效果报表
  @Get('tenant')
  async getTenantReport(
    @Query('type') type: string,
    @Query('date') date: string,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20,
    @Req() req: any,
  ) {
    const isSuperAdmin = req.isSuperAdmin;
    const tenantId = req.tenantId;
    const where: any = isSuperAdmin ? {} : { tenant: { id: tenantId } };
    if (type) where.type = type;
    if (date) where.date = date;
    const [data, total] = await this.reportRepo.findAndCount({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { date: 'DESC' },
    });
    return { data, total, page, pageSize };
  }

  // SaaS层全局用户与资金报表
  @Get('saas')
  async getSaasReport(
    @Query('type') type: string,
    @Query('date') date: string,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20,
    @Req() req: any,
  ) {
    const isSuperAdmin = req.isSuperAdmin;
    if (!isSuperAdmin) throw new BadRequestException('仅超级管理员可访问SaaS全局报表');
    const where: any = {};
    if (type) where.type = type;
    if (date) where.date = date;
    const [data, total] = await this.reportRepo.findAndCount({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { date: 'DESC' },
    });
    return { data, total, page, pageSize };
  }

  // 导出报表（CSV/Excel等，返回数据，前端自行格式化）
  @Get('export')
  async exportReport(
    @Query('type') type: string,
    @Query('date') date: string,
    @Query('limit') limit = 1000,
    @Req() req: any,
  ) {
    const isSuperAdmin = req.isSuperAdmin;
    const tenantId = req.tenantId;
    const where: any = isSuperAdmin ? {} : { tenant: { id: tenantId } };
    if (type) where.type = type;
    if (date) where.date = date;
    const data = await this.reportRepo.find({
      where,
      take: limit,
      order: { date: 'DESC' },
    });
    return { data, limit };
  }
}
