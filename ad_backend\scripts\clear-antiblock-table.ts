import { AppDataSource } from '../src/config/data-source';
import { AntiBlock } from '../src/modules/anti-block/antiBlock.entity';

async function clearAntiBlockTable() {
    await AppDataSource.initialize();
    const repo = AppDataSource.getRepository(AntiBlock);
    await repo.clear();
    console.log('AntiBlock table cleared.');
    await AppDataSource.destroy();
}

clearAntiBlockTable().catch(err => {
    console.error('Error clearing AntiBlock table:', err);
    process.exit(1);
}); 