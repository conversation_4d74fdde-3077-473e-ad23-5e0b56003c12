import { QueryService, InjectQueryService } from '@ptc-org/nestjs-query-core';
import { CRUDResolver, InjectPubSub, PagingStrategies } from '@ptc-org/nestjs-query-graphql';
import { Resolver } from '@nestjs/graphql';
import { PubSub } from 'graphql-subscriptions';
import { materiaCreateDTO } from '../dto/materiaCreate.dto';
import { materiaCreate } from '../materiaCreate.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from 'src/guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => materiaCreateDTO)
export class materiaCreateResolver extends CRUDResolver(materiaCreateDTO, {
	delete: { disabled: true },
	enableAggregate: true,
	enableTotalCount: true,
	pagingStrategy: PagingStrategies.OFFSET
}) {
	constructor(
		@InjectQueryService(materiaCreate) readonly service: QueryService<materiaCreate>,
		@InjectPubSub() readonly pubSub: PubSub
	) {
		super(service);
	}
}
