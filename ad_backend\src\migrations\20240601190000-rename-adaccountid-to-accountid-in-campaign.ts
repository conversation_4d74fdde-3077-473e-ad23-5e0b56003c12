import { MigrationInterface, QueryRunner } from "typeorm";

export class RenameAdAccountIdToAccountIdInCampaign20240601190000 implements MigrationInterface {
    name = 'RenameAdAccountIdToAccountIdInCampaign20240601190000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 删除原有外键
        await queryRunner.query(`
            ALTER TABLE "campaign" DROP CONSTRAINT IF EXISTS "FK_campaign_adAccount";
        `);
        // 2. 字段重命名
        await queryRunner.query(`
            ALTER TABLE "campaign" RENAME COLUMN "adAccountId" TO "accountId";
        `);
        // 3. 修改类型为 varchar(64)
        await queryRunner.query(`
            ALTER TABLE "campaign" ALTER COLUMN "accountId" TYPE character varying(64);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回滚：字段名改回 adAccountId，类型改回 uuid
        await queryRunner.query(`
            ALTER TABLE "campaign" RENAME COLUMN "accountId" TO "adAccountId";
        `);
        await queryRunner.query(`
            ALTER TABLE "campaign" ALTER COLUMN "adAccountId" TYPE uuid USING "adAccountId"::uuid;
        `);
        // 恢复原外键
        await queryRunner.query(`
            ALTER TABLE "campaign"
            ADD CONSTRAINT "FK_campaign_adAccount"
            FOREIGN KEY ("adAccountId") REFERENCES "ad_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
        `);
    }
} 