import { Connection } from 'typeorm';
import { materiaCreate } from './materiaCreate.entity';

export const createmateriaCreate = async (
	connection: Connection
): Promise<materiaCreate[]> => {
	const employees = new Array<materiaCreate>();

	const employee1 = new materiaCreate();
	employees.push(employee1);

	const employee2 = new materiaCreate();
	employees.push(employee2);

	return await insertmateriaCreate(connection, employees);
};

const insertmateriaCreate = async (
	connection: Connection,
	employees: materiaCreate[]
) => {
	const repo = connection.getRepository(materiaCreate);

	// await Promise.all(
	// 	employees.map(async (emp) => {
	// 		const existed = await repo.findOne({
	// 			where: {
	// 				url: emp.url,
	// 			},
	// 		});

	// 		if (existed) {
	// 			emp.id = existed.id;
	// 		}
	// 	})
	// );

	return await repo.save(employees);
};
