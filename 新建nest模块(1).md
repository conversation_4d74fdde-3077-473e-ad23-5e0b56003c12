## 1.文件的介绍

| 目录/文件                  | 说明                   |

|---------------------------|------------------------|

| src/modules/anti-block/ dto/antiBlock.dto.ts  | 定义 GraphQL 查询字段、验证规则、类型定义(与 entity 文件保持一致的数据类型包括是否必填) |

| src/modules/anti-block/ antiBlock.controller.ts     | 提供基于 HTTP 的 CRUD 接口 |

| src/modules/anti-block/ antiBlock.entity.ts            | 映射数据库表结构，定义数据模型(与 dto 文件保持一致的数据类型包括是否必填)  |

| src/modules/anti-block/antiBlock.module.ts         | 组织模块依赖，连接服务、控制器、解析器(将当前模块其他文件导入并导出 module) |

| src/modules/anti-block/ antiBlock.seed.ts              | 用于创建并插入一些实体数据   |

| src/modules/anti-block/ antiBlock.service.ts          | api 接口所需由于我们是使用 graphql 因此可以忽略 |

## 2.生成步骤

### 1.使用大模型生成 dto 文件

主要是前端所有需要字段需要生成并且加上例如类型校验的装饰器和是否可查询字段的装饰器

### 2.使用大模型生成 entity 文件

### 3.使用大模型生成 resolver 文件

这里需要继承**CRUDResolver**如此就可以自动生成对应的 curd 类

### 4.使用大模型生成 controller 文件

只用来导入到 module，使用 graphql 不会用到

### 5.使用大模型生成 service 文件

只用来导入到 module，使用 graphql 不会用到

### 6.使用大模型生成 seed 文件

只用来导入到/seeds/seed.service 下，使用 graphql 不会用到

### 7.使用大模型生成 module 文件

将 dto entity contraller service resolver 导入到 modules

## 3.导入步骤

### 1.将 antiBlock.entity 导入到/src/config/database.ts 中

### 2.将 antiBlock.module 导入到 /src/schema.module.ts 在 GraphQLModule 使用
