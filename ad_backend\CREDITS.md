# CREDITS

This application code mostly is a mix of third-party frameworks/libraries/components, configured to work well together (see details about all those below). It follows standard conventions for most large NestJs-based projects.  

We used exactly the same Stack (TS, NestJs, GraphQL/REST) in our company Open-Source projects (<https://github.com/ever-co/ever-gauzy>, <https://github.com/ever-co/ever-demand>, and many others) as well as our internal proprietary closed-source projects (e.g. <https://github.com/ever-co/ever-gauzy-ai>) for many years. We have also built numerous commercial projects with exactly the same software Stack and we love it!  

Essentially, all credit belongs to those libraries listed below and 100s of other packages from [package.json](package.json) file!  
So, you will not find any "unique" code in this repo, more just a "standard" way to use all those things together.

## Components, Libraries, Frameworks, Packages

This application uses Open Source components and 3rd party libraries, which are licensed under their own respective Open-Source licenses.
You can find the links to the source code of their open-source projects along with license information below.
We acknowledge and are grateful to these developers for their contributions to open source.
Please see [package.json](package.json) for a full large list of packages we used, but also review our favorites below:

-   [ngx-starter-kit](https://github.com/xmlking/ngx-starter-kit), Ngx Starter Kit, released under [MIT](https://github.com/xmlking/ngx-starter-kit/blob/develop/LICENSE), `Copyright (c) 2018 Sumanth Chinthagunta`

-   [Nest](https://github.com/nestjs/nest), progressive Node.js framework, released under [MIT](https://github.com/nestjs/nest/blob/master/LICENSE), `Copyright (c) 2017-2023 Kamil Mysliwiec` <https://kamilmysliwiec.com>

-   [graphql-code-generator](https://github.com/dotansimha/graphql-code-generator), A tool for generating code based on a GraphQL schema and GraphQL operations (query/mutation/subscription), with flexible support for custom plugins, released under [MIT](https://github.com/dotansimha/graphql-code-generator/blob/master/LICENSE), `Copyright (c) 2016 Dotan Simha`

-   [docker-compose-wait](https://github.com/ufoscout/docker-compose-wait), A small command-line utility to wait for other docker images to be started while using docker-compose. Released under [Apache-2.0 License](https://github.com/ufoscout/docker-compose-wait/blob/master/LICENSE).
