/*
 * @Author: 潘孝权
 * @Date: 2025-06-09 23:25:55
 * @Description: 
 * @FilePath: \sking_frontend\slash-admin\src\api\services\analysisService.ts
 */
import { axiosInstance } from '../apiClient';
const getOverviewCards = async () => {
  const res = await axiosInstance.get('/analysis/overview-cards')
  return res;
}

const getAdPerformanceAnalysis = async (timeRange: 'day' | 'week' | 'month' = 'day') => {
  const res = await axiosInstance.get(`/analysis/ad-performance?timeRange=${timeRange}`)
  return res;
}

const getAdTrafficAnalysis = async (timeRange: 'day' | 'week' | 'month' = 'day') => {
  const res = await axiosInstance.get(`/analysis/ad-traffic?timeRange=${timeRange}`)
  return res;
}

const getCreativePerformance = async () => {
  const res = await axiosInstance.get('/analysis/creative-performance')
  return res;
}

const getChannelAnalysis = async (timeRange: 'week' | 'month' | 'quarter' = 'month') => {
  const timestamp = Date.now();
  const res = await axiosInstance.get(`/analysis/channel-analysis?timeRange=${timeRange}&_t=${timestamp}`)
  return res;
}

export default {
  getOverviewCards,
  getAdPerformanceAnalysis,
  getAdTrafficAnalysis,
  getCreativePerformance,
  getChannelAnalysis
}