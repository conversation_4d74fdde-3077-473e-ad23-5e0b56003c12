import { DataSource } from 'typeorm';
import { Entities } from './database';

export const AppDataSource = new DataSource({
    type: 'postgres',
    host: "pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com",
    port: 5432,
    username: "yeeu",
    password: "Yy&20240505",
    database: "ad_auto",
    synchronize: true,
    logging: true,
    entities: Entities,
    migrations: ['src/migrations/*.ts'],
    subscribers: [],
}); 