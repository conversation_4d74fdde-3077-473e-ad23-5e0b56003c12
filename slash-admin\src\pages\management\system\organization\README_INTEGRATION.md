# VCC交易历史模块 - 前后端联调指南

## 🚀 **联调完成状态**

✅ **后端API已完成**
✅ **前端GraphQL查询已创建**
✅ **前端服务层已完成**
✅ **前端组件已更新**
✅ **类型定义已对齐**

## 📋 **联调检查清单**

### **1. 后端服务状态**
```bash
# 在 ad_backend 目录下启动后端
npm run start:dev

# 检查后端服务
curl http://localhost:3000/graphql
```

### **2. GraphQL Playground 验证**
访问：`http://localhost:3000/graphql`

测试查询：
```graphql
query GetVccTransactions {
  vccTransactions(
    pagination: { page: 1, limit: 10 }
  ) {
    data {
      id
      cardNo
      amount
      merchant
      transactionTime
      status
      type
    }
    total
    page
    limit
  }
}
```

### **3. 前端环境配置**
确保 `.env` 文件中配置正确的GraphQL端点：
```env
VITE_APP_GRAPHQL_API=http://localhost:3000/graphql
```

### **4. 前端依赖检查**
```bash
# 在 slash-admin 目录下
npm install
npm run dev
```

## 🔧 **API 端点映射**

| 前端功能 | GraphQL操作 | 后端端点 |
|---------|------------|---------|
| **交易列表查询** | `GET_VCC_TRANSACTIONS` | `query vccTransactions` |
| **交易详情查看** | `GET_VCC_TRANSACTION` | `query vccTransaction` |
| **交易统计** | `GET_VCC_TRANSACTION_STATS` | `query vccTransactionStats` |
| **数据导出** | `EXPORT_VCC_TRANSACTIONS` | `query exportVccTransactions` |
| **Facebook同步** | `SYNC_VCC_TRANSACTIONS_FROM_FACEBOOK` | `mutation syncVccTransactionsFromFacebook` |
| **手动充值** | `ADD_VCC_RECHARGE_TRANSACTION` | `mutation addVccRechargeTransaction` |

## 🎯 **功能测试用例**

### **测试1：查询交易记录**
1. 访问VCC管理页面的"交易历史"标签页
2. 验证交易记录列表加载
3. 测试分页功能
4. 测试排序功能（金额、时间）

### **测试2：搜索过滤**
1. 输入卡号进行搜索
2. 选择状态过滤（成功/处理中/失败）
3. 选择类型过滤（支付/充值/退款）
4. 设置时间范围过滤
5. 点击重置按钮

### **测试3：统计数据**
1. 验证顶部统计卡片显示
2. 检查总交易数、总金额、成功率计算

### **测试4：数据导出**
1. 点击"导出CSV"按钮
2. 点击"导出JSON"按钮
3. 验证文件下载和格式

### **测试5：交易详情**
1. 点击交易记录的"查看"按钮
2. 验证详情模态框显示
3. 检查所有字段的正确显示

### **测试6：Facebook同步**
1. 点击"Facebook同步"按钮
2. 验证同步功能（需要已绑定的VCC卡片和广告账户）

## 🐛 **常见问题排查**

### **问题1：GraphQL查询失败**
- 检查网络连接
- 验证GraphQL端点配置
- 检查后端服务状态
- 查看浏览器控制台错误

### **问题2：认证问题**
- 确保已登录系统
- 检查JWT token是否有效
- 验证权限配置

### **问题3：数据不显示**
- 检查数据库中是否有交易记录
- 验证租户隔离逻辑
- 查看后端日志

### **问题4：Facebook同步失败**
- 检查VCC卡片是否绑定了Facebook广告账户
- 验证Facebook OAuth token是否有效
- 检查Facebook API访问权限

## 📊 **性能优化建议**

1. **前端优化**
   - 启用GraphQL查询缓存
   - 实现虚拟滚动（大数据量）
   - 添加搜索防抖

2. **后端优化**
   - 数据库查询索引优化
   - 分页查询性能优化
   - Facebook API调用缓存

## 🔐 **安全注意事项**

1. **数据脱敏**：卡号已自动脱敏显示
2. **权限控制**：所有操作基于JWT认证
3. **租户隔离**：数据严格按租户隔离
4. **敏感信息**：Facebook token等敏感信息不在前端暴露

## 🚀 **部署准备**

1. **环境变量配置**
```bash
# 后端环境变量
DB_HOST=your_database_host
DB_NAME=your_database_name
JWT_SECRET=your_jwt_secret

# 前端环境变量
VITE_APP_GRAPHQL_API=https://your-api-domain/graphql
```

2. **数据库迁移**
- 确保VccTransaction表已创建
- 验证索引配置
- 运行数据迁移脚本

3. **服务监控**
- 配置日志监控
- 设置错误报警
- 监控API响应时间

---

**最后更新时间：** 2024年12月
**状态：** ✅ 联调完成，可进入测试阶段 