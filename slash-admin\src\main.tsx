import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Analytics } from "@vercel/analytics/react";
import { Suspense } from "react";
import ReactDOM from "react-dom/client";
import { HelmetProvider } from "react-helmet-async";
import worker from "./_mock";
import "./locales/i18n";
import "./global.css";
import "./theme/theme.css";
import App from "./App";
import { registerLocalIcons } from "./components/icon";
import { RouteLoadingProgress } from "./components/loading";
import { ApolloClient, InMemoryCache, ApolloProvider, ApolloLink, HttpLink, from } from "@apollo/client";
import { onError } from "@apollo/client/link/error";
import { setContext } from '@apollo/client/link/context';
import { notification } from "antd";
import { StorageEnum } from "#/enum";
import { TenantProvider } from './context/tenant';
import { App as AntdApp } from 'antd';

const charAt = `
  ╔═══════ SLASH ADMIN ═══════╗
  ║                           ║
  ║  Modern React Admin UI    ║
  ║  Built with React & Vite  ║
  ║                           ║
  ╚═══════════════════════════╝
`;

notification.config({
  placement: 'top',
  duration: 8,
  top: 80,
});

// 错误处理链接
const errorLink = onError(({ graphQLErrors, networkError, operation }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message: errorMessage, locations, path }) => {
      console.error(
        `[GraphQL error]: Message: ${errorMessage}, Location: ${locations}, Path: ${path}, Operation: ${operation.operationName}`
      );
      // 新增：token 失效处理
      if (
        errorMessage?.includes('无效或过期的token') ||
        errorMessage?.toLowerCase().includes('invalid token') ||
        errorMessage?.toLowerCase().includes('token expired') ||
        errorMessage?.toLowerCase().includes('jwt expired') ||
        errorMessage?.toLowerCase().includes('unauthorized')
      ) {
        // 清除本地token和用户信息
        localStorage.removeItem('auth_token');
        localStorage.removeItem('userStore');
        localStorage.removeItem('tenant_tokens');
        // 跳转到登录页（hash路由）
        window.location.hash = '#/login';
        return;
      }
      notification.error({
        message: '操作失败',
        description: errorMessage,
        duration: 8
      });
    });
  }
  if (networkError) {
    console.error(`[Network error]: ${networkError}`);
    notification.error({
      message: '网络错误',
      description: networkError.message,
      duration: 8
    });
  }
});

// 日志链接
const loggerLink = new ApolloLink((operation, forward) => {
  return forward(operation).map((result) => {
    return result;
  });
});

// 认证链接
const authLink = new ApolloLink((operation, forward) => {
  // 直接从localStorage读取token
  const directToken = localStorage.getItem('auth_token');
  if (directToken) {
    operation.setContext(({ headers = {} }) => ({
      headers: {
        ...headers,
        authorization: `Bearer ${directToken}`,
      }
    }));
    return forward(operation);
  }

  // 尝试从userStore中读取token
  try {
    const userStoreStr = localStorage.getItem('userStore');

    if (userStoreStr) {
      const userStore = JSON.parse(userStoreStr);

      // 检查所有可能的位置
      let token = '';

      // 检查方式1: 如果是直接存储的accessToken
      if (userStore.accessToken) {
        token = userStore.accessToken;
      }

      // 检查方式2: 如果是存储在userToken字段中
      else if (userStore.userToken && userStore.userToken.accessToken) {
        token = userStore.userToken.accessToken;
      }

      // 检查方式3: 如果是使用枚举存储的
      else if (userStore[StorageEnum.UserToken] && userStore[StorageEnum.UserToken].accessToken) {
        token = userStore[StorageEnum.UserToken].accessToken;
      }

      if (token) {
        // 为避免每次都需要解析，同时保存一份直接的token
        localStorage.setItem('auth_token', token);

        // 设置请求头
        const authHeader = `Bearer ${token}`;

        operation.setContext(({ headers = {} }) => ({
          headers: {
            ...headers,
            authorization: authHeader,
          }
        }));
      }
    }
  } catch (error) {
  }

  return forward(operation);
});

// HTTP链接
// 自定义 fetch 函数处理重定向
const customFetch = async (uri: RequestInfo | URL, options?: RequestInit) => {
  const response = await fetch(uri, options);
  const clonedResponse = response.clone(); // 克隆响应以便后续读取

  try {
    const data = await clonedResponse.json();
    if (data && data.redirect && data.url) {
      window.location.href = data.url;
      // 返回一个不会被 Apollo Client 处理的响应，或者抛出错误
      // 这里返回一个空的、成功的响应，避免 Apollo Client 报错
      return new Response(null, { status: 200 });
    }
  } catch (e) {
    // 如果解析 JSON 失败，说明不是重定向响应，忽略错误
  }

  // 如果不是重定向响应，返回原始响应给 Apollo Client 处理
  return response;
};
// HTTP链接，使用自定义 fetch 函数
const httpLink = new HttpLink({
  uri: import.meta.env.VITE_APP_GRAPHQL_API || 'http://localhost:3005/graphql',
  fetch: customFetch,
});

// 重定向处理链接
const redirectLink = new ApolloLink((operation, forward) => {
  return forward(operation).map(response => {
    if (response.data && response.data.redirect && response.data.url) {
      const redirectUrl = response.data.url;
      window.location.href = redirectUrl;
      // Optionally, you might want to stop further processing or throw an error
      // throw new Error(`Redirecting to ${redirectUrl}`);
    }

    return response;
  });
});

// 创建Apollo客户端
const client = new ApolloClient({
  link: from([errorLink, authLink, redirectLink, errorLink, loggerLink, httpLink]),
  cache: new InMemoryCache({
    addTypename: false, // 解决缓存键冲突问题
    typePolicies: {
      RolePagination: {
        fields: {
          totalCount: {
            read(existing) {
              return existing || 0;
            }
          },
          pageInfo: {
            merge(existing, incoming) {
              return incoming;
            }
          }
        }
      }
    }
  }),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'network-only',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'network-only',
      errorPolicy: 'all',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
});

await registerLocalIcons();

const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(
  <HelmetProvider>
    <QueryClientProvider client={new QueryClient()}>
      {/* <ReactQueryDevtools initialIsOpen={false} /> */}
      <Suspense>
        <RouteLoadingProgress />
        <Analytics />
        <AntdApp>
          <TenantProvider apolloClient={client}>
            <ApolloProvider client={client}>
              <App />
            </ApolloProvider>
          </TenantProvider>
        </AntdApp>
      </Suspense>
    </QueryClientProvider>
  </HelmetProvider>
);

// 🥵 start service worker mock in development mode
worker.start({ onUnhandledRequest: "bypass" });
