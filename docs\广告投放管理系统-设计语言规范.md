# 广告投放管理系统 - 设计语言规范

**更新日期：** 2025年5月8日

## 1. 设计原则

### 1.1 一致性

- 在整个应用中保持视觉和交互的一致性
- 使用一致的组件、颜色、字体和间距
- 确保相似功能有相似的交互方式

### 1.2 简洁性

- 界面设计应简洁明了，减少视觉噪音
- 每个页面应有明确的主要操作和次要操作
- 避免不必要的装饰元素

### 1.3 层次结构

- 通过视觉权重建立清晰的信息层次结构
- 重要内容应该更加突出
- 使用空间、大小和颜色来引导用户注意力

### 1.4 反馈性

- 所有用户操作都应提供明确的反馈
- 使用加载状态、成功/错误提示和动画过渡
- 确保用户知道他们的操作是否成功

### 1.5 可访问性

- 确保足够的颜色对比度
- 提供键盘导航支持
- 遵循WCAG 2.1 AA级标准

## 2. 颜色系统

### 2.1 主色调

- **主色 (Primary)**: `#1890ff` - 用于主要按钮、链接和强调元素
- **主色-浅色 (Primary Light)**: `#40a9ff` - 用于悬停状态
- **主色-深色 (Primary Dark)**: `#096dd9` - 用于激活状态

### 2.2 功能色

- **成功 (Success)**: `#52c41a` - 用于成功状态和积极反馈
- **警告 (Warning)**: `#faad14` - 用于警告和需要注意的信息
- **错误 (Error)**: `#f5222d` - 用于错误状态和危险操作
- **信息 (Info)**: `#1890ff` - 用于一般信息提示

### 2.3 中性色

- **标题文本 (Title)**: `#262626` - 用于页面标题和重要文本
- **主要文本 (Primary Text)**: `#595959` - 用于主要内容文本
- **次要文本 (Secondary Text)**: `#8c8c8c` - 用于辅助说明文本
- **禁用文本 (Disabled Text)**: `#bfbfbf` - 用于禁用状态的文本
- **边框 (Border)**: `#d9d9d9` - 用于边框和分隔线
- **背景 (Background)**: `#f0f2f5` - 用于页面背景
- **组件背景 (Component Background)**: `#ffffff` - 用于组件背景

### 2.4 数据可视化色板

- **图表颜色1**: `#1890ff`
- **图表颜色2**: `#52c41a`
- **图表颜色3**: `#faad14`
- **图表颜色4**: `#f5222d`
- **图表颜色5**: `#722ed1`
- **图表颜色6**: `#13c2c2`
- **图表颜色7**: `#fa8c16`
- **图表颜色8**: `#eb2f96`

## 3. 排版

### 3.1 字体家族

- **主要字体**: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'`
- **代码字体**: `'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace`

### 3.2 字体大小

- **超大标题 (H1)**: `38px`
- **大标题 (H2)**: `30px`
- **中标题 (H3)**: `24px`
- **小标题 (H4)**: `20px`
- **正文 (Body)**: `14px`
- **小字体 (Small)**: `12px`

### 3.3 字体粗细

- **常规 (Regular)**: `400`
- **中等 (Medium)**: `500`
- **粗体 (Bold)**: `600`

### 3.4 行高

- **紧凑 (Compact)**: `1.2`
- **常规 (Regular)**: `1.5`
- **宽松 (Loose)**: `1.8`

## 4. 间距系统

使用4px作为基础单位，构建一个一致的间距系统：

- **超小间距 (XS)**: `4px`
- **小间距 (S)**: `8px`
- **中间距 (M)**: `16px`
- **大间距 (L)**: `24px`
- **超大间距 (XL)**: `32px`
- **特大间距 (XXL)**: `48px`

## 5. 组件规范

### 5.1 按钮

#### 5.1.1 按钮类型

- **主要按钮 (Primary)**: 用于页面的主要操作
- **次要按钮 (Secondary)**: 用于次要操作
- **文本按钮 (Text)**: 用于不太重要的操作
- **链接按钮 (Link)**: 用于导航操作
- **危险按钮 (Danger)**: 用于危险或不可逆操作

#### 5.1.2 按钮大小

- **大型 (Large)**: 高度为`40px`
- **中型 (Medium)**: 高度为`32px`（默认）
- **小型 (Small)**: 高度为`24px`

### 5.2 表单

#### 5.2.1 输入框

- 高度为`32px`
- 内边距为左右`12px`，上下`4px`
- 边框颜色为`#d9d9d9`
- 聚焦时边框颜色为主色`#1890ff`

#### 5.2.2 标签

- 放置在输入框上方
- 与输入框间距为`8px`
- 使用次要文本颜色`#8c8c8c`

#### 5.2.3 帮助文本

- 放置在输入框下方
- 与输入框间距为`4px`
- 使用小字体`12px`和次要文本颜色`#8c8c8c`

### 5.3 表格

- 表头背景色为`#fafafa`
- 表格边框和分隔线颜色为`#f0f0f0`
- 行悬停背景色为`#e6f7ff`
- 选中行背景色为`#e6f7ff`
- 分页控件放置在表格右下角

### 5.4 卡片

- 背景色为白色`#ffffff`
- 边框颜色为`#f0f0f0`
- 圆角为`2px`
- 内边距为`24px`
- 卡片间距为`16px`

### 5.5 导航

#### 5.5.1 侧边导航

- 宽度为`256px`（展开状态）或`80px`（折叠状态）
- 背景色为`#001529`（深色主题）或`#ffffff`（浅色主题）
- 选中项背景色为主色`#1890ff`
- 项目高度为`40px`

#### 5.5.2 顶部导航

- 高度为`64px`
- 背景色为主色`#1890ff`（深色主题）或白色`#ffffff`（浅色主题）
- 固定在页面顶部

### 5.6 图标

- 使用统一的图标库（Ant Design Icons）
- 默认大小为`16px`
- 与相邻文本间距为`8px`

## 6. 响应式设计

### 6.1 断点

- **XS**: `< 576px` - 超小屏幕（手机竖屏）
- **SM**: `≥ 576px` - 小屏幕（手机横屏）
- **MD**: `≥ 768px` - 中等屏幕（平板）
- **LG**: `≥ 992px` - 大屏幕（桌面）
- **XL**: `≥ 1200px` - 超大屏幕（宽屏桌面）
- **XXL**: `≥ 1600px` - 特大屏幕（大型显示器）

### 6.2 栅格系统

- 使用24列栅格系统
- 列间距为`16px`（可调整）
- 在不同断点下调整列数和布局

### 6.3 适配策略

- 在小屏幕上简化UI，隐藏次要信息
- 调整组件大小和间距以适应不同屏幕
- 使用响应式布局，避免水平滚动

## 7. 动画与过渡

### 7.1 动画持续时间

- **快速 (Fast)**: `0.1s` - 用于即时反馈
- **标准 (Standard)**: `0.2s` - 用于大多数交互
- **缓慢 (Slow)**: `0.3s` - 用于复杂过渡

### 7.2 缓动函数

- **标准 (Standard)**: `cubic-bezier(0.23, 1, 0.32, 1)` - 用于大多数过渡
- **加速 (Ease-In)**: `cubic-bezier(0.55, 0.055, 0.675, 0.19)` - 用于退出动画
- **减速 (Ease-Out)**: `cubic-bezier(0.215, 0.61, 0.355, 1)` - 用于进入动画

### 7.3 常用动画

- **淡入淡出 (Fade)**: 透明度从0到1或从1到0
- **缩放 (Scale)**: 大小从0.8到1或从1到0.8
- **滑动 (Slide)**: 从一侧滑入或滑出
- **弹跳 (Bounce)**: 轻微的弹跳效果，用于引起注意

## 8. 图像与图标

### 8.1 图像

- 使用适当分辨率的图像，避免模糊
- 优先使用SVG格式的矢量图
- 图像应具有一致的风格和色调
- 图像应有适当的边距和圆角

### 8.2 图标

- 使用一致的图标库（Ant Design Icons或其他）
- 图标应与文本垂直居中对齐
- 交互式图标应有悬停和点击状态
- 图标大小应与周围元素协调

## 9. 数据可视化

### 9.1 图表类型

- **折线图**: 用于显示连续数据和趋势
- **柱状图**: 用于比较不同类别的数据
- **饼图/环形图**: 用于显示部分与整体的关系
- **散点图**: 用于显示两个变量之间的关系
- **热力图**: 用于显示复杂的多变量数据

### 9.2 图表样式

- 使用一致的颜色方案
- 提供清晰的标题、标签和图例
- 使用网格线提高可读性
- 提供适当的交互（如悬停提示、缩放）

## 10. 无障碍设计

### 10.1 颜色对比度

- 文本与背景的对比度应至少为4.5:1
- 大文本（18px以上）与背景的对比度应至少为3:1
- 不仅依靠颜色来传达信息，同时使用形状、文本或图案

### 10.2 键盘导航

- 所有交互元素应可通过键盘访问
- 提供清晰的焦点状态
- 使用合理的Tab顺序

### 10.3 屏幕阅读器支持

- 提供适当的ARIA标签和角色
- 确保动态内容变化可被屏幕阅读器捕获
- 提供图像的替代文本

## 11. 设计资源

### 11.1 设计系统组件库

- Ant Design组件库：[https://ant.design/components/overview/](https://ant.design/components/overview/)
- 自定义组件库（基于项目需求扩展）

### 11.2 图标资源

- Ant Design Icons：[https://ant.design/components/icon/](https://ant.design/components/icon/)
- Solar Icons：用于菜单和特殊功能

### 11.3 设计工具

- Figma：用于UI设计和原型
- Adobe Photoshop/Illustrator：用于图像编辑和创建
- Sketch：用于UI设计（Mac平台）

## 12. 最佳实践

### 12.1 页面布局

- 使用一致的页面结构
- 将相关内容分组在一起
- 使用足够的空白空间提高可读性
- 确保重要内容在首屏可见

### 12.2 表单设计

- 将表单分成逻辑部分
- 提供清晰的错误信息和验证反馈
- 使用适当的输入控件类型
- 提供默认值和自动完成功能

### 12.3 数据展示

- 使用适当的数据格式化（日期、货币、数字等）
- 提供排序、筛选和搜索功能
- 使用分页或虚拟滚动处理大量数据
- 提供数据加载和空状态的反馈

### 12.4 交互设计

- 提供清晰的操作提示
- 使用适当的确认机制防止误操作
- 提供撤销操作的能力
- 保持交互的一致性和可预测性
