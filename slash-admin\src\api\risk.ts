import request from '@/utils/request';
import type { RiskItem, SyncRisksResponse, ExportRisksResponse } from '@/types/ad';

interface SyncRisksParams {
  tenantId: string;
  riskList: RiskItem[];
}

interface ExportRisksParams {
  tenantId: string;
  type: string;
  limit: number;
}

export function syncRisks(params: SyncRisksParams) {
  return request.post<SyncRisksResponse>('/ad-platform/risk/batch', params);
}

export function exportRisks(params: ExportRisksParams) {
  return request.get<ExportRisksResponse>('/ad-platform/risk/export', { params });
}
