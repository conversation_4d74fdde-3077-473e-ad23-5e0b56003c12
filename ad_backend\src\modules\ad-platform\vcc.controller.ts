import { Controller, Get, Post, Put, Delete, Body, Param, Query, BadRequestException, UseGuards, Req } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Vcc } from './entities/vcc.entity';
import { Tenant } from '../../entity/tenant.entity';
import { JwtAuthGuard } from '../../modules/auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Controller('ad-platform/vcc')
export class VccController {
  constructor(
    @InjectRepository(Vcc) private readonly vccRepo: Repository<Vcc>,
    @InjectRepository(Tenant) private readonly tenantRepo: Repository<Tenant>,
  ) { }

  @Get()
  async list(@Query('page') page = 1, @Query('pageSize') pageSize = 20, @Req() req: any) {
    const tenantId = req.tenantId;
    const [data, total] = await this.vccRepo.findAndCount({
      where: { tenant: { id: tenantId } },
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { cardNumber: 'ASC' },
    });
    return { data, total, page, pageSize };
  }

  @Post()
  async create(@Body() body: any, @Req() req: any) {
    const tenantId = req.tenantId;
    if (!body.cardNumber) throw new BadRequestException('cardNumber required');
    const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
    if (!tenant) throw new BadRequestException('tenant not found');
    const vcc = this.vccRepo.create({ ...body, tenant });
    await this.vccRepo.save(vcc);
    return vcc;
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() body: any, @Req() req: any) {
    const tenantId = req.tenantId;
    const vcc = await this.vccRepo.findOne({ where: { id, tenant: { id: tenantId } } });
    if (!vcc) throw new BadRequestException('VCC not found');
    Object.assign(vcc, body);
    await this.vccRepo.save(vcc);
    return vcc;
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Req() req: any) {
    const tenantId = req.tenantId;
    const vcc = await this.vccRepo.findOne({ where: { id, tenant: { id: tenantId } } });
    if (!vcc) throw new BadRequestException('VCC not found');
    await this.vccRepo.remove(vcc);
    return { success: true };
  }

  @Post('batch')
  async batchCreate(@Body() body: { vccList: any[] }, @Req() req: any) {
    const tenantId = req.tenantId;
    if (!Array.isArray(body.vccList)) throw new BadRequestException('vccList required');
    const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
    if (!tenant) throw new BadRequestException('tenant not found');
    const vccs = body.vccList.map(item => this.vccRepo.create({ ...item, tenant }));
    await this.vccRepo.save((Array.isArray(vccs[0]) ? vccs.flat(1) : vccs) as any[]);
    return { inserted: vccs.length };
  }
}
