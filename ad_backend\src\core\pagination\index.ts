import { Field, InputType, Int, ObjectType } from '@nestjs/graphql';
import { IsInt, IsOptional, Min } from 'class-validator';

@ObjectType()
export class RolePageInfo {
    @Field(() => Int)
    page: number;

    @Field(() => Int)
    limit: number;

    @Field(() => Int)
    totalPages: number;

    @Field(() => Boolean)
    hasNextPage: boolean;

    @Field(() => Boolean)
    hasPreviousPage: boolean;
}

@InputType()
export class PaginationArgs {
    @Field(() => Int, { nullable: true, defaultValue: 1 })
    @IsOptional()
    @IsInt()
    @Min(1)
    page?: number = 1;

    @Field(() => Int, { nullable: true, defaultValue: 10 })
    @IsOptional()
    @IsInt()
    @Min(1)
    limit?: number = 10;
} 