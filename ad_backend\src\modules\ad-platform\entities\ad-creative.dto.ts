import { ObjectType, Field, ID } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import Graph<PERSON>JSON from 'graphql-type-json';
import { TenantDTO } from '../../../entity/tenant.dto';
import { AdAccountDTO } from './ad-account.dto';
import { AdCampaignDTO } from './ad-campaign.dto';

@ObjectType('AdCreative')
export class AdCreativeDTO {
  @IDField(() => ID)
  id: string;

  @FilterableField()
  platform: string;

  @FilterableField()
  creativeId: string;

  @FilterableField()
  name: string;

  @Field(() => AdAccountDTO)
  adAccount: AdAccountDTO;

  @Field(() => AdCampaignDTO)
  adCampaign: AdCampaignDTO;

  @Field(() => TenantDTO)
  tenant: TenantDTO;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  raw?: any;

  @FilterableField()
  status: string;
}
