import { InputType, Field, ID, Int } from '@nestjs/graphql';
import { IsOptional, IsString, IsArray, IsUUID } from 'class-validator';
import { UserStatus } from '../user.entity';

@InputType()
export class UserFilterInput {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    username?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    email?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    fullName?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone?: string;

    @Field(() => UserStatus, { nullable: true })
    @IsOptional()
    status?: UserStatus;

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID("4", { each: true })
    roleIds?: string[];

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID("4", { each: true })
    groupIds?: string[];
}

@InputType()
export class UserListInput {
    @Field(() => UserFilterInput, { nullable: true })
    @IsOptional()
    filter?: UserFilterInput;

    @Field(() => Int, { nullable: true, defaultValue: 1 })
    @IsOptional()
    page?: number;

    @Field(() => Int, { nullable: true, defaultValue: 10 })
    @IsOptional()
    limit?: number;
}

@InputType()
export class UserSortInput {
    @Field()
    field: string;

    @Field()
    direction: 'ASC' | 'DESC';
} 