import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import {
	Field,
	ID,
	ObjectType,
	GraphQLISODateTime,
	InputType,
	Int
} from '@nestjs/graphql';
import { FilterableField } from '@ptc-org/nestjs-query-graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@ObjectType('materiaCreate')
export class materiaCreateDTO {
	@FilterableField(() => Int, { nullable: true })
	@IsNumber()
	@IsOptional()
	id?: number;

	@FilterableField(() => String)
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	userId: string;

	@FilterableField(() => String)
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	name: string;

	@FilterableField(() => Number)
	@ApiPropertyOptional({ type: Number })
	@IsNumber()
	@IsOptional()
	materials: number;

	@FilterableField(() => String)
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	pageLading: string;

	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	createId: string;


	@FilterableField(() => String, { nullable: true })
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	slogan?: string;

	@FilterableField(() => String)
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	account: string;


	@FilterableField(() => String)
	@IsOptional()
	@IsString()
	@ApiPropertyOptional({ type: String })
	pageId: string;

	@FilterableField(() => String)
	@ApiPropertyOptional({ type: String })
	@IsString()
	@IsOptional()
	tenantId: string;

	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	createdAt?: Date;
	@FilterableField(() => GraphQLISODateTime, { nullable: true })
	@ApiPropertyOptional({
		type: 'string',
		format: 'date-time',
		example: '2022-11-21T04:22:36.242Z',
	})
	@IsOptional()
	updatedAt?: Date;
}

@InputType('materiaCreateInput')
export class materiaCreateInput {
	@Field(() => Int, { nullable: true })
	id?: number;
	@Field({ nullable: false })
	tenantId: string;
}

@InputType('materialManagementInput')
export class materialManagementInput {
	@Field(() => Int, { nullable: true })
	id?: number;
	@Field({ nullable: true })
	userId?: string;
	@Field({ nullable: true })
	name?: string;
	@Field({ nullable: true })
	materials?: number;
	@Field({ nullable: true })
	createId?: string;
	@Field({ nullable: true })
	slogan?: string;
	@Field({ nullable: true })
	account?: string;
	@Field({ nullable: true })
	pageId?: string;
	@Field({ nullable: true })
	createdAt?: Date;
	@Field({ nullable: true })
	updatedAt?: Date;
}

