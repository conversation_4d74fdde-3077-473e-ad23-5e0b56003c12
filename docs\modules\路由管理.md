# 组件路径 /pages/management/system/route/index

## 前端

- **实际路径**：slash-admin/src/pages/management/system/route/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，路由管理表单与树形列表，含增删改查、状态切换、弹窗等）
  - components/RouteForm（路由表单弹窗）
- **主要依赖API定义**：@/api/services/route.service

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取路由列表    | query    | getRoutes              | 无           | id, name, path, parentId, order, type, icon, status, ... | 支持树形结构 |
| 新增路由        | mutation | createRoute            | input: RouteInput | id, name, ... | 新建路由 |
| 编辑路由        | mutation | updateRoute            | id, input: RouteInput | id, name, ... | 编辑指定路由 |
| 删除路由        | mutation | deleteRoute            | id: string    | id       | 删除指定路由 |
| 路由状态切换    | mutation | toggleRouteStatus      | id, status    | id, status | 启用/禁用路由 |

#### 主要参数结构说明

- **RouteInput**（用于新增/编辑）  
  ```ts
  {
    name: string; // 路由名称
    path: string; // 路由路径
    parentId?: string; // 父级路由ID
    order?: number; // 排序
    type?: string; // 类型（menu/page/button等）
    icon?: string; // 图标
    status?: string; // 状态（enabled/disabled）
    component?: string; // 组件路径
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | id          | string    | 主键，UUID                 |
  | name        | string    | 路由名称                   |
  | path        | string    | 路由路径                   |
  | parentId    | string    | 父级路由ID                 |
  | order       | number    | 排序                       |
  | type        | string    | 类型（menu/page/button）   |
  | icon        | string    | 图标                       |
  | status      | string    | 状态（enabled/disabled）   |
  | component   | string    | 组件路径                   |
  | ...         | ...       | 其他业务字段               |

### 前端表单/组件说明

- 支持路由的增删改查，表单字段与上方RouteInput结构一致
- 支持树形结构展示，支持多级嵌套
- 支持排序、状态切换、图标选择
- 支持批量删除（如有实现）
- 路由名称、路径等字段唯一性校验

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（route，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| name        | varchar     | 路由名称                   | NOT NULL     |
| path        | varchar     | 路由路径                   | NOT NULL, UNIQUE |
| parentId    | uuid        | 父级路由ID                 | 可空         |
| order       | int         | 排序                       | 默认0        |
| type        | varchar     | 类型（menu/page/button）   | 默认menu     |
| icon        | varchar     | 图标                       | 可空         |
| status      | varchar     | 状态（enabled/disabled）   | 默认enabled  |
| component   | varchar     | 组件路径                   | 可空         |
| ...         | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query getRoutes / mutation createRoute(input) / ...
    后端->>数据库: 操作 route 表
    数据库-->>后端: 返回路由数据
    后端-->>前端: 返回路由数据
```

---

## 变更历史/注意事项

- 路由名称、路径需唯一，所有操作需校验唯一性
- 支持多级嵌套，建议用parentId递归实现
- 删除路由时需校验是否有子节点，防止误删
- 复杂业务字段建议在表结构中用jsonb扩展
