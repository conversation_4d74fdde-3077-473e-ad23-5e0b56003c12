import { ApolloClient } from '@apollo/client';
import {
    ROUTES_QUERY,
    CREATE_ROUTE,
    UPDATE_ROUTE,
    DELETE_ROUTE
} from '../route.graphql';
import type {
    RoutesResponse,
    CreateRouteResponse,
    UpdateRouteResponse,
    DeleteRouteResponse,
    CreateRouteVariables,
    UpdateRouteVariables,
    DeleteRouteVariables,
    CreateRouteInput,
    UpdateRouteInput
} from '@/pages/management/system/route/graphql/types';
import type { RouteItem } from '@/pages/management/system/route/types';

interface RouteListResponse {
    getRoutes: RouteItem[];
}

export class RouteService {
    constructor(private client: ApolloClient<any>) { }

    async getRoutes() {
        const { data } = await this.client.query<RouteListResponse>({
            query: ROUTES_QUERY,
            fetchPolicy: 'network-only'
        });
        return data.getRoutes;
    }

    // 示例：创建 Dashboard 路由
    async createDashboardRoute() {
        const dashboardRoute: CreateRouteInput = {
            name: "Dashboard",
            path: "/dashboard",
            component: "Dashboard",
            icon: "DashboardOutlined",
            order: 1,
            type: "menu",
            status: "enabled",
            isHidden: false,
            description: "Dashboard page"
        };

        return this.createRoute(dashboardRoute);
    }

    async createRoute(routeData: CreateRouteInput) {
        const { data } = await this.client.mutate<CreateRouteResponse, CreateRouteVariables>({
            mutation: CREATE_ROUTE,
            variables: {
                input: routeData
            },
            refetchQueries: [{ query: ROUTES_QUERY }]
        });
        return data?.createOneRoute;
    }

    async updateRoute(id: string, updateData: Partial<UpdateRouteInput>) {
        const { data } = await this.client.mutate<UpdateRouteResponse>({
            mutation: UPDATE_ROUTE,
            variables: {
                input: {
                    id,
                    update: {
                        ...updateData,
                        // Ensure required fields have default values if not provided
                        name: updateData.name ?? '',
                        path: updateData.path ?? '',
                        order: updateData.order ?? 0,
                        type: updateData.type ?? 'menu',
                        status: updateData.status ?? 'enabled',
                        isHidden: updateData.isHidden ?? false
                    }
                }
            },
            refetchQueries: [{ query: ROUTES_QUERY }]
        });
        return data?.updateOneRoute;
    }

    async deleteRoute(id: string) {
        const { data } = await this.client.mutate<DeleteRouteResponse, { id: string }>({
            mutation: DELETE_ROUTE,
            variables: { id },
            refetchQueries: [{ query: ROUTES_QUERY }]
        });
        return data?.deleteOneRoute;
    }

    async toggleRouteStatus(id: string, status: 'enabled' | 'disabled') {
        return this.updateRoute(id, { status });
    }
} 