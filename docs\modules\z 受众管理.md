# 组件路径 /pages/management/facebook/audience/index

## 前端

- **实际路径**：slash-admin/src/pages/management/facebook/audience/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，受众管理表单与列表，含增删改查、分页、搜索、弹窗等）
- **主要依赖API定义**：slash-admin/src/api/audience.graphql.ts

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取受众列表    | query    | audiences              | `filter: AudienceCustomFilter`<br>可选字段：id, name, platform, ... | id, name, geo_locations, ... | 支持分页、模糊搜索、平台过滤 |
| 新增受众        | mutation | createAudience         | `input: CreateAudience`<br>详见下方字段 | id, name, geo_locations, ... | 新建受众，id为UUID |
| 编辑受众        | mutation | updateAudience         | `id: string, input: CreateAudience` | id, name, geo_locations, ... | 编辑指定id的受众 |
| 删除受众        | mutation | deleteAudience         | `id: string` | id | 删除指定id的受众 |
| 兴趣选项        | query    | audienceInterestOptions| 无 | id, name | 获取兴趣库 |
| 行为选项        | query    | audienceBehaviorOptions| 无 | id, name | 获取行为库 |

#### 主要参数结构说明

- **CreateAudience**（用于新增/编辑）  
  ```ts
  {
    id: string; // UUID
    name: string; // 受众名称
    geo_locations: { countries?: string[], regions?: {id, name}[], cities?: {id, name}[] }; // 地理位置
    excluded_geo_locations?: { countries?: string[], regions?: {id, name}[], cities?: {id, name}[] }; // 排除地理位置
    locales: number[]; // 语言ID数组
    gender?: number; // 性别 1=男,2=女,0/空=不限
    age_min?: number; // 最小年龄
    age_max?: number; // 最大年龄
    interests?: { id: string, name?: string }[]; // 兴趣
    behaviors?: { id: string, name?: string }[]; // 行为
    notes?: string; // 备注
    platform?: string[]; // 归属平台
  }
  ```

- **geo_locations/ excluded_geo_locations 示例**  
  ```json
  {
    "countries": ["CN", "US"],
    "regions": [{"id": "123", "name": "华东"}],
    "cities": [{"id": "456", "name": "上海"}]
  }
  ```

- **interests/behaviors 示例**  
  ```json
  [
    { "id": "6003139266461", "name": "电子商务" },
    { "id": "6003277229372", "name": "旅游" }
  ]
  ```

- **locales 示例**  
  `[101, 102] // Facebook语言ID，101=中文, 102=英文等`

### 前端表单/组件说明

- 支持受众的增删改查，表单字段与上方CreateAudience结构一致
- 支持兴趣、行为的动态搜索与选择
- 支持国家、地区、城市多级选择
- 支持分页、模糊搜索、平台过滤
- 支持批量删除（如有实现）

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（audience，详见 ad_backend/src/modules/ad-platform/entities/audience.entity.ts）：

| 字段名                | 类型                | 说明                       | 约束         |
|----------------------|---------------------|----------------------------|--------------|
| id                   | uuid                | 主键，受众唯一标识         | PRIMARY KEY  |
| name                 | varchar(128)        | 受众名称                   | 可空         |
| notes                | text                | 备注                       | 可空         |
| audienceId           | varchar(64)         | 受众ID（业务唯一）         | UNIQUE, 可空 |
| platform             | text[]              | 归属平台（如facebook等）   | 可空         |
| geo_locations        | jsonb               | 地理位置，结构见上         | 可空         |
| excluded_geo_locations | jsonb             | 排除地理位置，结构见上     | 可空         |
| locales              | int[]               | 语言ID数组                 | 可空         |
| gender               | int                 | 性别（1=男,2=女,0/空=不限）| 可空         |
| age_min              | int                 | 最小年龄                   | 可空         |
| age_max              | int                 | 最大年龄                   | 可空         |
| interests            | jsonb               | 兴趣 [{id, name}]          | 可空         |
| behaviors            | jsonb               | 行为 [{id, name}]          | 可空         |
| tenant               | uuid                | 租户ID（外键）             | NOT NULL     |

- **geo_locations / excluded_geo_locations 结构**  
  - countries: 国家代码数组（如["CN","US"]）
  - regions: 区域对象数组（如[{id:"123",name:"华东"}]）
  - cities: 城市对象数组（如[{id:"456",name:"上海"}]）

- **interests/behaviors 结构**  
  - [{id: string, name?: string}]

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query audiences(filter) / mutation createAudience(input) / ...
    后端->>数据库: 操作 audience 表
    数据库-->>后端: 返回受众数据
    后端-->>前端: 返回受众数据
```

---

## 变更历史/注意事项

- audienceId（业务ID）唯一，所有操作需校验唯一性
- geo_locations、excluded_geo_locations、interests、behaviors等为json结构，前端需严格按格式传递
- 归属平台（platform）支持多平台扩展，建议前后端都用数组
- 语言（locales）为Facebook语言ID数组，需与Facebook API保持一致
- 性别、年龄等字段如为空则视为不限
- 受众数据与Facebook广告平台同步时，需确保字段映射一致
- 删除受众时需校验是否被广告组等引用