import { AppDataSource } from '../src/config/data-source';
import { Ad } from '../src/modules/ad-platform/entities/ad.entity';

async function clearAdTable() {
    await AppDataSource.initialize();
    const repo = AppDataSource.getRepository(Ad);
    await repo.clear();
    console.log('Ad table cleared.');
    await AppDataSource.destroy();
}

clearAdTable().catch(err => {
    console.error('Error clearing Ad table:', err);
    process.exit(1);
}); 