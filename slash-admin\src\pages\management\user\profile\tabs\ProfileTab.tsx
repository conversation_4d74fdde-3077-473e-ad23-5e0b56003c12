import { useTranslation } from 'react-i18next';
import React from 'react';
import { Card, Row, Col, Avatar, Typography, Button, Divider, Upload, message, Progress } from 'antd';
import { UserOutlined, MailOutlined, PhoneOutlined, EnvironmentOutlined, EditOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { t } = useTranslation();

interface Skill {
  name: string;
  level: number;
}

interface Stats {
  campaigns: number;
  materials: number;
  accounts: number;
  completionRate: number;
}

interface UserProfile {
  avatar: string;
  name: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  bio: string;
  skills: Skill[];
  stats: Stats;
  joinDate: string;
}

const ProfileTab: React.FC = () => {
  // 模拟用户数据
  const userProfile: UserProfile = {
    avatar: '',
    name: t('pages.key_865'),
    title: t('pages.key_787'),
    email: 'z<PERSON><PERSON>@example.com',
    phone: '***********',
    location: t('pages.key_8'),
    bio: '5年广告投放经验，擅长社交媒体广告优化和数据分析，曾负责多个大型品牌的线上营销活动。',
    skills: [
      { name: 'Facebook广告', level: 95 },
      { name: 'Google Ads', level: 88 },
      { name: t('pages.key_1090'), level: 90 },
      { name: t('pages.key_1630'), level: 85 },
      { name: t('pages.key_2562'), level: 92 }
    ],
    stats: {
      campaigns: 127,
      materials: 342,
      accounts: 8,
      completionRate: 94
    },
    joinDate: '2020-05-15'
  };

  return (
    <div className="profile-container">
      <Card bordered={false} className="profile-card" style={{ overflow: 'hidden', borderRadius: '8px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
        {/* 顶部背景区域 */}
        <div style={{
          height: '120px',
          background: 'linear-gradient(120deg, #1677ff, #0958d9)',
          margin: '-24px -24px 0',
          position: 'relative'
        }}>
          <div style={{
            position: 'absolute',
            bottom: '-50px',
            left: '50px',
            backgroundColor: '#fff',
            padding: '4px',
            borderRadius: '50%',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
          }}>
            <Avatar
              size={100}
              src={userProfile.avatar}
              icon={<UserOutlined />}
              style={{
                border: '4px solid #fff',
                backgroundColor: '#1677ff'
              }}
            />
          </div>
        </div>

        <Row gutter={24} style={{ marginTop: '60px' }}>
          {/* 左侧个人信息 */}
          <Col xs={24} sm={24} md={8} lg={7} xl={6}>
            <div style={{ paddingLeft: '20px', paddingTop: '10px' }}>
              <Title level={3} style={{ margin: '0' }}>{userProfile.name}</Title>
              <Text type="secondary" style={{ fontSize: '16px' }}>{userProfile.title}</Text>

              <div style={{ marginTop: '20px' }}>
                <Upload
                  showUploadList={false}
                  beforeUpload={(_file) => {
                    message.info(t('pages.key_600'));
                    return false;
                  }}
                >
                  <Button type="primary" icon={<EditOutlined />} size="small">{t('pages.key_1236')}</Button>
                </Upload>
              </div>

              <Divider style={{ margin: '20px 0' }} />

              {/* 统计数据 */}
              <div style={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', marginBottom: '20px' }}>
                <div style={{ width: '50%', marginBottom: '16px' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1677ff' }}>{userProfile.stats.campaigns}</div>
                  <div style={{ fontSize: '14px', color: '#8c8c8c' }}>{t('pages.key_796')}</div>
                </div>
                <div style={{ width: '50%', marginBottom: '16px' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1677ff' }}>{userProfile.stats.materials}</div>
                  <div style={{ fontSize: '14px', color: '#8c8c8c' }}>{t('pages.key_272')}</div>
                </div>
                <div style={{ width: '50%', marginBottom: '16px' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1677ff' }}>{userProfile.stats.accounts}</div>
                  <div style={{ fontSize: '14px', color: '#8c8c8c' }}>{t('pages.key_819')}</div>
                </div>
                <div style={{ width: '50%', marginBottom: '16px' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1677ff' }}>{userProfile.stats.completionRate}%</div>
                  <div style={{ fontSize: '14px', color: '#8c8c8c' }}>{t('pages.key_650')}</div>
                </div>
              </div>

              <div style={{ marginBottom: '16px' }}>
                <Text strong>{t('pages.key_177')}</Text>
                <div>{userProfile.joinDate}</div>
              </div>
            </div>
          </Col>

          {/* 右侧详细信息 */}
          <Col xs={24} sm={24} md={16} lg={17} xl={18}>
            <Card
              title={t('pages.key_26')}
              bordered={false}
              style={{ marginBottom: '24px' }}
              headStyle={{ borderBottom: '1px solid #f0f0f0', paddingLeft: 0 }}
              bodyStyle={{ paddingLeft: 0, paddingRight: 0 }}
            >
              <Row gutter={[32, 24]}>
                <Col span={8}>
                  <div>
                    <MailOutlined style={{ color: '#1677ff', marginRight: '8px' }} />
                    <Text type="secondary">邮箱：</Text>
                  </div>
                  <div style={{ marginTop: '4px' }}>{userProfile.email}</div>
                </Col>
                <Col span={8}>
                  <div>
                    <PhoneOutlined style={{ color: '#1677ff', marginRight: '8px' }} />
                    <Text type="secondary">电话：</Text>
                  </div>
                  <div style={{ marginTop: '4px' }}>{userProfile.phone}</div>
                </Col>
                <Col span={8}>
                  <div>
                    <EnvironmentOutlined style={{ color: '#1677ff', marginRight: '8px' }} />
                    <Text type="secondary">地点：</Text>
                  </div>
                  <div style={{ marginTop: '4px' }}>{userProfile.location}</div>
                </Col>
              </Row>
            </Card>

            <Card
              title={t('pages.key_27')}
              bordered={false}
              style={{ marginBottom: '24px' }}
              headStyle={{ borderBottom: '1px solid #f0f0f0', paddingLeft: 0 }}
              bodyStyle={{ paddingLeft: 0, paddingRight: 0 }}
            >
              <Paragraph style={{ fontSize: '14px', lineHeight: '1.8' }}>
                {userProfile.bio}
              </Paragraph>
            </Card>

            <Card
              title={t('pages.key_21')}
              bordered={false}
              headStyle={{ borderBottom: '1px solid #f0f0f0', paddingLeft: 0 }}
              bodyStyle={{ paddingLeft: 0, paddingRight: 0 }}
            >
              <Row gutter={[24, 16]}>
                {userProfile.skills.map((skill) => (
                  <Col span={12} key={skill.name}>
                    <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between' }}>
                      <Text>{skill.name}</Text>
                      <Text type="secondary">{skill.level}%</Text>
                    </div>
                    <Progress percent={skill.level} size="small" strokeColor={{
                      '0%': '#1677ff',
                      '100%': '#0958d9',
                    }} />
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default ProfileTab;
