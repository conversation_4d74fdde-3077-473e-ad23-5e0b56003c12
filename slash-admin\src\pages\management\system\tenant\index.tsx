import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Table, Button, Input, Space, Modal, Form, message, Popconfirm, Checkbox } from 'antd';
import { SearchOutlined, PlusOutlined, ReloadOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery, useMutation } from '@apollo/client';
import {
    TENANT_LIST,
    TENANT_DETAIL,
    CREATE_TENANT,
    UPDATE_TENANT,
    DELETE_TENANT
} from '@/api/graphql/tenant.graphql';

const pageSizeDefault = 10;

function stripTypename(obj: any): any {
    if (Array.isArray(obj)) {
        return obj.map(stripTypename);
    } else if (obj && typeof obj === 'object') {
        const newObj: any = {};
        for (const key in obj) {
            if (key !== '__typename') {
                newObj[key] = stripTypename(obj[key]);
            }
        }
        return newObj;
    }
    return obj;
}

export default function TenantManagement() {
    const { t } = useTranslation();
    const [searchText, setSearchText] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(pageSizeDefault);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingTenant, setEditingTenant] = useState<any>(null);
    const [form] = Form.useForm();
    const [showAccountFields, setShowAccountFields] = useState(true);
    const [deleteLoadingId, setDeleteLoadingId] = useState<string | null>(null);

    // 查询租户列表
    const { data, loading, refetch } = useQuery(TENANT_LIST, {
        variables: { search: searchText, page: currentPage, limit: pageSize },
        fetchPolicy: 'network-only',
    });

    // 编辑时拉取最新详情
    const { refetch: refetchDetail } = useQuery(TENANT_DETAIL, { skip: true });

    // 新建、编辑、删除租户
    const [createTenant, { loading: creating }] = useMutation(CREATE_TENANT, { onCompleted: () => refetch() });
    const [updateTenant, { loading: updating }] = useMutation(UPDATE_TENANT, { onCompleted: () => refetch() });
    const [deleteTenant, { loading: deleting }] = useMutation(DELETE_TENANT, { onCompleted: () => refetch() });

    // 打开新增/编辑弹窗
    const showModal = (tenant?: any) => {
        setEditingTenant(tenant || null);
        setModalVisible(true);
        if (tenant) {
            form.setFieldsValue({
                name: tenant.name || '',
                type: tenant.type || '',
            });
            console.log('tenant', tenant);
        } else {
            form.resetFields();
        }
    };

    // 提交表单
    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            console.log('表单提交内容:', values); // 调试name字段
            if (!values.name) {
                message.error(t('pages.key_1727'));
                return;
            }
            let submitValues = stripTypename({ ...values });
            // 过滤undefined，空字符串转null
            submitValues = Object.fromEntries(
                Object.entries(submitValues).map(([k, v]) => [k, v === '' ? null : v]).filter(([_, v]) => v !== undefined)
            );
            if (editingTenant) {
                await updateTenant({ variables: { input: { ...submitValues, id: editingTenant.id } } });
                message.success(t('pages.key_1730'));
            } else {
                await createTenant({ variables: { input: submitValues } });
                message.success(t('pages.key_1724'));
            }
            setModalVisible(false);
            setEditingTenant(null);
            form.resetFields();
        } catch (err: any) {
            console.error(t('pages.key_1728'), err);
            message.error(err?.message || t('pages.key_1060'));
        }
    };

    // 删除租户
    const handleDelete = async (id: string) => {
        setDeleteLoadingId(id);
        try {
            await deleteTenant({ variables: { id } });
            message.success(t('pages.key_1725'));
        } finally {
            setDeleteLoadingId(null);
        }
    };

    // 复选框变化时控制显示
    const handleCreateAccountChange = (e: any) => {
        setShowAccountFields(e.target.checked);
    };

    // 表格列
    const columns = [
        { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
        { title: t('pages.key_483'), dataIndex: 'name', key: 'name', width: 120 },
        { title: t('pages.key_1765'), dataIndex: 'type', key: 'type', width: 100 },
        { title: 'Logo', dataIndex: 'logo', key: 'logo', width: 120, render: (logo: string) => logo ? <img src={logo} alt="logo" style={{ width: 32, height: 32 }} /> : '-' },
        {
            title: t('pages.key_1058'),
            key: 'actions',
            width: 160,
            render: (_: any, record: any) => (
                <Space>
                    <Button icon={<EditOutlined />} size="small" onClick={() => showModal(record)} disabled={creating || updating}>{t('pages.key_1853')}</Button>
                    <Popconfirm title="确定删除此租户吗？" onConfirm={() => handleDelete(record.id)} okText="是" cancelText="否" okButtonProps={{ loading: deleteLoadingId === record.id }}>
                        <Button icon={<DeleteOutlined />} size="small" danger disabled={deleteLoadingId === record.id}>{t('pages.key_287')}</Button>
                    </Popconfirm>
                </Space>
            )
        }
    ];

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold">{t('pages.key_1731')}</h1>
                <Space>
                    <Button onClick={() => refetch()} icon={<ReloadOutlined />} disabled={loading}>{t('pages.key_310')}</Button>
                    <Button type="primary" icon={<PlusOutlined />} onClick={() => showModal()}>{t('pages.key_1151')}</Button>
                </Space>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
                <div className="mb-4">
                    <Space>
                        <Input
                            placeholder={t('pages.key_1050')}
                            prefix={<SearchOutlined />}
                            value={searchText}
                            onChange={e => setSearchText(e.target.value)}
                            onPressEnter={() => refetch()}
                            style={{ width: 200 }}
                        />
                        <Button type="primary" onClick={() => refetch()}>{t('pages.key_1044')}</Button>
                        <Button onClick={() => { setSearchText(''); refetch({ search: '', page: 1, limit: pageSize }); }}>{t('pages.key_2483')}</Button>
                    </Space>
                </div>
                <Table
                    columns={columns}
                    dataSource={data?.tenants?.nodes || []}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                        current: currentPage,
                        pageSize,
                        total: data?.tenants?.total || 0,
                        onChange: (page, pageSize) => {
                            setCurrentPage(page);
                            setPageSize(pageSize);
                            refetch({ search: searchText, page, limit: pageSize });
                        },
                        showSizeChanger: true,
                        showTotal: total => `共 ${total} 条记录`
                    }}
                />
            </div>
            <Modal
                title={editingTenant ? t('pages.key_1863') : t('pages.key_1151')}
                open={modalVisible}
                onOk={handleOk}
                onCancel={() => { setModalVisible(false); setEditingTenant(null); form.resetFields(); }}
                confirmLoading={creating || updating}
                destroyOnClose
            >
                <Form form={form} layout="vertical">
                    <Form.Item name="name" label={t('pages.key_1726')} rules={[{ required: true, message: t('pages.key_2220') }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="type" label={t('pages.key_1765')}>
                        <Input placeholder="small-tenant 或 big-tenant" />
                    </Form.Item>
                    <Form.Item name="createAccount" valuePropName="checked" initialValue={true} style={{ marginBottom: 0 }}>
                        <label style={{ marginRight: 8 }}>{t('pages.key_1157')}</label>
                        <Checkbox defaultChecked onChange={handleCreateAccountChange} />
                    </Form.Item>
                    {showAccountFields && <>
                        <Form.Item
                            name="accountUsername"
                            label={t('pages.key_1627')}
                            initialValue="admin"
                            rules={[
                                { required: true, message: t('pages.key_2216') },
                                { min: 3, max: 20, message: t('pages.key_1629') },
                                { pattern: /^[a-zA-Z0-9_]+$/, message: t('pages.key_1628') }
                            ]}
                        >
                            <Input placeholder={t('pages.key_2216')} />
                        </Form.Item>
                        <Form.Item
                            name="accountPassword"
                            label={t('pages.key_674')}
                            initialValue="123456"
                            rules={[
                                { required: true, message: t('pages.key_2193') },
                                { min: 6, message: t('pages.key_676') }
                            ]}
                        >
                            <Input.Password placeholder={t('pages.key_2193')} />
                        </Form.Item>
                    </>}
                </Form>
            </Modal>
        </div>
    );
} 