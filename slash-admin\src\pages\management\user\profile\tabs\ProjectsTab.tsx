import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Card, Table, Tag, Button, Space, Progress, Modal, Form, Input, Select, DatePicker, message } from 'antd';
import { PlusOutlined, EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';

const { t } = useTranslation();

// 模拟项目数据
const mockProjects = [
  {
    id: '1',
    name: t('pages.key_593'),
    type: t('pages.key_796'),
    status: 'active',
    progress: 75,
    startDate: '2025-05-01',
    endDate: '2025-08-31',
    team: '广告投放团队A',
  },
  {
    id: '2',
    name: t('pages.key_1132'),
    type: t('pages.key_670'),
    status: 'active',
    progress: 30,
    startDate: '2025-06-15',
    endDate: '2025-07-15',
    team: t('pages.key_277'),
  },
  {
    id: '3',
    name: t('pages.key_1631'),
    type: t('pages.key_1090'),
    status: 'active',
    progress: 50,
    startDate: '2025-04-01',
    endDate: '2025-06-30',
    team: t('pages.key_1091'),
  },
  {
    id: '4',
    name: '2024年度营销总结',
    type: t('pages.key_991'),
    status: 'completed',
    progress: 100,
    startDate: '2024-12-01',
    endDate: '2024-12-31',
    team: t('pages.key_394'),
  },
];

const ProjectsTab = () => {
  const [projects, setProjects] = useState(mockProjects);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<any>(null);
  const [form] = Form.useForm();

  const showCreateModal = () => {
    setEditingProject(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const showEditModal = (project: any) => {
    setEditingProject(project);
    form.setFieldsValue({
      ...project,
      dateRange: [dayjs(project.startDate), dayjs(project.endDate)],
    });
    setIsModalVisible(true);
  };

  const handleDeleteProject = (id: string) => {
    Modal.confirm({
      title: t('pages.key_1706'),
      content: '确定要删除这个项目吗？此操作不可恢复。',
      okText: t('pages.key_1703'),
      cancelText: t('pages.key_407'),
      onOk: () => {
        setProjects(projects.filter(project => project.id !== id));
        message.success(t('pages.key_2549'));
      },
    });
  };

  const handleSaveProject = (values: any) => {
    const [startDate, endDate] = values.dateRange.map((date: any) => date.format('YYYY-MM-DD'));
    const projectData = {
      ...values,
      startDate,
      endDate,
      progress: editingProject ? editingProject.progress : 0,
    };

    if (editingProject) {
      // 编辑现有项目
      setProjects(projects.map(project =>
        project.id === editingProject.id ? { ...project, ...projectData } : project
      ));
      message.success(t('pages.key_2550'));
    } else {
      // 创建新项目
      const newProject = {
        id: Date.now().toString(),
        ...projectData,
        status: 'active',
      };
      setProjects([...projects, newProject]);
      message.success(t('pages.key_2548'));
    }

    setIsModalVisible(false);
  };

  // 日期选择器的禁用日期逻辑
  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    return current && current < dayjs().startOf('day');
  };

  const columns = [
    {
      title: t('pages.key_2547'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('pages.key_1765'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        let color = 'blue';
        if (type === t('pages.key_796')) color = 'green';
        if (type === t('pages.key_670')) color = 'orange';
        if (type === t('pages.key_1090')) color = 'purple';
        if (type === t('pages.key_991')) color = 'cyan';
        return <Tag color={color}>{type}</Tag>;
      },
    },
    {
      title: t('pages.key_1602'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : status === 'completed' ? 'blue' : 'gray'}>
          {status === 'active' ? t('pages.key_2431') : status === 'completed' ? t('pages.key_740') : t('pages.key_1307')}
        </Tag>
      ),
    },
    {
      title: t('pages.key_2425'),
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => <Progress percent={progress} size="small" />,
    },
    {
      title: t('pages.key_1196'),
      key: 'dateRange',
      render: (_: any, record: any) => (
        <span>{record.startDate} 至 {record.endDate}</span>
      ),
    },
    {
      title: t('pages.key_517'),
      dataIndex: 'team',
      key: 'team',
    },
    {
      title: t('pages.key_1058'),
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button type="text" icon={<EyeOutlined />} />
          <Button type="text" icon={<EditOutlined />} onClick={() => showEditModal(record)} />
          <Button type="text" danger icon={<DeleteOutlined />} onClick={() => handleDeleteProject(record.id)} />
        </Space>
      ),
    },
  ];

  return (
    <div className="projects-tab">
      <Card
        title={t('pages.key_945')}
        extra={<Button type="primary" icon={<PlusOutlined />} onClick={showCreateModal}>{t('pages.key_265')}</Button>}
        bordered={false}
      >
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          pagination={{ pageSize: 5 }}
        />
      </Card>

      <Modal
        title={editingProject ? t('pages.key_1868') : t('pages.key_249')}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveProject}
        >
          <Form.Item
            name="name"
            label={t('pages.key_2547')}
            rules={[{ required: true, message: t('pages.key_2242') }]}
          >
            <Input placeholder={t('pages.key_2242')} />
          </Form.Item>
          <Form.Item
            name="type"
            label={t('pages.key_2553')}
            rules={[{ required: true, message: t('pages.key_2296') }]}
          >
            <Select placeholder={t('pages.key_2296')}>
              <Select.Option value={t('pages.key_796')}>{t('pages.key_796')}</Select.Option>
              <Select.Option value={t('pages.key_670')}>{t('pages.key_670')}</Select.Option>
              <Select.Option value={t('pages.key_1090')}>{t('pages.key_1090')}</Select.Option>
              <Select.Option value={t('pages.key_991')}>{t('pages.key_991')}</Select.Option>
              <Select.Option value={t('pages.key_193')}>{t('pages.key_193')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="dateRange"
            label={t('pages.key_2552')}
            rules={[{ required: true, message: t('pages.key_2295') }]}
          >
            <DatePicker.RangePicker
              style={{ width: '100%' }}
              disabledDate={disabledDate}
            />
          </Form.Item>
          <Form.Item
            name="team"
            label={t('pages.key_2319')}
            rules={[{ required: true, message: t('pages.key_2289') }]}
          >
            <Select placeholder={t('pages.key_2289')}>
              <Select.Option value="广告投放团队A">广告投放团队A</Select.Option>
              <Select.Option value={t('pages.key_277')}>{t('pages.key_277')}</Select.Option>
              <Select.Option value={t('pages.key_1091')}>{t('pages.key_1091')}</Select.Option>
              <Select.Option value={t('pages.key_394')}>{t('pages.key_394')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
              {editingProject ? t('pages.key_134') : t('pages.key_236')}
            </Button>
            <Button onClick={() => setIsModalVisible(false)}>{t('pages.key_407')}</Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectsTab;
