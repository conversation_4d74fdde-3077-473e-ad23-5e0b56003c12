/*
 * @Author: 潘孝权
 * @Date: 2025-05-16 13:06:10
 * @Description: 
 * @FilePath: \sking_frontend\ad_backend\src\modules\ip-restriction\ipRestriction.service.ts
 */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@dataui/crud-typeorm';
import { ipRestriction } from './ipRestriction.entity';
import { Repository } from 'typeorm';

// Service responsible to register (and decommission) employees
@Injectable()
export class ipRestrictionService extends TypeOrmCrudService<ipRestriction> {
	constructor(
		@InjectRepository(ipRestriction) readonly repo: Repository<ipRestriction>
	) {
		super(repo);
	}

	public async getActiveWithJobSearchCriteria(): Promise<ipRestriction[]> {
		return await this.repo.find({
			relations: [],
		});
	}

	/**
	 * 获取所有启用状态的IP限制记录
	 * @returns 启用状态的IP限制记录列表
	 */
	public async getEnabledIpRestrictions(): Promise<ipRestriction[]> {
		return await this.repo.find({
			where: { status: '正常' }
		});
	}

	async createWithTenant(data: Partial<ipRestriction>, tenantId: string) {
		return this.repo.save({ ...data, tenantId });
	}

	async updateWithTenant(id: number, data: Partial<ipRestriction>, tenantId: string) {
		return this.repo.update({ id, tenantId }, data);
	}
}
