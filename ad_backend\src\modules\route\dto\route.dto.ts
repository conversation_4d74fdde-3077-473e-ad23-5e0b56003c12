import { InputType, ObjectType, Field, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { IsString, IsOptional, IsEnum, IsBoolean, IsNumber, IsUUID } from 'class-validator';
import { FilterableField } from '@ptc-org/nestjs-query-graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@ObjectType('Route')
export class RouteDTO {
    @FilterableField(() => ID)
    @ApiProperty({ type: String })
    @IsUUID()
    id: string;

    @FilterableField(() => ID, { nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsOptional()
    @IsUUID()
    parentId?: string | null;

    @FilterableField()
    @ApiProperty({ type: String })
    @IsString()
    name: string;

    @FilterableField()
    @ApiProperty({ type: String })
    @IsString()
    path: string;

    @FilterableField({ nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsOptional()
    @IsString()
    component?: string;

    @FilterableField({ nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsOptional()
    @IsString()
    icon?: string;

    @FilterableField()
    @ApiProperty({ type: Number })
    @IsNumber()
    order: number;

    @FilterableField()
    @ApiProperty({ enum: ['menu', 'page'] })
    @IsEnum(['menu', 'page'])
    type: 'menu' | 'page';

    @FilterableField()
    @ApiProperty({ enum: ['enabled', 'disabled'] })
    @IsEnum(['enabled', 'disabled'])
    status: 'enabled' | 'disabled';

    @FilterableField()
    @ApiProperty({ type: Boolean })
    @IsBoolean()
    isHidden: boolean;

    @FilterableField({ nullable: true })
    @ApiPropertyOptional({ type: String })
    @IsOptional()
    @IsString()
    description?: string;

    @FilterableField(() => GraphQLISODateTime)
    @ApiProperty({ type: Date })
    createdAt: Date;

    @FilterableField(() => GraphQLISODateTime)
    @ApiProperty({ type: Date })
    updatedAt: Date;

    @Field(() => [RouteDTO], { nullable: true })
    @ApiPropertyOptional({ type: () => [RouteDTO] })
    children?: RouteDTO[];
}

@InputType('RouteInput')
export class RouteInput {
    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsUUID()
    parentId?: string | null;

    @Field()
    @IsString()
    name: string;

    @Field()
    @IsString()
    path: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    component?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    icon?: string;

    @Field()
    @IsNumber()
    order: number;

    @Field()
    @IsEnum(['menu', 'page'])
    type: 'menu' | 'page';

    @Field()
    @IsEnum(['enabled', 'disabled'])
    status: 'enabled' | 'disabled';

    @Field()
    @IsBoolean()
    isHidden: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    description?: string;
}

@InputType('CreateRouteInput')
export class CreateRouteInput {
    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsUUID()
    parentId?: string | null;

    @Field()
    @IsString()
    name: string;

    @Field()
    @IsString()
    path: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    component?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    icon?: string;

    @Field()
    @IsNumber()
    order: number;

    @Field()
    @IsEnum(['menu', 'page'])
    type: 'menu' | 'page';

    @Field()
    @IsEnum(['enabled', 'disabled'])
    status: 'enabled' | 'disabled';

    @Field()
    @IsBoolean()
    isHidden: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    description?: string;
}

@InputType('UpdateRouteInput')
export class UpdateRouteInput {
    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsUUID()
    parentId?: string | null;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    name?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    path?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    component?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    icon?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    order?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsEnum(['menu', 'page'])
    type?: 'menu' | 'page';

    @Field({ nullable: true })
    @IsOptional()
    @IsEnum(['enabled', 'disabled'])
    status?: 'enabled' | 'disabled';

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    isHidden?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    description?: string;
}

@InputType('UpdateOneRouteInput')
export class UpdateOneRouteInput {
    @Field(() => ID)
    @IsUUID()
    id: string;

    @Field(() => UpdateRouteInput)
    update: UpdateRouteInput;
} 