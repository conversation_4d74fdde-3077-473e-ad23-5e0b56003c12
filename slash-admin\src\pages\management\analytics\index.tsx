import { useTranslation } from 'react-i18next';
const { t } = useTranslation();
// React is automatically imported by the JSX transform
import { useState } from 'react';
import { Button, Modal, message, Table, Space, Card } from 'antd';
import { fetchTenantReport, exportReport } from '@/api/report';
import { useTenant } from '@/hooks/useTenant';
import type { ExportRisksResponse } from '@/types/api';

export default function Analytics() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);

  const handleFetchReport = async () => {
    setLoading(true);
    try {
      const type = "campaign";
      const date = "";
      const page = 1;
      const pageSize = 20;
      const res = await fetchTenantReport({ tenantId: useTenant() || '', type, date, page, pageSize });
      setData(res.data || []);
    } catch (e) {
      message.error("查询失败：" + (e?.message || t('pages.key_1312')));
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      const res = await exportReport({
        tenantId: useTenant() || '',
        type: 'analytics',
        date: new Date().toISOString(),
        limit: 1000
      });

      if ('data' in res) {
        const data = res.data as ExportRisksResponse;
        Modal.info({
          title: t('pages.key_693'),
          content: (
            <div>
              <div>导出成功，文件地址：{data.url || '无'}</div>
              {data.logs && data.logs.length > 0 && (
                <div>
                  <div>处理日志：</div>
                  <pre style={{ maxHeight: 200, overflow: 'auto', background: '#f8f8f8' }}>
                    {JSON.stringify(data.logs, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )
        });
      }
    } catch (error) {
      message.error(t('pages.key_686'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title={t('pages.key_1090')} bordered={false}>
      <Space style={{ marginBottom: 16 }}>
        <Button loading={loading} onClick={handleFetchReport}>{t('pages.key_1364')}</Button>
        <Button loading={loading} onClick={handleExport}>{t('pages.key_690')}</Button>
      </Space>
      <Table
        rowKey="id"
        columns={[
          { title: "对象ID", dataIndex: "objectId" },
          { title: t('pages.key_1765'), dataIndex: "type" },
          { title: t('pages.key_1188'), dataIndex: "date" },
          { title: t('pages.key_197'), dataIndex: "data" },
        ]}
        dataSource={data}
        loading={loading}
        pagination={{ pageSize: 20 }}
      />
    </Card>
  );
}
