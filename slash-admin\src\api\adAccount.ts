import request from '@/utils/request';
import { AdAccountParams, ListParams } from '../types/api';

export function syncAdAccounts({ tenantId, accessToken, accountId }: AdAccountParams) {
  return request.post('/ad-platform/facebook/sync', { tenantId, accessToken, accountId });
}

export function fetchAdAccountList({ tenantId, page, pageSize }: ListParams) {
  return request.get('/ad-platform/ad-account', { params: { tenantId, page, pageSize } });
}
