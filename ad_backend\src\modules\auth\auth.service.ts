import { Injectable, UnauthorizedException, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../user/user.service';
import { LoginInput, RegisterInput, LoginResponse, RegisterResponse, AuthUserResponse } from './dto/auth.dto';
import { UserStatus } from '../user/user.entity';
import { User } from '../user/user.entity';
import { plainToInstance } from 'class-transformer';
import { UserDTO } from '../user/dto/user.dto';

@Injectable()
export class AuthService {
    constructor(
        private readonly userService: UserService,
        private readonly jwtService: JwtService,
        private readonly configService: ConfigService
    ) { }

    async validateUser(username: string, password: string): Promise<User> {
        const user = await this.userService.findByUsername(username, undefined, false);
        if (!user) {
            throw new UnauthorizedException('用户名或密码不正确');
        }

        if (user.status !== UserStatus.ACTIVE) {
            throw new UnauthorizedException('用户账号未激活或已被禁用');
        }

        const isPasswordValid = await user.comparePassword(password);
        if (!isPasswordValid) {
            throw new UnauthorizedException('用户名或密码不正确');
        }

        return user;
    }

    createToken(user: User): string {
        const secret = this.configService.get('JWT_SECRET') || 'defaultSecretKey';
        const expiresIn = this.configService.get('JWT_EXPIRES_IN') || '24h';

        const roleNames = user.roles?.map(role => role.name) || [];
        const groupIds = user.groups?.map(group => group.id) || [];

        const payload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            roles: roleNames,
            groups: groupIds,
            tenantId: user.tenantId,
        };

        return this.jwtService.sign(payload, {
            secret,
            expiresIn
        });
    }

    async login(loginInput: LoginInput): Promise<LoginResponse> {
        const user = await this.validateUser(loginInput.username, loginInput.password);
        const accessToken = this.createToken(user);

        const roleNames = user.roles?.map(role => role.name) || [];

        return {
            userId: user.id,
            accessToken: accessToken,
            username: user.username,
            roles: roleNames
        };
    }

    async register(registerInput: RegisterInput): Promise<RegisterResponse> {
        // 检查用户名是否已存在
        const usernameExists = await this.userService.findByUsername(registerInput.username, undefined, false);
        if (usernameExists) {
            throw new ConflictException('用户名已存在');
        }

        // 检查邮箱是否已存在
        const emailExists = await this.userService.findByEmail(registerInput.email);
        if (emailExists) {
            throw new ConflictException('邮箱已存在');
        }

        // 创建用户
        const user = await this.userService.create({
            ...registerInput,
            status: UserStatus.ACTIVE,
            tenantId: registerInput.tenantId,
        });

        const accessToken = this.createToken(user);

        return {
            userId: user.id,
            accessToken: accessToken,
            username: user.username,
        };
    }

    async getAuthUser(userId: string): Promise<AuthUserResponse> {
        const user = await this.userService.findOne(userId, undefined, false);
        const accessToken = this.createToken(user);

        return {
            user: plainToInstance(UserDTO, user),
            accessToken,
        };
    }
} 