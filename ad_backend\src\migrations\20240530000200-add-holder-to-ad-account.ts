import { MigrationInterface, QueryRunner } from "typeorm";

export class AddHolderToAdAccount20240530000200 implements MigrationInterface {
    name = 'AddHolderToAdAccount20240530000200'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "holder" character varying(128);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "holder";`);
    }
} 