import { Injectable, BadRequestException } from '@nestjs/common';
import { TypeOrmCrudService } from '@dataui/crud-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Group } from './group.entity';
import { Repository } from 'typeorm';
import { GroupStatus } from './dto/group.dto';

@Injectable()
export class GroupService extends TypeOrmCrudService<Group> {
    constructor(
        @InjectRepository(Group) readonly repo: Repository<Group>
    ) {
        super(repo);
    }

    public async getActiveGroups(tenantId?: string): Promise<Group[]> {
        if (!tenantId) throw new BadRequestException('tenantId required');
        return await this.repo.find({
            where: {
                status: GroupStatus.ACTIVE,
                tenantId
            },
            relations: [],
        });
    }
} 