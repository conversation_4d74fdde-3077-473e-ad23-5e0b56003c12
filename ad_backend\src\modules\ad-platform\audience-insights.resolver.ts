import { Resolver, Query, Args, ObjectType, Field, Int, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Audience } from './entities/audience.entity';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';
import { UseGuards } from '@nestjs/common';

@ObjectType()
class DistributionItem {
    @Field() name: string;
    @Field(() => Int) value: number;
}

@ObjectType()
class AudienceInsightsDTO {
    @Field(() => [DistributionItem]) ageDistribution: DistributionItem[];
    @Field(() => [DistributionItem]) genderDistribution: DistributionItem[];
    @Field(() => [DistributionItem]) locationDistribution: DistributionItem[];
    @Field(() => [DistributionItem]) interestDistribution: DistributionItem[];
}

function ageOverlap(aMin: number | undefined, aMax: number | undefined, bMin: number, bMax: number) {
    // 判断[aMin,aMax]与[bMin,bMax]是否有交集，空视为全区间
    const min = aMin ?? 0;
    const max = aMax ?? 200;
    return min <= bMax && max >= bMin;
}

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => AudienceInsightsDTO)
export class AudienceInsightsResolver {
    constructor(
        @InjectRepository(Audience)
        private readonly audienceRepo: Repository<Audience>,
    ) { }

    @Query(() => AudienceInsightsDTO)
    async audienceInsights(
        @Args('startDate') startDate: String,
        @Args('endDate') endDate: String,
        @Context() ctx: any,
    ): Promise<AudienceInsightsDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? {} : { tenant: { id: tenantId } };
        const audiences = await this.audienceRepo.find({ where });
        // 年龄分布（区间重叠）
        const ageBuckets = [
            { name: '18-24岁', min: 18, max: 24 },
            { name: '25-34岁', min: 25, max: 34 },
            { name: '35-44岁', min: 35, max: 44 },
            { name: '45-54岁', min: 45, max: 54 },
            { name: '55岁以上', min: 55, max: 200 },
        ];
        const ageDistribution = ageBuckets.map(bucket => ({
            name: bucket.name,
            value: audiences.filter(a => ageOverlap(a.age_min, a.age_max, bucket.min, bucket.max)).length,
        }));
        // 性别分布
        const genderDistribution = [
            { name: '女性', value: audiences.filter(a => a.gender === 2).length },
            { name: '男性', value: audiences.filter(a => a.gender === 1).length },
            { name: '不限', value: audiences.filter(a => !a.gender || a.gender === 0).length },
        ];
        // 地域分布（优先城市，补充区域/国家）
        const cityMap = new Map<string, number>();
        const regionMap = new Map<string, number>();
        const countryMap = new Map<string, number>();
        for (const a of audiences) {
            const geo = a.geo_locations || {};
            if (Array.isArray(geo.cities) && geo.cities.length > 0) {
                for (const city of geo.cities) {
                    if (city?.name) cityMap.set(city.name, (cityMap.get(city.name) || 0) + 1);
                }
            } else if (Array.isArray(geo.regions) && geo.regions.length > 0) {
                for (const region of geo.regions) {
                    if (region?.name) regionMap.set(region.name, (regionMap.get(region.name) || 0) + 1);
                }
            } else if (Array.isArray(geo.countries) && geo.countries.length > 0) {
                for (const country of geo.countries) {
                    countryMap.set(country, (countryMap.get(country) || 0) + 1);
                }
            }
        }
        let locationDistribution: DistributionItem[] = [];
        if (cityMap.size > 0) {
            locationDistribution = Array.from(cityMap.entries()).map(([name, value]) => ({ name, value }));
        } else if (regionMap.size > 0) {
            locationDistribution = Array.from(regionMap.entries()).map(([name, value]) => ({ name, value }));
        } else {
            locationDistribution = Array.from(countryMap.entries()).map(([name, value]) => ({ name, value }));
        }
        // 兴趣分布
        const interestMap = new Map<string, number>();
        for (const a of audiences) {
            const interests = Array.isArray(a.interests) ? a.interests : [];
            for (const i of interests) {
                if (i?.name) interestMap.set(i.name, (interestMap.get(i.name) || 0) + 1);
            }
        }
        const interestDistribution = Array.from(interestMap.entries()).map(([name, value]) => ({ name, value }));
        return { ageDistribution, genderDistribution, locationDistribution, interestDistribution };
    }
} 