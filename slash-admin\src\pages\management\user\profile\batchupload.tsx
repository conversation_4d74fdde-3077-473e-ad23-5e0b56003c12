import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import {
  Button,
  Modal,
  Upload,
  Table,
  message,
  UploadProps,
  UploadFile,
  Select,
} from 'antd';
import * as XLSX from 'xlsx';
import JSZip from 'jszip';
import { InboxOutlined } from '@ant-design/icons';
import { useTenant } from '@/context/tenant';
import { uploadToCloudflare } from '@/utils/upload';
import { useMutation, useQuery } from '@apollo/client';
import { CREATE_MANY_MATERIALMANAGEMENT_MUTATION } from '@/api/graphql/materialManagement.graphql';
import { toast } from 'sonner';
import { GET_AD_ACCOUNT_LIST } from '@/api/adAccount.graphql';
import facebookService from "@/api/facebook/index";
import { useLocalStorage } from 'react-use';
const { Dragger } = Upload;

// 类型定义
interface ExcelRecord {
  id: number;
  名称: string;
  标签: string;
  备注: string;
}

interface ZipMediaFile {
  filename: string;
  id: string;
  content: Blob; // 存储文件内容
}

type MatchedRecord = Omit<ExcelRecord, 'id'> & {
  id: number | string;
  mediaFilename: string;
};

interface UploadedFile {
  id: string;
  url: {};
  fileUrl: string;
}

const BatchUploader = ({ initData }: { initData: any }) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [excelData, setExcelData] = useState<ExcelRecord[]>([]);
  const [zipFiles, setZipFiles] = useState<ZipMediaFile[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [excelFile, setExcelFile] = useState<File | null>(null);
  const [zipFile, setZipFile] = useState<File | null>(null);
  const [account, setAccount] = useState(undefined)
  const [userInfo] = useLocalStorage<any>('userStore')
  const tenantId = userInfo?.state?.userInfo?.id;
  const showModal = () => setIsModalVisible(true);
  const handleCancel = () => {
    setIsModalVisible(false);
    setExcelFile(null);
    setZipFile(null);
    setAccount(undefined)
    setExcelData([]);
    setZipFiles([]);
  };

  const { data: accountData } = useQuery(GET_AD_ACCOUNT_LIST, {
    variables: {
      filter: {},
    }
  });

  const [createMany] = useMutation(CREATE_MANY_MATERIALMANAGEMENT_MUTATION);

  const beforeUpload = (file: File) => {
    if (file.type === 'application/zip' ||
      file.type === 'application/x-zip-compressed' ||
      file.name.endsWith('.zip')) {
      setZipFile(file);
    } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
      setExcelFile(file);
    } else {
      message.error(t('pages.key_432'));
      return false;
    }
    return false;
  };

  const handleParse = async () => {
    if (!excelFile || !zipFile) {
      message.warning(t('pages.key_2144'));
      return;
    }

    setLoading(true);

    try {
      const parsedExcel = await parseExcel(excelFile);
      setExcelData(parsedExcel);

      const parsedZip = await parseZIP(zipFile);
      setZipFiles(parsedZip);
    } catch (e) {
      console.error(e);
      message.error(t('pages.key_2092'));
    }

    setLoading(false);
  };

  const parseExcel = (file: File): Promise<ExcelRecord[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const json: ExcelRecord[] = XLSX.utils.sheet_to_json(sheet);

        const requiredFields = ['id', t('pages.key_483'), t('pages.key_1373'), t('pages.key_588')];
        const keys = Object.keys(json[0] || {});
        if (!requiredFields.every((f) => keys.includes(f))) {
          reject(new Error('Excel 缺少必要字段'));
          return;
        }

        resolve(json);
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file);
    });
  };

  const parseZIP = (file: File): Promise<ZipMediaFile[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const content = e.target?.result;
          const zip = await JSZip.loadAsync(content as any);

          const fileEntries = Object.entries(zip.files); // 获取所有文件及其路径

          const result: ZipMediaFile[] = [];

          for (const [path, fileEntry] of fileEntries) {
            const entry = fileEntry as JSZip.JSZipObject;
            if (!entry.dir) { // 忽略文件夹条目
              const parts = path.split('/'); // 按路径分割
              const fileName = parts[parts.length - 1]; // 提取文件名
              const idPart = fileName.split('.')[0]; // 提取 ID

              const isMatch = /^\d+$/.test(idPart.trim());
              console.log('path:', path, 'idPart:', idPart, 'isMatch:', isMatch);

              if (isMatch && entry) {
                const blob = await entry.async('blob'); // 获取文件内容
                result.push({
                  filename: path,
                  id: idPart,
                  content: blob,
                });
              }
            }
          }

          console.log('result:', result); // 检查最终结果
          resolve(result);
        } catch (err) {
          console.error('解析 ZIP 失败:', err);
          reject(err);
        }
      };

      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file); // 使用 Array Buffer 更稳定
    });
  };
  function getMimeTypeFromFilename(filename: string): string {
    const ext = filename.slice(filename.lastIndexOf('.')).toLowerCase();
    switch (ext) {
      case '.mp4': return 'video/mp4';
      case '.webm': return 'video/webm';
      case '.mov': return 'video/quicktime';
      case '.avi': return 'video/x-msvideo';
      case '.ogg': return 'video/ogg';
      default: return '';
    }
  }
  const blobToFile = (blob: Blob, filename: string): File => {
    const mimeType = getMimeTypeFromFilename(filename);
    return new File([blob], filename, {
      type: mimeType,
      lastModified: Date.now()
    });
  };
  const uploadAllFiles = async (
    files: ZipMediaFile[],
    tenantId: string,
    folder: string
  ): Promise<any> => {
    const uploadPromises = files.map(async (file) => {
      try {
        // 将 Blob 转换为 File
        const fileToUpload = blobToFile(file.content, file.filename);
        // 假设你的上传方法接受 File 类型
        // const url = await uploadToCloudflare(fileToUpload, tenantId, folder);
        return Promise.all([facebookService.uploadToFaccebook(fileToUpload, file.filename, (account as unknown as string)), uploadToCloudflare(fileToUpload, tenantId, folder)]).then(res => {
          const fbUploadData = res[0]
          const cfUploadData = res[1]
          let params = { id: file.id } as any;
          if (fbUploadData.success && cfUploadData.success) {
            params = { ...params, ...cfUploadData }
            if (fileToUpload.type.startsWith('video/')) {
              // 是视频文件
              params.facebookVedioId = fbUploadData?.data?.id
            } else {
              // 不是视频文件 或者 type 无法识别（如某些 .mov 文件）
              params.facebookImageId = (Object.values(fbUploadData['data']['images'])[0] as any)?.hash
            }
            return params;
          }
        })
      } catch (error) {
        console.error(`上传失败: ${file.filename}`, error);
        return { id: file.id, url: null }; // 可选处理失败情况
      }
    });

    const results = await Promise.all(uploadPromises);
    return results.filter((item: any) => item?.fileUrl); // 过滤掉上传失败的
  };
  const batchupload = async () => {
    setLoading(true)
    const uploadList = await uploadAllFiles(zipFiles, tenantId as string, '')
    const finalList = excelData.map(row => {
      const match = uploadList.find((item: any) => item.id === String(row.id));
      return {
        ...row,
        fileUrl: match?.fileUrl || null,
        facebookVedioId: match?.facebookVedioId,
        facebookImageId: match?.facebookImageId
      };
    }).filter(item => item.fileUrl);
    const fieldMapping = {
      '名称': 'fileName',
      '标签': 'tags',
      '备注': 'notes',
      'fileUrl': 'file',
      'tenantId': 'tenantId',
      'facebookImageId': 'facebookImageId',
      'facebookVedioId': 'facebookVedioId',
    }
    // 转换单个对象字段
    const mapFields = (record: any) => {
      const result: Record<string, any> = {};
      for (const key in fieldMapping) {
        if (record.hasOwnProperty(key)) {
          const newKey = fieldMapping[key as keyof typeof fieldMapping];
          if (record[key]) {
            result[newKey] = record[key];
          }
        }
      }
      return result;
    };

    // 对整个数组应用转换
    const transformedData = finalList.map(mapFields);
    const data = await createMany({
      variables: {
        input: {
          materialManagements: transformedData
        }
      }
    });
    if (data?.data?.createManyMaterialManagements?.length) {
      setLoading(false)
      toast.success(t('pages.key_1149'));
      initData()
      handleCancel()
    }
  }

  const matchData = (): MatchedRecord[] => {
    return excelData.map((row) => {
      const idStr = row.id?.toString(); // 防止 undefined
      const media = zipFiles.find((f) => f.id === idStr);
      return {
        ...row,
        mediaFilename: media ? media.filename : t('pages.key_1174'),
      };
    });
  };

  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '名称', dataIndex: '名称', key: '名称' },
    { title: '标签', dataIndex: '标签', key: '标签' },
    { title: '备注', dataIndex: '备注', key: '备注' },
    { title: t('pages.key_624'), dataIndex: 'mediaFilename', key: 'mediaFilename' },
  ];

  const downLoad = () => {
    const link = document.createElement('a');
    link.href = '/template.xlsx'; // public 下的文件
    link.download = '模板.xlsx';   // 自定义下载文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  return (
    <>
      {/* 主页面按钮 */}
      <Button type="primary" onClick={showModal}>{t('pages.key_984')}</Button>

      {/* 模态框内容 */}
      <Modal
        destroyOnHidden={true}
        title={t('pages.key_984')}
        visible={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>{t('pages.key_407')}</Button>,
          matchData()?.length ? (
            <Button
              key="confirm"
              type="primary"
              onClick={batchupload}
              loading={loading}
              disabled={!excelFile || !zipFile}
            >{t('pages.key_2')}</Button>
          ) : (
            <Button
              key="confirm"
              type="primary"
              onClick={handleParse}
              loading={loading}
              disabled={!excelFile || !zipFile || !account}
            >{t('pages.key_2088')}</Button>
          )
        ]}
        width={800}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>{t('pages.key_2447')}</div>
          <Select
            fieldNames={{ label: 'account', value: 'accountId' }}
            style={{ width: '100%' }}
            placeholder={t('pages.key_2248')}
            value={account}
            onChange={(value) => setAccount(value)}
            options={accountData?.adAccounts || []}
          />
        </div>
        <div style={{ marginBottom: 20 }}>
          <span>文本上传：</span>
          <Upload
            accept=".xlsx,.xls"
            beforeUpload={beforeUpload}
            showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
            maxCount={1}
          >
            <Button>{t('pages.key_2450')}</Button>
          </Upload>
          <p onClick={downLoad} style={{ cursor: 'pointer', fontSize: 12, color: '#999' }}>{t('pages.key_1453')}</p>
        </div>

        <div style={{ marginBottom: 20 }}>
          <span>文件上传：</span>
          <Upload
            accept=".zip"
            beforeUpload={beforeUpload}
            showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
            maxCount={1}
          >
            <Button>{t('pages.key_2449')}</Button>
          </Upload>
          <p style={{ fontSize: 12, color: '#999' }}>{t('pages.key_2145')}</p>
        </div>

        {excelData.length > 0 && (
          <div style={{ marginTop: 30 }}>
            <h4>Excel 数据与 ZIP 文件匹配结果</h4>
            <Table dataSource={matchData()} columns={columns} rowKey="id" />
          </div>
        )}
      </Modal>
    </>
  );
};

export default BatchUploader;