import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Base } from '../core/entities/base.entity';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';

@ObjectType()
@Entity('tenant')
export class Tenant extends Base {
	// The name or title of the tenant organization.
	@ApiProperty({ type: () => String })
	@Index()
	@Column()
	@Field()
	name: string;

	// The logo of the tenant organization.
	@ApiPropertyOptional({ type: () => String })
	@Column({ nullable: true })
	@Field({ nullable: true })
	logo: string;

	// 租户类型（big-tenant=分库，small-tenant=表隔离）
	@ApiPropertyOptional({ type: () => String, description: '租户类型（big-tenant=分库，small-tenant=表隔离）' })
	@Column({ length: 32, default: 'small-tenant' })
	@Field({ nullable: true })
	type: string;

	// 分库连接信息（仅大客户用，JSON）
	@ApiPropertyOptional({ type: () => Object, description: '分库连接信息，仅大客户用' })
	@Column({ type: 'jsonb', nullable: true })
	@Field(() => GraphQLJSON, { nullable: true })
	db_config?: any;

	// 同步计划配置（JSON）
	@ApiPropertyOptional({ type: () => Object, description: '同步计划配置' })
	@Column({ type: 'jsonb', nullable: true })
	@Field(() => GraphQLJSON, { nullable: true })
	sync_config?: any;

	// 套餐类型（如 BASIC、STANDARD、ENTERPRISE）
	@ApiPropertyOptional({ type: () => String, description: '套餐类型（如 BASIC、STANDARD、ENTERPRISE）' })
	@Column({ length: 32, default: 'BASIC' })
	@Field({ nullable: true })
	plan: string;

	// 功能与配额配置（JSON）
	@ApiPropertyOptional({ type: () => Object, description: '功能与配额配置' })
	@Column({ type: 'jsonb', nullable: true })
	@Field(() => GraphQLJSON, { nullable: true })
	features?: any;

	// Indicates if tenant is active (for soft delete/disable)
	@ApiPropertyOptional({ type: Boolean, default: true })
	@Column({ nullable: false, default: true })
	isActive: boolean;
}
