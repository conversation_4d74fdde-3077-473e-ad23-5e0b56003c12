import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SeedModule } from './seed.module';
import { SeedDataService } from './seed.service';

const log = new Logger('Seed');

const runSeed = async () => {
	const app = await NestFactory.create(SeedModule);
	const seedDataService = app.get(SeedDataService);

	await seedDataService.executeSeed(true);
	await app.close();
};

runSeed()
	.then(() => {
		log.debug('Seed completed!');
	})
	.catch((error) => {
		log.error('Seed failed!', error);
		throw error;
	});
