import { useTranslation } from 'react-i18next';
const { t } = useTranslation();
import { useState } from "react";
import { Card, Tabs, Table, Button, Tag, Space, Modal, Input, Form, Select, Switch, Statistic, Row, Col, Tooltip, Badge, Divider, Alert, Progress, Timeline, Descriptions, message } from "antd";
import { syncRisks, exportRisks } from '@/api/risks';
import { useTenant } from '@/hooks/useTenant';
import { fetchTenantReport, exportReport } from '@/api/report';
import { ExclamationCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, WarningOutlined, BellOutlined, SafetyCertificateOutlined, SecurityScanOutlined, SettingOutlined, SyncOutlined, EyeOutlined, FilterOutlined, SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, LineChartOutlined, DownloadOutlined } from "@ant-design/icons";
import type { TabsProps } from "antd";
import type { RiskItem } from "@/types/ad";
import type { FC } from "react";
import { SyncRisksParams, ExportRisksParams, ExportRisksResponse } from '@/types/api';

// 风险等级映射
const riskLevelMap = {
    high: { color: 'red', label: t('pages.key_2615'), icon: <ExclamationCircleOutlined /> },
    medium: { color: 'orange', label: t('pages.key_45'), icon: <WarningOutlined /> },
    low: { color: 'green', label: t('pages.key_104'), icon: <CheckCircleOutlined /> }
};

// 风险状态映射
const riskStatusMap = {
    active: { color: 'red', label: t('pages.key_1507') },
    processing: { color: 'orange', label: t('pages.key_568') },
    resolved: { color: 'green', label: t('pages.key_750') }
};

const RiskControlPage: FC = () => {
    const tenantId = useTenant();
    const [activeTab, setActiveTab] = useState("dashboard");
    const [loading, setLoading] = useState(false);
    const [risks, setRisks] = useState<RiskItem[]>([]);
    const [form] = Form.useForm();

    // 同步风险数据
    const handleSync = async () => {
        try {
            const params: SyncRisksParams = { tenantId: tenantId || '' };
            const result = await syncRisks(params);
            message.success(t('pages.key_2582'));
            // TODO: 刷新数据
        } catch (error) {
            message.error('风险数据同步失败');
        } finally {
            setLoading(false);
        }
    };

    // 导出风险数据
    const handleExport = async () => {
        try {
            const params: ExportRisksParams = { tenantId: tenantId || '' };
            const result = await exportRisks(params);
            if ('data' in result && result.data?.url) {
                window.open(result.data.url, '_blank');
                message.success(t('pages.key_2584'));
            }
        } catch (error) {
            message.error(t('pages.key_2583'));
        } finally {
            setLoading(false);
        }
    };

    // 表格列配置
    const columns = [
        {
            title: '风险ID',
            dataIndex: 'id',
            key: 'id',
            width: 100,
        },
        {
            title: t('pages.key_2588'),
            dataIndex: 'type',
            key: 'type',
            width: 150,
        },
        {
            title: t('pages.key_2586'),
            dataIndex: 'level',
            key: 'level',
            width: 120,
            render: (level: keyof typeof riskLevelMap) => (
                <Tag color={riskLevelMap[level].color} icon={riskLevelMap[level].icon}>
                    {riskLevelMap[level].label}
                </Tag>
            ),
        },
        {
            title: t('pages.key_1027'),
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
        },
        {
            title: t('pages.key_1602'),
            dataIndex: 'status',
            key: 'status',
            width: 120,
            render: (status: keyof typeof riskStatusMap) => (
                <Tag color={riskStatusMap[status].color}>
                    {riskStatusMap[status].label}
                </Tag>
            ),
        },
        {
            title: t('pages.key_1058'),
            key: 'action',
            width: 200,
            render: (_: any, record: RiskItem) => (
                <Space size="middle">
                    <Button type="link" icon={<EyeOutlined />}>{t('pages.key_1346')}</Button>
                    <Button type="link" icon={<EditOutlined />}>{t('pages.key_565')}</Button>
                </Space>
            ),
        },
    ];

    // 标签页配置
    const items: TabsProps['items'] = [
        {
            key: 'dashboard',
            label: t('pages.key_2575'),
            children: (
                <div>
                    <Row gutter={[16, 16]} className="mb-4">
                        <Col span={6}>
                            <Card>
                                <Statistic
                                    title={t('pages.key_928')}
                                    value={risks.length}
                                    prefix={<SecurityScanOutlined />}
                                />
                            </Card>
                        </Col>
                        <Col span={6}>
                            <Card>
                                <Statistic
                                    title={t('pages.key_2615')}
                                    value={risks.filter(r => r.level === 'high').length}
                                    valueStyle={{ color: '#cf1322' }}
                                    prefix={<ExclamationCircleOutlined />}
                                />
                            </Card>
                        </Col>
                        <Col span={6}>
                            <Card>
                                <Statistic
                                    title={t('pages.key_45')}
                                    value={risks.filter(r => r.level === 'medium').length}
                                    valueStyle={{ color: '#faad14' }}
                                    prefix={<WarningOutlined />}
                                />
                            </Card>
                        </Col>
                        <Col span={6}>
                            <Card>
                                <Statistic
                                    title={t('pages.key_104')}
                                    value={risks.filter(r => r.level === 'low').length}
                                    valueStyle={{ color: '#52c41a' }}
                                    prefix={<CheckCircleOutlined />}
                                />
                            </Card>
                        </Col>
                    </Row>
                    <Card>
                        <Alert
                            message={t('pages.key_1781')}
                            description="系统检测到3个高风险项目需要立即处理，请及时查看并采取相应措施。"
                            type="warning"
                            showIcon
                            className="mb-4"
                        />
                        <Table
                            columns={columns}
                            dataSource={risks}
                            loading={loading}
                            rowKey="id"
                            pagination={{
                                total: risks.length,
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                            }}
                        />
                    </Card>
                </div>
            ),
        },
        {
            key: 'rules',
            label: t('pages.key_2577'),
            children: (
                <Card>
                    <div className="mb-4">
                        <Space>
                            <Button type="primary" icon={<PlusOutlined />}>{t('pages.key_1155')}</Button>
                            <Button icon={<SyncOutlined />} onClick={handleSync} loading={loading}>{t('pages.key_481')}</Button>
                            <Button icon={<FilterOutlined />}>{t('pages.key_1759')}</Button>
                        </Space>
                    </div>
                    {/* TODO: 实现风控规则表格 */}
                </Card>
            ),
        },
    ];

    return (
        <div className="p-6">
            <Card>
                <Tabs
                    activeKey={activeTab}
                    items={items}
                    onChange={setActiveTab}
                    tabBarExtraContent={
                        <Space>
                            <Button icon={<SyncOutlined />} onClick={handleSync} loading={loading}>{t('pages.key_478')}</Button>
                            <Button icon={<DownloadOutlined />} onClick={handleExport} loading={loading}>{t('pages.key_691')}</Button>
                        </Space>
                    }
                />
            </Card>
        </div>
    );
};

export default RiskControlPage;