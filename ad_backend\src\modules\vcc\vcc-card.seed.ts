import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VccCard } from './entities/vcc-card.entity';
import { Tenant } from '../../entity/tenant.entity';

@Injectable()
export class VccCardSeed {
    constructor(
        @InjectRepository(VccCard)
        private readonly vccCardRepo: Repository<VccCard>,
        @InjectRepository(Tenant)
        private readonly tenantRepo: Repository<Tenant>,
    ) { }

    async seed() {
        // 获取第一个租户作为测试数据的所有者
        const tenant = await this.tenantRepo.findOne({
            order: { createdAt: 'ASC' }
        });

        if (!tenant) {
            console.log('没有找到租户，跳过VCC卡片种子数据创建');
            return;
        }

        // 检查是否已经有测试数据
        const existingCount = await this.vccCardRepo.count({
            where: { tenant: { id: tenant.id } }
        });

        if (existingCount > 0) {
            console.log('VCC卡片测试数据已存在，跳过种子数据创建');
            return;
        }

        // 创建测试VCC卡片数据
        const testCards = [
            {
                channel: 'Visa',
                country: '美国',
                cardHolder: '测试持卡人001',
                countryCode: 'US',
                cardNumber: '****************',
                balance: 1000.00,
                consumption: 100.50,
                boundAdAccount: 'FB账号001, Google账号001',
                group: '测试组A',
                transactionCount: 5,
                adAccountStatus: 2,
                expiryMonth: '12',
                expiryYear: '2025',
                cvv: '123',
                zipCode: '10001',
                usedCount: 1,
                bindCount: 2,
                totalAdAccounts: 2,
                limitCount: 10,
                status: '已使用',
                remark: '测试VCC卡片001',
                tenant,
                tenantId: tenant.id
            },
            {
                channel: 'Mastercard',
                country: '加拿大',
                cardHolder: '测试持卡人002',
                countryCode: 'CA',
                cardNumber: '****************',
                balance: 2000.00,
                consumption: 0,
                boundAdAccount: null,
                group: '测试组B',
                transactionCount: 0,
                adAccountStatus: 0,
                expiryMonth: '06',
                expiryYear: '2026',
                cvv: '456',
                zipCode: 'M5V 3A8',
                usedCount: 0,
                bindCount: 0,
                totalAdAccounts: 0,
                limitCount: 15,
                status: '未使用',
                remark: '测试VCC卡片002',
                tenant,
                tenantId: tenant.id
            },
            {
                channel: 'American Express',
                country: '英国',
                cardHolder: '测试持卡人003',
                countryCode: 'GB',
                cardNumber: '***************',
                balance: 500.00,
                consumption: 50.00,
                boundAdAccount: 'TikTok账号001',
                group: '测试组C',
                transactionCount: 2,
                adAccountStatus: 1,
                expiryMonth: '03',
                expiryYear: '2027',
                cvv: '789',
                zipCode: 'SW1A 1AA',
                usedCount: 1,
                bindCount: 1,
                totalAdAccounts: 1,
                limitCount: 5,
                status: '已使用',
                remark: '测试VCC卡片003',
                tenant,
                tenantId: tenant.id
            }
        ];

        // 创建并保存测试数据
        const cards = testCards.map(cardData => this.vccCardRepo.create(cardData));
        await this.vccCardRepo.save(cards);

        console.log(`成功创建 ${cards.length} 条VCC卡片测试数据`);
    }
} 