import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class AutoOptimizationDTO {
    @Field()
    status: string;

    @Field(() => Int)
    progress: number;

    @Field()
    lastOptimized: string;

    @Field(() => Int)
    recommendations: number;

    @Field({ nullable: true })
    potentialSavings?: string;

    @Field({ nullable: true })
    potentialImprovement?: string;

    @Field({ nullable: true })
    potentialReach?: string;
} 