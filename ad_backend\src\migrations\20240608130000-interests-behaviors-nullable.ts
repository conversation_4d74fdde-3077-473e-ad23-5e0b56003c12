import { MigrationInterface, QueryRunner } from "typeorm";

export class InterestsBehaviorsNullable20240608130000 implements MigrationInterface {
    name = 'InterestsBehaviorsNullable20240608130000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "interests" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "behaviors" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "interests" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "behaviors" SET NOT NULL`);
    }
} 