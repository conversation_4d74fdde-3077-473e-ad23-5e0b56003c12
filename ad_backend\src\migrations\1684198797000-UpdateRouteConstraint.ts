import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateRouteConstraint1684198797000 implements MigrationInterface {
    name = 'UpdateRouteConstraint1684198797000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 先删除现有的外键约束
        await queryRunner.query(`ALTER TABLE "system_route" DROP CONSTRAINT "FK_8ae098f794434a807301deac360"`);

        // 重新创建带有CASCADE的外键约束
        await queryRunner.query(`ALTER TABLE "system_route" ADD CONSTRAINT "FK_8ae098f794434a807301deac360" 
            FOREIGN KEY ("parentId") REFERENCES "system_route"("id") ON DELETE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回滚时，先删除CASCADE约束
        await queryRunner.query(`ALTER TABLE "system_route" DROP CONSTRAINT "FK_8ae098f794434a807301deac360"`);

        // 重新创建原来的外键约束
        await queryRunner.query(`ALTER TABLE "system_route" ADD CONSTRAINT "FK_8ae098f794434a807301deac360" 
            FOREIGN KEY ("parentId") REFERENCES "system_route"("id")`);
    }
} 