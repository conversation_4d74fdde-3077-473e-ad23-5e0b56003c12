{"sys": {"api": {"apiRequestFailed": "The interface request failed, please try again later!", "apiTimeoutMessage": "The interface request timed out, please refresh the page and try again!", "errMsg401": "The user does not have permission (token, user name, password error)!", "errMsg403": "The user is authorized, but access is forbidden!", "errMsg404": "Network request error, the resource was not found!", "errMsg405": "Network request error, request method not allowed!", "errMsg408": "Network request timed out!", "errMsg500": "Server error, please contact the administrator!", "errMsg501": "The network is not implemented!", "errMsg502": "Network Error!", "errMsg503": "The service is unavailable, the server is temporarily overloaded or maintained!", "errMsg504": "Network timeout!", "errMsg505": "The http version does not support the request!", "errorMessage": "The operation failed, the system is abnormal!", "errorTip": "<PERSON><PERSON><PERSON>", "networkException": "network anomaly", "networkExceptionMsg": "Please check if your network connection is normal! The network is abnormal", "operationFailed": "Operation failed", "operationSuccess": "Operation Success", "successTip": "Success Tip", "timeoutMessage": "<PERSON><PERSON> timed out, please log in again!"}, "docs": "Document", "login": {"accountPlaceholder": "Please input username", "backSignIn": "Back sign in", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Please input confirm password", "diffPwd": "The two passwords are inconsistent", "email": "Email", "emaildPlaceholder": "Please input email", "forgetFormSecondTitle": "Please enter the email address associated with your account and We will email you a link to reset your password.", "forgetFormTitle": "Reset password", "forgetPassword": "Forget Password?", "loginButton": "Sign in", "loginSuccessDesc": "Welcome back", "loginSuccessTitle": "Login successful", "logout": "Logout", "mobile": "Mobile", "mobilePlaceholder": "Please input mobile", "mobileSignInFormTitle": "Mobile sign in", "otherSignIn": "Or continue with", "password": "Password", "passwordPlaceholder": "Please input password", "policy": "I agree to the xxx Privacy Policy", "policyPlaceholder": "Register after checking", "privacyPolicy": " Privacy policy ", "qrSignInFormTitle": "Qr code sign in", "registerAndAgree": "By signing up, I agree to", "registerButton": "Sign up", "rememberMe": "Remember me", "scanSign": "scanning the code to complete the login", "sendEmailButton": "Send Email", "sendSmsButton": "Send SMS code", "sendSmsText": "Reacquire in {{second}}s", "signInDesc": "Enter your personal details and get started!", "signInFormTitle": "Login to your account", "signInPrimaryTitle": "Hi, Welcome Back", "signInSecondTitle": "Backstage management system", "signInTitle": "", "signUpFormTitle": "Sign up", "smsCode": "SMS code", "smsPlaceholder": "Please input sms code", "termsOfService": " Terms of service ", "userName": "Username", "signInFormDescription": "Enter your username to login to your account", "noAccount": "Don't have an account?"}, "menu": {"analysis": "Analysis", "animate": "Animate", "blank": "Blank", "blog": "Blog", "calendar": "Calendar", "chart": "Chart", "clipboard": "Clipboard", "components": "Components", "dashboard": "Dashboard", "disabled": "<PERSON><PERSON> Disabled", "editor": "Editor", "error": {"403": "403", "404": "404", "500": "500", "index": "Error <PERSON>"}, "external_link": "External Link", "frame": "Item <PERSON>", "functions": "Functions", "i18n": "Multi Language", "icon": "Icon", "iframe": "<PERSON><PERSON><PERSON>", "kanban": "Ka<PERSON><PERSON>", "label": "Item Label", "management": "Management", "markdown": "<PERSON><PERSON>", "menulevel": {"1a": "Menu Level 1a", "1b": {"2a": "Menu Level 2a", "2b": {"3a": "Menu Level 3a", "3b": "Menu Level 3b", "index": "Menu Level 2b"}, "index": "Menu Level 1b"}, "index": "Menu Level"}, "scroll": "<PERSON><PERSON>", "system": {"index": "System", "organization": "Organization", "permission": "Permission", "role": "Role", "user": "User", "user_detail": "User Detail"}, "toast": "Toast", "token_expired": "Token Expired", "upload": "Upload", "user": {"account": "Account", "index": "User", "profile": "Profile"}, "workbench": "Workbench"}, "settings": {"accordion": "Accordion <PERSON>", "breadcrumb": "BreadCrumb", "darkSidebar": "Dark Sidebar", "exitFullscreen": "Exit FullScreen", "family": "Family", "font": "Font", "fullscreen": "FullScreen", "layout": "Layout", "mode": "Mode", "multiTab": "Multi Tab", "page": "Page", "presetThemes": "Preset Themes", "size": "Size", "stretch": "<PERSON><PERSON><PERSON>", "stretchTip": "Only available at large resolutions > 1600px (xl)", "title": "Settings"}, "tab": {"close": "Close", "closeAll": "Close All", "closeLeft": "Close Left", "closeOthers": "Close Others", "closeRight": "Close Right", "fullscreen": "FullScreen", "refresh": "Refresh"}}}