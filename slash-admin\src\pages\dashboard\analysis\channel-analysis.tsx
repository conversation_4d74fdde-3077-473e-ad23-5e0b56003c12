import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { useState, useEffect } from "react";
import { useQuery, gql } from '@apollo/client';
import { useTenant } from '@/hooks/useTenant';

// GraphQL 查询
const CHANNEL_DISTRIBUTION = gql`
  query ChannelDistribution($tenantId: String!) {
    channelDistribution(tenantId: $tenantId) {
      channel
      channelName
      count
      totalImpressions
      totalClicks
      totalConversion
      totalSpend
    }
  }
`;

// 数据类型定义
interface ChannelDistributionItem {
  channel: string;
  channelName: string;
  count: number;
  totalImpressions?: number;
  totalClicks?: number;
  totalConversion?: number;
  totalSpend?: number;
}

interface ChannelMetric {
  channel: string;
  channelName: string;
  impressions: number;
  clicks: number;
  conversions: number;
  cost: number;
  ctr: number;
  cvr: number;
  cpc: number;
  roi: number;
  accountCount: number;
}

interface ChannelAnalysisData {
  channels: ChannelMetric[];
  channelNames: string[];
  impressionsData: number[];
  roiData: number[];
}

interface ChannelAnalysisProps {
  timeRange: 'week' | 'month' | 'quarter';
  onTimeRangeChange: (timeRange: 'week' | 'month' | 'quarter') => void;
}

const ChannelAnalysis = ({ timeRange, onTimeRangeChange }: ChannelAnalysisProps) => {
  const tenantId = useTenant();
  const { t } = useTranslation();
  const [processedData, setProcessedData] = useState<ChannelAnalysisData | null>(null);

  const { data: channelDataRes, loading, error } = useQuery(CHANNEL_DISTRIBUTION, {
    variables: { tenantId },
    skip: !tenantId,
    fetchPolicy: 'network-only',
  });

  // 处理真实数据
  useEffect(() => {
    if (channelDataRes?.channelDistribution) {
      const channelDistribution: ChannelDistributionItem[] = channelDataRes.channelDistribution;

      // 转换为ChannelMetric格式
      const channels: ChannelMetric[] = channelDistribution.map(item => {
        const impressions = item.totalImpressions || 0;
        const clicks = item.totalClicks || 0;
        const conversions = item.totalConversion || 0;
        const cost = item.totalSpend || 0;

        // 计算指标
        const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
        const cvr = clicks > 0 ? (conversions / clicks) * 100 : 0;
        const cpc = clicks > 0 ? cost / clicks : 0;

        // 假设每个转化的平均价值为200元来计算ROI
        const conversionValue = conversions * 200;
        const roi = cost > 0 ? ((conversionValue - cost) / cost) * 100 : 0;

        return {
          channel: item.channel,
          channelName: item.channelName,
          impressions,
          clicks,
          conversions,
          cost,
          ctr,
          cvr,
          cpc,
          roi,
          accountCount: item.count
        };
      });

      const processedChannelData: ChannelAnalysisData = {
        channels,
        channelNames: channels.map(c => c.channelName),
        impressionsData: channels.map(c => c.impressions),
        roiData: channels.map(c => c.roi),
      };

      setProcessedData(processedChannelData);
    }
  }, [channelDataRes]);

  // 准备图表配置（始终调用useChart hooks）
  const distributionChartOptions = useChart({
    labels: processedData?.channelNames || [],
    legend: {
      position: 'bottom',
    },
    tooltip: {
      fillSeriesColor: false,
      y: {
        formatter: (value: number) => `${value} 个账户`,
      },
    },
    stroke: {
      show: false,
    },
    colors: ['#3b82f6', '#4ade80', '#f59e0b', '#ec4899', '#a855f7'],
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
        },
      },
    },
  });

  const roiChartOptions = useChart({
    xaxis: {
      categories: processedData?.channelNames || [],
    },
    yaxis: {
      labels: {
        formatter: (value: number) => `${value.toFixed(0)}%`,
      },
    },
    tooltip: {
      y: {
        formatter: (value: number) => `${value.toFixed(2)}%`,
      },
    },
    colors: ['#4ade80'],
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: '60%',
        dataLabels: {
          position: 'top',
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(0)}%`,
      offsetY: -20,
      style: {
        fontSize: '12px',
        colors: ['#304758'],
      },
    },
  });

  const radarChartOptions = useChart({
    xaxis: {
      categories: [t('pages.key_725'), t('pages.key_1590'), t('pages.key_2362'), t('pages.key_1587'), t('pages.key_2359'), t('pages.key_1583'), 'ROI'],
    },
    yaxis: {
      show: false,
    },
    markers: {
      size: 4,
    },
    stroke: {
      width: 2,
    },
    fill: {
      opacity: 0.2,
    },
    colors: ['#3b82f6', '#4ade80', '#f59e0b', '#ec4899', '#a855f7'],
  });

  // 加载状态
  if (loading || !processedData) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-base font-medium">广告投放渠道分析</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-base font-medium">广告投放渠道分析</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center text-muted-foreground">
            数据加载失败
          </div>
        </CardContent>
      </Card>
    );
  }

  // 无数据状态
  if (!processedData.channels.length) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-base font-medium">广告投放渠道分析</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center text-muted-foreground">
            暂无渠道数据
          </div>
        </CardContent>
      </Card>
    );
  }

  const channelMetrics = processedData.channels;

  // 账户数量数据用于饼图
  const accountCountData = channelMetrics.map(c => c.accountCount);

  // 雷达图数据 - 渠道效果对比
  // 注意：为了使数据在雷达图上显示合理，对数据进行了归一化处理
  const maxImpressions = Math.max(...channelMetrics.map(item => item.impressions), 1);
  const maxClicks = Math.max(...channelMetrics.map(item => item.clicks), 1);
  const maxConversions = Math.max(...channelMetrics.map(item => item.conversions), 1);
  const maxCTR = Math.max(...channelMetrics.map(item => item.ctr), 1);
  const maxCVR = Math.max(...channelMetrics.map(item => item.cvr), 1);
  const minCPC = Math.min(...channelMetrics.map(item => item.cpc));
  const maxCPCRange = Math.max(...channelMetrics.map(item => item.cpc)) - minCPC;
  const maxROI = Math.max(...channelMetrics.map(item => item.roi), 1);

  const radarSeries = channelMetrics.map(item => ({
    name: item.channelName,
    data: [
      // 展示量 - 归一化到0-10
      (item.impressions / maxImpressions) * 10,
      // 点击量 - 归一化到0-10
      (item.clicks / maxClicks) * 10,
      // 转化量 - 归一化到0-10
      (item.conversions / maxConversions) * 10,
      // 点击率 - 归一化到0-10
      (item.ctr / maxCTR) * 10,
      // 转化率 - 归一化到0-10
      (item.cvr / maxCVR) * 10,
      // 点击成本 - 反向指标，值越低越好，归一化到0-10
      maxCPCRange > 0 ? 10 - ((item.cpc - minCPC) / maxCPCRange) * 10 : 10,
      // ROI - 归一化到0-10
      maxROI > 0 ? (item.roi / maxROI) * 10 : 0
    ]
  }));

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-base font-medium">{t('pages.key_789')}</CardTitle>
        <div className="flex space-x-2">
          <button
            onClick={() => onTimeRangeChange('week')}
            className={`px-3 py-1 text-sm rounded-md ${timeRange === 'week' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >
            周
          </button>
          <button
            onClick={() => onTimeRangeChange('month')}
            className={`px-3 py-1 text-sm rounded-md ${timeRange === 'month' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >
            月
          </button>
          <button
            onClick={() => onTimeRangeChange('quarter')}
            className={`px-3 py-1 text-sm rounded-md ${timeRange === 'quarter' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
          >{t('pages.key_645')}</button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="distribution">
          <TabsList className="mb-4">
            <TabsTrigger value="distribution">{t('pages.key_1558')}</TabsTrigger>
            <TabsTrigger value="comparison">{t('pages.key_1562')}</TabsTrigger>
            <TabsTrigger value="roi">ROI分析</TabsTrigger>
          </TabsList>

          <TabsContent value="distribution" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="h-[350px]">
                <h4 className="text-sm font-medium mb-2 text-center">渠道账户分布</h4>
                <Chart
                  key={`donut-accounts-${JSON.stringify(accountCountData)}`}
                  type="donut"
                  series={accountCountData}
                  options={distributionChartOptions}
                  height={320}
                />
              </div>
              <div className="space-y-4">
                <h4 className="text-sm font-medium">渠道性能概览</h4>
                {channelMetrics.map((channel, index) => (
                  <div key={channel.channel} className="space-y-2 p-3 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-2`} style={{ backgroundColor: ['#3b82f6', '#4ade80', '#f59e0b', '#ec4899', '#a855f7'][index] || '#gray' }}></div>
                        <span className="font-medium">{channel.channelName}</span>
                      </div>
                      <span className="text-sm text-muted-foreground">{channel.accountCount} 个账户</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                      <span>展示: {channel.impressions.toLocaleString()}</span>
                      <span>点击: {channel.clicks.toLocaleString()}</span>
                      <span>转化: {channel.conversions.toLocaleString()}</span>
                      <span>消耗: ¥{channel.cost.toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="comparison" className="h-[350px]">
            <Chart
              type="radar"
              series={radarSeries}
              options={radarChartOptions}
              height={350}
            />
          </TabsContent>

          <TabsContent value="roi" className="h-[350px]">
            <Chart
              type="bar"
              series={[{ name: 'ROI', data: processedData.roiData }]}
              options={roiChartOptions}
              height={350}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ChannelAnalysis;
