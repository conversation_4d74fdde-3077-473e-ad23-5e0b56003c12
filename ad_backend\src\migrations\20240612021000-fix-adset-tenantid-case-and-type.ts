import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAdSetTenantIdCaseAndType20240612021000 implements MigrationInterface {
    name = 'FixAdSetTenantIdCaseAndType20240612021000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 如果存在 tenantid 字段，先重命名为 tenantId
        await queryRunner.query(`DO $$
        BEGIN
            IF EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name='ad_set' AND column_name='tenantid'
            ) THEN
                EXECUTE 'ALTER TABLE "ad_set" RENAME COLUMN "tenantid" TO "tenantId"';
            END IF;
        END$$;`);
        // 2. 清理非 uuid 格式的脏数据
        await queryRunner.query(`DELETE FROM "ad_set" WHERE "tenantId" IS NOT NULL AND ("tenantId"::text !~* '^[0-9a-fA-F-]{36}$')`);
        // 3. 修改字段类型为 uuid
        await queryRunner.query(`ALTER TABLE "ad_set" ALTER COLUMN "tenantId" TYPE uuid USING "tenantId"::uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回退为 varchar(64)
        await queryRunner.query(`ALTER TABLE "ad_set" ALTER COLUMN "tenantId" TYPE varchar(64)`);
        // 可选：如需回退字段名
        // await queryRunner.query(`ALTER TABLE "ad_set" RENAME COLUMN "tenantId" TO "tenantid"`);
    }
} 