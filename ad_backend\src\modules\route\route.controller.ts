import { Controller, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { RouteService } from './route.service';
import { ApiTags, ApiOperation } from '@nestjs/swagger';

@ApiTags('路由管理')
@Controller('routes')
export class RouteController {
    constructor(private readonly routeService: RouteService) { }

    @Delete('clear')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({ summary: '清空所有路由数据' })
    async clearAll(): Promise<void> {
        await this.routeService.clearAll();
    }
} 