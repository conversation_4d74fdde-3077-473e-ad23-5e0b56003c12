import { ObjectType, Field, Float, Int } from '@nestjs/graphql';
import { PieDataDTO } from './creative-analysis.dto';

@ObjectType()
export class AdGroupOverviewItemDTO {
    @Field()
    name: string;
    @Field()
    status: string;
    @Field(() => Float)
    budget: number;
    @Field(() => Float)
    spent: number;
    @Field(() => Int)
    progress: number;
    @Field(() => Float)
    ctr: number;
    @Field(() => Float)
    cvr: number;
    @Field(() => Float)
    roi: number;
}

@ObjectType()
export class AdGroupsOverviewDTO {
    @Field(() => [AdGroupOverviewItemDTO])
    adGroups: AdGroupOverviewItemDTO[];

    @Field(() => [PieDataDTO])
    distribution: PieDataDTO[];
} 