import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('landing_page')
export class LandingPage {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ unique: true, length: 3 })
    code: string; // 3位唯一数字字符串

    @Column()
    title: string;

    @Column()
    url: string;

    @Column()
    slogan: string;

    @Column({ type: 'simple-json', nullable: true })
    appIcon: any[];

    @Column({ type: 'simple-json', nullable: true })
    background: any[];

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'float', default: 0 })
    star: number;

    @Column({ type: 'simple-json', nullable: true })
    promoImages: any[];

    @Column({ default: 'admin' })
    creator: string;

    @CreateDateColumn({ type: 'timestamp with time zone', name: 'created_at' })
    createdAt: Date;

    @Column({ length: 64 })
    tenantId: string;
} 