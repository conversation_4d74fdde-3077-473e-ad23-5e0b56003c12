import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Route } from './route.entity';

@Injectable()
export class RouteSeedService {
    constructor(
        @InjectRepository(Route)
        private readonly repository: Repository<Route>,
    ) { }

    async seed() {
        const count = await this.repository.count();
        if (count === 0) {
            // 创建初始路由数据
            const routes = [
                {
                    id: '1',
                    parentId: null,
                    name: '仪表盘',
                    path: '/dashboard',
                    icon: 'solar:chart-bold-duotone',
                    order: 1,
                    type: 'menu',
                    status: 'enabled',
                    isHidden: false,
                    description: '系统仪表盘'
                },
                {
                    id: '2',
                    parentId: null,
                    name: '用户中心',
                    path: '/user',
                    icon: 'solar:user-bold-duotone',
                    order: 2,
                    type: 'menu',
                    status: 'enabled',
                    isHidden: false,
                    description: '用户中心模块'
                },
                {
                    id: '21',
                    parentId: '2',
                    name: '个人信息',
                    path: '/user/profile',
                    component: '/pages/user/profile/index',
                    order: 1,
                    type: 'page',
                    status: 'enabled',
                    isHidden: false,
                    description: '用户个人信息页面'
                },
                {
                    id: '3',
                    parentId: null,
                    name: '系统设置',
                    path: '/system',
                    icon: 'solar:settings-bold-duotone',
                    order: 3,
                    type: 'menu',
                    status: 'enabled',
                    isHidden: false,
                    description: '系统设置模块'
                },
                {
                    id: '31',
                    parentId: '3',
                    name: '权限管理',
                    path: '/system/permission',
                    icon: 'solar:shield-keyhole-bold-duotone',
                    order: 1,
                    type: 'menu',
                    status: 'enabled',
                    isHidden: false,
                    description: '权限管理子模块'
                },
                {
                    id: '311',
                    parentId: '31',
                    name: '角色列表',
                    path: '/system/permission/roles',
                    component: '/pages/system/permission/roles/index',
                    order: 1,
                    type: 'page',
                    status: 'enabled',
                    isHidden: false,
                    description: '角色管理页面'
                }
            ] as Route[];

            await this.repository.save(routes);
        }
    }
} 