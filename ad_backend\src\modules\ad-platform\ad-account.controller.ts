import { <PERSON>, Post, Body, Get, Query, Res, Req } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdAccount } from './entities/ad-account.entity';
import { axiosProxyGet } from '../../utils/axios-proxy';
import { Response, Request } from 'express';
import { FacebookService } from './facebook/facebook.service';
import { Tenant } from '../../entity/tenant.entity';
const mode = process.env.NODE_ENV
const backUrl = mode === 'production' ? 'https://ad.yeeu.net' : 'http://localhost:3001';
const rediectUrl = mode === 'production' ? 'https://ad-api.yeeu.net' : 'https://799380fy71ow.vicp.fun';
console.log(backUrl, 'backUrl')
console.log(rediectUrl, 'rediectUrl')


@Controller('ad-platform/ad-account')

export class AdAccountController {
    constructor(
        @InjectRepository(AdAccount)
        private readonly adAccountRepo: Repository<AdAccount>,
        @InjectRepository(Tenant)
        private readonly tenantRepo: Repository<Tenant>,
        private readonly facebookService: FacebookService,
    ) { }

    @Post('batch-upload')
    async batchUpload(@Body() body: { accounts: { account: string }[] }) {
        const results = [];
        for (const { account } of body.accounts) {
            let acc = await this.adAccountRepo.findOne({ where: { accountId: account } });
            if (!acc) {
                acc = this.adAccountRepo.create({ accountId: account, status: 'pending' });
                await this.adAccountRepo.save(acc);
            }
            const authorized = !!acc.accessToken;
            results.push({ account, status: authorized ? '已授权' : '未授权' });
        }
        return results;
    }

    @Get('unauthorized-list')
    async unauthorizedList() {
        const accounts = await this.adAccountRepo.find();
        return accounts
            .filter(acc => !acc.accessToken)
            .map(acc => ({ account: acc.accountId, status: '未授权' }));
    }

    @Get('oauth2-url')
    getOAuth2Url(@Query('account') account: string) {
        const redirectUri = encodeURIComponent('https://your-backend.com/api/ad-platform/ad-account/oauth2-callback');
        const clientId = '你的FacebookAppId';
        const state = encodeURIComponent(account);
        const url = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=ads_management,ads_read`;
        return { url };
    }

    @Get('oauth2-callback')
    async oauth2Callback(@Query('code') code: string, @Query('state') state: string, @Res() res: Response) {
        // 解析 state，获取 tenantId
        const [tenantId] = decodeURIComponent(state).split('|');
        if (!tenantId) {
            throw new Error('未获取到租户ID');
        }
        const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
        if (!tenant) {
            throw new Error('租户不存在');
        }
        const clientId = process.env.FACEBOOK_APPID;
        const clientSecret = process.env.FACEBOOK_SECET;
        const redirectUri = `${rediectUrl}/ad-platform/ad-account/oauth2-callback`;
        // 打印所有关键参数

        console.log('[FB OAUTH2] redirectUri:', redirectUri);
        let tokenRes;
        try {
            tokenRes = await axiosProxyGet(
                `https://graph.facebook.com/v18.0/oauth/access_token?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&client_secret=${clientSecret}&code=${code}`
            );
        } catch (e) {
            console.error('[FB OAUTH2] 获取access_token失败:', e.response?.data || e.message);
            throw e;
        }
        const accessToken = tokenRes.data.access_token;
        // 拉取 Facebook 广告账户列表
        let fbAccounts: any[] = [];
        try {
            const result = await this.facebookService.getAccounts({ accessToken });
            fbAccounts = result.accounts || [];
            console.log('Facebook 返回广告账户原始数据:', JSON.stringify(result, null, 2));
        } catch (e) {
            console.error('拉取 Facebook 广告账户失败:', e);
        }
        // 批量 upsert 到 ad_account 表
        for (const fbAcc of fbAccounts) {
            const pureAccountId = fbAcc.id.replace(/^act_/, '');
            const fbStatus = this.mapFbAccountStatus(Number(fbAcc.account_status));
            let acc = await this.adAccountRepo.findOne({ where: { accountId: pureAccountId, platform: 'facebook', tenant: { id: tenantId } } });
            if (!acc) {
                acc = this.adAccountRepo.create({
                    accountId: pureAccountId,
                    account: fbAcc.name,
                    platform: 'facebook',
                    status: 'active',
                    fbStatus,
                    accessToken,
                    raw: fbAcc,
                    tenant,
                });
            } else {
                acc.account = fbAcc.name;
                acc.status = 'active';
                acc.fbStatus = fbStatus;
                acc.accessToken = accessToken;
                acc.raw = fbAcc;
                acc.tenant = tenant;
            }
            await this.adAccountRepo.save(acc);
        }


        res.redirect(`${backUrl}/#/facebook/ad-account`);
    }

    private mapFbAccountStatus(account_status: number): string {
        switch (account_status) {
            case 1: return 'active';
            case 2: return 'disabled';
            case 3: return 'unsettled';
            case 7: return 'pending_risk_review';
            case 8: return 'pending_settlement';
            case 9: return 'in_grace_period';
            case 100: return 'pending_closure';
            case 101: return 'closed';
            case 201: return 'any_active';
            case 202: return 'any_closed';
            default: return 'unknown';
        }
    }
} 