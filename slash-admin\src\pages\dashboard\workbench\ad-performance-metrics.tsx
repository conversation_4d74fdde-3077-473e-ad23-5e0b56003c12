import { useTranslation } from 'react-i18next';
import { Icon } from "@/components/icon";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { cn } from "@/utils";
import { useQuery } from '@apollo/client';
import { GET_AD_METRICS } from '@/api/adMetrics.graphql';

interface MetricCardProps {
  title: string;
  value: string;
  icon: string;
  change: number;
  iconColor?: string;
}

const MetricCard = ({ title, value, icon, change, iconColor = "bg-primary/20 text-primary" }: MetricCardProps) => {
  const { t } = useTranslation();
  const isPositive = change >= 0;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">{title}</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-semibold">{value}</p>
              <span className={cn(
                "flex items-center text-xs font-medium",
                isPositive ? "text-green-600" : "text-red-600"
              )}>
                <Icon
                  icon={isPositive ? "solar:arrow-up-linear" : "solar:arrow-down-linear"}
                  className="mr-1"
                  size={14}
                />
                {Math.abs(change)}%
              </span>
            </div>
          </div>
          <div className={cn("p-2 rounded-full", iconColor)}>
            <Icon icon={icon} size={24} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

type AdPerformanceMetricsProps = {
  startDate: string;
  endDate: string;
};

const AdPerformanceMetrics = ({ startDate, endDate }: AdPerformanceMetricsProps) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(GET_AD_METRICS, {
    variables: { startDate, endDate },
    fetchPolicy: 'network-only',
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;

  const metrics = [
    {
      title: t('pages.key_725'),
      value: data.adMetrics.impressions.toLocaleString(),
      icon: "solar:eye-bold-duotone",
      change: 12.5,
      iconColor: "bg-blue-100 text-blue-700"
    },
    {
      title: t('pages.key_1590'),
      value: data.adMetrics.clicks.toLocaleString(),
      icon: "solar:cursor-bold-duotone",
      change: 8.3,
      iconColor: "bg-green-100 text-green-700"
    },
    {
      title: t('pages.key_2362'),
      value: data.adMetrics.conversions.toLocaleString(),
      icon: "solar:flag-bold-duotone",
      change: 5.7,
      iconColor: "bg-purple-100 text-purple-700"
    },
    {
      title: t('pages.key_1071'),
      value: `¥${data.adMetrics.cost.toLocaleString()}`,
      icon: "solar:dollar-minimalistic-bold-duotone",
      change: -3.2,
      iconColor: "bg-amber-100 text-amber-700"
    }
  ];

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
};

export default AdPerformanceMetrics;
