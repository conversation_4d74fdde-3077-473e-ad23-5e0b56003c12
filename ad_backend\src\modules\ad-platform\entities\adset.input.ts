import { InputType, Field, ID, Float } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

@InputType()
export class AdSetFilterInput {
    @Field(() => ID, { nullable: true })
    id?: string;
    @Field({ nullable: true })
    name?: string;
    @Field({ nullable: true })
    platform?: string;

    // 可根据实际 filter 字段继续补充
}

@InputType('CreateAdSetInput')
export class CreateAdSetInput {
    @Field()
    name: string;
    @Field(() => ID)
    campaignId: string;
    @Field(() => ID)
    audienceId: string;
    @Field(() => Float)
    dailyBudget: number;
    @Field()
    startTime: Date;
    @Field({ nullable: true })
    endTime?: Date;
    @Field()
    optimizationGoal: string;
    @Field()
    billingEvent: string;
    @Field()
    status: string;
    @Field(() => Float, { nullable: true })
    bid_amount?: number;
    @Field({ nullable: true })
    applicationId?: string;
    @Field({ nullable: true })
    objectStoreUrl?: string;
    @Field({ nullable: true })
    appEventType?: string;
    @Field({ nullable: true })
    pixelId?: string;
    @Field({ nullable: true })
    customEventType?: string;
    @Field(() => GraphQLJSON, { nullable: true })
    promoted_object?: any;
}

@InputType('UpdateAdSetInput')
export class UpdateAdSetInput {
    @Field(() => ID)
    id: string;
    @Field()
    name: string;
    @Field(() => ID)
    campaignId: string;
    @Field(() => ID)
    audienceId: string;
    @Field(() => Float)
    dailyBudget: number;
    @Field()
    startTime: Date;
    @Field({ nullable: true })
    endTime?: Date;
    @Field()
    optimizationGoal: string;
    @Field()
    billingEvent: string;
    @Field()
    status: string;
    @Field(() => Float, { nullable: true })
    bid_amount?: number;
    @Field({ nullable: true })
    applicationId?: string;
    @Field({ nullable: true })
    objectStoreUrl?: string;
    @Field({ nullable: true })
    appEventType?: string;
    @Field({ nullable: true })
    pixelId?: string;
    @Field({ nullable: true })
    customEventType?: string;
    @Field(() => GraphQLJSON, { nullable: true })
    promoted_object?: any;
} 