import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BudgetUtilizationDTO, PlatformBudgetDTO, MonthlyBudgetTrendDTO } from './entities/budget-utilization.dto';
import { AdCampaign } from './entities/ad-campaign.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => BudgetUtilizationDTO)
export class BudgetUtilizationResolver {
    constructor(
        @InjectRepository(AdCampaign)
        private readonly campaignRepo: Repository<AdCampaign>,
    ) { }

    @Query(() => BudgetUtilizationDTO)
    async budgetUtilization(
        @Args('startDate') startDate: string,
        @Args('endDate') endDate: string,
        @Context() ctx: any,
    ): Promise<BudgetUtilizationDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const where: any = {
            startDate: startDate ? (startDate as any) : undefined,
            endDate: endDate ? (endDate as any) : undefined,
        };
        if (!isSuperAdmin) {
            where.tenant = { id: tenantId };
        }
        // 按平台聚合预算与消耗
        const campaigns = await this.campaignRepo.find({ where });
        const platformMap: Record<string, { allocated: number; spent: number }> = {};
        campaigns.forEach(c => {
            const platform = c.platform || '未知平台';
            const allocated = Number(c.budget) || 0;
            const spent = Number((c.raw && c.raw.spend) || 0);
            if (!platformMap[platform]) platformMap[platform] = { allocated: 0, spent: 0 };
            platformMap[platform].allocated += allocated;
            platformMap[platform].spent += spent;
        });
        const platformBudgets: PlatformBudgetDTO[] = Object.entries(platformMap).map(([name, { allocated, spent }]) => ({
            name,
            allocated,
            spent,
            percentage: allocated > 0 ? Math.round((spent / allocated) * 100) : 0,
        }));
        // 按月份聚合
        const monthMap: Record<string, { allocated: number; spent: number }> = {};
        campaigns.forEach(c => {
            const month = c.startDate ? `${c.startDate.getMonth() + 1}月` : '未知';
            const allocated = Number(c.budget) || 0;
            const spent = Number((c.raw && c.raw.spend) || 0);
            if (!monthMap[month]) monthMap[month] = { allocated: 0, spent: 0 };
            monthMap[month].allocated += allocated;
            monthMap[month].spent += spent;
        });
        const monthlyTrend: MonthlyBudgetTrendDTO[] = Object.entries(monthMap).map(([month, { allocated, spent }]) => ({
            month,
            allocated,
            spent,
        }));
        return { platformBudgets, monthlyTrend };
    }
} 