import { lazy, ReactNode, Suspense } from "react";
import { Navigate, Outlet } from "react-router";

import { Icon } from "@/components/icon";
import { LineLoading } from "@/components/loading";

import type { AppRouteObject } from "#/router";

const ProfilePage = lazy(() => import("@/pages/management/user/profile"));
const AccountPage = lazy(() => import("@/pages/management/user/account"));

const OrganizationPage = lazy(() => import("@/pages/management/system/organization"));
const PermissioPage = lazy(() => import("@/pages/management/system/permission"));

const Blog = lazy(() => import("@/pages/management/blog"));
const AdAccountManagementPage = lazy(() => import("@/pages/management/ad-account"));

const LandingPage = lazy(() => import("@/pages/management/landing-page"));
const CampaignPage = lazy(() => import("@/pages/management/campaign"));
const OptimizationPage = lazy(() => import("@/pages/dashboard/auto-optimization"));
const AnalyticsPage = lazy(() => import("@/pages/dashboard/data-analysis"));

// 禁用原有的广告投放管理系统菜单，已将其子菜单提升为一级菜单
const management: AppRouteObject = {
  order: 999, // 设置一个很大的顺序值，确保不会显示
  path: "management",
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: 'pages.key_790',
    icon: <Icon icon="local:ic-management" className="ant-menu-item-icon" size="24" />,
    key: "/management",
    hideMenu: true, // 在菜单中隐藏
  },
  children: [
    {
      index: true,
      element: <Navigate to="ad-account" replace />,
    },
    {
      path: "ad-account",
      element: <AdAccountManagementPage />,
      meta: { label: 'pages.key_836', key: "/management/ad-account" },
    },
    {
      path: "material",
      element: <ProfilePage />, // 物料管理页面复用原 profile
      meta: { label: 'pages.key_1597', key: "/management/material" },
    },
    {
      path: "vcc",
      element: <OrganizationPage />, // VCC管理页面复用原 organization
      meta: { label: "VCC管理", key: "/management/vcc" },
    },
    {
      path: "landing-page",
      element: <Suspense fallback={<LineLoading />}>
        <LandingPage />
      </Suspense>,
      meta: { label: 'pages.key_1514', key: "/management/landing-page" },
    },
    {
      path: "campaign",
      element: <Suspense fallback={<LineLoading />}>
        <CampaignPage />
      </Suspense>,
      meta: { label: 'pages.key_778', key: "/management/campaign" },
    },
    {
      path: "optimization",
      element: <Suspense fallback={<LineLoading />}>
        <OptimizationPage />
      </Suspense>,
      meta: { label: 'pages.key_1902', key: "/management/optimization" },
    },
    {
      path: "analytics",
      element: <Suspense fallback={<LineLoading />}>
        <AnalyticsPage />
      </Suspense>,
      meta: { label: 'pages.key_1090', key: "/management/analytics" },
    },
  ],
};

export default management;
