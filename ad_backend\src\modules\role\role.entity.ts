import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable } from 'typeorm';
import { Route } from '../route/route.entity';

@Entity('system_role')
export class Role {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ length: 50, unique: true })
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: ['enabled', 'disabled'],
        default: 'enabled'
    })
    status: 'enabled' | 'disabled';

    @Column({ type: 'int', default: 0 })
    order: number;

    @ManyToMany(() => Route)
    @JoinTable({
        name: 'system_role_routes',
        joinColumn: {
            name: 'roleId',
            referencedColumnName: 'id'
        },
        inverseJoinColumn: {
            name: 'routeId',
            referencedColumnName: 'id'
        }
    })
    routes: Route[];

    // 虚拟属性，不存储在数据库中，用于GraphQL查询
    routeIds?: string[];

    @CreateDateColumn({
        type: 'timestamp with time zone',
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        type: 'timestamp with time zone',
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
        onUpdate: 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;
} 