import { gql, useQuery, useMutation } from '@apollo/client';

export const GET_AD_ACCOUNT_LIST = gql`
  query GetAdAccounts($filter: AdAccountFilterInput) {
    adAccounts(filter: $filter) {
      id
      accountId
      account
      status
      fbStatus
      riskLevel
      group
      oauth
      accessToken
      createdAt
      tag
      channel
    }
  }
`;

export const CREATE_AD_ACCOUNT = gql`
  mutation CreateAdAccount($input: CreateOneAdAccountInput!) {
    createOneAdAccount(input: $input) {
      id
      accountId
      account
      status
      riskLevel
      group
      oauth
      createdAt
      tag
      channel
    }
  }
`;

export const DELETE_AD_ACCOUNT = gql`
  mutation DeleteAdAccount($accountId: String!) {
    deleteOneAdAccount(accountId: $accountId) {
      id
      accountId
    }
  }
`;
