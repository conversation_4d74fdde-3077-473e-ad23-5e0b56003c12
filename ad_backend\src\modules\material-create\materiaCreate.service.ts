import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@dataui/crud-typeorm';
import { materiaCreate } from './materiaCreate.entity';
import { Repository } from 'typeorm';

// Service responsible to register (and decommission) employees
@Injectable()
export class materiaCreateService extends TypeOrmCrudService<materiaCreate> {
	constructor(
		@InjectRepository(materiaCreate) readonly repo: Repository<materiaCreate>
	) {
		super(repo);
	}

	public async getActiveWithJobSearchCriteria(): Promise<materiaCreate[]> {
		return await this.repo.find({
			relations: [],
		});
	}

}
