import { ObjectType, Field, ID } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import GraphQLJSON from 'graphql-type-json';
import { AdAccountDTO } from './ad-account.dto';
import { AdCampaignDTO } from './ad-campaign.dto';
import { AdCreativeDTO } from './ad-creative.dto';
import { TenantDTO } from '../../../entity/tenant.dto';

@ObjectType('AdDTO')
export class AdDTO {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field()
  adset_id: string;

  @Field({ nullable: true })
  campaign_id?: string;

  @Field()
  account_id: string;

  @Field({ nullable: true })
  account_name?: string;

  @Field()
  status: string;

  @Field(() => GraphQLJSON, { nullable: true })
  creative?: any;

  @Field(() => GraphQLJSON, { nullable: true })
  tracking_specs?: any;

  @Field({ nullable: true })
  bid_amount?: number;

  @Field({ nullable: true })
  effective_status?: string;

  @Field({ nullable: true })
  configured_status?: string;

  @Field({ nullable: true })
  created_time?: string;

  @Field({ nullable: true })
  updated_time?: string;

  @Field(() => AdAccountDTO)
  adAccount: AdAccountDTO;

  @Field(() => AdCampaignDTO)
  adCampaign: AdCampaignDTO;

  @Field(() => AdCreativeDTO, { nullable: true })
  adCreative?: AdCreativeDTO;

  @Field(() => TenantDTO)
  tenant: TenantDTO;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  raw?: any;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  insight?: any;

  @Field({ nullable: true })
  adId?: string;

  @Field({ nullable: true })
  adset_name?: string;

  @Field({ nullable: true })
  campaign_name?: string;
}
