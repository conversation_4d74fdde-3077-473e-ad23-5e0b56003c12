import { createConnection } from 'typeorm';
import { AdCreative } from '../modules/ad-platform/entities/ad-creative.entity';
import { AdAccount } from '../modules/ad-platform/entities/ad-account.entity';
import { AdCampaign } from '../modules/ad-platform/entities/ad-campaign.entity';
import { Tenant } from '../../../entity/tenant.entity';

async function main() {
    const connection = await createConnection();

    // TODO: 替换为你实际的租户、账户、系列ID
    const tenantId = 'YOUR_TENANT_ID';
    const adAccountId = 'YOUR_AD_ACCOUNT_ID';
    const adCampaignId = 'YOUR_AD_CAMPAIGN_ID';

    const tenantRepo = connection.getRepository(Tenant);
    const adAccountRepo = connection.getRepository(AdAccount);
    const adCampaignRepo = connection.getRepository(AdCampaign);
    const adCreativeRepo = connection.getRepository(AdCreative);

    const tenant = await tenantRepo.findOne({ where: { id: tenantId } });
    const adAccount = await adAccountRepo.findOne({ where: { id: adAccountId } });
    const adCampaign = await adCampaignRepo.findOne({ where: { id: adCampaignId } });

    if (!tenant || !adAccount || !adCampaign) {
        throw new Error('请先确认租户、账户、系列ID均存在！');
    }

    const creative = adCreativeRepo.create({
        creativeId: '****************',
        name: 'TestFlight 2025-05-31-e5eafaf25d832f5c9cb443b86fb298ad',
        platform: 'facebook',
        status: 'ACTIVE',
        adAccount,
        adCampaign,
        tenant,
        raw: {
            id: '****************',
            name: 'TestFlight 2025-05-31-e5eafaf25d832f5c9cb443b86fb298ad',
            platform: 'facebook',
            status: 'ACTIVE',
        },
    });

    await adCreativeRepo.save(creative);
    console.log('插入成功:', creative);
    await connection.close();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 