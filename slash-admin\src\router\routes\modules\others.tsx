import { useTranslation } from 'react-i18next';
import { Icon } from "@/components/icon";
import { LineLoading } from "@/components/loading";
import { Badge } from "@/ui/badge";
import { Suspense, lazy } from "react";
import type { AppRouteObject } from "#/router";

const Calendar = lazy(() => import("@/pages/sys/others/calendar"));

function Wrapper({ children }: { children: React.ReactNode }) {
	const { t } = useTranslation();
	return <Suspense fallback={<LineLoading />}>{children}</Suspense>;
}

const others: AppRouteObject[] = [
	{
		path: "calendar",
		order: 4,
		element: (
			<Wrapper>
				<Calendar />
			</Wrapper>
		),
		meta: {
			label: 'pages.key_797',
			icon: <Icon icon="solar:calendar-bold-duotone" size={24} />,
			key: "/calendar",
			info: <Badge variant="warning">+12</Badge>,
		},
	},
];

export default others;
