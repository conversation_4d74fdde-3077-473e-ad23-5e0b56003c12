import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { AudienceGeoLocation } from './audience.dto';

@ObjectType('Audience')
@Entity('audience')
export class Audience {
  @Field(() => ID)
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field({ nullable: true })
  @ApiProperty({ description: '受众名称' })
  @Column({ length: 128, nullable: true })
  name?: string;

  @Field({ nullable: true })
  @ApiProperty({ description: '备注' })
  @Column({ type: 'text', nullable: true })
  notes: string;

  @Field({ nullable: true })
  @ApiProperty({ description: '受众ID' })
  @Column({ length: 64, unique: true, nullable: true })
  audienceId?: string;

  @Field(() => [String], { nullable: true })
  @ApiProperty({ description: '归属平台' })
  @Column('text', { array: true, nullable: true })
  platform?: string[];

  @Field(() => AudienceGeoLocation, { nullable: true })
  @ApiProperty({ description: '地理位置 geo_locations' })
  @Column({ type: 'jsonb', nullable: true })
  geo_locations?: AudienceGeoLocation;

  @Field(() => AudienceGeoLocation, { nullable: true })
  @ApiProperty({ description: '排除地理位置 excluded_geo_locations' })
  @Column({ type: 'jsonb', nullable: true })
  excluded_geo_locations?: AudienceGeoLocation;

  @Field(() => [Number], { nullable: true })
  @ApiProperty({ description: '语言ID locales' })
  @Column('int', { array: true, nullable: true })
  locales?: number[];

  @Field({ nullable: true })
  @ApiProperty({ description: '性别 gender [1=男,2=女,0/空=不限]' })
  @Column({ type: 'int', nullable: true })
  gender?: number;

  @Field({ nullable: true })
  @ApiProperty({ description: '最小年龄 age_min' })
  @Column('int', { nullable: true })
  age_min?: number;

  @Field({ nullable: true })
  @ApiProperty({ description: '最大年龄 age_max' })
  @Column('int', { nullable: true })
  age_max?: number;

  @Field(() => [Object], { nullable: true })
  @ApiProperty({ description: '兴趣 targeting.interests [{id,name}]' })
  @Column({ type: 'jsonb', nullable: true })
  interests?: { id: string, name?: string }[];

  @Field(() => [Object], { nullable: true })
  @ApiProperty({ description: '行为 targeting.behaviors [{id,name}]' })
  @Column({ type: 'jsonb', nullable: true })
  behaviors?: { id: string, name?: string }[];

  @ApiProperty({ description: '租户ID' })
  @Index()
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;
}
