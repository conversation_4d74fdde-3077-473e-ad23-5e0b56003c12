import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPlatformToAudience20250525113000 implements MigrationInterface {
    name = 'AddPlatformToAudience20250525113000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "audience"
            ADD COLUMN "platform" character varying(32)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "audience"
            DROP COLUMN "platform"
        `);
    }
} 