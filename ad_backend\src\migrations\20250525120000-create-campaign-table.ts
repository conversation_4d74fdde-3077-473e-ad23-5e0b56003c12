import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCampaignTable20250525120000 implements MigrationInterface {
    name = 'CreateCampaignTable20250525120000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "campaign" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "name" character varying(128) NOT NULL,
                "objective" character varying(64) NOT NULL,
                "status" character varying(32) NOT NULL,
                "specialAdCategories" text[] DEFAULT '{}',
                "facebookCampaignId" character varying(64),
                "adAccountId" uuid NOT NULL,
                "syncStatus" character varying,
                "syncError" character varying,
                "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT now()
            );
        `);
        await queryRunner.query(`
            ALTER TABLE "campaign"
            ADD CONSTRAINT "FK_campaign_adAccount" FOREIGN KEY ("adAccountId") REFERENCES "ad_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "campaign"`);
    }
} 