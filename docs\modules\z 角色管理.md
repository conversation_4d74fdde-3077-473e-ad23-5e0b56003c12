# 组件路径 /pages/management/system/role/index

## 前端

- **实际路径**：slash-admin/src/pages/management/system/role/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，角色管理表单与列表，含增删改查、分页、搜索、弹窗、权限树等）
  - role-modal.tsx（角色表单弹窗）
- **主要依赖API定义**：@/api/services/role.service, @/api/services/route.service

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取角色列表    | query    | getRoles               | page, limit  | id, name, description, status, routeIds, createdAt, ... | 支持分页、搜索、过滤 |
| 新增角色        | mutation | createRole             | input: RoleInput | id, name, ... | 新建角色，id为UUID |
| 编辑角色        | mutation | updateRole             | input: RoleInput | id, name, ... | 编辑指定角色 |
| 删除角色        | mutation | deleteRole             | id: string    | id       | 删除指定角色 |
| 获取路由列表    | query    | getRoutes              | 无           | id, name, parentId, ... | 权限树配置用 |

#### 主要参数结构说明

- **RoleInput**（用于新增/编辑）  
  ```ts
  {
    id?: string; // 角色ID，编辑时必填
    name: string; // 角色名称
    description?: string; // 描述
    status?: string; // 状态（active/inactive）
    routeIds?: string[]; // 绑定的路由ID数组（权限）
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名      | 类型      | 说明                       |
  |-------------|-----------|----------------------------|
  | id          | string    | 主键，UUID                 |
  | name        | string    | 角色名称                   |
  | description | string    | 描述                       |
  | status      | string    | 状态（active/inactive）    |
  | routeIds    | string[]  | 绑定的路由ID数组           |
  | createdAt   | Date      | 创建时间                   |
  | updatedAt   | Date      | 更新时间                   |

### 前端表单/组件说明

- 支持角色的增删改查，表单字段与上方RoleInput结构一致
- 支持权限树配置（路由ID绑定）
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）
- 支持角色描述、状态等字段的录入

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（role，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，角色唯一标识         | PRIMARY KEY  |
| name        | varchar     | 角色名称                   | NOT NULL     |
| description | varchar     | 描述                       | 可空         |
| status      | varchar     | 状态（active/inactive）    | 默认active   |
| routeIds    | uuid[]      | 绑定的路由ID数组           | 可空         |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| updatedAt   | timestamp   | 更新时间                   | 自动生成     |
| ...         | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query getRoles / mutation createRole(input) / ...
    后端->>数据库: 操作 role 表
    数据库-->>后端: 返回角色数据
    后端-->>前端: 返回角色数据
```

---

## 变更历史/注意事项

- 角色名称需唯一，所有操作需校验唯一性
- 支持权限树结构扩展，建议用uuid[]或jsonb存储
- 删除角色时需校验是否被用户、资源等引用
- 复杂业务字段建议在表结构中用jsonb扩展