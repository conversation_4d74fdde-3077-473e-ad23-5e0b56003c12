import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** Cursor for paging through collections */
  ConnectionCursor: { input: any; output: any; }
  /** A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format. */
  DateTime: { input: any; output: any; }
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: { input: any; output: any; }
};

export type AdAccount = {
  __typename?: 'AdAccount';
  accessToken?: Maybe<Scalars['String']['output']>;
  account: Scalars['String']['output'];
  accountId: Scalars['String']['output'];
  adNumber?: Maybe<Scalars['String']['output']>;
  channel?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  fbStatus?: Maybe<Scalars['String']['output']>;
  group?: Maybe<Scalars['String']['output']>;
  holder?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  login?: Maybe<Scalars['String']['output']>;
  oauth?: Maybe<Scalars['JSON']['output']>;
  password?: Maybe<Scalars['String']['output']>;
  platform: Scalars['String']['output'];
  raw?: Maybe<Scalars['JSON']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
  riskLevel?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  tag?: Maybe<Scalars['String']['output']>;
  tenant: Tenant;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type AntiBlock = {
  __typename?: 'AntiBlock';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  relatedDomain?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type AntiBlockConnection = {
  __typename?: 'AntiBlockConnection';
  /** Array of nodes. */
  nodes: Array<AntiBlock>;
  /** Paging information */
  pageInfo: OffsetPageInfo;
};

export type AntiBlockDeleteFilter = {
  and?: InputMaybe<Array<AntiBlockDeleteFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  or?: InputMaybe<Array<AntiBlockDeleteFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  url?: InputMaybe<StringFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type AntiBlockDeleteResponse = {
  __typename?: 'AntiBlockDeleteResponse';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  relatedDomain?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type AntiBlockFilter = {
  and?: InputMaybe<Array<AntiBlockFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  or?: InputMaybe<Array<AntiBlockFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  url?: InputMaybe<StringFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type AntiBlockInput = {
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  relatedDomain?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  updatedAt?: InputMaybe<Scalars['DateTime']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type AntiBlockSort = {
  direction: SortDirection;
  field: AntiBlockSortFields;
  nulls?: InputMaybe<SortNulls>;
};

export enum AntiBlockSortFields {
  CreatedAt = 'createdAt',
  Id = 'id',
  Status = 'status',
  UpdatedAt = 'updatedAt',
  Url = 'url',
  UserId = 'userId'
}

export type AntiBlockSubscriptionFilter = {
  and?: InputMaybe<Array<AntiBlockSubscriptionFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  or?: InputMaybe<Array<AntiBlockSubscriptionFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  url?: InputMaybe<StringFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type AntiBlockUpdateFilter = {
  and?: InputMaybe<Array<AntiBlockUpdateFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  or?: InputMaybe<Array<AntiBlockUpdateFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  url?: InputMaybe<StringFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type AudienceBehavior = {
  __typename?: 'AudienceBehavior';
  id: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
};

export type AudienceBehaviorInput = {
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type AudienceCity = {
  __typename?: 'AudienceCity';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type AudienceCityInput = {
  id?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type AudienceCustomEdge = {
  __typename?: 'AudienceCustomEdge';
  cursor: Scalars['String']['output'];
  node: AudienceDto;
};

export type AudienceDto = {
  __typename?: 'AudienceDTO';
  age_max?: Maybe<Scalars['Float']['output']>;
  age_min?: Maybe<Scalars['Float']['output']>;
  approximateCount?: Maybe<Scalars['Float']['output']>;
  audienceId: Scalars['String']['output'];
  behaviors?: Maybe<Array<AudienceBehavior>>;
  custom_audiences?: Maybe<Scalars['JSON']['output']>;
  excluded_geo_locations?: Maybe<AudienceGeoLocation>;
  gender?: Maybe<Scalars['Float']['output']>;
  geo_locations?: Maybe<AudienceGeoLocation>;
  id: Scalars['ID']['output'];
  interests?: Maybe<Array<AudienceInterest>>;
  locales?: Maybe<Array<Scalars['Float']['output']>>;
  name?: Maybe<Scalars['String']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  platform?: Maybe<Array<Scalars['String']['output']>>;
  raw?: Maybe<Scalars['JSON']['output']>;
  tenantId: Scalars['String']['output'];
  tenantName?: Maybe<Scalars['String']['output']>;
};

export type AudienceGeoLocation = {
  __typename?: 'AudienceGeoLocation';
  cities?: Maybe<Array<AudienceCity>>;
  countries?: Maybe<Array<Scalars['String']['output']>>;
  regions?: Maybe<Array<AudienceRegion>>;
};

export type AudienceGeoLocationInput = {
  cities?: InputMaybe<Array<AudienceCityInput>>;
  countries?: InputMaybe<Array<Scalars['String']['input']>>;
  regions?: InputMaybe<Array<AudienceRegionInput>>;
};

export type AudienceInterest = {
  __typename?: 'AudienceInterest';
  id: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
};

export type AudienceInterestInput = {
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type AudiencePageInfo = {
  __typename?: 'AudiencePageInfo';
  endCursor?: Maybe<Scalars['String']['output']>;
  hasNextPage: Scalars['Boolean']['output'];
  hasPreviousPage: Scalars['Boolean']['output'];
  startCursor?: Maybe<Scalars['String']['output']>;
};

export type AudienceRegion = {
  __typename?: 'AudienceRegion';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type AudienceRegionInput = {
  id?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type AuthUserResponse = {
  __typename?: 'AuthUserResponse';
  accessToken: Scalars['String']['output'];
  user: User;
};

export type BindAdAccountInput = {
  /** 广告账户ID列表 */
  adAccountIds: Array<Scalars['String']['input']>;
  /** VCC卡片ID */
  vccCardId: Scalars['String']['input'];
};

export type BooleanFieldComparison = {
  is?: InputMaybe<Scalars['Boolean']['input']>;
  isNot?: InputMaybe<Scalars['Boolean']['input']>;
};

export type CreateAntiBlock = {
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  relatedDomain?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  updatedAt?: InputMaybe<Scalars['DateTime']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type CreateAntiBlockSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: AntiBlockSubscriptionFilter;
};

export type CreateEmployee = {
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  isActive: Scalars['Boolean']['input'];
  isArchived: Scalars['Boolean']['input'];
  lastName?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  updatedAt?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CreateEmployeeSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: EmployeeSubscriptionFilter;
};

export type CreateGroupInput = {
  belongTo?: InputMaybe<Scalars['String']['input']>;
  contactInfo?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  memberCount?: InputMaybe<Scalars['Float']['input']>;
  name: Scalars['String']['input'];
  permissions?: InputMaybe<Scalars['JSON']['input']>;
  status: Scalars['String']['input'];
  tenantId?: InputMaybe<Scalars['String']['input']>;
};

export type CreateGroupSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: GroupSubscriptionFilter;
};

export type CreateIpRestrictionSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: IpRestrictionSubscriptionFilter;
};

export type CreateManyEmployeesInput = {
  /** Array of records to create */
  employees: Array<CreateEmployee>;
};

export type CreateManyGroupsInput = {
  /** Array of records to create */
  groups: Array<CreateGroupInput>;
};

export type CreateManyRoutesInput = {
  /** Array of records to create */
  routes: Array<CreateRouteInput>;
};

export type CreateOneAntiBlockInput = {
  /** The record to create */
  antiBlock: CreateAntiBlock;
};

export type CreateOneEmployeeInput = {
  /** The record to create */
  employee: CreateEmployee;
};

export type CreateOneGroupInput = {
  /** The record to create */
  group: CreateGroupInput;
};

export type CreateOneRouteInput = {
  /** The record to create */
  route: CreateRouteInput;
};

export type CreateRoleInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  order?: InputMaybe<Scalars['Int']['input']>;
  routeIds?: InputMaybe<Array<Scalars['String']['input']>>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type CreateRouteInput = {
  component?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  isHidden: Scalars['Boolean']['input'];
  name: Scalars['String']['input'];
  order: Scalars['Float']['input'];
  parentId?: InputMaybe<Scalars['ID']['input']>;
  path: Scalars['String']['input'];
  status: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type CreateUserInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  avatar?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  fullName?: InputMaybe<Scalars['String']['input']>;
  groupIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  password: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  registerIp?: InputMaybe<Scalars['String']['input']>;
  roleIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  status?: InputMaybe<UserStatus>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  username: Scalars['String']['input'];
};

export type CreateVccCardInput = {
  /** 广告号存活数量 */
  adAccountStatus?: Scalars['Int']['input'];
  /** 余额 */
  balance: Scalars['Float']['input'];
  /** 绑定次数 */
  bindCount?: Scalars['Int']['input'];
  /** 已绑定广告号 */
  boundAdAccount?: InputMaybe<Scalars['String']['input']>;
  /** 绑定的广告账户ID数组 */
  boundAdAccountIds?: InputMaybe<Array<Scalars['String']['input']>>;
  /** 持卡人 */
  cardHolder: Scalars['String']['input'];
  /** 卡号 */
  cardNumber: Scalars['String']['input'];
  /** 渠道 */
  channel: Scalars['String']['input'];
  /** 消费 */
  consumption?: Scalars['Float']['input'];
  /** 国家 */
  country: Scalars['String']['input'];
  /** 国家代码 */
  countryCode: Scalars['String']['input'];
  /** CVV */
  cvv: Scalars['String']['input'];
  /** 过期月 */
  expiryMonth: Scalars['String']['input'];
  /** 过期年 */
  expiryYear: Scalars['String']['input'];
  /** 所属群组 */
  group?: InputMaybe<Scalars['String']['input']>;
  /** 限制次数 */
  limitCount?: Scalars['Int']['input'];
  /** 备注 */
  remark?: InputMaybe<Scalars['String']['input']>;
  /** 状态 */
  status?: Scalars['String']['input'];
  /** 租户ID */
  tenantId: Scalars['String']['input'];
  /** 绑定广告号总数 */
  totalAdAccounts?: Scalars['Int']['input'];
  /** 交易数 */
  transactionCount?: Scalars['Int']['input'];
  /** 已使用次数 */
  usedCount?: Scalars['Int']['input'];
  /** 邮编 */
  zipCode: Scalars['String']['input'];
};

export type CreateVccTransactionInputDto = {
  /** 交易金额 */
  amount: Scalars['Float']['input'];
  /** 广告活动名称 */
  campaignName?: InputMaybe<Scalars['String']['input']>;
  /** VCC卡片ID */
  cardId: Scalars['ID']['input'];
  /** 交易描述 */
  description?: InputMaybe<Scalars['String']['input']>;
  /** Facebook广告账户ID */
  facebookAccountId?: InputMaybe<Scalars['String']['input']>;
  /** Facebook交易ID */
  facebookTransactionId?: InputMaybe<Scalars['String']['input']>;
  /** 商户名称 */
  merchant: Scalars['String']['input'];
  /** 交易状态 */
  status: TransactionStatus;
  /** 交易时间 */
  transactionTime: Scalars['String']['input'];
  /** 交易类型 */
  type: TransactionType;
};

export type CursorPaging = {
  /** Paginate after opaque cursor */
  after?: InputMaybe<Scalars['ConnectionCursor']['input']>;
  /** Paginate before opaque cursor */
  before?: InputMaybe<Scalars['ConnectionCursor']['input']>;
  /** Paginate first */
  first?: InputMaybe<Scalars['Int']['input']>;
  /** Paginate last */
  last?: InputMaybe<Scalars['Int']['input']>;
};

export type DateFieldComparison = {
  between?: InputMaybe<DateFieldComparisonBetween>;
  eq?: InputMaybe<Scalars['DateTime']['input']>;
  gt?: InputMaybe<Scalars['DateTime']['input']>;
  gte?: InputMaybe<Scalars['DateTime']['input']>;
  in?: InputMaybe<Array<Scalars['DateTime']['input']>>;
  is?: InputMaybe<Scalars['Boolean']['input']>;
  isNot?: InputMaybe<Scalars['Boolean']['input']>;
  lt?: InputMaybe<Scalars['DateTime']['input']>;
  lte?: InputMaybe<Scalars['DateTime']['input']>;
  neq?: InputMaybe<Scalars['DateTime']['input']>;
  notBetween?: InputMaybe<DateFieldComparisonBetween>;
  notIn?: InputMaybe<Array<Scalars['DateTime']['input']>>;
};

export type DateFieldComparisonBetween = {
  lower: Scalars['DateTime']['input'];
  upper: Scalars['DateTime']['input'];
};

export type DeleteManyAntiBlocksInput = {
  /** Filter to find records to delete */
  filter: AntiBlockDeleteFilter;
};

export type DeleteManyEmployeesInput = {
  /** Filter to find records to delete */
  filter: EmployeeDeleteFilter;
};

export type DeleteManyGroupsInput = {
  /** Filter to find records to delete */
  filter: GroupDeleteFilter;
};

export type DeleteManyIpRestrictionsInput = {
  /** Filter to find records to delete */
  filter: IpRestrictionDeleteFilter;
};

export type DeleteManyResponse = {
  __typename?: 'DeleteManyResponse';
  /** The number of records deleted. */
  deletedCount: Scalars['Int']['output'];
};

export type DeleteManyRoutesInput = {
  /** Filter to find records to delete */
  filter: RouteDeleteFilter;
};

export type DeleteOneAntiBlockInput = {
  /** The id of the record to delete. */
  id: Scalars['ID']['input'];
};

export type DeleteOneAntiBlockSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: AntiBlockSubscriptionFilter;
};

export type DeleteOneEmployeeInput = {
  /** The id of the record to delete. */
  id: Scalars['ID']['input'];
};

export type DeleteOneEmployeeSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: EmployeeSubscriptionFilter;
};

export type DeleteOneGroupInput = {
  /** The id of the record to delete. */
  id: Scalars['ID']['input'];
};

export type DeleteOneGroupSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: GroupSubscriptionFilter;
};

export type DeleteOneIpRestrictionInput = {
  /** The id of the record to delete. */
  id: Scalars['ID']['input'];
};

export type DeleteOneIpRestrictionSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: IpRestrictionSubscriptionFilter;
};

export type DeleteOneRouteInput = {
  /** The id of the record to delete. */
  id: Scalars['ID']['input'];
};

export type DeleteRoleInput = {
  id: Scalars['ID']['input'];
};

export type Employee = {
  __typename?: 'Employee';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  isActive: Scalars['Boolean']['output'];
  isArchived: Scalars['Boolean']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type EmployeeAggregateFilter = {
  and?: InputMaybe<Array<EmployeeAggregateFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  firstName?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isActive?: InputMaybe<BooleanFieldComparison>;
  isArchived?: InputMaybe<BooleanFieldComparison>;
  lastName?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<EmployeeAggregateFilter>>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type EmployeeAggregateGroupBy = {
  __typename?: 'EmployeeAggregateGroupBy';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  isArchived?: Maybe<Scalars['Boolean']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};


export type EmployeeAggregateGroupByCreatedAtArgs = {
  by?: GroupBy;
};


export type EmployeeAggregateGroupByUpdatedAtArgs = {
  by?: GroupBy;
};

export type EmployeeAggregateResponse = {
  __typename?: 'EmployeeAggregateResponse';
  count?: Maybe<EmployeeCountAggregate>;
  groupBy?: Maybe<EmployeeAggregateGroupBy>;
  max?: Maybe<EmployeeMaxAggregate>;
  min?: Maybe<EmployeeMinAggregate>;
};

export type EmployeeConnection = {
  __typename?: 'EmployeeConnection';
  /** Array of edges. */
  edges: Array<EmployeeEdge>;
  /** Paging information */
  pageInfo: PageInfo;
  /** Fetch total count of records */
  totalCount: Scalars['Int']['output'];
};

export type EmployeeCountAggregate = {
  __typename?: 'EmployeeCountAggregate';
  createdAt?: Maybe<Scalars['Int']['output']>;
  firstName?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  isActive?: Maybe<Scalars['Int']['output']>;
  isArchived?: Maybe<Scalars['Int']['output']>;
  lastName?: Maybe<Scalars['Int']['output']>;
  updatedAt?: Maybe<Scalars['Int']['output']>;
};

export type EmployeeDeleteFilter = {
  and?: InputMaybe<Array<EmployeeDeleteFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  firstName?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isActive?: InputMaybe<BooleanFieldComparison>;
  isArchived?: InputMaybe<BooleanFieldComparison>;
  lastName?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<EmployeeDeleteFilter>>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type EmployeeDeleteResponse = {
  __typename?: 'EmployeeDeleteResponse';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  isActive?: Maybe<Scalars['Boolean']['output']>;
  isArchived?: Maybe<Scalars['Boolean']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type EmployeeEdge = {
  __typename?: 'EmployeeEdge';
  /** Cursor for this node. */
  cursor: Scalars['ConnectionCursor']['output'];
  /** The node containing the Employee */
  node: Employee;
};

export type EmployeeFilter = {
  and?: InputMaybe<Array<EmployeeFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  firstName?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isActive?: InputMaybe<BooleanFieldComparison>;
  isArchived?: InputMaybe<BooleanFieldComparison>;
  lastName?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<EmployeeFilter>>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type EmployeeInput = {
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  isActive: Scalars['Boolean']['input'];
  isArchived: Scalars['Boolean']['input'];
  lastName?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  updatedAt?: InputMaybe<Scalars['DateTime']['input']>;
};

export type EmployeeMaxAggregate = {
  __typename?: 'EmployeeMaxAggregate';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type EmployeeMinAggregate = {
  __typename?: 'EmployeeMinAggregate';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type EmployeeSort = {
  direction: SortDirection;
  field: EmployeeSortFields;
  nulls?: InputMaybe<SortNulls>;
};

export enum EmployeeSortFields {
  CreatedAt = 'createdAt',
  FirstName = 'firstName',
  Id = 'id',
  IsActive = 'isActive',
  IsArchived = 'isArchived',
  LastName = 'lastName',
  UpdatedAt = 'updatedAt'
}

export type EmployeeSubscriptionFilter = {
  and?: InputMaybe<Array<EmployeeSubscriptionFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  firstName?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isActive?: InputMaybe<BooleanFieldComparison>;
  isArchived?: InputMaybe<BooleanFieldComparison>;
  lastName?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<EmployeeSubscriptionFilter>>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type EmployeeUpdateFilter = {
  and?: InputMaybe<Array<EmployeeUpdateFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  firstName?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isActive?: InputMaybe<BooleanFieldComparison>;
  isArchived?: InputMaybe<BooleanFieldComparison>;
  lastName?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<EmployeeUpdateFilter>>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type Group = {
  __typename?: 'Group';
  belongTo?: Maybe<Scalars['String']['output']>;
  contactInfo?: Maybe<Scalars['String']['output']>;
  createTime?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  memberCount?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  permissions?: Maybe<Scalars['JSON']['output']>;
  status: Scalars['String']['output'];
  tenant?: Maybe<Tenant>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['DateTime']['output']>;
};

/** Group by */
export enum GroupBy {
  Day = 'DAY',
  Month = 'MONTH',
  Week = 'WEEK',
  Year = 'YEAR'
}

export type GroupConnection = {
  __typename?: 'GroupConnection';
  /** Array of nodes. */
  nodes: Array<Group>;
  /** Paging information */
  pageInfo: OffsetPageInfo;
  /** Fetch total count of records */
  totalCount: Scalars['Int']['output'];
};

export type GroupDeleteFilter = {
  and?: InputMaybe<Array<GroupDeleteFilter>>;
  belongTo?: InputMaybe<StringFieldComparison>;
  contactInfo?: InputMaybe<StringFieldComparison>;
  createTime?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  memberCount?: InputMaybe<IntFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<GroupDeleteFilter>>;
  permissions?: InputMaybe<JsonFilterComparison>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updateTime?: InputMaybe<DateFieldComparison>;
};

export type GroupDeleteResponse = {
  __typename?: 'GroupDeleteResponse';
  belongTo?: Maybe<Scalars['String']['output']>;
  contactInfo?: Maybe<Scalars['String']['output']>;
  createTime?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  memberCount?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  permissions?: Maybe<Scalars['JSON']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenant?: Maybe<Tenant>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['DateTime']['output']>;
};

export type GroupFilter = {
  and?: InputMaybe<Array<GroupFilter>>;
  belongTo?: InputMaybe<StringFieldComparison>;
  contactInfo?: InputMaybe<StringFieldComparison>;
  createTime?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  memberCount?: InputMaybe<IntFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<GroupFilter>>;
  permissions?: InputMaybe<JsonFilterComparison>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updateTime?: InputMaybe<DateFieldComparison>;
};

export type GroupSort = {
  direction: SortDirection;
  field: GroupSortFields;
  nulls?: InputMaybe<SortNulls>;
};

export enum GroupSortFields {
  BelongTo = 'belongTo',
  ContactInfo = 'contactInfo',
  CreateTime = 'createTime',
  Description = 'description',
  Id = 'id',
  MemberCount = 'memberCount',
  Name = 'name',
  Permissions = 'permissions',
  Status = 'status',
  TenantId = 'tenantId',
  UpdateTime = 'updateTime'
}

export type GroupSubscriptionFilter = {
  and?: InputMaybe<Array<GroupSubscriptionFilter>>;
  belongTo?: InputMaybe<StringFieldComparison>;
  contactInfo?: InputMaybe<StringFieldComparison>;
  createTime?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  memberCount?: InputMaybe<IntFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<GroupSubscriptionFilter>>;
  permissions?: InputMaybe<JsonFilterComparison>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updateTime?: InputMaybe<DateFieldComparison>;
};

export type GroupUpdateFilter = {
  and?: InputMaybe<Array<GroupUpdateFilter>>;
  belongTo?: InputMaybe<StringFieldComparison>;
  contactInfo?: InputMaybe<StringFieldComparison>;
  createTime?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  memberCount?: InputMaybe<IntFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<GroupUpdateFilter>>;
  permissions?: InputMaybe<JsonFilterComparison>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updateTime?: InputMaybe<DateFieldComparison>;
};

export type IdFilterComparison = {
  eq?: InputMaybe<Scalars['ID']['input']>;
  gt?: InputMaybe<Scalars['ID']['input']>;
  gte?: InputMaybe<Scalars['ID']['input']>;
  iLike?: InputMaybe<Scalars['ID']['input']>;
  in?: InputMaybe<Array<Scalars['ID']['input']>>;
  is?: InputMaybe<Scalars['Boolean']['input']>;
  isNot?: InputMaybe<Scalars['Boolean']['input']>;
  like?: InputMaybe<Scalars['ID']['input']>;
  lt?: InputMaybe<Scalars['ID']['input']>;
  lte?: InputMaybe<Scalars['ID']['input']>;
  neq?: InputMaybe<Scalars['ID']['input']>;
  notILike?: InputMaybe<Scalars['ID']['input']>;
  notIn?: InputMaybe<Array<Scalars['ID']['input']>>;
  notLike?: InputMaybe<Scalars['ID']['input']>;
};

export type IntFieldComparison = {
  between?: InputMaybe<IntFieldComparisonBetween>;
  eq?: InputMaybe<Scalars['Int']['input']>;
  gt?: InputMaybe<Scalars['Int']['input']>;
  gte?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<Scalars['Int']['input']>>;
  is?: InputMaybe<Scalars['Boolean']['input']>;
  isNot?: InputMaybe<Scalars['Boolean']['input']>;
  lt?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['Int']['input']>;
  neq?: InputMaybe<Scalars['Int']['input']>;
  notBetween?: InputMaybe<IntFieldComparisonBetween>;
  notIn?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export type IntFieldComparisonBetween = {
  lower: Scalars['Int']['input'];
  upper: Scalars['Int']['input'];
};

export type IpRestrictionConnection = {
  __typename?: 'IpRestrictionConnection';
  /** Array of nodes. */
  nodes: Array<IpRestriction>;
  /** Paging information */
  pageInfo: OffsetPageInfo;
  /** Fetch total count of records */
  totalCount: Scalars['Int']['output'];
};

export type IpRestrictionDeleteResponse = {
  __typename?: 'IpRestrictionDeleteResponse';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  operator?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type JsonFilterComparison = {
  eq?: InputMaybe<Scalars['JSON']['input']>;
  gt?: InputMaybe<Scalars['JSON']['input']>;
  gte?: InputMaybe<Scalars['JSON']['input']>;
  iLike?: InputMaybe<Scalars['JSON']['input']>;
  in?: InputMaybe<Array<Scalars['JSON']['input']>>;
  is?: InputMaybe<Scalars['Boolean']['input']>;
  isNot?: InputMaybe<Scalars['Boolean']['input']>;
  like?: InputMaybe<Scalars['JSON']['input']>;
  lt?: InputMaybe<Scalars['JSON']['input']>;
  lte?: InputMaybe<Scalars['JSON']['input']>;
  neq?: InputMaybe<Scalars['JSON']['input']>;
  notILike?: InputMaybe<Scalars['JSON']['input']>;
  notIn?: InputMaybe<Array<Scalars['JSON']['input']>>;
  notLike?: InputMaybe<Scalars['JSON']['input']>;
};

export type LoginInput = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  accessToken: Scalars['String']['output'];
  roles?: Maybe<Array<Scalars['String']['output']>>;
  userId: Scalars['String']['output'];
  username: Scalars['String']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  /** 手动添加VCC充值记录 */
  addVccRechargeTransaction: VccTransactionDto;
  /** 绑定广告账户到VCC卡片 */
  bindAdAccountToVccCard: VccCard;
  changeUserStatus: User;
  createManyAntiBlocks: Array<AntiBlock>;
  createManyEmployees: Array<Employee>;
  createManyGroups: Array<Group>;
  createManyRoutes: Array<Route>;
  createOneAntiBlock: AntiBlock;
  createOneEmployee: Employee;
  createOneGroup: Group;
  createOneIpRestriction: IpRestriction;
  createOneRoute: Route;
  createRole: Role;
  createRoute: Route;
  createUser: User;
  /** 创建VCC卡片 */
  createVccCard: VccCard;
  /** 批量创建VCC卡片 */
  createVccCardsBatch: Array<VccCard>;
  /** 创建VCC交易记录 */
  createVccTransaction: VccTransactionDto;
  deleteManyAntiBlocks: DeleteManyResponse;
  deleteManyEmployees: DeleteManyResponse;
  deleteManyGroups: DeleteManyResponse;
  deleteManyIpRestrictions: DeleteManyResponse;
  deleteManyRoutes: DeleteManyResponse;
  deleteOneAntiBlock: AntiBlockDeleteResponse;
  deleteOneEmployee: EmployeeDeleteResponse;
  deleteOneGroup: GroupDeleteResponse;
  deleteOneIpRestriction: IpRestrictionDeleteResponse;
  deleteOneRoute: RouteDeleteResponse;
  deleteRole: Scalars['Boolean']['output'];
  deleteRoute: Scalars['Boolean']['output'];
  deleteUser: User;
  /** 删除VCC卡片 */
  deleteVccCard: Scalars['Boolean']['output'];
  /** 删除VCC交易记录 */
  deleteVccTransaction: Scalars['Boolean']['output'];
  login: LoginResponse;
  register: RegisterResponse;
  /** 从Facebook API同步VCC卡片的广告消费记录 */
  syncVccTransactionsFromFacebook: Array<VccTransactionDto>;
  toggleRouteStatus: Route;
  updateManyAntiBlocks: UpdateManyResponse;
  updateManyEmployees: UpdateManyResponse;
  updateManyGroups: UpdateManyResponse;
  updateManyIpRestrictions: UpdateManyResponse;
  updateManyRoutes: UpdateManyResponse;
  updateOneAntiBlock: AntiBlock;
  updateOneEmployee: Employee;
  updateOneGroup: Group;
  updateOneIpRestriction: IpRestriction;
  updateOneRoute: Route;
  updateRole: Role;
  updateUser: User;
  /** 更新VCC卡片 */
  updateVccCard: VccCard;
  /** 更新VCC交易记录 */
  updateVccTransaction: VccTransactionDto;
};


export type MutationAddVccRechargeTransactionArgs = {
  amount: Scalars['Float']['input'];
  cardId: Scalars['ID']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
};


export type MutationBindAdAccountToVccCardArgs = {
  input: BindAdAccountInput;
};


export type MutationChangeUserStatusArgs = {
  id: Scalars['ID']['input'];
  status: UserStatus;
};


export type MutationCreateManyAntiBlocksArgs = {
  antiBlocks: Array<AntiBlockInput>;
};


export type MutationCreateManyEmployeesArgs = {
  input: CreateManyEmployeesInput;
};


export type MutationCreateManyGroupsArgs = {
  input: CreateManyGroupsInput;
};


export type MutationCreateManyRoutesArgs = {
  input: CreateManyRoutesInput;
};


export type MutationCreateOneAntiBlockArgs = {
  input: CreateOneAntiBlockInput;
};


export type MutationCreateOneEmployeeArgs = {
  input: CreateOneEmployeeInput;
};


export type MutationCreateOneGroupArgs = {
  input: CreateOneGroupInput;
};


export type MutationCreateOneIpRestrictionArgs = {
  ip?: InputMaybe<Scalars['String']['input']>;
  operator?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateOneRouteArgs = {
  input: CreateOneRouteInput;
};


export type MutationCreateRoleArgs = {
  input: CreateRoleInput;
};


export type MutationCreateRouteArgs = {
  input: CreateRouteInput;
};


export type MutationCreateUserArgs = {
  input: CreateUserInput;
};


export type MutationCreateVccCardArgs = {
  input: CreateVccCardInput;
};


export type MutationCreateVccCardsBatchArgs = {
  inputs: Array<CreateVccCardInput>;
};


export type MutationCreateVccTransactionArgs = {
  input: CreateVccTransactionInputDto;
};


export type MutationDeleteManyAntiBlocksArgs = {
  input: DeleteManyAntiBlocksInput;
};


export type MutationDeleteManyEmployeesArgs = {
  input: DeleteManyEmployeesInput;
};


export type MutationDeleteManyGroupsArgs = {
  input: DeleteManyGroupsInput;
};


export type MutationDeleteManyIpRestrictionsArgs = {
  input: DeleteManyIpRestrictionsInput;
};


export type MutationDeleteManyRoutesArgs = {
  input: DeleteManyRoutesInput;
};


export type MutationDeleteOneAntiBlockArgs = {
  input: DeleteOneAntiBlockInput;
};


export type MutationDeleteOneEmployeeArgs = {
  input: DeleteOneEmployeeInput;
};


export type MutationDeleteOneGroupArgs = {
  input: DeleteOneGroupInput;
};


export type MutationDeleteOneIpRestrictionArgs = {
  input: DeleteOneIpRestrictionInput;
};


export type MutationDeleteOneRouteArgs = {
  input: DeleteOneRouteInput;
};


export type MutationDeleteRoleArgs = {
  input: DeleteRoleInput;
};


export type MutationDeleteRouteArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteUserArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteVccCardArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteVccTransactionArgs = {
  id: Scalars['ID']['input'];
};


export type MutationLoginArgs = {
  input: LoginInput;
};


export type MutationRegisterArgs = {
  input: RegisterInput;
};


export type MutationSyncVccTransactionsFromFacebookArgs = {
  input: SyncFacebookTransactionsInputDto;
};


export type MutationToggleRouteStatusArgs = {
  id: Scalars['String']['input'];
};


export type MutationUpdateManyAntiBlocksArgs = {
  input: UpdateManyAntiBlocksInput;
};


export type MutationUpdateManyEmployeesArgs = {
  input: UpdateManyEmployeesInput;
};


export type MutationUpdateManyGroupsArgs = {
  input: UpdateManyGroupsInput;
};


export type MutationUpdateManyIpRestrictionsArgs = {
  input: UpdateManyIpRestrictionsInput;
};


export type MutationUpdateManyRoutesArgs = {
  input: UpdateManyRoutesInput;
};


export type MutationUpdateOneAntiBlockArgs = {
  input: UpdateOneAntiBlockInput;
};


export type MutationUpdateOneEmployeeArgs = {
  input: UpdateOneEmployeeInput;
};


export type MutationUpdateOneGroupArgs = {
  input: UpdateOneGroupInput;
};


export type MutationUpdateOneIpRestrictionArgs = {
  input: UpdateOneIpRestrictionInput;
};


export type MutationUpdateOneRouteArgs = {
  input: UpdateOneRouteInput;
};


export type MutationUpdateRoleArgs = {
  input: UpdateRoleInput;
};


export type MutationUpdateUserArgs = {
  input: UpdateUserInput;
};


export type MutationUpdateVccCardArgs = {
  id: Scalars['ID']['input'];
  input: UpdateVccCardInput;
};


export type MutationUpdateVccTransactionArgs = {
  id: Scalars['ID']['input'];
  input: UpdateVccTransactionInputDto;
};

export type NumberFieldComparison = {
  between?: InputMaybe<NumberFieldComparisonBetween>;
  eq?: InputMaybe<Scalars['Float']['input']>;
  gt?: InputMaybe<Scalars['Float']['input']>;
  gte?: InputMaybe<Scalars['Float']['input']>;
  in?: InputMaybe<Array<Scalars['Float']['input']>>;
  is?: InputMaybe<Scalars['Boolean']['input']>;
  isNot?: InputMaybe<Scalars['Boolean']['input']>;
  lt?: InputMaybe<Scalars['Float']['input']>;
  lte?: InputMaybe<Scalars['Float']['input']>;
  neq?: InputMaybe<Scalars['Float']['input']>;
  notBetween?: InputMaybe<NumberFieldComparisonBetween>;
  notIn?: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type NumberFieldComparisonBetween = {
  lower: Scalars['Float']['input'];
  upper: Scalars['Float']['input'];
};

export type OffsetPageInfo = {
  __typename?: 'OffsetPageInfo';
  /** true if paging forward and there are more records. */
  hasNextPage?: Maybe<Scalars['Boolean']['output']>;
  /** true if paging backwards and there are more records. */
  hasPreviousPage?: Maybe<Scalars['Boolean']['output']>;
};

export type OffsetPaging = {
  /** Limit the number of records returned */
  limit?: InputMaybe<Scalars['Int']['input']>;
  /** Offset to start returning records from */
  offset?: InputMaybe<Scalars['Int']['input']>;
};

export type PageInfo = {
  __typename?: 'PageInfo';
  /** The cursor of the last returned record. */
  endCursor?: Maybe<Scalars['ConnectionCursor']['output']>;
  /** true if paging forward and there are more records. */
  hasNextPage?: Maybe<Scalars['Boolean']['output']>;
  /** true if paging backwards and there are more records. */
  hasPreviousPage?: Maybe<Scalars['Boolean']['output']>;
  /** The cursor of the first returned record. */
  startCursor?: Maybe<Scalars['ConnectionCursor']['output']>;
};

export type PaginatedVccTransactionResult = {
  __typename?: 'PaginatedVccTransactionResult';
  /** 交易记录列表 */
  data: Array<VccTransactionDto>;
  /** 每页条数 */
  limit: Scalars['Int']['output'];
  /** 当前页 */
  page: Scalars['Int']['output'];
  /** 总数 */
  total: Scalars['Int']['output'];
  /** 总页数 */
  totalPages: Scalars['Int']['output'];
};

export type Query = {
  __typename?: 'Query';
  antiBlock: AntiBlock;
  antiBlocks: AntiBlockConnection;
  employee: Employee;
  employeeAggregate: Array<EmployeeAggregateResponse>;
  employees: EmployeeConnection;
  /** 导出VCC交易记录（CSV或JSON格式） */
  exportVccTransactions: Scalars['String']['output'];
  getRoute?: Maybe<Route>;
  getRouteTree: Array<Route>;
  getRoutes: Array<Route>;
  group: Group;
  groups: GroupConnection;
  ipRestriction: IpRestriction;
  ipRestrictionAggregate: Array<IpRestrictionAggregateResponse>;
  ipRestrictions: IpRestrictionConnection;
  me: AuthUserResponse;
  role: Role;
  roles: RolePagination;
  route: Route;
  routeAggregate: Array<RouteAggregateResponse>;
  routes: RouteConnection;
  user: User;
  users: UserConnection;
  /** 根据ID获取VCC卡片详情 */
  vccCard: VccCard;
  /** 获取VCC卡片列表 */
  vccCards: Array<VccCard>;
  /** 根据ID查询单个VCC交易记录 */
  vccTransaction: VccTransactionDto;
  /** 获取VCC交易统计数据 */
  vccTransactionStats: VccTransactionStatsDto;
  /** 查询VCC交易记录列表（支持前端所有过滤条件） */
  vccTransactions: PaginatedVccTransactionResult;
};


export type QueryAntiBlockArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAntiBlocksArgs = {
  filter?: AntiBlockFilter;
  paging?: OffsetPaging;
  sorting?: Array<AntiBlockSort>;
};


export type QueryEmployeeArgs = {
  id: Scalars['ID']['input'];
};


export type QueryEmployeeAggregateArgs = {
  filter?: InputMaybe<EmployeeAggregateFilter>;
};


export type QueryEmployeesArgs = {
  filter?: EmployeeFilter;
  paging?: CursorPaging;
  sorting?: Array<EmployeeSort>;
};


export type QueryExportVccTransactionsArgs = {
  filter?: InputMaybe<VccTransactionFilterInputDto>;
  format?: Scalars['String']['input'];
};


export type QueryGetRouteArgs = {
  id: Scalars['String']['input'];
};


export type QueryGroupArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGroupsArgs = {
  filter?: GroupFilter;
  paging?: OffsetPaging;
  sorting?: Array<GroupSort>;
};


export type QueryIpRestrictionArgs = {
  id: Scalars['ID']['input'];
};


export type QueryIpRestrictionAggregateArgs = {
  filter?: InputMaybe<IpRestrictionAggregateFilter>;
};


export type QueryIpRestrictionsArgs = {
  filter?: IpRestrictionFilter;
  paging?: OffsetPaging;
  sorting?: Array<IpRestrictionSort>;
};


export type QueryRoleArgs = {
  id: Scalars['String']['input'];
};


export type QueryRolesArgs = {
  input: RoleSearchInput;
};


export type QueryRouteArgs = {
  id: Scalars['ID']['input'];
};


export type QueryRouteAggregateArgs = {
  filter?: InputMaybe<RouteAggregateFilter>;
};


export type QueryRoutesArgs = {
  filter?: RouteFilter;
  paging?: CursorPaging;
  sorting?: Array<RouteSort>;
};


export type QueryUserArgs = {
  id: Scalars['ID']['input'];
};


export type QueryUsersArgs = {
  input?: InputMaybe<UserListInput>;
  sorting?: InputMaybe<Array<UserSortInput>>;
};


export type QueryVccCardArgs = {
  id: Scalars['ID']['input'];
};


export type QueryVccCardsArgs = {
  filter?: InputMaybe<VccCardFilterInput>;
};


export type QueryVccTransactionArgs = {
  id: Scalars['ID']['input'];
};


export type QueryVccTransactionStatsArgs = {
  filter?: InputMaybe<VccTransactionFilterInputDto>;
};


export type QueryVccTransactionsArgs = {
  filter?: InputMaybe<VccTransactionFilterInputDto>;
  pagination?: InputMaybe<VccTransactionPaginationInputDto>;
};

export type RegisterInput = {
  email: Scalars['String']['input'];
  fullName?: InputMaybe<Scalars['String']['input']>;
  groupIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  password: Scalars['String']['input'];
  roleIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  tenantId: Scalars['String']['input'];
  username: Scalars['String']['input'];
};

export type RegisterResponse = {
  __typename?: 'RegisterResponse';
  accessToken: Scalars['String']['output'];
  userId: Scalars['String']['output'];
  username: Scalars['String']['output'];
};

export type Role = {
  __typename?: 'Role';
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  order: Scalars['Int']['output'];
  routeIds?: Maybe<Array<Scalars['String']['output']>>;
  routeList?: Maybe<Array<Route>>;
  status: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type RolePageInfo = {
  __typename?: 'RolePageInfo';
  hasNextPage: Scalars['Boolean']['output'];
  hasPreviousPage: Scalars['Boolean']['output'];
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  totalPages: Scalars['Int']['output'];
};

export type RolePagination = {
  __typename?: 'RolePagination';
  items: Array<Role>;
  pageInfo: RolePageInfo;
  totalCount: Scalars['Int']['output'];
};

export type RoleSearchInput = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type Route = {
  __typename?: 'Route';
  children?: Maybe<Array<Route>>;
  component?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  icon?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  isHidden: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  order: Scalars['Float']['output'];
  parentId?: Maybe<Scalars['ID']['output']>;
  path: Scalars['String']['output'];
  status: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type RouteAggregateFilter = {
  and?: InputMaybe<Array<RouteAggregateFilter>>;
  component?: InputMaybe<StringFieldComparison>;
  createdAt?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  icon?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isHidden?: InputMaybe<BooleanFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<RouteAggregateFilter>>;
  order?: InputMaybe<NumberFieldComparison>;
  parentId?: InputMaybe<IdFilterComparison>;
  path?: InputMaybe<StringFieldComparison>;
  status?: InputMaybe<StringFieldComparison>;
  type?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type RouteAggregateGroupBy = {
  __typename?: 'RouteAggregateGroupBy';
  component?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  icon?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  isHidden?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  order?: Maybe<Scalars['Float']['output']>;
  parentId?: Maybe<Scalars['ID']['output']>;
  path?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};


export type RouteAggregateGroupByCreatedAtArgs = {
  by?: GroupBy;
};


export type RouteAggregateGroupByUpdatedAtArgs = {
  by?: GroupBy;
};

export type RouteAggregateResponse = {
  __typename?: 'RouteAggregateResponse';
  avg?: Maybe<RouteAvgAggregate>;
  count?: Maybe<RouteCountAggregate>;
  groupBy?: Maybe<RouteAggregateGroupBy>;
  max?: Maybe<RouteMaxAggregate>;
  min?: Maybe<RouteMinAggregate>;
  sum?: Maybe<RouteSumAggregate>;
};

export type RouteAvgAggregate = {
  __typename?: 'RouteAvgAggregate';
  order?: Maybe<Scalars['Float']['output']>;
};

export type RouteConnection = {
  __typename?: 'RouteConnection';
  /** Array of edges. */
  edges: Array<RouteEdge>;
  /** Paging information */
  pageInfo: PageInfo;
  /** Fetch total count of records */
  totalCount: Scalars['Int']['output'];
};

export type RouteCountAggregate = {
  __typename?: 'RouteCountAggregate';
  component?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['Int']['output']>;
  description?: Maybe<Scalars['Int']['output']>;
  icon?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  isHidden?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['Int']['output']>;
  order?: Maybe<Scalars['Int']['output']>;
  parentId?: Maybe<Scalars['Int']['output']>;
  path?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  type?: Maybe<Scalars['Int']['output']>;
  updatedAt?: Maybe<Scalars['Int']['output']>;
};

export type RouteDeleteFilter = {
  and?: InputMaybe<Array<RouteDeleteFilter>>;
  component?: InputMaybe<StringFieldComparison>;
  createdAt?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  icon?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isHidden?: InputMaybe<BooleanFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<RouteDeleteFilter>>;
  order?: InputMaybe<NumberFieldComparison>;
  parentId?: InputMaybe<IdFilterComparison>;
  path?: InputMaybe<StringFieldComparison>;
  status?: InputMaybe<StringFieldComparison>;
  type?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type RouteDeleteResponse = {
  __typename?: 'RouteDeleteResponse';
  children?: Maybe<Array<Route>>;
  component?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  icon?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  isHidden?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  order?: Maybe<Scalars['Float']['output']>;
  parentId?: Maybe<Scalars['ID']['output']>;
  path?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type RouteEdge = {
  __typename?: 'RouteEdge';
  /** Cursor for this node. */
  cursor: Scalars['ConnectionCursor']['output'];
  /** The node containing the Route */
  node: Route;
};

export type RouteFilter = {
  and?: InputMaybe<Array<RouteFilter>>;
  component?: InputMaybe<StringFieldComparison>;
  createdAt?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  icon?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isHidden?: InputMaybe<BooleanFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<RouteFilter>>;
  order?: InputMaybe<NumberFieldComparison>;
  parentId?: InputMaybe<IdFilterComparison>;
  path?: InputMaybe<StringFieldComparison>;
  status?: InputMaybe<StringFieldComparison>;
  type?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type RouteMaxAggregate = {
  __typename?: 'RouteMaxAggregate';
  component?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  icon?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  order?: Maybe<Scalars['Float']['output']>;
  parentId?: Maybe<Scalars['ID']['output']>;
  path?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type RouteMinAggregate = {
  __typename?: 'RouteMinAggregate';
  component?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  icon?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  order?: Maybe<Scalars['Float']['output']>;
  parentId?: Maybe<Scalars['ID']['output']>;
  path?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type RouteSort = {
  direction: SortDirection;
  field: RouteSortFields;
  nulls?: InputMaybe<SortNulls>;
};

export enum RouteSortFields {
  Component = 'component',
  CreatedAt = 'createdAt',
  Description = 'description',
  Icon = 'icon',
  Id = 'id',
  IsHidden = 'isHidden',
  Name = 'name',
  Order = 'order',
  ParentId = 'parentId',
  Path = 'path',
  Status = 'status',
  Type = 'type',
  UpdatedAt = 'updatedAt'
}

export type RouteSumAggregate = {
  __typename?: 'RouteSumAggregate';
  order?: Maybe<Scalars['Float']['output']>;
};

export type RouteUpdateFilter = {
  and?: InputMaybe<Array<RouteUpdateFilter>>;
  component?: InputMaybe<StringFieldComparison>;
  createdAt?: InputMaybe<DateFieldComparison>;
  description?: InputMaybe<StringFieldComparison>;
  icon?: InputMaybe<StringFieldComparison>;
  id?: InputMaybe<IdFilterComparison>;
  isHidden?: InputMaybe<BooleanFieldComparison>;
  name?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<RouteUpdateFilter>>;
  order?: InputMaybe<NumberFieldComparison>;
  parentId?: InputMaybe<IdFilterComparison>;
  path?: InputMaybe<StringFieldComparison>;
  status?: InputMaybe<StringFieldComparison>;
  type?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
};

export type RuleCondition = {
  __typename?: 'RuleCondition';
  field: Scalars['String']['output'];
  operator: Scalars['String']['output'];
  unit?: Maybe<Scalars['String']['output']>;
  value: Scalars['String']['output'];
};

/** Sort Directions */
export enum SortDirection {
  Asc = 'ASC',
  Desc = 'DESC'
}

/** Sort Nulls Options */
export enum SortNulls {
  NullsFirst = 'NULLS_FIRST',
  NullsLast = 'NULLS_LAST'
}

export type StringFieldComparison = {
  eq?: InputMaybe<Scalars['String']['input']>;
  gt?: InputMaybe<Scalars['String']['input']>;
  gte?: InputMaybe<Scalars['String']['input']>;
  iLike?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<Scalars['String']['input']>>;
  is?: InputMaybe<Scalars['Boolean']['input']>;
  isNot?: InputMaybe<Scalars['Boolean']['input']>;
  like?: InputMaybe<Scalars['String']['input']>;
  lt?: InputMaybe<Scalars['String']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  neq?: InputMaybe<Scalars['String']['input']>;
  notILike?: InputMaybe<Scalars['String']['input']>;
  notIn?: InputMaybe<Array<Scalars['String']['input']>>;
  notLike?: InputMaybe<Scalars['String']['input']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  createdAntiBlock: AntiBlock;
  createdEmployee: Employee;
  createdGroup: Group;
  createdIpRestriction: IpRestriction;
  deletedManyAntiBlocks: DeleteManyResponse;
  deletedManyEmployees: DeleteManyResponse;
  deletedManyGroups: DeleteManyResponse;
  deletedManyIpRestrictions: DeleteManyResponse;
  deletedOneAntiBlock: AntiBlockDeleteResponse;
  deletedOneEmployee: EmployeeDeleteResponse;
  deletedOneGroup: GroupDeleteResponse;
  deletedOneIpRestriction: IpRestrictionDeleteResponse;
  updatedManyAntiBlocks: UpdateManyResponse;
  updatedManyEmployees: UpdateManyResponse;
  updatedManyGroups: UpdateManyResponse;
  updatedManyIpRestrictions: UpdateManyResponse;
  updatedOneAntiBlock: AntiBlock;
  updatedOneEmployee: Employee;
  updatedOneGroup: Group;
  updatedOneIpRestriction: IpRestriction;
};


export type SubscriptionCreatedAntiBlockArgs = {
  input?: InputMaybe<CreateAntiBlockSubscriptionFilterInput>;
};


export type SubscriptionCreatedEmployeeArgs = {
  input?: InputMaybe<CreateEmployeeSubscriptionFilterInput>;
};


export type SubscriptionCreatedGroupArgs = {
  input?: InputMaybe<CreateGroupSubscriptionFilterInput>;
};


export type SubscriptionCreatedIpRestrictionArgs = {
  input?: InputMaybe<CreateIpRestrictionSubscriptionFilterInput>;
};


export type SubscriptionDeletedOneAntiBlockArgs = {
  input?: InputMaybe<DeleteOneAntiBlockSubscriptionFilterInput>;
};


export type SubscriptionDeletedOneEmployeeArgs = {
  input?: InputMaybe<DeleteOneEmployeeSubscriptionFilterInput>;
};


export type SubscriptionDeletedOneGroupArgs = {
  input?: InputMaybe<DeleteOneGroupSubscriptionFilterInput>;
};


export type SubscriptionDeletedOneIpRestrictionArgs = {
  input?: InputMaybe<DeleteOneIpRestrictionSubscriptionFilterInput>;
};


export type SubscriptionUpdatedOneAntiBlockArgs = {
  input?: InputMaybe<UpdateOneAntiBlockSubscriptionFilterInput>;
};


export type SubscriptionUpdatedOneEmployeeArgs = {
  input?: InputMaybe<UpdateOneEmployeeSubscriptionFilterInput>;
};


export type SubscriptionUpdatedOneGroupArgs = {
  input?: InputMaybe<UpdateOneGroupSubscriptionFilterInput>;
};


export type SubscriptionUpdatedOneIpRestrictionArgs = {
  input?: InputMaybe<UpdateOneIpRestrictionSubscriptionFilterInput>;
};

export type SyncFacebookTransactionsInputDto = {
  /** VCC卡片ID */
  cardId: Scalars['ID']['input'];
  /** 同步结束日期 */
  endDate?: InputMaybe<Scalars['String']['input']>;
  /** 是否强制覆盖已有记录 */
  forceOverwrite?: InputMaybe<Scalars['Boolean']['input']>;
  /** 同步开始日期 */
  startDate?: InputMaybe<Scalars['String']['input']>;
};

export type Tenant = {
  __typename?: 'Tenant';
  db_config?: Maybe<Scalars['JSON']['output']>;
  features?: Maybe<Scalars['JSON']['output']>;
  logo?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  plan?: Maybe<Scalars['String']['output']>;
  sync_config?: Maybe<Scalars['JSON']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

/** 交易状态 */
export enum TransactionStatus {
  Failed = 'FAILED',
  Pending = 'PENDING',
  Success = 'SUCCESS'
}

/** 交易类型 */
export enum TransactionType {
  Deposit = 'DEPOSIT',
  Payment = 'PAYMENT',
  Refund = 'REFUND'
}

export type UpdateAntiBlock = {
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  relatedDomain?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  updatedAt?: InputMaybe<Scalars['DateTime']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateEmployee = {
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  isActive?: InputMaybe<Scalars['Boolean']['input']>;
  isArchived?: InputMaybe<Scalars['Boolean']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  updatedAt?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdateIpRestriction = {
  createdAt?: InputMaybe<Scalars['DateTime']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  ip?: InputMaybe<Scalars['String']['input']>;
  operator?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  updatedAt?: InputMaybe<Scalars['DateTime']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateManyAntiBlocksInput = {
  /** Filter used to find fields to update */
  filter: AntiBlockUpdateFilter;
  /** The update to apply to all records found using the filter */
  update: UpdateAntiBlock;
};

export type UpdateManyEmployeesInput = {
  /** Filter used to find fields to update */
  filter: EmployeeUpdateFilter;
  /** The update to apply to all records found using the filter */
  update: UpdateEmployee;
};

export type UpdateManyGroupsInput = {
  /** Filter used to find fields to update */
  filter: GroupUpdateFilter;
  /** The update to apply to all records found using the filter */
  update: CreateGroupInput;
};

export type UpdateManyIpRestrictionsInput = {
  /** Filter used to find fields to update */
  filter: IpRestrictionUpdateFilter;
  /** The update to apply to all records found using the filter */
  update: UpdateIpRestriction;
};

export type UpdateManyResponse = {
  __typename?: 'UpdateManyResponse';
  /** The number of records updated. */
  updatedCount: Scalars['Int']['output'];
};

export type UpdateManyRoutesInput = {
  /** Filter used to find fields to update */
  filter: RouteUpdateFilter;
  /** The update to apply to all records found using the filter */
  update: UpdateRouteInput;
};

export type UpdateOneAntiBlockInput = {
  /** The id of the record to update */
  id: Scalars['ID']['input'];
  /** The update to apply. */
  update: UpdateAntiBlock;
};

export type UpdateOneAntiBlockSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: AntiBlockSubscriptionFilter;
};

export type UpdateOneEmployeeInput = {
  /** The id of the record to update */
  id: Scalars['ID']['input'];
  /** The update to apply. */
  update: UpdateEmployee;
};

export type UpdateOneEmployeeSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: EmployeeSubscriptionFilter;
};

export type UpdateOneGroupInput = {
  /** The id of the record to update */
  id: Scalars['ID']['input'];
  /** The update to apply. */
  update: CreateGroupInput;
};

export type UpdateOneGroupSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: GroupSubscriptionFilter;
};

export type UpdateOneIpRestrictionInput = {
  /** The id of the record to update */
  id: Scalars['ID']['input'];
  /** The update to apply. */
  update: UpdateIpRestriction;
};

export type UpdateOneIpRestrictionSubscriptionFilterInput = {
  /** Specify to filter the records returned. */
  filter: IpRestrictionSubscriptionFilter;
};

export type UpdateOneRouteInput = {
  /** The id of the record to update */
  id: Scalars['ID']['input'];
  /** The update to apply. */
  update: UpdateRouteInput;
};

export type UpdateRoleInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  routeIds?: InputMaybe<Array<Scalars['String']['input']>>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateRouteInput = {
  component?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  isHidden?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  order?: InputMaybe<Scalars['Float']['input']>;
  parentId?: InputMaybe<Scalars['ID']['input']>;
  path?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  avatar?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  fullName?: InputMaybe<Scalars['String']['input']>;
  groupIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  id: Scalars['ID']['input'];
  password?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  registerIp?: InputMaybe<Scalars['String']['input']>;
  roleIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  status?: InputMaybe<UserStatus>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateVccCardInput = {
  /** 广告号存活数量 */
  adAccountStatus?: InputMaybe<Scalars['Int']['input']>;
  /** 余额 */
  balance?: InputMaybe<Scalars['Float']['input']>;
  /** 绑定次数 */
  bindCount?: InputMaybe<Scalars['Int']['input']>;
  /** 已绑定广告号 */
  boundAdAccount?: InputMaybe<Scalars['String']['input']>;
  /** 绑定的广告账户ID数组 */
  boundAdAccountIds?: InputMaybe<Array<Scalars['String']['input']>>;
  /** 持卡人 */
  cardHolder?: InputMaybe<Scalars['String']['input']>;
  /** 渠道 */
  channel?: InputMaybe<Scalars['String']['input']>;
  /** 消费 */
  consumption?: InputMaybe<Scalars['Float']['input']>;
  /** 国家 */
  country?: InputMaybe<Scalars['String']['input']>;
  /** 国家代码 */
  countryCode?: InputMaybe<Scalars['String']['input']>;
  /** 过期月 */
  expiryMonth?: InputMaybe<Scalars['String']['input']>;
  /** 过期年 */
  expiryYear?: InputMaybe<Scalars['String']['input']>;
  /** 所属群组 */
  group?: InputMaybe<Scalars['String']['input']>;
  /** 限制次数 */
  limitCount?: InputMaybe<Scalars['Int']['input']>;
  /** 备注 */
  remark?: InputMaybe<Scalars['String']['input']>;
  /** 状态 */
  status?: InputMaybe<Scalars['String']['input']>;
  /** 绑定广告号总数 */
  totalAdAccounts?: InputMaybe<Scalars['Int']['input']>;
  /** 交易数 */
  transactionCount?: InputMaybe<Scalars['Int']['input']>;
  /** 已使用次数 */
  usedCount?: InputMaybe<Scalars['Int']['input']>;
  /** 邮编 */
  zipCode?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateVccTransactionInputDto = {
  /** 交易描述 */
  description?: InputMaybe<Scalars['String']['input']>;
  /** 交易状态 */
  status?: InputMaybe<TransactionStatus>;
};

export type User = {
  __typename?: 'User';
  address?: Maybe<Scalars['String']['output']>;
  avatar?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  email: Scalars['String']['output'];
  fullName?: Maybe<Scalars['String']['output']>;
  groupIds?: Maybe<Array<Scalars['ID']['output']>>;
  groups?: Maybe<Array<Group>>;
  id?: Maybe<Scalars['ID']['output']>;
  password?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  registerIp?: Maybe<Scalars['String']['output']>;
  roleIds?: Maybe<Array<Scalars['ID']['output']>>;
  roles?: Maybe<Array<Role>>;
  status: UserStatus;
  tenant?: Maybe<Tenant>;
  tenantId?: Maybe<Scalars['String']['output']>;
  tenantName?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  username: Scalars['String']['output'];
};

export type UserConnection = {
  __typename?: 'UserConnection';
  nodes: Array<User>;
  pageInfo: UserPageInfo;
  totalCount: Scalars['Int']['output'];
};

export type UserFilterInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  fullName?: InputMaybe<Scalars['String']['input']>;
  groupIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  phone?: InputMaybe<Scalars['String']['input']>;
  roleIds?: InputMaybe<Array<Scalars['ID']['input']>>;
  status?: InputMaybe<UserStatus>;
  username?: InputMaybe<Scalars['String']['input']>;
};

export type UserListInput = {
  filter?: InputMaybe<UserFilterInput>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type UserPageInfo = {
  __typename?: 'UserPageInfo';
  hasNextPage: Scalars['Boolean']['output'];
  hasPreviousPage: Scalars['Boolean']['output'];
};

export type UserSortInput = {
  direction: Scalars['String']['input'];
  field: Scalars['String']['input'];
};

export enum UserStatus {
  Active = 'ACTIVE',
  Blocked = 'BLOCKED',
  Inactive = 'INACTIVE'
}

export type VccCard = {
  __typename?: 'VccCard';
  /** 广告号存活数量 */
  adAccountStatus: Scalars['Int']['output'];
  /** 余额 */
  balance: Scalars['Float']['output'];
  /** 绑定次数 */
  bindCount: Scalars['Int']['output'];
  /** 已绑定广告号 */
  boundAdAccount?: Maybe<Scalars['String']['output']>;
  /** 绑定的广告账户ID数组 */
  boundAdAccountIds?: Maybe<Array<Scalars['String']['output']>>;
  /** 持卡人 */
  cardHolder: Scalars['String']['output'];
  /** 卡号 */
  cardNumber: Scalars['String']['output'];
  /** 渠道 */
  channel: Scalars['String']['output'];
  /** 消费 */
  consumption: Scalars['Float']['output'];
  /** 国家 */
  country: Scalars['String']['output'];
  /** 国家代码 */
  countryCode: Scalars['String']['output'];
  /** 创建时间 */
  createdAt: Scalars['DateTime']['output'];
  /** CVV */
  cvv: Scalars['String']['output'];
  /** 过期月 */
  expiryMonth: Scalars['String']['output'];
  /** 过期年 */
  expiryYear: Scalars['String']['output'];
  /** 所属群组 */
  group?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** 限制次数 */
  limitCount: Scalars['Int']['output'];
  /** 备注 */
  remark?: Maybe<Scalars['String']['output']>;
  /** 状态 */
  status: Scalars['String']['output'];
  /** 绑定广告号总数 */
  totalAdAccounts: Scalars['Int']['output'];
  /** 交易数 */
  transactionCount: Scalars['Int']['output'];
  /** 更新时间 */
  updatedAt: Scalars['DateTime']['output'];
  /** 已使用次数 */
  usedCount: Scalars['Int']['output'];
  /** 邮编 */
  zipCode: Scalars['String']['output'];
};

export type VccCardFilterInput = {
  /** 持卡人 */
  cardHolder?: InputMaybe<Scalars['String']['input']>;
  /** 卡号 */
  cardNumber?: InputMaybe<Scalars['String']['input']>;
  /** 渠道 */
  channel?: InputMaybe<Scalars['String']['input']>;
  /** 国家 */
  country?: InputMaybe<Scalars['String']['input']>;
  /** 状态 */
  status?: InputMaybe<Scalars['String']['input']>;
  /** 租户ID */
  tenantId?: InputMaybe<Scalars['String']['input']>;
};

export type VccTransactionDto = {
  __typename?: 'VccTransactionDTO';
  /** 交易金额 */
  amount: Scalars['Float']['output'];
  /** 广告活动名称 */
  campaignName?: Maybe<Scalars['String']['output']>;
  /** VCC卡片ID */
  cardId: Scalars['ID']['output'];
  /** 卡号（脱敏显示） */
  cardNo: Scalars['String']['output'];
  /** 交易描述 */
  description?: Maybe<Scalars['String']['output']>;
  /** Facebook广告账户ID */
  facebookAccountId?: Maybe<Scalars['String']['output']>;
  /** Facebook交易ID */
  facebookTransactionId?: Maybe<Scalars['String']['output']>;
  /** 交易ID */
  id: Scalars['ID']['output'];
  /** 商户名称 */
  merchant: Scalars['String']['output'];
  /** 交易状态 */
  status: TransactionStatus;
  /** 交易时间 */
  transactionTime: Scalars['String']['output'];
  /** 交易类型 */
  type: TransactionType;
};

export type VccTransactionFilterInputDto = {
  /** VCC卡片ID */
  cardId?: InputMaybe<Scalars['ID']['input']>;
  /** 卡号模糊搜索 */
  cardNo?: InputMaybe<Scalars['String']['input']>;
  /** 结束时间（日期范围） */
  endTime?: InputMaybe<Scalars['String']['input']>;
  /** 最大金额 */
  maxAmount?: InputMaybe<Scalars['Float']['input']>;
  /** 商户名称模糊搜索 */
  merchant?: InputMaybe<Scalars['String']['input']>;
  /** 最小金额 */
  minAmount?: InputMaybe<Scalars['Float']['input']>;
  /** 开始时间（日期范围） */
  startTime?: InputMaybe<Scalars['String']['input']>;
  /** 交易状态 */
  status?: InputMaybe<TransactionStatus>;
  /** 租户ID */
  tenantId?: InputMaybe<Scalars['ID']['input']>;
  /** 交易类型 */
  type?: InputMaybe<TransactionType>;
};

export type VccTransactionPaginationInputDto = {
  /** 每页条数 */
  limit?: InputMaybe<Scalars['Int']['input']>;
  /** 页码 */
  page?: InputMaybe<Scalars['Int']['input']>;
  /** 排序字段 */
  sortBy?: InputMaybe<Scalars['String']['input']>;
  /** 排序方向 */
  sortOrder?: InputMaybe<Scalars['String']['input']>;
};

export type VccTransactionStatsDto = {
  __typename?: 'VccTransactionStatsDTO';
  /** 平均交易金额 */
  averageAmount: Scalars['Float']['output'];
  /** 失败交易数 */
  failedCount: Scalars['Int']['output'];
  /** 待处理交易数 */
  pendingCount: Scalars['Int']['output'];
  /** 成功交易数 */
  successCount: Scalars['Int']['output'];
  /** 成功率（%） */
  successRate: Scalars['Float']['output'];
  /** 总交易金额 */
  totalAmount: Scalars['Float']['output'];
  /** 总交易数 */
  totalCount: Scalars['Int']['output'];
};

export type IpRestriction = {
  __typename?: 'ipRestriction';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  operator?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type IpRestrictionAggregateFilter = {
  and?: InputMaybe<Array<IpRestrictionAggregateFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  ip?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<IpRestrictionAggregateFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type IpRestrictionAggregateGroupBy = {
  __typename?: 'ipRestrictionAggregateGroupBy';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};


export type IpRestrictionAggregateGroupByCreatedAtArgs = {
  by?: GroupBy;
};


export type IpRestrictionAggregateGroupByUpdatedAtArgs = {
  by?: GroupBy;
};

export type IpRestrictionAggregateResponse = {
  __typename?: 'ipRestrictionAggregateResponse';
  avg?: Maybe<IpRestrictionAvgAggregate>;
  count?: Maybe<IpRestrictionCountAggregate>;
  groupBy?: Maybe<IpRestrictionAggregateGroupBy>;
  max?: Maybe<IpRestrictionMaxAggregate>;
  min?: Maybe<IpRestrictionMinAggregate>;
  sum?: Maybe<IpRestrictionSumAggregate>;
};

export type IpRestrictionAvgAggregate = {
  __typename?: 'ipRestrictionAvgAggregate';
  id?: Maybe<Scalars['Float']['output']>;
};

export type IpRestrictionCountAggregate = {
  __typename?: 'ipRestrictionCountAggregate';
  createdAt?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  ip?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  tenantId?: Maybe<Scalars['Int']['output']>;
  updatedAt?: Maybe<Scalars['Int']['output']>;
  userId?: Maybe<Scalars['Int']['output']>;
};

export type IpRestrictionDeleteFilter = {
  and?: InputMaybe<Array<IpRestrictionDeleteFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  ip?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<IpRestrictionDeleteFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type IpRestrictionFilter = {
  and?: InputMaybe<Array<IpRestrictionFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  ip?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<IpRestrictionFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type IpRestrictionMaxAggregate = {
  __typename?: 'ipRestrictionMaxAggregate';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type IpRestrictionMinAggregate = {
  __typename?: 'ipRestrictionMinAggregate';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type IpRestrictionSort = {
  direction: SortDirection;
  field: IpRestrictionSortFields;
  nulls?: InputMaybe<SortNulls>;
};

export enum IpRestrictionSortFields {
  CreatedAt = 'createdAt',
  Id = 'id',
  Ip = 'ip',
  Status = 'status',
  TenantId = 'tenantId',
  UpdatedAt = 'updatedAt',
  UserId = 'userId'
}

export type IpRestrictionSubscriptionFilter = {
  and?: InputMaybe<Array<IpRestrictionSubscriptionFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  ip?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<IpRestrictionSubscriptionFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type IpRestrictionSumAggregate = {
  __typename?: 'ipRestrictionSumAggregate';
  id?: Maybe<Scalars['Float']['output']>;
};

export type IpRestrictionUpdateFilter = {
  and?: InputMaybe<Array<IpRestrictionUpdateFilter>>;
  createdAt?: InputMaybe<DateFieldComparison>;
  id?: InputMaybe<IntFieldComparison>;
  ip?: InputMaybe<StringFieldComparison>;
  or?: InputMaybe<Array<IpRestrictionUpdateFilter>>;
  status?: InputMaybe<StringFieldComparison>;
  tenantId?: InputMaybe<StringFieldComparison>;
  updatedAt?: InputMaybe<DateFieldComparison>;
  userId?: InputMaybe<StringFieldComparison>;
};

export type EmployeeQueryVariables = Exact<{ [key: string]: never; }>;


export type EmployeeQuery = { __typename?: 'Query', employees: { __typename?: 'EmployeeConnection', pageInfo: { __typename?: 'PageInfo', hasNextPage?: boolean | null, hasPreviousPage?: boolean | null, startCursor?: any | null, endCursor?: any | null }, edges: Array<{ __typename?: 'EmployeeEdge', node: { __typename?: 'Employee', id?: string | null, firstName?: string | null, lastName?: string | null } }> } };

export type EmployeeByNameQueryVariables = Exact<{
  firstNameFilter: Scalars['String']['input'];
  lastNameFilter: Scalars['String']['input'];
}>;


export type EmployeeByNameQuery = { __typename?: 'Query', employees: { __typename?: 'EmployeeConnection', totalCount: number, edges: Array<{ __typename?: 'EmployeeEdge', node: { __typename?: 'Employee', id?: string | null, firstName?: string | null, lastName?: string | null } }> } };

export type UpdateOneEmployeeMutationVariables = Exact<{
  input: UpdateOneEmployeeInput;
}>;


export type UpdateOneEmployeeMutation = { __typename?: 'Mutation', updateOneEmployee: { __typename?: 'Employee', isActive: boolean, isArchived: boolean, firstName?: string | null, lastName?: string | null } };


export const EmployeeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"employee"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"employees"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasPreviousPage"}},{"kind":"Field","name":{"kind":"Name","value":"startCursor"}},{"kind":"Field","name":{"kind":"Name","value":"endCursor"}}]}},{"kind":"Field","name":{"kind":"Name","value":"edges"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"node"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}}]}}]}}]}}]}}]} as unknown as DocumentNode<EmployeeQuery, EmployeeQueryVariables>;
export const EmployeeByNameDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"employeeByName"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"firstNameFilter"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"lastNameFilter"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"employees"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"firstName"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"firstNameFilter"}}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"lastName"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"lastNameFilter"}}}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"edges"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"node"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"totalCount"}}]}}]}}]} as unknown as DocumentNode<EmployeeByNameQuery, EmployeeByNameQueryVariables>;
export const UpdateOneEmployeeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"updateOneEmployee"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"UpdateOneEmployeeInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateOneEmployee"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isActive"}},{"kind":"Field","name":{"kind":"Name","value":"isArchived"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}}]}}]}}]} as unknown as DocumentNode<UpdateOneEmployeeMutation, UpdateOneEmployeeMutationVariables>;