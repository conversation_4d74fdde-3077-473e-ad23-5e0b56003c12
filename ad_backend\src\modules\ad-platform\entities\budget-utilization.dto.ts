import { ObjectType, Field, Float, Int } from '@nestjs/graphql';

@ObjectType()
export class BudgetUtilizationDTO {
    @Field(() => [PlatformBudgetDTO])
    platformBudgets: PlatformBudgetDTO[];

    @Field(() => [MonthlyBudgetTrendDTO])
    monthlyTrend: MonthlyBudgetTrendDTO[];
}

@ObjectType()
export class PlatformBudgetDTO {
    @Field()
    name: string;
    @Field(() => Float)
    allocated: number;
    @Field(() => Float)
    spent: number;
    @Field(() => Int)
    percentage: number;
}

@ObjectType()
export class MonthlyBudgetTrendDTO {
    @Field()
    month: string;
    @Field(() => Float)
    allocated: number;
    @Field(() => Float)
    spent: number;
} 