import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStartTimeEndTimeToAdSet1748523000000 implements MigrationInterface {
    name = 'AddStartTimeEndTimeToAdSet1748523000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" ADD COLUMN "start_time" timestamp NOT NULL DEFAULT now();`);
        await queryRunner.query(`ALTER TABLE "ad_set" ADD COLUMN "end_time" timestamp;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN "end_time";`);
        await queryRunner.query(`ALTER TABLE "ad_set" DROP COLUMN "start_time";`);
    }
} 