# 广告投放管理系统 - 前端项目文档

**更新日期：** 2025年5月8日

## 1. 项目概述

### 1.1 项目简介

广告投放管理系统是一个全面的广告管理平台，旨在帮助广告主和营销团队高效管理广告账户、创建和优化广告活动、分析广告效果，并提供风险控制功能。系统采用现代化的前端技术栈，提供直观、高效的用户界面和流畅的用户体验。

### 1.2 目标用户

- 广告主
- 营销经理
- 广告优化师
- 内容创作者
- 系统管理员

### 1.3 核心功能

- 广告账户管理
- 物料管理
- 广告活动创建与管理
- 数据分析与报表
- 自动优化系统
- 风险控制
- 用户权限管理

## 2. 技术栈

### 2.1 核心框架

- **React 18**: 用于构建用户界面的 JavaScript 库
- **TypeScript**: 添加静态类型检查的 JavaScript 超集
- **React Router v6**: 用于处理路由的库

### 2.2 UI 组件库

- **Ant Design 5.x**: 企业级 UI 设计语言和 React 组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Shadcn/ui**: 基于 Radix UI 和 Tailwind CSS 的组件集合

### 2.3 状态管理

- **React Context API**: 用于跨组件共享状态
- **React Hooks**: 用于组件内状态管理和副作用处理

### 2.4 数据可视化

- **Ant Design Charts**: 基于 G2Plot 的图表库
- **Recharts**: 基于 D3 的 React 图表库

### 2.5 工具库

- **Axios**: 用于 HTTP 请求
- **Day.js**: 轻量级日期处理库
- **Lodash**: 实用工具库

### 2.6 开发工具

- **Vite**: 现代前端构建工具
- **ESLint**: JavaScript 代码检查工具
- **Prettier**: 代码格式化工具
- **Husky**: Git hooks 工具

## 3. 项目结构

```
/ad_project_frontend
  /public                # 静态资源
  /src
    /assets              # 图片、字体等资源
    /components          # 共享组件
      /icon              # 图标组件
      /loading           # 加载组件
      /nav               # 导航组件
      /...               # 其他共享组件
    /hooks               # 自定义 Hooks
    /layouts             # 布局组件
      /dashboard         # 主布局
        /nav-bar         # 导航栏
        /side-bar        # 侧边栏
    /pages               # 页面组件
      /dashboard         # 仪表盘相关页面
        /auto-optimization  # 自动优化
        /data-analysis      # 数据分析
      /management        # 管理相关页面
        /user            # 用户管理
        /facebook        # Facebook管理
        /domain          # 域名管理
        /dofend          # 斗篷系统
        /system          # 系统设置
        /campaign        # 广告活动管理
        /material        # 物料管理
        /...             # 其他管理页面
    /router              # 路由配置
      /routes            # 路由定义
        /modules         # 路由模块
      /hooks             # 路由相关hooks
      /utils.ts          # 路由工具函数
    /services            # API服务
    /store               # 状态管理
    /types               # TypeScript类型定义
    /ui                  # UI组件
    /utils               # 工具函数
    /App.tsx             # 应用入口组件
    /main.tsx            # 应用入口文件
  /.eslintrc.js          # ESLint配置
  /.prettierrc           # Prettier配置
  /package.json          # 项目依赖
  /tsconfig.json         # TypeScript配置
  /vite.config.ts        # Vite配置
```

## 4. 路由系统

### 4.1 路由结构

路由系统采用模块化设计，每个功能模块有自己的路由配置文件，位于 `/src/router/routes/modules/` 目录下。主要路由模块包括：

- `menu-structure.tsx`: 定义菜单结构和路由配置
- `dashboard.tsx`: 仪表盘相关路由
- `management.tsx`: 管理相关路由
- `others.tsx`: 其他功能路由

### 4.2 路由类型

系统中定义了以下路由类型：

```typescript
// 路由元数据
export interface RouteMeta {
  key: string;        // 唯一标识
  label: string;      // 菜单标签
  icon?: ReactNode;   // 菜单图标
  info?: ReactNode;   // 附加信息
  hideMenu?: boolean; // 是否在菜单中隐藏
  hideTab?: boolean;  // 是否在多标签中隐藏
  disabled?: boolean; // 是否禁用
  outlet?: ReactNode; // React Router outlet
  timeStamp?: string; // 用于刷新标签
  frameSrc?: URL;     // 外部链接
  params?: Params<string>; // 动态路由参数
}

// 应用路由对象
export interface AppRouteObject extends RouteObject {
  order?: number;     // 排序
  meta?: RouteMeta;   // 路由元数据
  children?: AppRouteObject[]; // 子路由
}
```

### 4.3 权限路由

系统使用 `usePermissionRoutes` hook 来处理基于权限的路由生成：

```typescript
// src/router/hooks/use-permission-routes.tsx
export const usePermissionRoutes = () => {
  const { routes } = useRoutesContext();
  const { userInfo } = useUserContext();
  
  // 根据用户权限过滤路由
  const filterRoutesByPermission = (routes: AppRouteObject[]) => {
    // 权限过滤逻辑
  };
  
  // 获取最终的路由配置
  const permissionRoutes = useMemo(() => {
    return filterRoutesByPermission(routes);
  }, [routes, userInfo]);
  
  return permissionRoutes;
};
```

## 5. 组件系统

### 5.1 共享组件

系统中定义了多种共享组件，位于 `/src/components` 目录下：

- **Icon**: 图标组件，支持多种图标库
- **Loading**: 加载状态组件
- **Nav**: 导航组件，包括垂直和水平导航
- **Table**: 增强的表格组件
- **Form**: 增强的表单组件
- **Modal**: 模态框组件
- **Card**: 卡片组件

### 5.2 布局组件

布局组件位于 `/src/layouts` 目录下，主要包括：

- **DashboardLayout**: 主布局，包含侧边栏、顶部导航和内容区域
- **NavBar**: 顶部导航栏
- **SideBar**: 侧边导航栏

### 5.3 页面组件

页面组件位于 `/src/pages` 目录下，按功能模块组织：

- **Dashboard**: 仪表盘相关页面
- **Management**: 管理相关页面
  - **User**: 用户管理
  - **Facebook**: Facebook广告管理
  - **Domain**: 域名管理
  - **System**: 系统设置
  - **Campaign**: 广告活动管理
  - **Material**: 物料管理

### 5.4 UI组件

UI组件位于 `/src/ui` 目录下，是对Ant Design和Shadcn/ui组件的封装和扩展：

- **Button**: 按钮组件
- **Input**: 输入框组件
- **Select**: 选择器组件
- **DatePicker**: 日期选择器组件
- **Tabs**: 标签页组件
- **Badge**: 徽章组件

## 6. 状态管理

### 6.1 Context API

系统使用React Context API进行全局状态管理，主要Context包括：

- **UserContext**: 用户信息和认证状态
- **ThemeContext**: 主题设置
- **RoutesContext**: 路由配置
- **SettingsContext**: 系统设置

### 6.2 Hooks

系统定义了多种自定义Hooks，位于 `/src/hooks` 目录下：

- **useUser**: 用户相关操作
- **useTheme**: 主题相关操作
- **usePermissionRoutes**: 权限路由相关操作
- **useSettings**: 系统设置相关操作

## 7. API服务

### 7.1 服务结构

API服务位于 `/src/services` 目录下，按功能模块组织：

- **auth.ts**: 认证相关API
- **user.ts**: 用户相关API
- **campaign.ts**: 广告活动相关API
- **material.ts**: 物料相关API
- **analytics.ts**: 数据分析相关API

### 7.2 请求封装

系统使用Axios进行HTTP请求，并进行了封装：

```typescript
// src/utils/request.ts
import axios from 'axios';

const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加token等认证信息
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 处理响应数据
    return response.data;
  },
  (error) => {
    // 处理错误
    return Promise.reject(error);
  }
);

export default request;
```

## 8. 数据可视化

### 8.1 图表组件

系统使用Ant Design Charts和Recharts进行数据可视化，主要图表组件包括：

- **LineChart**: 折线图
- **BarChart**: 柱状图
- **PieChart**: 饼图
- **AreaChart**: 面积图
- **ScatterChart**: 散点图
- **HeatMap**: 热力图

### 8.2 数据处理

数据处理相关函数位于 `/src/utils/chart-data.ts`，用于处理和转换图表数据：

```typescript
// src/utils/chart-data.ts
export const transformTimeSeriesData = (data, timeKey, valueKey) => {
  // 转换时间序列数据
};

export const aggregateData = (data, groupKey, valueKey) => {
  // 聚合数据
};

export const calculatePercentage = (data, totalKey, valueKey) => {
  // 计算百分比
};
```

## 9. 主题和样式

### 9.1 主题配置

系统支持明暗两种主题，主题配置位于 `/src/store/theme-context.tsx`：

```typescript
// src/store/theme-context.tsx
export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  
  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };
  
  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
```

### 9.2 样式系统

系统使用Tailwind CSS和CSS-in-JS进行样式管理：

- **Tailwind CSS**: 用于基础样式和布局
- **CSS-in-JS**: 用于组件特定样式和主题

## 10. 国际化

### 10.1 多语言支持

系统支持中英两种语言，使用i18next进行国际化：

```typescript
// src/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import enUS from './locales/en-US';
import zhCN from './locales/zh-CN';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enUS,
      },
      zh: {
        translation: zhCN,
      },
    },
    lng: 'zh',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
```

### 10.2 语言切换

语言切换功能位于顶部导航栏，使用 `useTranslation` hook 进行语言切换：

```typescript
// src/components/language-switcher.tsx
import { useTranslation } from 'react-i18next';

export const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  
  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };
  
  return (
    <div>
      <button onClick={() => changeLanguage('en')}>English</button>
      <button onClick={() => changeLanguage('zh')}>中文</button>
    </div>
  );
};
```

## 11. 权限控制

### 11.1 权限模型

系统采用基于角色的访问控制（RBAC）模型，主要角色包括：

- **管理员**: 拥有所有权限
- **运营**: 拥有广告管理和数据分析权限
- **编辑**: 拥有物料管理权限
- **访客**: 拥有只读权限

### 11.2 权限检查

权限检查使用 `usePermission` hook 进行：

```typescript
// src/hooks/use-permission.ts
export const usePermission = () => {
  const { userInfo } = useUserContext();
  
  const hasPermission = (permission) => {
    // 检查用户是否拥有指定权限
  };
  
  return { hasPermission };
};
```

## 12. 错误处理

### 12.1 全局错误边界

系统使用React Error Boundary进行全局错误捕获：

```typescript
// src/components/error-boundary.tsx
import { Component } from 'react';

export class ErrorBoundary extends Component {
  state = { hasError: false, error: null };
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    // 记录错误信息
  }
  
  render() {
    if (this.state.hasError) {
      return <div>出错了，请刷新页面重试</div>;
    }
    
    return this.props.children;
  }
}
```

### 12.2 API错误处理

API错误处理在请求拦截器中进行：

```typescript
// src/utils/request.ts
request.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 处理错误
    if (error.response) {
      const { status } = error.response;
      
      if (status === 401) {
        // 处理未授权错误
      } else if (status === 403) {
        // 处理禁止访问错误
      } else if (status === 404) {
        // 处理资源不存在错误
      } else if (status === 500) {
        // 处理服务器错误
      }
    }
    
    return Promise.reject(error);
  }
);
```

## 13. 性能优化

### 13.1 代码分割

系统使用React.lazy和Suspense进行代码分割：

```typescript
// src/router/routes/modules/menu-structure.tsx
import { lazy, Suspense } from 'react';
import { LineLoading } from '@/components/loading';

const UserProfile = lazy(() => import('@/pages/management/user/profile'));

const routes = [
  {
    path: 'profile',
    element: (
      <Suspense fallback={<LineLoading />}>
        <UserProfile />
      </Suspense>
    ),
    meta: {
      label: '用户资料',
      key: '/user/profile',
    },
  },
];
```

### 13.2 虚拟列表

对于大量数据的列表，系统使用虚拟列表进行优化：

```typescript
// src/components/virtual-list.tsx
import { FixedSizeList } from 'react-window';

export const VirtualList = ({ items, height, width, itemSize }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      {items[index]}
    </div>
  );
  
  return (
    <FixedSizeList
      height={height}
      width={width}
      itemCount={items.length}
      itemSize={itemSize}
    >
      {Row}
    </FixedSizeList>
  );
};
```

### 13.3 缓存

系统使用React.memo和useMemo进行组件和计算结果缓存：

```typescript
// src/components/expensive-component.tsx
import { memo, useMemo } from 'react';

export const ExpensiveComponent = memo(({ data }) => {
  const processedData = useMemo(() => {
    // 复杂计算
    return data.map(item => /* 处理逻辑 */);
  }, [data]);
  
  return (
    <div>
      {processedData.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
});
```

## 14. 测试

### 14.1 单元测试

系统使用Jest和React Testing Library进行单元测试：

```typescript
// src/components/__tests__/button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../button';

describe('Button', () => {
  test('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
  
  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### 14.2 集成测试

系统使用Cypress进行集成测试：

```typescript
// cypress/integration/login.spec.js
describe('Login', () => {
  beforeEach(() => {
    cy.visit('/login');
  });
  
  it('should login successfully', () => {
    cy.get('input[name="username"]').type('admin');
    cy.get('input[name="password"]').type('admin123');
    cy.get('button[type="submit"]').click();
    cy.url().should('include', '/dashboard');
  });
  
  it('should show error message for invalid credentials', () => {
    cy.get('input[name="username"]').type('admin');
    cy.get('input[name="password"]').type('wrong');
    cy.get('button[type="submit"]').click();
    cy.contains('用户名或密码错误').should('be.visible');
  });
});
```

## 15. 部署

### 15.1 构建流程

系统使用Vite进行构建：

```bash
# 开发环境
npm run dev

# 生产环境构建
npm run build

# 预览构建结果
npm run preview
```

### 15.2 环境配置

系统使用.env文件进行环境配置：

```
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api

# .env.production
VITE_API_BASE_URL=https://api.example.com
```

### 15.3 CI/CD

系统使用GitHub Actions进行CI/CD：

```yaml
# .github/workflows/ci.yml
name: CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Build
      run: npm run build
      
    - name: Deploy
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

## 16. 开发指南

### 16.1 开发环境设置

```bash
# 克隆仓库
git clone https://github.com/your-org/ad-project.git

# 进入项目目录
cd ad-project

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 16.2 代码规范

系统使用ESLint和Prettier进行代码规范检查和格式化：

```bash
# 检查代码
npm run lint

# 格式化代码
npm run format
```

### 16.3 提交规范

系统使用Commitlint进行提交规范检查：

```
# 提交格式
<type>(<scope>): <subject>

# 示例
feat(user): add user profile page
fix(auth): fix login error
docs(readme): update installation guide
```

### 16.4 分支管理

系统使用Git Flow进行分支管理：

- **main**: 主分支，用于发布
- **develop**: 开发分支，用于集成功能
- **feature/xxx**: 功能分支，用于开发新功能
- **bugfix/xxx**: 修复分支，用于修复bug
- **release/xxx**: 发布分支，用于准备发布

## 17. 常见问题

### 17.1 路由问题

**问题**: 路由配置后页面无法访问

**解决方案**:
1. 检查路由配置是否正确
2. 检查路由组件是否正确导入
3. 检查权限设置是否正确

### 17.2 API请求问题

**问题**: API请求失败

**解决方案**:
1. 检查API地址是否正确
2. 检查网络连接是否正常
3. 检查认证信息是否正确
4. 检查请求参数是否正确

### 17.3 性能问题

**问题**: 页面加载缓慢

**解决方案**:
1. 使用React.memo避免不必要的重渲染
2. 使用useMemo缓存计算结果
3. 使用useCallback缓存函数
4. 使用代码分割减小包体积
5. 使用虚拟列表处理大量数据

## 18. 参考资源

### 18.1 官方文档

- [React 文档](https://reactjs.org/docs/getting-started.html)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [Ant Design 文档](https://ant.design/docs/react/introduce)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [Vite 文档](https://vitejs.dev/guide/)

### 18.2 学习资源

- [React Hooks 完全指南](https://www.valentinog.com/blog/hooks/)
- [TypeScript 入门教程](https://ts.xcatliu.com/)
- [Ant Design 实战教程](https://www.yuque.com/ant-design/course)

### 18.3 工具

- [React DevTools](https://chrome.google.com/webstore/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)
- [Redux DevTools](https://chrome.google.com/webstore/detail/redux-devtools/lmhkpmbekcpmknklioeibfkpmmfibljd)
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)

## 19. 联系方式

如有问题或建议，请联系：

- **项目负责人**: 张三 (<EMAIL>)
- **前端开发**: 李四 (<EMAIL>)
- **UI设计**: 王五 (<EMAIL>)
- **产品经理**: 赵六 (<EMAIL>)

## 20. 版本历史

### v1.0.0 (2025-05-05)
- 初始版本发布
- 实现基本结构

### v1.1.0 (2025-05-08)
- 重构菜单和路由系统
- 添加新页面组件
- 优化用户界面
