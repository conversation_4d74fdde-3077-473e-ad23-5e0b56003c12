import { Injectable } from '@nestjs/common';
import { axiosProxyPost, axiosProxyGet, axiosProxyDelete } from '../../../utils/axios-proxy';
import axios from 'axios';

@Injectable()
export class FacebookAdService {
    async createAd(params: {
        account_id: string;
        name: string;
        adset_id: string;
        campaign_id: string;
        status: string;
        creative?: any;
        tracking_specs?: any;
        bid_amount?: number;
        access_token: string;
    }): Promise<any> {
        // POST https://graph.facebook.com/v23.0/act_{account_id}/ads
        const url = `https://graph.facebook.com/v23.0/act_${params.account_id}/ads`;
        const payload: any = {
            name: params.name,
            adset_id: params.adset_id,
            campaign_id: params.campaign_id,
            status: params.status,
            access_token: params.access_token,
        };
        if (params.creative) payload.creative = params.creative;
        if (params.tracking_specs) payload.tracking_specs = params.tracking_specs;
        if (params.bid_amount) payload.bid_amount = params.bid_amount;
        // TODO: 你可以根据需要补充更多字段
        // 打印请求参数
        console.log('[FacebookAdService.createAd] 请求参数:', { url, payload });
        // 发送请求，走代理
        try {
            const res = await axiosProxyPost(url, payload);
            console.log('[FacebookAdService.createAd] Facebook返回:', res.data);
            return res.data;
        } catch (e) {
            console.error('[FacebookAdService.createAd] Facebook请求异常:', e?.response?.data || e);
            throw e;
        }
    }

    async updateAd(ad_id: string, params: {
        name?: string;
        status?: string;
        creative?: any;
        tracking_specs?: any;
        bid_amount?: number;
        access_token: string;
    }): Promise<any> {
        // POST https://graph.facebook.com/v23.0/{ad_id}
        const url = `https://graph.facebook.com/v23.0/${ad_id}`;
        const payload: any = { access_token: params.access_token };
        if (params.name) payload.name = params.name;
        if (params.status) payload.status = params.status;
        if (params.creative) payload.creative = params.creative;
        if (params.tracking_specs) payload.tracking_specs = params.tracking_specs;
        if (params.bid_amount) payload.bid_amount = params.bid_amount;
        console.log('[facebookAdService.updateAd] 请求:', url, payload);
        try {
            const res = await axiosProxyPost(url, payload);
            console.log('[facebookAdService.updateAd] 响应:', res.data);
            return res.data;
        } catch (e) {
            console.error('[facebookAdService.updateAd] 请求异常:', e?.response?.data || e);
            throw e;
        }
    }

    async deleteAd(ad_id: string, access_token: string): Promise<any> {
        const url = `https://graph.facebook.com/v23.0/${ad_id}`;
        try {
            const res = await axiosProxyDelete(url, { params: { access_token } });
            return res.data;
        } catch (e) {
            console.error('[facebookAdService.deleteAd] 请求异常:', e, e?.response?.data);
            throw e;
        }
    }

    async getAd(ad_id: string, access_token: string): Promise<any> {
        // GET https://graph.facebook.com/v23.0/{ad_id}?fields=...&access_token=xxx
        const url = `https://graph.facebook.com/v23.0/${ad_id}`;
        const params = {
            access_token,
            fields: [
                'id',
                'name',
                'adset_id',
                'campaign_id',
                'account_id',
                'status',
                'bid_amount',
                'effective_status',
                'configured_status',
                'created_time',
                'updated_time',
                'creative',
                'tracking_specs',
            ].join(','),
        };
        const res = await axiosProxyGet(url, { params });
        return res.data;
    }

    async listAds(params: {
        account_id: string;
        access_token: string;
        limit?: number;
        offset?: number;
    }): Promise<any[]> {
        // GET https://graph.facebook.com/v23.0/act_{account_id}/ads?fields=...&access_token=xxx
        const url = `https://graph.facebook.com/v23.0/act_${params.account_id}/ads`;
        const query: any = {
            access_token: params.access_token,
            fields: [
                'id',
                'name',
                'adset_id',
                'campaign_id',
                'account_id',
                'status',
                'bid_amount',
                'effective_status',
                'configured_status',
                'created_time',
                'updated_time',
                'creative',
                'tracking_specs',
            ].join(','),
        };
        if (params.limit) query.limit = params.limit;
        if (params.offset) query.offset = params.offset;
        const res = await axiosProxyGet(url, { params: query });
        console.log('Facebook Ads API 返回:', res.data);
        return res.data.data;
    }

    async syncAds(params: any): Promise<any> {
        // TODO: 拉取并同步 Facebook 广告数据
        return { synced: true, params };
    }

    async getAdPreview(adId: string): Promise<any> {
        // TODO: 获取广告预览
        return { previewUrl: '', adId };
    }

    async getAdInsights(adId: string): Promise<any> {
        // TODO: 获取广告洞察数据
        return { insights: {}, adId };
    }
} 