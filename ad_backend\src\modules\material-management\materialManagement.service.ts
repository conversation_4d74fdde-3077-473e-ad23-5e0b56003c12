/*
 * @Author: 潘孝权
 * @Date: 2025-05-16 13:06:10
 * @Description: 
 * @FilePath: \sking_frontend\ad_backend\src\modules\material-management\materialManagement.service.ts
 */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@dataui/crud-typeorm';
import { materialManagement } from './materialManagement.entity';
import { Repository } from 'typeorm';

// Service responsible to register (and decommission) employees
@Injectable()
export class materialManagementService extends TypeOrmCrudService<materialManagement> {
	constructor(
		@InjectRepository(materialManagement) readonly repo: Repository<materialManagement>
	) {
		super(repo);
	}

	public async getActiveWithJobSearchCriteria(): Promise<materialManagement[]> {
		return await this.repo.find({
			relations: [],
		});
	}

}
