{"version": "0.2", "language": "en", "$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "words": ["adminer", "AGPL", "Akveo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "apistage", "Artboard", "asar", "asymmetrik", "authdev", "authstage", "autobuild", "Autobuild", "automake", "billrate", "binutils", "bmap", "Buildable", "buildx", "Buildx", "cacheable", "camelcase", "chartjs", "CHATWOOT", "checkmark", "<PERSON><PERSON><PERSON><PERSON>", "circlon", "ckeditor", "cloc", "Cloudinary", "CLOUDINARY", "Codacy", "codecov", "codelyzer", "Codementor", "codeql", "commitlint", "compat", "compodoc", "configobj", "conv", "copyfiles", "cqrs", "Datepicker", "daterangepicker", "datetime", "datname", "daygrid", "DDTHH", "dependecies", "devkit", "DOCKERHUB", "doctl", "echarts", "empl", "entrypoint", "envalid", "envsubst", "esbuild", "exif", "ezkfxg", "Fargate", "fastify", "fbstate", "fieldname", "filepond", "FIVERR", "<PERSON>lickr", "fontawesome", "fontface", "formcontrol", "formcontrolname", "fortawesome", "Freemont", "fullcalendar", "Fullname", "fullscreenable", "Gitter", "<PERSON><PERSON>dy", "googlemaps", "<PERSON><PERSON>", "graphicsmagick", "GraphQLISODateTime", "grpc", "Guazy", "healthcheck", "HUBSTAFF", "huntr", "IAWS", "icnsutils", "icrud", "ienvironment", "IKPI", "ILIKE", "immer", "instantane<PERSON>us", "ionicons", "ISMTP", "IURL", "jasminewd", "javascripts", "Jen<PERSON>file", "juli<PERSON>y", "kafka<PERSON>s", "KEYCLOAK", "KEYMETRICS", "key<PERSON>ult", "keyresults", "knexfile", "Konviser", "KPIS", "kube", "kubeconfig", "kubectl", "Kubernetes", "kurkle", "Las<PERSON>", "latlng", "letsencrypt", "libappindicator", "libexecinfo", "libgcc", "lib<PERSON><PERSON>", "libstdc", "linebreak", "linkedin", "masuk", "<PERSON><PERSON><PERSON><PERSON>", "maximizable", "Metadatas", "metatype", "microservices", "<PERSON><PERSON>", "minio", "MINIO", "minte", "mjml", "multitenant", "napi", "nasm", "nats", "ncipollo", "<PERSON><PERSON><PERSON>", "nestjsx", "ngcc", "ngdevtools", "nginx", "ngneat", "ngsw", "ngtools", "notif", "nrwl", "nsis", "nstudio", "ntegral", "orgname", "Orgs", "originalname", "ormconfig", "ormlogs", "oxsecurity", "<PERSON><PERSON>r", "pagedata", "pageobjects", "<PERSON><PERSON><PERSON>", "payperiod", "pdfmake", "pgcrypto", "pgsql", "pgweb", "Pgweb", "piecelabel", "pinghost", "postbuild", "postgres", "postmarkapp", "prebuild", "preload", "prestart", "pricetags", "psql", "pulumi", "<PERSON><PERSON><PERSON>", "Quer", "randomcolor", "repos", "roboto", "Rusian", "<PERSON><PERSON><PERSON>", "screenfull", "scrollbars", "scrollblock", "setuptools", "Shaked", "Sharings", "siderbar", "signups", "sluggable", "snyk", "socicon", "solidvar", "so<PERSON>off", "squirrelly", "sslmode", "SSSSZ", "stackoverflow", "s<PERSON><PERSON><PERSON><PERSON><PERSON>l", "stylelint", "<PERSON><PERSON><PERSON>", "supertokens", "swapoff", "swiper", "Tabset", "takeshot", "timegrid", "TIMELOG", "timelogs", "Timeoff", "TIMERTRX", "TIMERTRXER", "TIMERTRXERUPDATE", "Timesheet", "timesheets", "Timesheets", "timeslot", "timeslots", "timestamptz", "titlecase", "toastr", "TOOLSDIRECTORY", "tsbuildinfo", "twing", "typeorm", "Udemy", "UNINVOICED", "UNPROCESSABLE", "unsubmit", "unzipper", "uploader", "urlpath", "Wakatime", "wasabi<PERSON>s", "webapp", "websockets", "xpack", "xplat"], "useGitignore": true, "ignorePaths": [".git/*", ".git/!{COMMIT_EDITMSG,EDITMSG}", ".git/*/**", ".yarn", "**/*.jar", ".pnp.js", "**/.git/**", ".vscode", ".giti<PERSON>re", "action/lib/**", "coverage", ".cspell.json", "cspell.json", "__snapshots__", "__recordings__", "**/coverage/**", "**/fixtures/**/*.json", "**/fixtures/sampleCode/*errors/", "**/node_modules/**", "**/vscode-extension/**", "package-lock.json", "yarn.lock", "**/assets/i18n/*.json", "**/migrations/**", "packages/**/*.seed.json", "**/*.svg", "tools/build/webpack.config.js"]}