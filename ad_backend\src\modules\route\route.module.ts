import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NestjsQueryGraphQLModule } from '@ptc-org/nestjs-query-graphql';
import { NestjsQueryTypeOrmModule } from '@ptc-org/nestjs-query-typeorm';
import { Route } from './route.entity';
import { RouteDTO, CreateRouteInput, UpdateRouteInput } from './dto/route.dto';
import { RouteResolver } from './resolvers/route.resolver';
import { RouteSeedService } from './route.seed';
import { RouteService } from './route.service';
import { RouteController } from './route.controller';
import { AuthModule } from '../auth/auth.module';

@Module({
    imports: [
        TypeOrmModule.forFeature([Route]),
        NestjsQueryTypeOrmModule.forFeature([Route]),
        AuthModule,
        NestjsQueryGraphQLModule.forFeature({
            imports: [NestjsQueryTypeOrmModule.forFeature([Route])],
            resolvers: [
                {
                    DTOClass: RouteDTO,
                    EntityClass: Route,
                    CreateDTOClass: CreateRouteInput,
                    UpdateDTOClass: UpdateRouteInput,
                    enableTotalCount: true,
                    enableAggregate: true
                }
            ]
        })
    ],
    providers: [RouteResolver, RouteSeedService, RouteService],
    controllers: [RouteController],
    exports: [RouteResolver, RouteSeedService, TypeOrmModule, RouteService]
})
export class RouteModule { } 