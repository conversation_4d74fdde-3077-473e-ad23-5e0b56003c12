import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Role } from './role.entity';
import {
    CreateRoleInput,
    DeleteRoleInput,
    RolePagination,
    RoleSearchInput,
    UpdateRoleInput
} from './dto/role.dto';
import { Route } from '../route/route.entity';
import { RolePageInfo } from '../../core/pagination';

@Injectable()
export class RoleService {
    constructor(
        @InjectRepository(Role)
        private roleRepository: Repository<Role>,
        @InjectRepository(Route)
        private routeRepository: Repository<Route>
    ) { }

    async findAll(input: RoleSearchInput, currentUser?: any): Promise<RolePagination> {
        const { page = 1, limit = 10, search, status } = input;
        const skip = (page - 1) * limit;

        const queryBuilder = this.roleRepository.createQueryBuilder('role')
            .leftJoinAndSelect('role.routes', 'routes');

        if (search) {
            queryBuilder.andWhere('role.name ILIKE :search', { search: `%${search}%` })
                .orWhere('role.description ILIKE :search', { search: `%${search}%` });
        }

        if (status) {
            queryBuilder.andWhere('role.status = :status', { status });
        }

        queryBuilder.orderBy('role.order', 'ASC')
            .addOrderBy('role.name', 'ASC')
            .skip(skip)
            .take(limit);

        const [items, totalCount] = await queryBuilder.getManyAndCount();

        const totalPages = Math.ceil(totalCount / limit);
        const pageInfo: RolePageInfo = {
            page,
            limit,
            totalPages,
            hasNextPage: page < totalPages,
            hasPreviousPage: page > 1
        };

        // 判断当前用户是否为超级管理员
        const isSuperAdmin = currentUser?.roles?.some(r => r.name === '超级管理员');

        // 转换routes为routeIds以符合DTO格式，并过滤超级管理员
        const itemsWithRouteIds = items
            .filter(role => isSuperAdmin || role.name !== '超级管理员')
            .map(role => ({
                ...role,
                routeIds: role.routes?.map(route => route.id) || []
            }));

        return {
            items: itemsWithRouteIds,
            pageInfo,
            totalCount: itemsWithRouteIds.length
        };
    }

    async findOne(id: string): Promise<Role> {
        const role = await this.roleRepository.findOne({
            where: { id },
            relations: ['routes']
        });

        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`);
        }

        return {
            ...role,
            routeIds: role.routes?.map(route => route.id) || []
        };
    }

    async create(input: CreateRoleInput): Promise<Role> {
        const { name, description, status, order, routeIds } = input;

        // 检查名称是否已存在
        const existingRole = await this.roleRepository.findOne({
            where: { name }
        });

        if (existingRole) {
            throw new Error(`Role with name "${name}" already exists`);
        }

        // 创建新角色
        const role = this.roleRepository.create({
            name,
            description,
            status: status || 'enabled',
            order: order || 0,
        });

        // 如果提供了路由ID，则关联路由
        if (routeIds && routeIds.length > 0) {
            const routes = await this.routeRepository.find({
                where: { id: In(routeIds) }
            });
            role.routes = routes;
        }

        await this.roleRepository.save(role);

        return {
            ...role,
            routeIds: role.routes?.map(route => route.id) || []
        };
    }

    async update(input: UpdateRoleInput): Promise<Role> {
        const { id, name, description, status, order, routeIds } = input;

        const role = await this.roleRepository.findOne({
            where: { id },
            relations: ['routes']
        });

        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`);
        }

        // 检查名称是否已被其他角色使用
        if (name && name !== role.name) {
            const existingRole = await this.roleRepository.findOne({
                where: { name }
            });

            if (existingRole) {
                throw new Error(`Role with name "${name}" already exists`);
            }
        }

        // 更新角色属性
        if (name !== undefined) role.name = name;
        if (description !== undefined) role.description = description;
        if (status !== undefined) role.status = status;
        if (order !== undefined) role.order = order;

        // 如果提供了路由ID，则更新路由关联
        if (routeIds !== undefined) {
            if (routeIds.length > 0) {
                const routes = await this.routeRepository.find({
                    where: { id: In(routeIds) }
                });
                role.routes = routes;
            } else {
                role.routes = [];
            }
        }

        await this.roleRepository.save(role);

        return {
            ...role,
            routeIds: role.routes?.map(route => route.id) || []
        };
    }

    async delete(input: DeleteRoleInput): Promise<boolean> {
        const { id } = input;

        const role = await this.roleRepository.findOne({
            where: { id }
        });

        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`);
        }

        await this.roleRepository.remove(role);

        return true;
    }
} 