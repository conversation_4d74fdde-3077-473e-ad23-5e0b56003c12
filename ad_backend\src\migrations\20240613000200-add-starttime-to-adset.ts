import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStartTimeToAdSet20240613000200 implements MigrationInterface {
    name = 'AddStartTimeToAdSet20240613000200'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set"
            ADD COLUMN "startTime" timestamp NOT NULL DEFAULT now()
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "ad_set"
            DROP COLUMN "startTime"
        `);
    }
} 