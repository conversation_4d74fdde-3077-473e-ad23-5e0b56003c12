import { QueryService, InjectQueryService } from '@ptc-org/nestjs-query-core';
import { CRUDResolver, InjectPubSub, PagingStrategies } from '@ptc-org/nestjs-query-graphql';
import { Resolver, Args, Context, Mutation } from '@nestjs/graphql';
import { PubSub } from 'graphql-subscriptions';
import { ipRestrictionDTO, ipRestrictionInput, CreateIpRestrictionInput } from '../dto/ipRestriction.dto';
import { ipRestriction } from '../ipRestriction.entity';
import { ArgsType, Field, InputType } from '@nestjs/graphql';
import { Inject, UseGuards } from '@nestjs/common';
import { ipRestrictionService } from '../ipRestriction.service';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';

@InputType()
class CreateOneIpRestrictionInput {
	@Field(() => ipRestrictionInput)
	ipRestriction: ipRestrictionInput;
}

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => ipRestrictionDTO)
export class ipRestrictionResolver extends CRUDResolver(ipRestrictionDTO, {
	create: { disabled: true },
	update: { disabled: true },
	delete: { disabled: true },
	enableAggregate: true,
	enableTotalCount: true,
	pagingStrategy: PagingStrategies.OFFSET
}) {
	constructor(
		@InjectQueryService(ipRestriction) readonly service: QueryService<ipRestriction>,
		@Inject('PUB_SUB') readonly pubSub: PubSub,
		private readonly ipRestrictionService: ipRestrictionService
	) {
		super(service);
	}

	@Mutation(() => ipRestrictionDTO)
	async createOneIpRestriction(
		@Args('ip', { nullable: true }) ip: string,
		@Args('userId', { nullable: true }) userId: string,
		@Args('status', { nullable: true }) status: string,
		@Args('operator', { nullable: true }) operator: string,
		@Context() ctx
	) {
		const req = ctx.req || (ctx as any).req;
		const user = req?.user || ctx.user;
		const tenantId = ctx.tenantId ?? req?.tenantId;
		const isSuperAdmin = ctx.isSuperAdmin ?? req?.isSuperAdmin;
		const data = { ip, userId, status, operator };
		console.log('createOneIpRestriction before:', data, 'tenantId:', tenantId, 'isSuperAdmin:', isSuperAdmin, 'user:', user);
		if (!user) throw new Error('未登录');
		const finalData = isSuperAdmin ? data : { ...data, tenantId };
		console.log('createOneIpRestriction after patch:', finalData);
		return this.ipRestrictionService.createWithTenant(finalData, tenantId);
	}
}
