import { Args, Mutation, Query, Resolver, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { RoleService } from '../role.service';
import { UserService } from '../../user/user.service';
import {
    CreateRoleInput,
    DeleteRoleInput,
    Role,
    RolePagination,
    RoleSearchInput,
    UpdateRoleInput
} from '../dto/role.dto';

@Resolver(() => Role)
@UseGuards(JwtAuthGuard)
export class RoleResolver {
    constructor(
        private readonly roleService: RoleService,
        private readonly userService: UserService
    ) { }

    @Query(() => RolePagination)
    async roles(@Args('input') input: RoleSearchInput, @Context() context?: any): Promise<RolePagination> {
        const currentUser = context?.req?.user ? await this.userService.findOne(context.req.user.userId, undefined, false) : undefined;
        return this.roleService.findAll(input, currentUser);
    }

    @Query(() => Role)
    async role(@Args('id') id: string): Promise<Role> {
        return this.roleService.findOne(id);
    }

    @Mutation(() => Role)
    async createRole(@Args('input') input: CreateRoleInput): Promise<Role> {
        return this.roleService.create(input);
    }

    @Mutation(() => Role)
    async updateRole(@Args('input') input: UpdateRoleInput): Promise<Role> {
        return this.roleService.update(input);
    }

    @Mutation(() => Boolean)
    async deleteRole(@Args('input') input: DeleteRoleInput): Promise<boolean> {
        return this.roleService.delete(input);
    }
} 