import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';
import { VccTransaction, TransactionStatus, TransactionType } from '../entities/vcc-transaction.entity';

// Facebook API响应接口
interface FacebookInsightsResponse {
    data: FacebookInsightData[];
    paging?: {
        cursors?: {
            before: string;
            after: string;
        };
        next?: string;
    };
}

interface FacebookInsightData {
    date_start: string;
    date_stop: string;
    account_id: string;
    account_name: string;
    spend: string;
    clicks: string;
    impressions: string;
    campaign_name?: string;
    ad_id?: string;
    ad_name?: string;
}

interface FacebookAccountInfo {
    id: string;
    name: string;
    account_status: number;
    business_name?: string;
    currency: string;
}

// 转换后的交易数据
export interface FacebookTransactionData {
    accountId: string;
    accountName: string;
    amount: number;
    date: string;
    campaignName?: string;
    adId?: string;
    adName?: string;
    currency: string;
    clicks: number;
    impressions: number;
}

@Injectable()
export class FacebookTransactionProxyService {
    private readonly logger = new Logger(FacebookTransactionProxyService.name);
    private readonly baseUrl = 'https://graph.facebook.com/v18.0';

    constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService
    ) { }

    /**
     * 从Facebook获取广告账户的消费记录
     */
    async getAdAccountTransactions(
        accountId: string,
        accessToken: string,
        startDate?: string,
        endDate?: string
    ): Promise<FacebookTransactionData[]> {
        try {
            this.logger.log(`开始获取Facebook广告账户 ${accountId} 的交易记录`);

            // 1. 先验证账户信息
            const accountInfo = await this.getAccountInfo(accountId, accessToken);

            // 2. 获取广告消费数据
            const insights = await this.getAccountInsights(accountId, accessToken, startDate, endDate);

            // 3. 转换为交易数据格式
            const transactions = this.convertInsightsToTransactions(insights, accountInfo);

            this.logger.log(`成功获取 ${transactions.length} 条交易记录`);
            return transactions;

        } catch (error: any) {
            this.logger.error(`获取Facebook交易记录失败: ${this.extractFacebookError(error)}`, error.stack);
            throw new BadRequestException(`Facebook API调用失败: ${this.extractFacebookError(error)}`);
        }
    }

    /**
     * 批量获取多个广告账户的交易记录
     */
    async getBatchAccountTransactions(
        accounts: { accountId: string; accessToken: string }[],
        startDate?: string,
        endDate?: string
    ): Promise<Map<string, FacebookTransactionData[]>> {
        const results = new Map<string, FacebookTransactionData[]>();

        for (const account of accounts) {
            try {
                const transactions = await this.getAdAccountTransactions(
                    account.accountId,
                    account.accessToken,
                    startDate,
                    endDate
                );
                results.set(account.accountId, transactions);
            } catch (error: any) {
                this.logger.warn(`账户 ${account.accountId} 获取失败: ${this.extractFacebookError(error)}`);
                results.set(account.accountId, []); // 失败的账户设置为空数组
            }
        }

        return results;
    }

    /**
     * 将Facebook交易数据转换为VCC交易记录
     */
    convertToVccTransactions(
        facebookData: FacebookTransactionData[],
        cardId: string,
        cardNo: string,
        tenantId: string
    ): Partial<VccTransaction>[] {
        return facebookData.map(data => ({
            cardId,
            cardNo: this.maskCardNumber(cardNo),
            amount: data.amount,
            merchant: 'Facebook Ads',
            transactionTime: new Date(data.date),
            status: TransactionStatus.SUCCESS,
            type: TransactionType.PAYMENT,
            facebookAccountId: data.accountId,
            facebookTransactionId: `${data.accountId}_${data.date}`,
            campaignName: data.campaignName,
            description: `Facebook广告消费 - ${data.accountName}`,
            tenantId
        }));
    }

    // ===== 私有方法 =====

    /**
     * 获取广告账户基本信息
     */
    private async getAccountInfo(accountId: string, accessToken: string): Promise<FacebookAccountInfo> {
        const url = `${this.baseUrl}/act_${accountId}`;
        const params = {
            access_token: accessToken,
            fields: 'id,name,account_status,business_name,currency'
        };

        const response: AxiosResponse<FacebookAccountInfo> = await firstValueFrom(
            this.httpService.get<FacebookAccountInfo>(url, { params })
        );

        return response.data;
    }

    /**
     * 获取广告账户洞察数据
     */
    private async getAccountInsights(
        accountId: string,
        accessToken: string,
        startDate?: string,
        endDate?: string
    ): Promise<FacebookInsightData[]> {
        const url = `${this.baseUrl}/act_${accountId}/insights`;

        // 设置默认时间范围（最近30天）
        const endDateObj = endDate ? new Date(endDate) : new Date();
        const startDateObj = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        const params = {
            access_token: accessToken,
            fields: 'account_id,account_name,spend,clicks,impressions,campaign_name',
            time_range: JSON.stringify({
                since: startDateObj.toISOString().split('T')[0],
                until: endDateObj.toISOString().split('T')[0]
            }),
            time_increment: 1, // 按天分组
            level: 'campaign', // 按广告活动级别
            limit: 100
        };

        let allData: FacebookInsightData[] = [];
        let nextPageUrl: string | undefined = url;

        // 处理分页
        while (nextPageUrl) {
            const response: AxiosResponse<FacebookInsightsResponse> = await firstValueFrom(
                this.httpService.get<FacebookInsightsResponse>(nextPageUrl, {
                    params: nextPageUrl === url ? params : undefined
                })
            );

            allData = allData.concat(response.data.data);

            // 检查是否有下一页
            nextPageUrl = response.data.paging?.next;

            // 防止无限循环
            if (allData.length > 10000) {
                this.logger.warn('数据量过大，停止获取更多页面');
                break;
            }
        }

        return allData;
    }

    /**
     * 将Facebook洞察数据转换为交易数据
     */
    private convertInsightsToTransactions(
        insights: FacebookInsightData[],
        accountInfo: FacebookAccountInfo
    ): FacebookTransactionData[] {
        return insights
            .filter(insight => parseFloat(insight.spend) > 0) // 只要有消费的记录
            .map(insight => ({
                accountId: insight.account_id,
                accountName: insight.account_name || accountInfo.name,
                amount: parseFloat(insight.spend),
                date: insight.date_start,
                campaignName: insight.campaign_name,
                currency: accountInfo.currency,
                clicks: parseInt(insight.clicks) || 0,
                impressions: parseInt(insight.impressions) || 0
            }));
    }

    /**
     * 卡号脱敏处理
     */
    private maskCardNumber(cardNumber: string): string {
        if (!cardNumber || cardNumber.length < 8) {
            return cardNumber;
        }

        // 保留前4位和后4位，中间用*替换
        const first4 = cardNumber.substring(0, 4);
        const last4 = cardNumber.substring(cardNumber.length - 4);
        const middleLength = cardNumber.length - 8;
        const masked = '*'.repeat(Math.max(middleLength, 4));

        return `${first4} ${masked} ${last4}`;
    }

    /**
     * 验证Facebook访问令牌
     */
    async validateAccessToken(accessToken: string): Promise<boolean> {
        try {
            const url = `${this.baseUrl}/me`;
            const params = {
                access_token: accessToken,
                fields: 'id,name'
            };

            await firstValueFrom(this.httpService.get(url, { params }));
            return true;
        } catch (error) {
            this.logger.warn('Facebook访问令牌验证失败', error);
            return false;
        }
    }

    /**
     * 获取Facebook错误详情
     */
    private extractFacebookError(error: any): string {
        if (error.response?.data?.error) {
            const fbError = error.response.data.error;
            return `${fbError.message} (Code: ${fbError.code}, Type: ${fbError.type})`;
        }
        return error.message || '未知Facebook API错误';
    }
} 