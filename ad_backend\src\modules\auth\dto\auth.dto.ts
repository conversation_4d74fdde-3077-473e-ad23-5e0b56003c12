import { InputType, Field, ObjectType, ID } from '@nestjs/graphql';
import { IsEmail, IsNotEmpty, IsString, MinLength, IsOptional } from 'class-validator';
import { UserDTO } from '../../user/dto/user.dto';

@InputType()
export class LoginInput {
    @Field()
    @IsNotEmpty()
    @IsString()
    username: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    password: string;
}

@ObjectType()
export class LoginResponse {
    @Field()
    userId: string;

    @Field()
    accessToken: string;

    @Field()
    username: string;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    roles?: string[];
}

@InputType()
export class RegisterInput {
    @Field()
    @IsNotEmpty()
    @IsString()
    username: string;

    @Field()
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    @MinLength(6)
    password: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    fullName?: string;

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    roleIds?: string[];

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    groupIds?: string[];

    @Field(() => String)
    @IsNotEmpty()
    tenantId: string;
}

@ObjectType()
export class RegisterResponse {
    @Field()
    userId: string;

    @Field()
    accessToken: string;

    @Field()
    username: string;
}

@ObjectType()
export class AuthUserResponse {
    @Field(() => UserDTO)
    user: UserDTO;

    @Field()
    accessToken: string;
} 