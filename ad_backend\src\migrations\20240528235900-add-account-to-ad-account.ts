import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAccountToAdAccount20240528235900 implements MigrationInterface {
    name = 'AddAccountToAdAccount20240528235900'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "account" character varying(128);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "account";
        `);
    }
} 