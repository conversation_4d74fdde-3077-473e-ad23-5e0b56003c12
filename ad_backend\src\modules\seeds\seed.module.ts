import { Module } from '@nestjs/common';
import { EmployeeModule } from '../employee/employee.module';
import { RouteModule } from '../route/route.module';
import { AntiBlockModule } from '../anti-block/antiBlock.module';
import { GroupModule } from '../group/group.module';
import { RoleModule } from '../role/role.module';
import { UserModule } from '../user/user.module';
import { VccCardModule } from '../vcc/vcc-card.module';
import { SeedDataService } from './seed.service';
import { TypeOrmConfigService } from '../../config/database';
import { ConfigurationService } from '../../config/config';
import { ipRestrictionModule } from '../ip-restriction/ipRestriction.module';

@Module({
	imports: [
		EmployeeModule,
		RouteModule,
		AntiBlockModule,
		GroupModule,
		RoleModule,
		UserModule,
		VccCardModule,
		ipRestrictionModule
	],
	providers: [
		SeedDataService,
		TypeOrmConfigService,
		ConfigurationService,
	],
	exports: [SeedDataService]
})
export class SeedModule { }
