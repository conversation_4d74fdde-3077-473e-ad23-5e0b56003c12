import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class CreativeAnalysisDTO {
    @Field(() => [CreativeTrendDTO])
    typeTrends: CreativeTrendDTO[];

    @Field(() => [PieDataDTO])
    sizePerformance: PieDataDTO[];

    @Field(() => [PieDataDTO])
    elementPerformance: PieDataDTO[];
}

@ObjectType()
export class CreativeTrendDTO {
    @Field()
    type: string;
    @Field(() => [Int])
    data: number[];
}

@ObjectType()
export class PieDataDTO {
    @Field()
    name: string;
    @Field(() => Int)
    value: number;
} 