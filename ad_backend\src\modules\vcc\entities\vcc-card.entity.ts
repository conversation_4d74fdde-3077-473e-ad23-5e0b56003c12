import {
    Entity,
    Column,
    PrimaryGeneratedColumn,
    ManyToOne,
    CreateDateColumn,
    UpdateDateColumn,
    Index
} from 'typeorm';
import { Tenant } from '../../../entity/tenant.entity';

@Entity('vcc_cards')
export class VccCard {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'varchar', length: 50, comment: '渠道' })
    channel: string;

    @Column({ type: 'varchar', length: 50, comment: '国家' })
    country: string;

    @Column({ type: 'varchar', length: 100, comment: '持卡人' })
    cardHolder: string;

    @Column({ type: 'varchar', length: 10, comment: '国家代码' })
    countryCode: string;

    @Index('idx_vcc_card_number')
    @Column({ type: 'varchar', length: 20, unique: true, comment: '卡号' })
    cardNumber: string;

    @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, comment: '余额' })
    balance: number;

    @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, comment: '消费' })
    consumption: number;

    @Column({ type: 'varchar', length: 200, nullable: true, comment: '已绑定广告号' })
    boundAdAccount: string;

    @Column({ type: 'json', nullable: true, comment: '绑定的广告账户ID数组' })
    boundAdAccountIds: string[];

    @Column({ type: 'varchar', length: 100, nullable: true, comment: '所属群组' })
    group: string;

    @Column({ type: 'int', default: 0, comment: '交易数' })
    transactionCount: number;

    @Column({ type: 'int', default: 0, comment: '广告号存活数量' })
    adAccountStatus: number;

    @Column({ type: 'varchar', length: 2, comment: '过期月' })
    expiryMonth: string;

    @Column({ type: 'varchar', length: 4, comment: '过期年' })
    expiryYear: string;

    @Column({ type: 'varchar', length: 4, comment: 'CVV' })
    cvv: string;

    @Column({ type: 'varchar', length: 20, comment: '邮编' })
    zipCode: string;

    @Column({ type: 'int', default: 0, comment: '已使用次数' })
    usedCount: number;

    @Column({ type: 'int', default: 0, comment: '绑定次数' })
    bindCount: number;

    @Column({ type: 'int', default: 0, comment: '绑定广告号总数' })
    totalAdAccounts: number;

    @Column({ type: 'int', default: 10, comment: '限制次数' })
    limitCount: number;

    @Column({
        type: 'varchar',
        length: 20,
        default: '未使用',
        comment: '状态: 未使用/已使用/已封禁'
    })
    status: string;

    @Column({ type: 'varchar', length: 500, nullable: true, comment: '备注' })
    remark: string;

    @ManyToOne(() => Tenant, { nullable: false, onDelete: 'CASCADE' })
    tenant: Tenant;

    @Column({ type: 'uuid', comment: '租户ID' })
    tenantId: string;

    @CreateDateColumn({ type: 'timestamp', comment: '创建时间' })
    createdAt: Date;

    @UpdateDateColumn({ type: 'timestamp', comment: '更新时间' })
    updatedAt: Date;
} 