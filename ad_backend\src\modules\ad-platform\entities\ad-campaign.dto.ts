import { ObjectType, Field, ID } from '@nestjs/graphql';
import { FilterableField, IDField } from '@ptc-org/nestjs-query-graphql';
import GraphQLJSON from 'graphql-type-json';
import { TenantDTO } from '../../../entity/tenant.dto';
import { AdAccountDTO } from './ad-account.dto';

@ObjectType('AdCampaign')
export class AdCampaignDTO {
  @IDField(() => ID)
  id: string;

  @FilterableField()
  platform: string;

  @FilterableField()
  campaignId: string;

  @FilterableField()
  name: string;

  @Field(() => AdAccountDTO)
  adAccount: AdAccountDTO;

  @Field(() => TenantDTO)
  tenant: TenantDTO;

  @FilterableField(() => GraphQLJSON, { nullable: true })
  raw?: any;

  @FilterableField()
  status: string;

  @Field()
  budget: number;

  @Field()
  startDate: Date;

  @Field()
  endDate: Date;

  @Field(() => [String], { nullable: true })
  tags?: string[];

  @Field({ nullable: true })
  description?: string;
}
