/*
 * @Author: 潘孝权
 * @Date: 2025-05-12 23:19:37
 * @Description: 
 * @FilePath: \sking_frontend\ad_backend\src\modules\ip-restriction\ipRestriction.module.ts
 */
import { ConfigModule } from '@nestjs/config';
import { NestjsQueryGraphQLModule, PagingStrategies, Authorizer, AuthorizerContext } from '@ptc-org/nestjs-query-graphql';
import { NestjsQueryTypeOrmModule } from '@ptc-org/nestjs-query-typeorm';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ipRestrictionDTO } from './dto/ipRestriction.dto';
import { ipRestriction } from './ipRestriction.entity';
import { ipRestrictionController } from './ipRestriction.controller';
import { ipRestrictionService } from './ipRestriction.service';
import { ipRestrictionResolver } from './resolvers/ipRestriction.resolver'
import config from '../../config/config';
import { User } from '../user/user.entity';
import { Role } from '../role/role.entity';
import { AuthModule } from '../auth/auth.module';
import { IpRestrictionTenantAuthorizer } from './ipRestriction.tenant.authorizer';
import { IpRestrictionTenantHook } from './ipRestriction.tenant.hook';

@Module({
	imports: [
		ConfigModule.forRoot({
			load: [config],
		}),
		TypeOrmModule.forFeature([ipRestriction, User, Role]),
		NestjsQueryTypeOrmModule.forFeature([ipRestriction]),
		NestjsQueryGraphQLModule.forFeature({
			imports: [NestjsQueryTypeOrmModule.forFeature([ipRestriction])],
			resolvers: [
				{
					DTOClass: ipRestrictionDTO,
					EntityClass: ipRestriction,
					enableSubscriptions: true,
					delete: {
						disabled: false,
					},
					pagingStrategy: PagingStrategies.OFFSET,
					create: {
						disabled: true,
					},
				},
			],
		}),
		AuthModule,
	],
	controllers: [ipRestrictionController],
	providers: [
		ipRestrictionService,
		ipRestrictionResolver,
		{
			provide: 'ipRestrictionDTOAuthorizer',
			useClass: IpRestrictionTenantAuthorizer,
		},
		// @ts-ignore
		IpRestrictionTenantHook,
		{
			provide: 'PUB_SUB',
			useValue: new (require('graphql-subscriptions').PubSub)(),
		},
	],
	exports: [ipRestrictionService],
})
export class ipRestrictionModule { }
