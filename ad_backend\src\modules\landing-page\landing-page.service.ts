import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LandingPage } from './landing-page.entity';
import { CreateLandingPageDto } from './dto/create-landing-page.dto';
import { UpdateLandingPageDto } from './dto/update-landing-page.dto';

@Injectable()
export class LandingPageService {
    constructor(
        @InjectRepository(LandingPage)
        private readonly landingPageRepository: Repository<LandingPage>
    ) { }

    async findAll(tenantId: string): Promise<LandingPage[]> {
        if (!tenantId) {
            throw new BadRequestException('tenantId is required');
        }
        return this.landingPageRepository.find({ 
            where: { tenantId }, 
            order: { createdAt: 'DESC' } 
        });
    }

    async findOne(id: string): Promise<LandingPage> {
        return this.landingPageRepository.findOne({ where: { id } });
    }

    async create(dto: CreateLandingPageDto, context?: any): Promise<LandingPage> {
        const entity = this.landingPageRepository.create(dto);
        entity.tenantId = context.tenantId;
        // 自动生成唯一3位数字字符串 code
        let code: string;
        let tryCount = 0;
        do {
            code = Math.floor(100 + Math.random() * 900).toString();
            // 检查唯一性
            const exist = await this.landingPageRepository.findOne({ where: { code } });
            if (!exist) break;
            tryCount++;
        } while (tryCount < 10);
        if (tryCount >= 10) throw new BadRequestException('无法生成唯一code');
        let creator: string;
        if (context?.user?.username) {
            creator = context.user.username;
        } else if (context?.req?.user?.username) {
            creator = context.req.user.username;
        } else {
            creator = '系统';
        }
        entity.code = code;
        entity.creator = creator;
        return this.landingPageRepository.save(entity);
    }

    async update(id: string, dto: UpdateLandingPageDto): Promise<LandingPage> {
        const entity = await this.findOne(id);
        Object.assign(entity, dto);
        return this.landingPageRepository.save(entity);
    }

    async remove(id: string): Promise<void> {
        await this.landingPageRepository.delete(id);
    }
} 