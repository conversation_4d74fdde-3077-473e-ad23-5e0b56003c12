import { RouteItem } from '../types';

export interface RoutesResponse {
    routes: {
        edges: {
            node: RouteItem;
        }[];
        pageInfo: {
            hasNextPage: boolean;
            hasPreviousPage: boolean;
            startCursor: string;
            endCursor: string;
        };
    };
}

export interface CreateRouteInput {
    parentId?: string | null;
    name: string;
    path: string;
    component?: string;
    icon?: string;
    order: number;
    type: 'menu' | 'page';
    status: 'enabled' | 'disabled';
    isHidden: boolean;
    description?: string;
}

export interface CreateRouteVariables {
    input: CreateRouteInput;
}

export interface CreateRouteResponse {
    createOneRoute: RouteItem;
}

export interface UpdateRouteInput {
    name: string;
    path: string;
    component?: string;
    icon?: string;
    order: number;
    type: 'menu' | 'page';
    status: 'enabled' | 'disabled';
    isHidden: boolean;
    description?: string;
    parentId?: string | null;
}

export interface UpdateOneRouteInput {
    id: string;
    update: UpdateRouteInput;
}

export interface UpdateRouteVariables {
    input: UpdateOneRouteInput;
}

export interface UpdateRouteResponse {
    updateOneRoute: RouteItem;
}

export interface DeleteRouteVariables {
    id: string;
}

export interface DeleteRouteResponse {
    deleteOneRoute: RouteItem;
} 