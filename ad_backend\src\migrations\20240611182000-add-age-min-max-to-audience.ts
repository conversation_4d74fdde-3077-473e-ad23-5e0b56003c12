import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAgeMinMaxToAudience20240611182000 implements MigrationInterface {
    name = 'AddAgeMinMaxToAudience20240611182000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "age_min" integer`);
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "age_max" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "age_min"`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "age_max"`);
    }
} 