import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VccCard } from './entities/vcc-card.entity';

@Injectable()
export class VccCardService {
    constructor(
        @InjectRepository(VccCard)
        private readonly vccCardRepo: Repository<VccCard>,
    ) { }

    // 这里可以添加业务逻辑方法
    // 由于使用GraphQL，主要逻辑在Resolver中实现
} 