import { Controller, Post, Body, Query } from '@nestjs/common';
import { FacebookPixelService } from './facebook-pixel.service';

@Controller('ad-platform/facebook/pixels')
export class FacebookPixelController {
  constructor(private readonly facebookPixelService: FacebookPixelService) {}

  @Post('sync')
  async syncPixels(@Body() body: any) {
    return this.facebookPixelService.syncPixels(body);
  }

  @Post('event')
  async reportEvent(@Query('pixelId') pixelId: string, @Body() event: any) {
    return this.facebookPixelService.reportEvent(pixelId, event);
  }
}
