import { AppDataSource } from '../src/config/data-source';
import { Ad } from '../src/modules/ad-platform/entities/ad.entity';

async function main() {
    await AppDataSource.initialize();
    const repo = AppDataSource.getRepository(Ad);
    const ads = await repo.find();
    let fixed = 0;
    for (const ad of ads) {
        if (!ad.raw || typeof ad.raw !== 'object') continue;
        let changed = false;
        // 补齐 name
        if (!ad.raw.name && ad.name) {
            ad.raw.name = ad.name;
            changed = true;
        }
        // 补齐 id
        if (!ad.raw.id && ad.adId) {
            ad.raw.id = ad.adId;
            changed = true;
        }
        // 补齐 adset_id
        if (!ad.raw.adset_id && ad.adCampaign && ad.adCampaign.facebookCampaignId) {
            ad.raw.adset_id = ad.adCampaign.facebookCampaignId;
            changed = true;
        }
        // 补齐 account_id
        if (!ad.raw.account_id && ad.adAccount && ad.adAccount.accountId) {
            ad.raw.account_id = ad.adAccount.accountId;
            changed = true;
        }
        // 补齐 created_time/updated_time
        // 已移除，因为 Ad 实体没有 createdAt/updatedAt 字段
        // 补齐 creative
        if (!ad.raw.creative && ad.adCreative && ad.adCreative.creativeId) {
            ad.raw.creative = { id: ad.adCreative.creativeId };
            changed = true;
        }
        // 补齐 status
        if (!ad.raw.status && ad.status) {
            ad.raw.status = ad.status;
            changed = true;
        }
        // 其他字段可按需补充
        if (changed) {
            await repo.save(ad);
            fixed++;
        }
    }
    console.log(`已修复 ${fixed} 条广告的 raw 字段`);
    await AppDataSource.destroy();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 