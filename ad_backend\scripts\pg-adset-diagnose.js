const { Client } = require('pg');

const client = new Client({
    host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
    port: 5432,
    user: 'yeeu',
    password: 'Yy&20240505',
    database: 'ad_auto',
});

async function main() {
    await client.connect();

    // 1. 打印 ad_set 表结构
    const columns = await client.query(`
    SELECT column_name, data_type
    FROM information_schema.columns
    WHERE table_name = 'ad_set'
    ORDER BY ordinal_position
  `);
    console.log('ad_set 表结构:');
    columns.rows.forEach(row => console.log(row));

    // 2. 打印 ad_set 表所有索引
    const indexes = await client.query(`
    SELECT * FROM pg_indexes WHERE tablename = 'ad_set'
  `);
    console.log('\nad_set 表索引:');
    indexes.rows.forEach(row => console.log(row));

    // 3. 检查是否有同名视图
    const views = await client.query(`
    SELECT table_name FROM information_schema.views WHERE table_name = 'ad_set'
  `);
    console.log('\nad_set 相关视图:');
    console.log(views.rows);

    // 4. 检查是否有关联触发器
    const triggers = await client.query(`
    SELECT trigger_name, event_manipulation, action_statement
    FROM information_schema.triggers
    WHERE event_object_table = 'ad_set'
  `);
    console.log('\nad_set 相关触发器:');
    triggers.rows.forEach(row => console.log(row));

    // 5. 检查 ad_set 表所有数据类型（抽样前5行）
    const sample = await client.query(`
    SELECT id, tenantId, pg_typeof(id) as id_type, pg_typeof(tenantId) as tenantId_type FROM "ad_set" LIMIT 5
  `);
    console.log('\nad_set 表数据类型抽样:');
    sample.rows.forEach(row => console.log(row));

    await client.end();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 