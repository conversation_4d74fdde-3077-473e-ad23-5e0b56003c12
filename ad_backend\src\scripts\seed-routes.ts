import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import { config } from 'dotenv';

// 加载环境变量
config();

// 创建数据库连接
const AppDataSource = new DataSource({
    type: "postgres",
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "5432"),
    username: process.env.DB_USER || "postgres",
    password: process.env.DB_PASS || "123456",
    database: process.env.DB_NAME || "ever",
    synchronize: true,  // 设置为 true 以确保表结构存在
    logging: true,
    entities: [path.join(__dirname, '../modules/route/route.entity{.ts,.js}')],
    migrations: [],
    subscribers: []
});

async function main() {
    try {
        // 初始化数据库连接
        await AppDataSource.initialize();
        console.log("Database connection initialized");

        // 读取SQL文件
        const sqlPath = path.join(__dirname, '../modules/route/route.seed.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');

        // 执行SQL
        await AppDataSource.query(sql);
        console.log("Routes seeded successfully");

        // 关闭连接
        await AppDataSource.destroy();
        console.log("Database connection closed");
    } catch (error) {
        console.error("Error during seeding:", error);
        process.exit(1);
    }
}

main(); 