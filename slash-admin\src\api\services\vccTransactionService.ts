import { useTranslation } from 'react-i18next';
import { useMutation, useQuery, useLazyQuery } from '@apollo/client';
import {
    GET_VCC_TRANSACTIONS,
    GET_VCC_TRANSACTION,
    GET_VCC_TRANSACTION_STATS,
    EXPORT_VCC_TRANSACTIONS,
    CREATE_VCC_TRANSACTION,
    UPDATE_VCC_TRANSACTION,
    DELETE_VCC_TRANSACTION,
    SYNC_VCC_TRANSACTIONS_FROM_FACEBOOK,
    ADD_VCC_RECHARGE_TRANSACTION,
    type VccTransactionRecord,
    type VccTransactionFilter,
    type VccTransactionPagination,
    type PaginatedVccTransactionResult,
    type VccTransactionStats,
    type CreateVccTransactionInput,
    type UpdateVccTransactionInput,
    type SyncFacebookTransactionsInput
} from '../vccTransaction.graphql';

// ===== 查询Hooks =====

/**
 * 查询交易记录列表（支持分页和过滤）
 */
export function useVccTransactions(
    filter?: VccTransactionFilter,
    pagination?: VccTransactionPagination
) {
    const { t } = useTranslation();
    return useQuery<{ vccTransactions: PaginatedVccTransactionResult }>(
        GET_VCC_TRANSACTIONS,
        {
            variables: { filter, pagination },
            errorPolicy: 'all'
        }
    );
}

/**
 * 查询单个交易记录详情
 */
export function useVccTransaction(id: string) {
    return useQuery<{ vccTransaction: VccTransactionRecord }>(
        GET_VCC_TRANSACTION,
        {
            variables: { id },
            skip: !id,
            errorPolicy: 'all'
        }
    );
}

/**
 * 获取交易统计数据
 */
export function useVccTransactionStats(filter?: VccTransactionFilter) {
    return useQuery<{ vccTransactionStats: VccTransactionStats }>(
        GET_VCC_TRANSACTION_STATS,
        {
            variables: { filter },
            errorPolicy: 'all'
        }
    );
}

/**
 * 导出交易记录
 */
export function useExportVccTransactions() {
    return useLazyQuery<{ exportVccTransactions: string }>(
        EXPORT_VCC_TRANSACTIONS,
        {
            errorPolicy: 'all'
        }
    );
}

// ===== 变更Hooks =====

/**
 * 创建交易记录
 */
export function useCreateVccTransaction() {
    return useMutation<{ createVccTransaction: VccTransactionRecord }>(
        CREATE_VCC_TRANSACTION,
        {
            errorPolicy: 'all',
            refetchQueries: [{ query: GET_VCC_TRANSACTIONS }]
        }
    );
}

/**
 * 更新交易记录
 */
export function useUpdateVccTransaction() {
    return useMutation<{ updateVccTransaction: VccTransactionRecord }>(
        UPDATE_VCC_TRANSACTION,
        {
            errorPolicy: 'all',
            refetchQueries: [{ query: GET_VCC_TRANSACTIONS }]
        }
    );
}

/**
 * 删除交易记录
 */
export function useDeleteVccTransaction() {
    return useMutation<{ deleteVccTransaction: boolean }>(
        DELETE_VCC_TRANSACTION,
        {
            errorPolicy: 'all',
            refetchQueries: [{ query: GET_VCC_TRANSACTIONS }]
        }
    );
}

/**
 * 🚀 从Facebook同步交易记录
 */
export function useSyncVccTransactionsFromFacebook() {
    return useMutation<{ syncVccTransactionsFromFacebook: VccTransactionRecord[] }>(
        SYNC_VCC_TRANSACTIONS_FROM_FACEBOOK,
        {
            errorPolicy: 'all',
            refetchQueries: [
                { query: GET_VCC_TRANSACTIONS },
                { query: GET_VCC_TRANSACTION_STATS }
            ]
        }
    );
}

/**
 * 手动添加充值记录
 */
export function useAddVccRechargeTransaction() {
    return useMutation<{ addVccRechargeTransaction: VccTransactionRecord }>(
        ADD_VCC_RECHARGE_TRANSACTION,
        {
            errorPolicy: 'all',
            refetchQueries: [
                { query: GET_VCC_TRANSACTIONS },
                { query: GET_VCC_TRANSACTION_STATS }
            ]
        }
    );
}

// ===== 工具函数 =====

/**
 * 下载导出文件
 */
export function downloadExportedData(data: string, filename: string, format: 'csv' | 'json') {
    const blob = new Blob([data], {
        type: format === 'csv' ? 'text/csv;charset=utf-8;' : 'application/json'
    });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
    URL.revokeObjectURL(link.href);
}

/**
 * 格式化交易状态显示
 */
export function formatTransactionStatus(status: string): { text: string; color: string } {
    const { t } = useTranslation();
    switch (status) {
        case 'success':
            return { text: t('pages.key_936'), color: 'green' };
        case 'pending':
            return { text: t('pages.key_568'), color: 'orange' };
        case 'failed':
            return { text: t('pages.key_598'), color: 'red' };
        default:
            return { text: status, color: 'default' };
    }
}

/**
 * 格式化交易类型显示
 */
export function formatTransactionType(type: string): { text: string; color: string } {
    const { t } = useTranslation();
    switch (type) {
        case 'payment':
            return { text: t('pages.key_1066'), color: 'blue' };
        case 'deposit':
            return { text: t('pages.key_157'), color: 'green' };
        case 'refund':
            return { text: t('pages.key_2434'), color: 'orange' };
        default:
            return { text: type, color: 'default' };
    }
}

/**
 * 格式化金额显示
 */
export function formatAmount(amount: number): string {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(Math.abs(amount));
}

/**
 * 格式化时间显示
 */
export function formatTransactionTime(timeString: string): string {
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
} 