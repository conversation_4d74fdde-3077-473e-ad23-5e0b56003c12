import { InputType, Field, ID } from '@nestjs/graphql';
import { IsString, IsOptional } from 'class-validator';

@InputType('AdAccountInput')
export class AdAccountInputDTO {
  @Field(() => ID)
  id: string;

  @Field()
  platform: string;

  @Field()
  accountId: string;

  @Field()
  account: string;

  @Field({ nullable: true })
  password?: string;

  @Field({ nullable: true })
  accessToken?: string;

  @Field({ nullable: true })
  holder?: string;

  @Field({ nullable: true })
  adNumber?: string;

  @Field({ nullable: true })
  remark?: string;

  @Field({ nullable: true })
  updatedAt?: Date;
}

@InputType()
export class AdAccountFilterInput {
  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  platform?: string;

  @Field({ nullable: true })
  tenantId?: string;

  @Field({ nullable: true })
  fbStatus?: string;

  // 可根据实际 filter 字段继续补充
}
