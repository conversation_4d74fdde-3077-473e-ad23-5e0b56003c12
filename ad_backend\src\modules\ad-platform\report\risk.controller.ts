import { Controller, Get, Post, Put, Delete, Body, Param, Query, BadRequestException, UseGuards, Req } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Risk } from './risk.entity';
import { Tenant } from '../../../entity/tenant.entity';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Controller('ad-platform/risk')
export class RiskController {
  constructor(
    @InjectRepository(Risk) private readonly riskRepo: Repository<Risk>,
    @InjectRepository(Tenant) private readonly tenantRepo: Repository<Tenant>,
  ) { }

  @Get()
  async list(@Query('page') page = 1, @Query('pageSize') pageSize = 20, @Req() req: any) {
    const tenantId = req.tenantId;
    const [data, total] = await this.riskRepo.findAndCount({
      where: { tenant: { id: tenantId } },
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { level: 'DESC' },
    });
    return { data, total, page, pageSize };
  }

  @Post()
  async create(@Body() body: any, @Req() req: any) {
    const tenantId = req.tenantId;
    if (!body.type || !body.objectId || !body.level) throw new BadRequestException('type, objectId, level required');
    const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
    if (!tenant) throw new BadRequestException('tenant not found');
    const risk = this.riskRepo.create({ ...body, tenant });
    await this.riskRepo.save(risk);
    return risk;
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() body: any, @Req() req: any) {
    const tenantId = req.tenantId;
    const risk = await this.riskRepo.findOne({ where: { id, tenant: { id: tenantId } } });
    if (!risk) throw new BadRequestException('Risk not found');
    Object.assign(risk, body);
    await this.riskRepo.save(risk);
    return risk;
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Req() req: any) {
    const tenantId = req.tenantId;
    const risk = await this.riskRepo.findOne({ where: { id, tenant: { id: tenantId } } });
    if (!risk) throw new BadRequestException('Risk not found');
    await this.riskRepo.remove(risk);
    return { success: true };
  }

  @Post('batch')
  async batchCreate(@Body() body: { riskList: any[] }, @Req() req: any) {
    const tenantId = req.tenantId;
    if (!Array.isArray(body.riskList)) throw new BadRequestException('riskList required');
    const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
    if (!tenant) throw new BadRequestException('tenant not found');
    const risks = body.riskList.map(item => this.riskRepo.create({ ...item, tenant }));
    await this.riskRepo.save((Array.isArray(risks[0]) ? risks.flat(1) : risks) as any[]);
    return { inserted: risks.length };
  }
}
