import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { useAudienceList, useCreateAudience, useUpdateAudience, useDeleteAudience } from '@/api/audience.graphql';
import { useTenant } from '@/hooks/useTenant';
import { Table, Button, Space, Modal, Form, Input, InputNumber, message, Tooltip, Switch, Select, Radio } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table/interface';
import { v4 as uuidv4 } from 'uuid';
import { useAudienceInterestOptions, useAudienceBehaviorOptions } from '@/hooks';
import { COUNTRY_OPTIONS, LANGUAGE_OPTIONS, GENDER_OPTIONS, PLATFORM_OPTIONS, AGE_RANGE_OPTIONS, COUNTRY_MAP, LANGUAGE_MAP, GENDER_MAP, getLabelByValue } from '@/constants/facebook';
import { useQuery, gql } from '@apollo/client';

// Facebook 动态选项 GraphQL 查询
const FACEBOOK_INTERESTS = gql`
  query FacebookInterests($query: String) {
    facebookInterests(query: $query) { id name }
  }
`;
const FACEBOOK_BEHAVIORS = gql`
  query FacebookBehaviors($query: String) {
    facebookBehaviors(query: $query) { id name }
  }
`;

// Facebook targeting 风格的 Audience 表单类型
type AudienceFormValues = {
  id?: string;
  name: string;
  geo_locations?: { countries?: string[] };
  excluded_geo_locations?: { countries?: string[] };
  locales?: number[];
  gender?: number;
  age_min?: number;
  age_max?: number;
  interests?: { id: string; name?: string }[];
  behaviors?: { id: string; name?: string }[];
  notes?: string;
  platform?: string[];
};

// 语言 value->id 映射表
const LOCALE_MAP = Object.fromEntries(LANGUAGE_OPTIONS.map(opt => [String(opt.value), Number(opt.value)]));

export default function AudienceManagement() {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const tenantId = useTenant();
  const [search, setSearch] = React.useState('');
  const [pageSize, setPageSize] = React.useState(20);
  const [cursor, setCursor] = React.useState<string | undefined>(undefined);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [editingAudience, setEditingAudience] = React.useState<any>(null);
  const [modalLoading, setModalLoading] = React.useState(false);
  const [deleteModal, setDeleteModal] = React.useState<{ visible: boolean, record: any | null }>({ visible: false, record: null });
  const [country, setCountry] = React.useState<string[]>([]);
  const [excludeCountry, setExcludeCountry] = React.useState<string[]>([]);
  const [interestSearch, setInterestSearch] = useState('');
  const [behaviorSearch, setBehaviorSearch] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);

  // hooks
  const filter: any = {};
  if (search) {
    filter.OR = [
      { name: { contains: search } },
      { id: { contains: search } }
    ];
  }
  const paging = { first: pageSize, after: cursor };
  const sorting: any[] = [];
  React.useEffect(() => {
    console.log('useAudienceList filter:', filter);
    console.log('useAudienceList paging:', paging);
    console.log('useAudienceList sorting:', sorting);
  }, [JSON.stringify(filter), JSON.stringify(paging), JSON.stringify(sorting)]);
  const { data, loading, refetch } = useAudienceList({
    filter,
    paging,
    sorting,
  });
  const [createAudience] = useCreateAudience();
  const [updateAudience] = useUpdateAudience();
  const [deleteAudience] = useDeleteAudience();
  const { data: interestOptData } = useQuery(FACEBOOK_INTERESTS, {
    variables: { query: interestSearch },
    skip: !interestSearch,
  });
  const { data: behaviorOptData } = useQuery(FACEBOOK_BEHAVIORS, {
    variables: { query: behaviorSearch },
    skip: !behaviorSearch,
  });

  // 适配新版数据结构
  const audienceNodes: any[] = data?.audiences || [];
  const pageInfo = data?.pageInfo;
  console.log('audienceNodes:', audienceNodes, 11111);
  // 打开新增/编辑弹窗
  const openModal = (record?: any) => {
    setEditingAudience(record || null);
    setModalVisible(true);
    if (record) {
      form.setFieldsValue({
        name: record.name,
        geo_locations: record.geo_locations,
        excluded_geo_locations: record.excluded_geo_locations,
        locales: record.locales,
        gender: Array.isArray(record.genders) ? record.genders[0] : record.gender ?? null,
        age_min: record.age_min,
        age_max: record.age_max,
        interests: record.interests,
        behaviors: record.behaviors,
        notes: record.notes,
        platform: record.platform || ['facebook'],
      });
    } else {
      form.resetFields();
      form.setFieldsValue({ platform: ['facebook'], gender: null });
    }
  };

  // interests/behaviors 选项库（动态接口）
  type OptionType = { id: string; name?: string };
  const INTEREST_OPTIONS = (interestOptData?.facebookInterests || []).map((opt: OptionType) => ({ label: opt.name || opt.id, value: opt.id, name: opt.name }));
  const BEHAVIOR_OPTIONS = (behaviorOptData?.facebookBehaviors || []).map((opt: OptionType) => ({ label: opt.name || opt.id, value: opt.id, name: opt.name }));

  // 提交表单
  const handleModalOk = async () => {
    try {
      setModalLoading(true);
      const values = await form.validateFields();
      // 防止字符串 "[]" 传递
      const fixArray = (v: any) => (v === '[]' ? [] : v);
      // 只保留必填字段
      const submitValues: any = {
        name: values.name,
        locales: fixArray((values.locales || []).map((val: any) => {
          if (typeof val === 'number') return val;
          if (LOCALE_MAP[val]) return LOCALE_MAP[val];
          return undefined;
        }).filter(Boolean)),
        id: editingAudience ? editingAudience.id : uuidv4(),
      };
      // 国家码映射，假设 COUNTRY_OPTIONS 里 value 为 ISO code
      const toCountryCodes = (arr: any[]) => (arr || []).map(val => {
        const opt = COUNTRY_OPTIONS.find(opt => opt.value === val || opt.label === val);
        return opt ? opt.value : val;
      });
      // geo_locations
      submitValues.geo_locations = {
        countries: toCountryCodes(values.geo_locations?.countries || []),
        regions: [],
        cities: [],
      };
      // excluded_geo_locations
      if (values.excluded_geo_locations && values.excluded_geo_locations.countries && values.excluded_geo_locations.countries.length > 0) {
        submitValues.excluded_geo_locations = {
          countries: toCountryCodes(values.excluded_geo_locations.countries),
          regions: [],
          cities: [],
        };
      }
      if (typeof values.gender === 'number') submitValues.gender = values.gender;
      if (typeof values.age_min === 'number') submitValues.age_min = values.age_min;
      if (typeof values.age_max === 'number') submitValues.age_max = values.age_max;
      if (Array.isArray(fixArray(values.interests)) && fixArray(values.interests).length > 0) {
        submitValues.interests = fixArray(values.interests).map((i: any) => {
          if (typeof i === 'string') {
            // 从INTEREST_OPTIONS查找name
            const found = INTEREST_OPTIONS.find((opt: any) => opt.value === i || opt.id === i);
            return { id: i, name: found?.name || found?.label || '' };
          }
          if (i.value) {
            const found = INTEREST_OPTIONS.find((opt: any) => opt.value === i.value || opt.id === i.value);
            return { id: i.value, name: found?.name || found?.label || i.label || '' };
          }
          if (i.id) {
            return { id: i.id, name: i.name || '' };
          }
          return null;
        }).filter(Boolean);
      }
      if (Array.isArray(fixArray(values.behaviors)) && fixArray(values.behaviors).length > 0) {
        submitValues.behaviors = fixArray(values.behaviors).map((i: any) => {
          if (typeof i === 'string') {
            const found = BEHAVIOR_OPTIONS.find((opt: any) => opt.value === i || opt.id === i);
            return { id: i, name: found?.name || found?.label || '' };
          }
          if (i.value) {
            const found = BEHAVIOR_OPTIONS.find((opt: any) => opt.value === i.value || opt.id === i.value);
            return { id: i.value, name: found?.name || found?.label || i.label || '' };
          }
          if (i.id) {
            return { id: i.id, name: i.name || '' };
          }
          return null;
        }).filter(Boolean);
      }
      if (Array.isArray(fixArray(values.platform)) && fixArray(values.platform).length > 0) {
        submitValues.platform = fixArray(values.platform);
      }
      if (values.notes && values.notes.trim()) submitValues.notes = values.notes.trim();
      // mustInclude/exclude 不传
      console.log('提交参数 submitValues:', submitValues, JSON.stringify(submitValues));
      if (editingAudience) {
        await updateAudience({ variables: { id: editingAudience.id, input: submitValues } });
        message.success(t('pages.key_1858'));
      } else {
        await createAudience({ variables: { input: submitValues } });
        message.success(t('pages.key_1149'));
      }
      setModalVisible(false);
      setEditingAudience(null);
      refetch();
    } catch (e: any) {
      message.error(e.message || t('pages.key_1060'));
    } finally {
      setModalLoading(false);
    }
  };

  // 删除受众
  const handleDelete = (record: any) => {
    setDeleteModal({ visible: true, record });
  };

  const handleDeleteOk = async () => {
    const record = deleteModal.record;
    if (!record) return;
    setDeleteLoading(true);
    try {
      const res = await deleteAudience({ variables: { id: record.id } });
      message.success(t('pages.key_294'));
      refetch();
    } catch (e: any) {
      message.error(e.message || t('pages.key_293'));
    } finally {
      setDeleteModal({ visible: false, record: null });
      setDeleteLoading(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({ visible: false, record: null });
  };

  const renderUuid = (value: string) => {
    if (!value) return '';
    if (value.length <= 8) return value;
    return (
      <Tooltip title={value}>
        {value.slice(0, 4)}...{value.slice(-4)}
      </Tooltip>
    );
  };

  const columns: ColumnsType<any> = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80, render: renderUuid },
    { title: t('pages.key_483'), dataIndex: 'name', key: 'name', width: 120, render: (text: string | null) => text || t('pages.key_1305') },
    { title: t('pages.key_524'), dataIndex: ['geo_locations', 'countries'], key: 'geo_locations', width: 120, render: (arr: string[]) => getLabelByValue(COUNTRY_MAP, arr) },
    { title: t('pages.key_1017'), dataIndex: ['excluded_geo_locations', 'countries'], key: 'excluded_geo_locations', width: 120, render: (arr: string[]) => getLabelByValue(COUNTRY_MAP, arr) },
    { title: t('pages.key_2142'), dataIndex: 'locales', key: 'locales', width: 100, render: (arr: number[]) => getLabelByValue(LANGUAGE_MAP, arr) },
    { title: t('pages.key_903'), dataIndex: 'gender', key: 'gender', width: 80, render: (gender: number | null) => getLabelByValue(GENDER_MAP, gender) },
    { title: t('pages.key_1278'), dataIndex: 'age_min', key: 'age_min', width: 80 },
    { title: t('pages.key_1275'), dataIndex: 'age_max', key: 'age_max', width: 80 },
    { title: t('pages.key_189'), dataIndex: 'interests', key: 'interests', width: 150, render: (arr: any[]) => arr?.map(i => i.name || i.id).join(', ') },
    { title: t('pages.key_2021'), dataIndex: 'behaviors', key: 'behaviors', width: 150, render: (arr: any[]) => arr?.map(i => i.name || i.id).join(', ') },
    { title: t('pages.key_588'), dataIndex: 'notes', key: 'notes', width: 150 },
    {
      title: t('pages.key_1058'),
      key: 'actions',
      width: 160,
      render: (_: any, record: any) => (
        <Space>
          <Button size="small" icon={<EditOutlined />} onClick={() => openModal(record)}>{t('pages.key_1853')}</Button>
          <Button size="small" icon={<DeleteOutlined />} danger onClick={() => handleDelete(record)}>{t('pages.key_287')}</Button>
        </Space>
      ),
    },
  ];

  React.useEffect(() => {
    console.log('AudienceManagement mounted');
    return () => {
      console.log('AudienceManagement unmounted');
    };
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{t('pages.key_419')}</h1>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => openModal()}>{t('pages.key_1136')}</Button>
      </div>
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="mb-4">
          <Space>
            <input
              placeholder={t('pages.key_1045')}
              value={search}
              onChange={e => setSearch(e.target.value)}
              style={{ width: 200, height: 32, border: '1px solid #d9d9d9', borderRadius: 4, paddingLeft: 8 }}
            />
            <Button onClick={() => { setCursor(undefined); refetch(); }} type="default">{t('pages.key_1044')}</Button>
          </Space>
        </div>
        <Table
          columns={columns}
          dataSource={audienceNodes}
          loading={loading}
          rowKey="id"
          scroll={{ x: 900 }}
          pagination={false}
        />
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          {pageInfo?.hasPreviousPage && (
            <Button style={{ marginRight: 8 }} onClick={() => setCursor(pageInfo.startCursor)}>{t('pages.key_1')}</Button>
          )}
          {pageInfo?.hasNextPage && (
            <Button type="primary" onClick={() => setCursor(pageInfo.endCursor)}>{t('pages.key_9')}</Button>
          )}
        </div>
      </div>
      <Modal
        title={editingAudience ? t('pages.key_1854') : t('pages.key_1145')}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => { setModalVisible(false); setEditingAudience(null); }}
        confirmLoading={modalLoading}
        destroyOnHidden
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Form.Item name="name" label={t('pages.key_483')} rules={[{ required: true, message: t('pages.key_2181') }]}> <Input /> </Form.Item>
          <Form.Item name={["geo_locations", "countries"]} label={t('pages.key_524')} rules={[{ required: true, message: t('pages.key_2255') }]}> <Select mode="multiple" options={COUNTRY_OPTIONS.map(opt => ({ ...opt, key: opt.value }))} style={{ width: '100%' }} /> </Form.Item>
          <Form.Item name={["excluded_geo_locations", "countries"]} label={t('pages.key_1017')}> <Select mode="multiple" options={COUNTRY_OPTIONS.map(opt => ({ ...opt, key: opt.value }))} style={{ width: '100%' }} /> </Form.Item>
          <Form.Item name="locales" label={t('pages.key_2142')} rules={[{ required: true, message: t('pages.key_2288') }]}> <Select mode="multiple" options={LANGUAGE_OPTIONS.map(opt => ({ label: opt.label, value: opt.value }))} style={{ width: '100%' }} /> </Form.Item>
          <Form.Item name="gender" label={t('pages.key_903')}> <Select style={{ width: '100%' }} allowClear placeholder={t('pages.key_2263')}> <Select.Option value={null}>{t('pages.key_20')}</Select.Option> <Select.Option value={1}>男</Select.Option> <Select.Option value={2}>女</Select.Option> </Select> </Form.Item>
          <Form.Item name="age_min" label={t('pages.key_1278')}> <InputNumber min={18} max={65} style={{ width: '100%' }} placeholder={t('pages.key_1277')} /> </Form.Item>
          <Form.Item name="age_max" label={t('pages.key_1275')}> <InputNumber min={13} max={65} style={{ width: '100%' }} placeholder={t('pages.key_1274')} /> </Form.Item>
          <Form.Item name="interests" label={t('pages.key_189')}> <Select mode="multiple" labelInValue showSearch filterOption={false} onSearch={setInterestSearch} options={INTEREST_OPTIONS} style={{ width: '100%' }} placeholder={t('pages.key_2174')} /> </Form.Item>
          <Form.Item name="behaviors" label={t('pages.key_2021')}> <Select mode="multiple" labelInValue showSearch filterOption={false} onSearch={setBehaviorSearch} options={BEHAVIOR_OPTIONS} style={{ width: '100%' }} placeholder={t('pages.key_2175')} /> </Form.Item>
          <Form.Item name="notes" label={t('pages.key_588')}> <Input.TextArea rows={2} /> </Form.Item>
        </Form>
      </Modal>
      <Modal
        open={deleteModal.visible}
        title={t('pages.key_287')}
        onOk={handleDeleteOk}
        onCancel={handleDeleteCancel}
        okText={t('pages.key_287')}
        okType="danger"
        cancelText={t('pages.key_407')}
        destroyOnHidden
        confirmLoading={deleteLoading}
      >
        <div>受众名称：{deleteModal.record?.name}</div>
      </Modal>
    </div>
  );
}
