import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveLandingPageNameFromPromotion1700000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN IF EXISTS "landingPageName"`);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "promotion" ADD COLUMN "landingPageName" character varying(128)`);
    }
} 