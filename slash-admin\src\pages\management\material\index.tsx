import { useTranslation } from 'react-i18next';
import { Table, Button, Modal, message, Space } from "antd";
import { useQuery } from '@apollo/client';
import { GET_MATERIAL_LIST } from '@/api/material.graphql';
import { useTenant } from '@/hooks/useTenant';

export default function MaterialManagement() {
  const { t } = useTranslation();
  const tenantId = useTenant();
  const { data, loading, refetch } = useQuery(GET_MATERIAL_LIST, {
    variables: {
      filter: { tenantId },
      paging: { first: 20 },
      sorting: [{ field: 'createdAt', direction: 'DESC' }]
    }
  });

  return (
    <div>
      <Table
        rowKey="id"
        columns={[
          { title: t('pages.key_1596'), dataIndex: "name" },
          { title: t('pages.key_1765'), dataIndex: "type" },
          { title: t('pages.key_1602'), dataIndex: "status" },
          { title: "URL", dataIndex: "url" },
          { title: t('pages.key_197'), dataIndex: "content" },
        ]}
        dataSource={data?.materials?.edges?.map((edge: any) => edge.node) || []}
        loading={loading}
        pagination={{ pageSize: 20 }}
      />
    </div>
  );
}
