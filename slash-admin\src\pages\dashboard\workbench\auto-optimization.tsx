import { useTranslation } from 'react-i18next';
import { Icon } from "@/components/icon";
import { But<PERSON> } from "@/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Progress } from "@/ui/progress";
import { Switch } from "@/ui/switch";
import { cn } from "@/utils";
import { useState, useEffect } from "react";
import { Badge } from "@/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/ui/tooltip";
import { Alert, AlertDescription, AlertTitle } from "@/ui/alert";
import { useQuery } from '@apollo/client';
import { GET_AUTO_OPTIMIZATION } from '@/api/autoOptimization.graphql';

type OptimizationType = "budget" | "bidding" | "targeting";

type AutoOptimizationProps = {
  type: OptimizationType;
};

interface OptimizationDataBase {
  status: string;
  progress: number;
  lastOptimized: string;
  recommendations: number;
}

interface BudgetData extends OptimizationDataBase {
  potentialSavings: string;
}

interface BiddingData extends OptimizationDataBase {
  potentialImprovement: string;
}

interface TargetingData extends OptimizationDataBase {
  potentialReach: string;
}

interface OptimizationData {
  budget: BudgetData;
  bidding: BiddingData;
  targeting: TargetingData;
}

const AutoOptimization = ({ type }: AutoOptimizationProps) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<OptimizationType>(type || "budget");
  const [autoMode, setAutoMode] = useState({
    budget: true,
    bidding: true,
    targeting: false
  });
  const [optimizing, setOptimizing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // 优化历史记录可保留模拟
  const [optimizationHistory] = useState([
    { date: "2025-05-07", type: "budget", result: "节省预算 ¥1,200" },
    { date: "2025-05-06", type: "bidding", result: "提升点击率 8.5%" },
    { date: "2025-05-05", type: "targeting", result: "增加目标受众 120k" },
    { date: "2025-05-04", type: "budget", result: "节省预算 ¥850" },
  ]);

  const { data, loading, error } = useQuery(GET_AUTO_OPTIMIZATION, {
    variables: { type: activeTab },
    pollInterval: 60000,
  });

  useEffect(() => {
    if (showSuccess) {
      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showSuccess]);

  const handleAutoModeChange = (type: OptimizationType) => {
    setAutoMode(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const handleOptimizeNow = () => {
    setOptimizing(true);
    setTimeout(() => {
      setOptimizing(false);
      setShowSuccess(true);
    }, 2000);
  };

  const renderTabContent = () => {
    if (!data || !data.autoOptimization) return null;
    const opt = data.autoOptimization;
    switch (activeTab) {
      case "budget":
        return (
          <div className="space-y-4 mt-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-medium">{t('pages.key_2561')}</h4>
                <p className="text-sm text-muted-foreground">
                  自动调整广告预算分配以提高ROI
                </p>
              </div>
              <Switch
                checked={autoMode.budget}
                onCheckedChange={() => handleAutoModeChange("budget")}
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">{t('pages.key_102')}</span>
                <span className="text-sm font-medium">{opt.progress}%</span>
              </div>
              <Progress value={opt.progress} />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">{t('pages.key_1286')}</span>
                <p className="font-medium">{opt.lastOptimized}</p>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">{t('pages.key_1578')}</span>
                <p className="font-medium text-green-600">{opt.potentialSavings}</p>
              </div>
            </div>

            <Button className="w-full" variant="outline">{t('pages.key_1347')}</Button>
          </div>
        );
      case "bidding":
        return (
          <div className="space-y-4 mt-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-medium">{t('pages.key_217')}</h4>
                <p className="text-sm text-muted-foreground">{t('pages.key_1220')}</p>
              </div>
              <Switch
                checked={autoMode.bidding}
                onCheckedChange={() => handleAutoModeChange("bidding")}
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">{t('pages.key_102')}</span>
                <span className="text-sm font-medium">{opt.progress}%</span>
              </div>
              <Progress value={opt.progress} />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">{t('pages.key_1286')}</span>
                <p className="font-medium">{opt.lastOptimized}</p>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">{t('pages.key_1577')}</span>
                <p className="font-medium text-green-600">{opt.potentialImprovement}</p>
              </div>
            </div>

            <Button className="w-full" variant="outline">{t('pages.key_1347')}</Button>
          </div>
        );
      case "targeting":
        return (
          <div className="space-y-4 mt-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-medium">{t('pages.key_1675')}</h4>
                <p className="text-sm text-muted-foreground">{t('pages.key_1911')}</p>
              </div>
              <Switch
                checked={autoMode.targeting}
                onCheckedChange={() => handleAutoModeChange("targeting")}
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">{t('pages.key_102')}</span>
                <span className="text-sm font-medium">{opt.progress}%</span>
              </div>
              <Progress value={opt.progress} />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">{t('pages.key_1286')}</span>
                <p className="font-medium">{opt.lastOptimized}</p>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">{t('pages.key_1579')}</span>
                <p className="font-medium text-green-600">{opt.potentialReach}</p>
              </div>
            </div>

            <Button className="w-full" variant="outline">{t('pages.key_1347')}</Button>
          </div>
        );
      default:
        return null;
    }
  };

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('pages.key_1902')}</CardTitle>
      </CardHeader>
      <CardContent>
        {showSuccess && (
          <Alert variant="success" className="mb-4">
            <AlertTitle>{t('pages.key_100')}</AlertTitle>
            <AlertDescription>
              {activeTab === "budget" ? "预算优化已完成，已为您节省了部分预算。" :
                activeTab === "bidding" ? "竞价优化已完成，已提升您的广告效果。" :
                  "定向优化已完成，已扩大您的受众覆盖面。"}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex space-x-1 rounded-md bg-muted p-1">
          <Button
            variant={activeTab === "budget" ? "default" : "ghost"}
            className="w-full"
            onClick={() => setActiveTab("budget")}
          >
            <Icon icon="solar:wallet-money-bold-duotone" className="mr-2" />{t('pages.key_2556')}</Button>
          <Button
            variant={activeTab === "bidding" ? "default" : "ghost"}
            className="w-full"
            onClick={() => setActiveTab("bidding")}
          >
            <Icon icon="solar:chart-bold-duotone" className="mr-2" />{t('pages.key_1745')}</Button>
          <Button
            variant={activeTab === "targeting" ? "default" : "ghost"}
            className="w-full"
            onClick={() => setActiveTab("targeting")}
          >
            <Icon icon="solar:user-id-bold-duotone" className="mr-2" />{t('pages.key_658')}</Button>
        </div>

        <div className="mt-4">
          {renderTabContent()}

          <div className="mt-6">
            <Button
              onClick={handleOptimizeNow}
              disabled={optimizing}
              className="w-full"
            >
              {optimizing ? (
                <>
                  <Icon icon="svg-spinners:180-ring" className="mr-2" />
                  正在优化...
                </>
              ) : (
                <>
                  <Icon icon="solar:bolt-bold-duotone" className="mr-2" />{t('pages.key_1740')}</>
              )}
            </Button>
          </div>

          <div className="mt-6">
            <h4 className="text-sm font-medium mb-2">{t('pages.key_98')}</h4>
            <div className="space-y-2">
              {optimizationHistory.map((item, index) => (
                <div key={index} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                  <div className="flex items-center">
                    <Badge variant="outline" className="mr-2">
                      {item.date}
                    </Badge>
                    <span className="text-sm">
                      {item.type === "budget" ? t('pages.key_2557') :
                        item.type === "bidding" ? t('pages.key_1746') :
                          t('pages.key_659')}
                    </span>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="text-sm font-medium">{item.result}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('pages.key_1586')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AutoOptimization;
