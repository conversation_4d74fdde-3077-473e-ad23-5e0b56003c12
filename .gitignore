# Dependencies
node_modules
**/node_modules
**/node_modules/**
.pnpm-store/
.npm
.yarn
.yarn-cache
.pnp.*

# Build outputs
dist
**/dist
.build
**/.build

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Test coverage
coverage
.nyc_output

# Temporary files
*.local
.temp
.cache

# Build analysis
stats.html

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo 