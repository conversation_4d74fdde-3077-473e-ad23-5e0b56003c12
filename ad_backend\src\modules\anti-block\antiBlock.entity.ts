// src/modules/your-module/entities/anti-block.entity.ts

import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class AntiBlock {
  @PrimaryGeneratedColumn()
  id?: number; // 改成 number 类型

  @Column({ nullable: true })
  url?: string;

  @Column({ nullable: true })
  userId?: string;

  @Column({ nullable: true })
  status?: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  relatedDomain?: string;

  @Column({ nullable: true })
  tenantId?: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt?: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;
}