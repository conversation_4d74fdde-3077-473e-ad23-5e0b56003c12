import { MigrationInterface, QueryRunner } from "typeorm";

export class BehaviorsJsonbToTextarray20240608152000 implements MigrationInterface {
    name = 'BehaviorsJsonbToTextarray20240608152000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN "behaviors_tmp" text[];`);
        await queryRunner.query(`
            UPDATE "audience"
            SET "behaviors_tmp" = (
                SELECT array_agg(elem->>'id')
                FROM jsonb_array_elements("behaviors") elem
                WHERE elem ? 'id'
            )
            WHERE pg_typeof("behaviors")::text = 'jsonb';
        `);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN "behaviors";`);
        await queryRunner.query(`ALTER TABLE "audience" RENAME COLUMN "behaviors_tmp" TO "behaviors";`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 不做回滚
    }
} 