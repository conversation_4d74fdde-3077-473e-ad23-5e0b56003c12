import { InputType, ObjectType, Field, ID, Float, Int } from '@nestjs/graphql';
import { IsOptional, IsUUID, IsEnum, IsNumber, IsString, IsDateString, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { TransactionType, TransactionStatus } from '../entities/vcc-transaction.entity';

// ===== 输出DTO - 与前端TransactionRecord完全对应 =====

@ObjectType()
export class VccTransactionDTO {
    @Field(() => ID, { description: '交易ID' })
    id: string;

    @Field(() => ID, { description: 'VCC卡片ID' })
    cardId: string;

    @Field({ description: '卡号（脱敏显示）' })
    cardNo: string;

    @Field(() => Float, { description: '交易金额' })
    amount: number;

    @Field({ description: '商户名称' })
    merchant: string;

    @Field({ description: '交易时间' })
    transactionTime: string; // 前端期望字符串格式

    @Field(() => TransactionStatus, { description: '交易状态' })
    status: TransactionStatus;

    @Field(() => TransactionType, { description: '交易类型' })
    type: TransactionType;

    @Field({ nullable: true, description: 'Facebook广告账户ID' })
    facebookAccountId?: string;

    @Field({ nullable: true, description: 'Facebook交易ID' })
    facebookTransactionId?: string;

    @Field({ nullable: true, description: '广告活动名称' })
    campaignName?: string;

    @Field({ nullable: true, description: '交易描述' })
    description?: string;
}

// ===== 输入DTO =====

@InputType()
export class CreateVccTransactionInputDTO {
    @Field(() => ID, { description: 'VCC卡片ID' })
    @IsUUID('4', { message: 'VCC卡片ID必须是有效的UUID' })
    cardId: string;

    @Field(() => Float, { description: '交易金额' })
    @IsNumber({ maxDecimalPlaces: 2 }, { message: '交易金额最多保留2位小数' })
    @Min(0.01, { message: '交易金额必须大于0.01' })
    @Max(999999.99, { message: '交易金额不能超过999999.99' })
    amount: number;

    @Field({ description: '商户名称' })
    @IsString({ message: '商户名称必须是字符串' })
    merchant: string;

    @Field({ description: '交易时间' })
    @IsDateString({}, { message: '交易时间格式不正确' })
    transactionTime: string;

    @Field(() => TransactionStatus, { description: '交易状态' })
    @IsEnum(TransactionStatus, { message: '交易状态不合法' })
    status: TransactionStatus;

    @Field(() => TransactionType, { description: '交易类型' })
    @IsEnum(TransactionType, { message: '交易类型不合法' })
    type: TransactionType;

    @Field({ nullable: true, description: 'Facebook广告账户ID' })
    @IsOptional()
    @IsString({ message: 'Facebook广告账户ID必须是字符串' })
    facebookAccountId?: string;

    @Field({ nullable: true, description: 'Facebook交易ID' })
    @IsOptional()
    @IsString({ message: 'Facebook交易ID必须是字符串' })
    facebookTransactionId?: string;

    @Field({ nullable: true, description: '广告活动名称' })
    @IsOptional()
    @IsString({ message: '广告活动名称必须是字符串' })
    campaignName?: string;

    @Field({ nullable: true, description: '交易描述' })
    @IsOptional()
    @IsString({ message: '交易描述必须是字符串' })
    description?: string;
}

@InputType()
export class UpdateVccTransactionInputDTO {
    @Field(() => TransactionStatus, { nullable: true, description: '交易状态' })
    @IsOptional()
    @IsEnum(TransactionStatus, { message: '交易状态不合法' })
    status?: TransactionStatus;

    @Field({ nullable: true, description: '交易描述' })
    @IsOptional()
    @IsString({ message: '交易描述必须是字符串' })
    description?: string;
}

// ===== 过滤和查询DTO - 支持前端所有搜索条件 =====

@InputType()
export class VccTransactionFilterInputDTO {
    @Field(() => ID, { nullable: true, description: 'VCC卡片ID' })
    @IsOptional()
    @IsUUID('4', { message: 'VCC卡片ID必须是有效的UUID' })
    cardId?: string;

    @Field({ nullable: true, description: '卡号模糊搜索' })
    @IsOptional()
    @IsString({ message: '卡号必须是字符串' })
    cardNo?: string;

    @Field({ nullable: true, description: '商户名称模糊搜索' })
    @IsOptional()
    @IsString({ message: '商户名称必须是字符串' })
    merchant?: string;

    @Field(() => TransactionStatus, { nullable: true, description: '交易状态' })
    @IsOptional()
    @IsEnum(TransactionStatus, { message: '交易状态不合法' })
    status?: TransactionStatus;

    @Field(() => TransactionType, { nullable: true, description: '交易类型' })
    @IsOptional()
    @IsEnum(TransactionType, { message: '交易类型不合法' })
    type?: TransactionType;

    @Field({ nullable: true, description: '开始时间（日期范围）' })
    @IsOptional()
    @IsDateString({}, { message: '开始时间格式不正确' })
    startTime?: string;

    @Field({ nullable: true, description: '结束时间（日期范围）' })
    @IsOptional()
    @IsDateString({}, { message: '结束时间格式不正确' })
    endTime?: string;

    @Field(() => Float, { nullable: true, description: '最小金额' })
    @IsOptional()
    @IsNumber({ maxDecimalPlaces: 2 }, { message: '最小金额最多保留2位小数' })
    @Min(0, { message: '最小金额不能为负数' })
    minAmount?: number;

    @Field(() => Float, { nullable: true, description: '最大金额' })
    @IsOptional()
    @IsNumber({ maxDecimalPlaces: 2 }, { message: '最大金额最多保留2位小数' })
    @Min(0, { message: '最大金额不能为负数' })
    maxAmount?: number;

    @Field(() => ID, { nullable: true, description: '租户ID' })
    @IsOptional()
    @IsUUID('4', { message: '租户ID必须是有效的UUID' })
    tenantId?: string;
}

@InputType()
export class VccTransactionPaginationInputDTO {
    @Field(() => Int, { nullable: true, description: '页码', defaultValue: 1 })
    @IsOptional()
    @Type(() => Number)
    @IsNumber({}, { message: '页码必须是数字' })
    @Min(1, { message: '页码必须大于0' })
    page?: number = 1;

    @Field(() => Int, { nullable: true, description: '每页条数', defaultValue: 20 })
    @IsOptional()
    @Type(() => Number)
    @IsNumber({}, { message: '每页条数必须是数字' })
    @Min(1, { message: '每页条数必须大于0' })
    @Max(100, { message: '每页条数不能超过100' })
    limit?: number = 20;

    @Field({ nullable: true, description: '排序字段', defaultValue: 'transactionTime' })
    @IsOptional()
    @IsString({ message: '排序字段必须是字符串' })
    sortBy?: string = 'transactionTime';

    @Field({ nullable: true, description: '排序方向', defaultValue: 'DESC' })
    @IsOptional()
    @IsString({ message: '排序方向必须是字符串' })
    sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

// ===== Facebook同步相关DTO =====

@InputType()
export class SyncFacebookTransactionsInputDTO {
    @Field(() => ID, { description: 'VCC卡片ID' })
    @IsUUID('4', { message: 'VCC卡片ID必须是有效的UUID' })
    cardId: string;

    @Field({ nullable: true, description: '同步开始日期' })
    @IsOptional()
    @IsDateString({}, { message: '开始日期格式不正确' })
    startDate?: string;

    @Field({ nullable: true, description: '同步结束日期' })
    @IsOptional()
    @IsDateString({}, { message: '结束日期格式不正确' })
    endDate?: string;

    @Field({ nullable: true, description: '是否强制覆盖已有记录', defaultValue: false })
    @IsOptional()
    forceOverwrite?: boolean = false;
}

// ===== 分页结果DTO =====

@ObjectType()
export class PaginatedVccTransactionResult {
    @Field(() => [VccTransactionDTO], { description: '交易记录列表' })
    data: VccTransactionDTO[];

    @Field(() => Int, { description: '总数' })
    total: number;

    @Field(() => Int, { description: '当前页' })
    page: number;

    @Field(() => Int, { description: '每页条数' })
    limit: number;

    @Field(() => Int, { description: '总页数' })
    totalPages: number;
}

// ===== 统计数据DTO =====

@ObjectType()
export class VccTransactionStatsDTO {
    @Field(() => Int, { description: '总交易数' })
    totalCount: number;

    @Field(() => Float, { description: '总交易金额' })
    totalAmount: number;

    @Field(() => Float, { description: '平均交易金额' })
    averageAmount: number;

    @Field(() => Int, { description: '成功交易数' })
    successCount: number;

    @Field(() => Int, { description: '待处理交易数' })
    pendingCount: number;

    @Field(() => Int, { description: '失败交易数' })
    failedCount: number;

    @Field(() => Float, { description: '成功率（%）' })
    successRate: number;
} 