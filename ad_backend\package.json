{"name": "ever-api-starter-kit", "version": "0.1.0", "description": "Ever API Starter Kit", "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "keywords": ["starter", "boilerplate", "graphql", "<PERSON><PERSON><PERSON>", "multitenant"], "license": "MIT", "homepage": "https://github.com/ever-co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-api-starter-kit.git"}, "bugs": {"url": "https://github.com/ever-co/ever-api-starter-kit/issues"}, "scripts": {"prebuild": "rimraf dist && rimraf tsconfig.tsbuildinfo && rimraf src/schemas/schema.graphql", "build": "pnpm nest build && pnpm generate", "build:prod": "pnpm nest build && pnpm generate", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "pnpm nest start --watch", "start:dev": "pnpm nest start --watch", "start:debug": "pnpm nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main.js", "start:online": "pnpm cross-env IS_DDB_LOCAL= nest start", "generate": "rimraf generated/sdk/ever-api-sdk.ts && cross-env DB_TYPE=postgres pnpm ts-node src/schema.ts && pnpm graphql-codegen --config ./generated/codegen.yml", "seed": "cross-env NODE_ENV=development NODE_OPTIONS=--max_old_space_size=8192 pnpm ts-node -r tsconfig-paths/register --project tsconfig.json ./src/modules/seeds/seed.ts", "seed:update": "cross-env NODE_ENV=development NODE_OPTIONS=--max_old_space_size=8192 pnpm ts-node -r tsconfig-paths/register --project tsconfig.json ./src/modules/seeds/seed.update.ts", "seed:routes": "ts-node src/scripts/seed-routes.ts", "seed:role": "ts-node -r tsconfig-paths/register src/modules/role/role.seed.ts", "reset:db": "echo 请根据实际数据库配置补充此命令，如使用typeorm migration:revert && migration:run", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "sls:offline": "pnpm sls offline start", "sls:deploy": "pnpm sls deploy --verbose", "sls:package": "pnpm sls package", "sls:start": "cross-env IS_NOT_SLS= pnpm sls offline start", "sls:online": "cross-env IS_NOT_SLS= cross-env IS_DDB_LOCAL= pnpm sls offline", "typeorm": "typeorm-ts-node-commonjs -d src/config/data-source.ts", "migration:generate": "npm run typeorm -- migration:generate", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@apollo/gateway": "^2.10.2", "@apollo/server": "^4.12.1", "@apollo/subgraph": "^2.10.2", "@aws-sdk/client-s3": "3.775.0", "@aws-sdk/lib-storage": "3.775.0", "@dataui/crud": "^5.3.4", "@dataui/crud-request": "^5.3.4", "@dataui/crud-typeorm": "^5.3.4", "@godaddy/terminus": "^4.12.1", "@nestjs/apollo": "^12.2.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.4.17", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.17", "@nestjs/cqrs": "^10.2.8", "@nestjs/graphql": "^12.2.2", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^10.4.17", "@nestjs/platform-express": "^10.4.17", "@nestjs/schedule": "^3.0.4", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^10.3.0", "@nestjs/typeorm": "^10.0.2", "@ptc-org/nestjs-query-core": "^4.4.0", "@ptc-org/nestjs-query-graphql": "^4.4.0", "@ptc-org/nestjs-query-typeorm": "^4.4.0", "@types/file-type": "10.9.3", "apollo-server-express": "^3.13.0", "aws-lambda": "^1.0.7", "aws-serverless-express": "^3.4.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cron": "^2.4.4", "crypto": "^1.0.1", "dataloader": "^2.2.3", "express": "^4.21.2", "facebook-nodejs-business-sdk": "^16.0.0", "file-type": "^16.5.4", "form-data": "^4.0.2", "graphql": "^16.11.0", "graphql-query-complexity": "^0.12.0", "graphql-relay": "^0.10.2", "graphql-subscriptions": "^2.0.0", "graphql-tools": "^9.0.18", "graphql-type-json": "^0.3.2", "html-to-text": "^9.0.5", "https-proxy-agent": "^7.0.6", "i18next": "^25.2.1", "lodash": "^4.17.21", "mime-types": "^3.0.1", "moment": "^2.30.1", "pg": "^8.16.0", "redis": "^4.7.1", "reflect-metadata": "^0.1.14", "rimraf": "^4.4.1", "rxjs": "^7.8.2", "sql.js": "^1.13.0", "type-graphql": "^1.1.1", "typeorm": "^0.3.24", "typeorm-express-query-builder": "^1.7.0", "uuid": "^9.0.1", "yargs": "^17.7.2"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typed-document-node": "^5.1.1", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-typed-document-node/core": "^3.2.0", "@graphql-typed-document-node/patch-cli": "^3.0.9", "@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.17", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.1.0", "@semantic-release/npm": "^9.0.2", "@types/aws-lambda": "^8.10.149", "@types/aws-serverless-express": "^3.3.10", "@types/express": "^4.17.22", "@types/html-to-text": "^9.0.4", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.17", "@types/multer": "^1.4.12", "@types/node": "^18.19.103", "@types/serverless": "^3.12.27", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "cloc": "^2.11.0", "codelyzer": "^6.0.2", "commitizen": "^4.3.1", "concurrently": "^7.6.0", "conventional-changelog": "^3.1.25", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "dotenv": "^16.5.0", "envalid": "^7.3.1", "eslint": "^8.57.1", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^40.3.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "js-yaml": "^4.1.0", "lint-staged": "^13.3.0", "nodemon": "^2.0.22", "prettier": "^2.8.8", "prettier-tslint": "^0.4.2", "pretty-quick": "^3.3.1", "semantic-release": "^20.1.3", "serverless": "3.33.0", "serverless-offline": "12.0.4", "serverless-plugin-optimize": "4.2.1-rc.1", "serverless-plugin-typescript": "^2.1.5", "serverless-plugin-warmup": "^8.3.0", "snyk": "^1.1297.1", "stylelint": "^13.13.1", "supertest": "^6.3.4", "swagger-ui-express": "^4.6.3", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-morph": "^17.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typedoc": "^0.23.28", "typescript": "~4.9.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node", "testTimeout": 45000}, "lint-staged": {"*.ts": ["prettier --write"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release": {"verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "publish": ["@semantic-release/github"], "generateNotes": {"preset": "angular"}, "npmPublish": false}, "prettier": {"singleQuote": true}, "engines": {"node": ">=18.0.0", "yarn": ">=1.13.0"}, "snyk": true}