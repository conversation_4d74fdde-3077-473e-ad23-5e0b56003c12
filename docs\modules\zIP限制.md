# 组件路径 /pages/management/dofend/ip-restriction/index

## 前端

- **实际路径**：slash-admin/src/pages/management/dofend/ip-restriction/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，IP限制管理表单与列表，含增删改查、分页、搜索、弹窗、批量导入等）
- **主要依赖API定义**：@/api/ipRestriction.graphql

### API 调用列表与详细说明

| API名称         | 方法     | GraphQL名/路径         | 参数类型/说明 | 返回字段 | 说明/示例 |
|----------------|----------|------------------------|--------------|----------|-----------|
| 获取IP限制列表  | query    | GET_IPRESTRICTION      | paging, filter, sorting | id, ip, status, operator, updatedAt, ... | 支持分页、搜索、过滤 |
| 新增IP限制      | mutation | CREATE_IPRESTRICTION_MUTATION | input: ipRestriction | id, ip, ... | 新增单条IP限制 |
| 编辑IP限制      | mutation | UPDATE_IP_RESTRICTION  | input: {id, update} | id, ip, ... | 编辑指定IP限制 |
| 删除IP限制      | mutation | DELETE_IP_RESTRICTION  | input: {id}  | id       | 删除指定IP限制 |

#### 主要参数结构说明

- **ipRestrictionType**（用于新增/展示）  
  ```ts
  {
    id: string; // IP限制ID
    ip: string; // IP地址
    status: '正常' | '异常' | '待审核'; // 状态
    operator: string; // 操作人
    updatedAt: string; // 更新时间
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名         | 类型      | 说明                       |
  |----------------|-----------|----------------------------|
  | id             | string    | 主键，UUID                 |
  | ip             | string    | IP地址                     |
  | status         | string    | 状态（正常/异常/待审核）   |
  | operator       | string    | 操作人                     |
  | updatedAt      | Date      | 更新时间                   |

### 前端表单/组件说明

- 支持IP限制的增删改查，表单字段与上方ipRestrictionType结构一致
- 支持批量导入（如Excel），批量解析、上传
- 支持分页、模糊搜索、过滤
- 支持状态、操作人等字段展示
- IP格式校验

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见上表
- **数据库表结构**（ip_restriction，需结合实际后端实现）：

| 字段名         | 类型         | 说明                       | 约束         |
|----------------|-------------|----------------------------|--------------|
| id             | uuid        | 主键，唯一标识             | PRIMARY KEY  |
| ip             | varchar     | IP地址                     | NOT NULL, UNIQUE |
| status         | varchar     | 状态（正常/异常/待审核）   | 默认正常     |
| operator       | varchar     | 操作人                     | 可空         |
| updatedAt      | timestamp   | 更新时间                   | 自动生成     |
| ...            | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query GET_IPRESTRICTION / mutation CREATE_IPRESTRICTION_MUTATION / ...
    后端->>数据库: 操作 ip_restriction 表
    数据库-->>后端: 返回IP限制数据
    后端-->>前端: 返回IP限制数据
```

---

## 变更历史/注意事项

- IP地址需唯一，所有操作需校验唯一性
- 支持批量导入，建议用事务处理
- 删除IP限制时需校验是否被业务引用，防止误删
- 状态字段建议用枚举类型，便于扩展
- 复杂业务字段建议在表结构中用jsonb扩展