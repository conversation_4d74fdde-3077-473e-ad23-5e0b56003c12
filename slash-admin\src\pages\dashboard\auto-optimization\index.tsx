import { useTranslation } from 'react-i18next';
import AutoOptimizationComponent from "../workbench/auto-optimization";

export default function AutoOptimization() {
  const { t } = useTranslation();
  return (
    <div className="space-y-8">
      <div className="flex items-center">
        <h2 className="text-3xl font-bold tracking-tight">{t('pages.key_1903')}</h2>
      </div>

      <div className="space-y-4">
        <AutoOptimizationComponent type="budget" />
      </div>
    </div>
  );
}
