import { MigrationInterface, QueryRunner } from "typeorm";

export class DropAndRecreateAudienceTable20240610123000 implements MigrationInterface {
    name = 'DropAndRecreateAudienceTable20240610123000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS "audience" CASCADE`);
        await queryRunner.query(`
            CREATE TABLE "audience" (
                "id" uuid PRIMARY KEY,
                "audienceId" varchar(255),
                "name" varchar(255) NOT NULL,
                "geo_locations" jsonb NOT NULL,
                "excluded_geo_locations" jsonb,
                "locales" integer[] DEFAULT '{}',
                "gender" integer,
                "age_min" integer,
                "age_max" integer,
                "interests" jsonb DEFAULT '[]',
                "behaviors" jsonb DEFAULT '[]',
                "notes" text,
                "platform" jsonb DEFAULT '[]',
                "tenantId" uuid NOT NULL,
                CONSTRAINT "FK_audience_tenant" FOREIGN KEY ("tenantId") REFERENCES "tenant"("id") ON DELETE CASCADE
            );
        `);
        await queryRunner.query(`CREATE INDEX "IDX_audience_tenant" ON "audience" ("tenantId")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS "audience" CASCADE`);
    }
} 