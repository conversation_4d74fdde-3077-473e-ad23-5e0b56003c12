import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LandingPage } from './landing-page.entity';
import { LandingPageService } from './landing-page.service';
import { LandingPageResolver } from './resolvers/landing-page.resolver';
import { AuthModule } from '../auth/auth.module';

@Module({
    imports: [TypeOrmModule.forFeature([LandingPage]), AuthModule],
    providers: [LandingPageService, LandingPageResolver],
    exports: [LandingPageService],
})
export class LandingPageModule { } 