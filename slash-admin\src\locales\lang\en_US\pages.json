{"pages": {"key_1": "Upload Failed", "key_2": "Upload Complete (Simulation Result", "key_3": "Upload Data", "key_4": "Upload File to Cloudflare (Backend API", "key_5": "Dropdown All Roles", "key_6": "Dropdown Debug Info When Opened", "key_7": "Dropdown Data", "key_8": "Files Under", "key_9": "Download Export File", "key_10": "Not Passed\n      console.log('Submit Parameters", "key_11": "Leave Blank If Not Modified", "key_12": "Login from Different Regions", "key_13": "Do Not Throw <PERSON><PERSON><PERSON>, Return Default Value", "key_14": "Not a Video File or Type Unrecognized (e.g., Some .mov Files", "key_15": "Does Not Comply with UUID v4 Format, Will Be Filtered", "key_16": "Cannot Delete Current Logged-in Account", "key_17": "Cannot Change Status of Current Logged-in Account", "key_18": "Cannot Disable/Enable Current Logged-in Account", "key_19": "Cannot Edit Current Logged-in Account", "key_20": "Unlimited", "key_21": "Professional Skills", "key_22": "Dedicated Token Field (Backup Field", "key_23": "Business Request Successful", "key_24": "Business Request Error", "key_25": "Business Logic Error - Card Status, Account Status, etc.", "key_26": "Personal Information", "key_27": "Personal Introduction", "key_28": "Personal Profile", "key_29": "Ad Accounts", "key_30": "Ad Accounts", "key_31": "Ad Accounts, Among Which", "key_32": "Invalid Routes", "key_33": "Months", "key_34": "Months Ago", "key_35": "Accounts", "key_36": "Accounts Have Retrieved Facebook Data", "key_37": "Accounts Have No Facebook Authorization, Can Sync Manually Later", "key_38": "Accounts' Facebook Data", "key_39": "Accounts' Token Status", "key_40": "Accounts' Aggregated Data", "key_41": "Routes", "key_42": "China", "key_43": "Chinese", "key_44": "Central Africa", "key_45": "Medium Risk", "key_46": "Denmark", "key_47": "Danish", "key_48": "Add Props Type for AdGroupsOverview", "key_49": "To Maintain Backward Compatibility, Provide Old Name Export", "key_50": "To Avoid Parsing Every Time, Also Save a Direct", "key_51": "Main Image", "key_52": "Primary Landing Page", "key_53": "Main Features", "key_54": "Main Fields", "key_55": "Key Features", "key_56": "Homepage Selection", "key_57": "Homepage Button", "key_58": "Ukraine", "key_59": "Ukrainian", "key_60": "Uzbekistan", "key_61": "Uganda", "key_62": "Chad", "key_63": "Yemen", "key_64": "Interaction", "key_65": "Armenia", "key_66": "Transaction", "key_67": "Transaction History", "key_68": "Transaction History Page", "key_69": "Number of Transactions", "key_70": "Transaction Time", "key_71": "Transaction Statistics", "key_72": "Transaction Record Basic Fields", "key_73": "Transaction Record Table", "key_74": "Transaction Details", "key_75": "Transaction Filter Conditions", "key_76": "Product Manager", "key_77": "Product Department", "key_78": "Demographics", "key_79": "Manual Review", "key_80": "Connections", "key_81": "Only Use onValuesChange to Listen to Form Field Changes", "key_82": "Sync Transaction Records from Facebook", "key_83": "Sync Transaction Records from Facebook - Core Function", "key_84": "Lookup from INTEREST_OPTIONS", "key_85": "Extract from JSON", "key_86": "Change from useMutation to", "key_87": "Find Selected Account from Current Ad Account Data", "key_88": "Query System for Existence of This Route", "key_89": "Extract More Detailed Info from Network Error", "key_90": "Paid", "key_91": "Code", "key_92": "The Following URL Format is Incorrect", "key_93": "The Following Ad Account ID Format is Incorrect", "key_94": "Israel", "key_95": "Dashboard", "key_96": "Iraq", "key_97": "Iran", "key_98": "Optimization History", "key_99": "Optimization History Can Retain Simulation", "key_100": "Optimization Successful", "key_101": "Optimization Goal", "key_102": "Optimization Progress", "key_103": "Number of Tokens Passed", "key_104": "Low Risk", "key_105": "Housing", "key_106": "Balance", "key_107": "Balance Warning Settings", "key_108": "Balance Warning Threshold\n}\n\n// Transaction Record Type - Fully Corresponds to Backend API", "key_109": "Cape Verde", "key_110": "You Can Dynamically Generate Dates Based on Actual Needs", "key_111": "Your Rules Will Apply to <PERSON>s Running When the Rule Executes", "key_112": "Commission", "key_113": "Use Array Buffer for More Stability", "key_114": "Use Framer Motion Spring Animation for Smooth Transition", "key_115": "Use actions Namespace to Store All", "key_116": "Use Spring Animation for Smoother Progress Bar Changes", "key_117": "Use Chart Component Configuration", "key_118": "Use Underscore Prefix to Indicate Intentionally Unused Parameters", "key_119": "Use Underscore Prefix to Indicate Intentionally Unused Variables", "key_120": "Use Backend API, No Need to Call Facebook API Directly\n    }\n\n    // Get Complete Data for Multiple Ad Accounts (Via Backend API Call", "key_121": "Use Existing Page as Temporary Replacement Until Actual Page is Created", "key_122": "Use Both Old and New APIs for Maximum Compatibility", "key_123": "Use New Direct Facebook API Service", "key_124": "Usage Example", "key_125": "Use First Label as Group", "key_126": "Use Project's", "key_127": "Use Project's Existing GET_GROUPS Query", "key_128": "For Example", "key_129": "Promotion", "key_130": "Russia", "key_131": "Russian", "key_132": "Bulgaria", "key_133": "Bulgarian", "key_134": "Save", "key_135": "Save Token to Multi-Tenant Partition", "key_136": "Save userInfo to Multi-Tenant Partition", "key_137": "Failed to Save Facebook Data to Backend", "key_138": "Save to Backend Database", "key_139": "Save User Info to", "key_140": "Failed to Save Bound Facebook Data to Backend", "key_141": "Role Save Failure Details", "key_142": "Save Settings", "key_143": "Information Failed", "key_144": "Credit", "key_145": "Modification Successful", "key_146": "Correction: Pass Empty String When tenantId is Empty", "key_147": "Value Change", "key_148": "Whether Values Are Equal", "key_149": "Value Type", "key_150": "Dummy Data and Field Definitions, Can Connect Later", "key_151": "Assume Your Upload Method Accepts File Type", "key_152": "Threshold to Stop Animation", "key_153": "Deactivate", "key_154": "Pixel: Insert", "key_155": "Allow Filter to Pass Through All Fields", "key_156": "Element Viewport Entry Ratio Threshold (Between 0-1)", "key_157": "Recharge", "key_158": "Recharge VCC Card", "key_159": "Balance After Recharge", "key_160": "Balance After Recharge Will Update Immediately", "key_161": "Recharge Failed", "key_162": "<PERSON><PERSON><PERSON>", "key_163": "Recharge Successful", "key_164": "Recharge Instructions", "key_165": "Recharge Amount", "key_166": "Recharge Amount Will Be Added Directly to Current Balance", "key_167": "Recharge Amount Must Be Greater Than 0", "key_168": "Do Not Skip Query for Now, Let It Execute Directly", "key_169": "Try Empty Filter First to See If It's a tenantId Issue", "key_170": "Sort by Order First to Ensure Sibling Nodes Display Correctly", "key_171": "Trigger Ad Group Change First, Pull Creative Materials", "key_172": "Set Basic Form Values First (Excluding Bound Accounts)", "key_173": "Croatia", "key_174": "Croatian", "key_175": "Clone Response for Subsequent Reading", "key_176": "Fallback for Empty Data, Ensure Consistent Hook Order", "key_177": "Date of Joining", "key_178": "Global Detail Table Paginated Data", "key_179": "Global Statistics Card/Pie Chart Data", "key_180": "Global Resource Details", "key_181": "Global Resource Overview", "key_182": "Refresh All", "key_183": "All Refresh Complete, Success", "key_184": "Company", "key_185": "Keyword Matching", "key_186": "Close", "key_187": "Close Loading Prompt", "key_188": "Reset Form When Closed", "key_189": "Interest", "key_190": "Interest Distribution", "key_191": "Hobbies", "key_192": "Interest Options", "key_193": "Other", "key_194": "Other Information", "key_195": "Compatible with Apollo Authentication", "key_196": "Compatible with Older Browsers", "key_197": "Content", "key_198": "Content Security", "key_199": "Content Security Detection Warning", "key_200": "Content Security Rules", "key_201": "Content Management System", "key_202": "Internal UUID (For Binding", "key_203": "Gambia", "key_204": "Flatten creative Field Again", "key_205": "Iceland", "key_206": "Prepare Facebook Data Fetching (Preprocessing Before Binding)", "key_207": "Prepare Chart Data Series", "key_208": "Prepare VCC Cards for Batch Sync", "key_209": "Prepare Batch Data Requests", "key_210": "Prepare Query Parameters", "key_211": "Prepare to Call Facebook API Directly, Account", "key_212": "Subtract 0.05px to Avoid Breakpoint Overlap", "key_213": "Reduce Number of Accounts Synced Simultaneously", "key_214": "Reduce Number of Accounts Bound Simultaneously", "key_215": "Guinea", "key_216": "Guinea-Bissau", "key_217": "Bid Auto-Optimization", "key_218": "Fetch Roles and Groups Separately to Handle Exceptions\n                try {\n                    console.log('Loading role data", "key_219": "Analysis", "key_220": "Analyze Facebook Token Status of Selected Accounts", "key_221": "Analyze Error Type and Provide Detailed Solutions", "key_222": "Grouping", "key_223": "Group 1", "key_224": "Group Name", "key_225": "Group Management", "key_226": "Minutes Ago", "key_227": "Pagination Info", "key_228": "Pagination Parameters", "key_229": "Pagination Controls", "key_230": "Pagination Result", "key_231": "Switch", "key_232": "Toggle Role Status", "key_233": "<PERSON>", "key_234": "Congo", "key_235": "Congo (DRC)", "key_236": "Create", "key_237": "Create Axios Instance", "key_238": "Create Apollo Client", "key_239": "Create Facebook Data Fetching Promise Using New Direct API Service", "key_240": "Create VCC Card", "key_241": "Create Transaction Record", "key_242": "Create Transaction Record - Manually Add Transaction Record", "key_243": "Creator", "key_244": "Create Team", "key_245": "Creation Failed", "key_246": "Creation Successful", "key_247": "Create New Team", "key_248": "Create New Combination", "key_249": "Create New Project", "key_250": "Creation Time", "key_251": "Create User", "key_252": "Create User\n        console.log('Create user", "key_253": "Create User Parameters", "key_254": "User Creation Failed", "key_255": "Created User", "key_256": "Created Role", "key_257": "Creation Result", "key_258": "Create Group", "key_259": "Create Custom Rule", "key_260": "Create Rules to Automatically Update Settings of Selected Campaigns, Ad Sets, or Ads", "key_261": "Create Role", "key_262": "Create Role\n\t\t\t\tconsole.log('Create role", "key_263": "Create Role Parameters", "key_264": "Role Creation Failed", "key_265": "Create Project", "key_266": "Creative Element Effect", "key_267": "Creative Analysis", "key_268": "Creative Analysis and Budget Usage", "key_269": "Creative Size Effect", "key_270": "Creative Studio", "key_271": "Creative Type Trend", "key_272": "Creative Materials", "key_273": "Creative Combination Deleted Successfully')\n    getDataList()\n  }\n  // hash 0109414d61c7860d37e29811b50e6641\n  // 1039826358255334", "key_274": "Creative Combination Added Successfully", "key_275": "Creative Combination Management", "key_276": "Creative Design", "key_277": "Creative Design Team", "key_278": "Creative Designer", "key_279": "Creative Design Department", "key_280": "Creative: <PERSON><PERSON><PERSON>", "key_281": "Initial 0, Automatically Calculated During Binding", "key_282": "<PERSON><PERSON> Once on Initial Load", "key_283": "Initialize Data - Only Fetch Data Not Dependent on Time Range", "key_284": "Set HTML lang Attribute on Initialization, Otherwise Browser Translation Prompt Pops Up If System Language Differs", "key_285": "On Initialization, Determine If Tenant Admin to Decide Whether to Show Groups", "key_286": "Initial State", "key_287": "Delete", "key_288": "Delete VCC Card", "key_289": "Delete Transaction Record", "key_290": "Delete Card", "key_291": "Delete Audience", "key_292": "Cannot Be Recovered After Deletion, Proceed with Caution", "key_293": "Deletion Failed", "key_294": "Deletion Successful", "key_295": "Delete User", "key_296": "User Deletion Failed", "key_297": "Deleted User", "key_298": "Delete Tenant", "key_299": "Deletion Result", "key_300": "Delete Group", "key_301": "Delete Role", "key_302": "Role Deletion Failed", "key_303": "Role Deletion Failure Details", "key_304": "Check If System Admin (Compatible with Multiple Formats)", "key_305": "Check If Current Logged-in User", "key_306": "<PERSON><PERSON> Handling to Ensure Hooks Always Called", "key_307": "Libya", "key_308": "Liberia", "key_309": "Go to Ad Account Management Page to Re-authorize Facebook OAuth", "key_310": "Refresh", "key_311": "Refresh VCC Card List (If Backend Supports Real-time Updates)", "key_312": "Refresh VCC Card Data", "key_313": "Refresh List\n    } catch (error) {\n      console.error(\"Operation Failed", "key_314": "Refresh Failed", "key_315": "Refresh Successful", "key_316": "Refresh Data", "key_317": "Refresh Data\n        } catch (error) {\n            message.error('Risk Data Sync Failed", "key_318": "Refresh Data\n      }\n    } catch (error) {\n      console.error('Sync Failed", "key_319": "Refresh Calendar Display", "key_320": "Refresh Table\n    } catch (e) {\n      message.error(\"Sync Failed", "key_321": "Refresh Page to Make New tenantId Effective", "key_322": "Frontend Service", "key_323": "Remaining Binding Attempts", "key_324": "Feature Status", "key_325": "Canada", "key_326": "Ghana", "key_327": "Gabon", "key_328": "Add to Cart", "key_329": "Loading", "key_330": "Loaded Route List", "key_331": "Load Failed", "key_332": "Loading State", "key_333": "Failed to Load User List", "key_334": "Load User Data", "key_335": "User Data Loaded Successfully But Return Value Empty", "key_336": "Failed to Load Group Data", "key_337": "Load Role List", "key_338": "Failed to Load Role List", "key_339": "Role List Load Failure Details", "key_340": "Load Role and Group Data", "key_341": "Overall Failure to Load Role and Group Data", "key_342": "Failed to Load Role Data", "key_343": "Load Route List", "key_344": "Failed to Load Route List", "key_345": "Route List Load Failure Details", "key_346": "Load Route Data", "key_347": "Failed to Load Route Data", "key_348": "Action", "key_349": "Action Type", "key_350": "Dynamic Options GraphQL Query", "key_351": "Animation Variants Explanation", "key_352": "Animation Container Component", "key_353": "Whether Animation Triggers Only Once", "key_354": "Animation State", "key_355": "Packaging Format", "key_356": "Wrap Component to Use", "key_357": "Hungary", "key_358": "Hungarian", "key_359": "Medical", "key_360": "Cost <PERSON> (CPM)", "key_361": "Single IP Exceeding Request Threshold in Short Time", "key_362": "Single Recharge Amount Cannot Exceed 10", "key_363": "Cost <PERSON>", "key_364": "Maximum Single Recharge 10", "key_365": "South Africa", "key_366": "Botswana", "key_367": "Card Number", "key_368": "Card Number Copied to Clipboard", "key_369": "Card Number (Masked)", "key_370": "Qatar", "key_371": "Card", "key_372": "Card {result.vccCardId.slice(-8", "key_373": "Card Facebook Data Update Failed", "key_374": "Card Facebook Data Update Successful", "key_375": "Card ID Empty, Please Select Card Again", "key_376": "Card ID Format Incorrect", "key_377": "Card ID Format Error", "key_378": "Card Recharge", "key_379": "Card List", "key_380": "Card List Table", "key_381": "Card Created Successfully", "key_382": "Card Management", "key_383": "Card Type Definition", "key_384": "Rwanda", "key_385": "Luxembourg", "key_386": "Hindi", "key_387": "Indonesian", "key_388": "India", "key_389": "Indonesia", "key_390": "OAuth Account to Be Used for Facebook API Calls", "key_391": "Basic Form Values to Be Set", "key_392": "Eritrea", "key_393": "Historical Data", "key_394": "Historical Project Team", "key_395": "Original Value", "key_396": "Original Selected Value", "key_397": "Is Original Selected Value an Array?", "key_398": "Original Selected Value Type", "key_399": "Deauthorize", "key_400": "<PERSON><PERSON><PERSON>, <PERSON> Parameter Name", "key_401": "Parameter Validation Passed, Start Sending Binding Request", "key_402": "Initiate Checkout", "key_403": "Send Message", "key_404": "Send Binding Request", "key_405": "Send Notification", "key_406": "Extract Selected Campaign's", "key_407": "Cancel", "key_408": "Audience", "key_409": "Audience Interest Distribution", "key_410": "Audience Analysis", "key_411": "Audience List Query (Cursor <PERSON>)", "key_412": "Audience Name", "key_413": "Audience Geographic Distribution", "key_414": "Audience Platform Distribution", "key_415": "Audience Age Distribution", "key_416": "Audience Gender Distribution", "key_417": "Number of Audiences", "key_418": "Audience Insights", "key_419": "Audience Management", "key_420": "Audience: <PERSON><PERSON><PERSON>", "key_421": "Affected User Count", "key_422": "Change", "key_423": "Change Variable Type", "key_424": "Change Response Type", "key_425": "Change Operation", "key_426": "Syria", "key_427": "Keep Only Required Fields", "key_428": "Only Sync Cards with <PERSON><PERSON>", "key_429": "Only Show Enabled Roles", "key_430": "Only Show Active Groups", "key_431": "Only IDs Passing UUID Format Validation Will Be Accepted", "key_432": "Only Excel or ZIP Files Can Be Uploaded", "key_433": "Assemble As Long as Form Fills App and App Store Link", "key_434": "Can Get Last Transaction Time from record", "key_435": "Can Use Facebook Official SDK to Avoid This Issue", "key_436": "Can Be", "key_437": "Can Customize Animation Effects by Passing Parameters via varContainer Function", "key_438": "Extensible: Toggle userInfo/settings Cache", "key_439": "Can Popup/Global Prompt as Needed\n    if (error.response) {\n      // Backend Has Response", "key_440": "Can Handle According to Project Convention", "key_441": "Can Configure Based on Actual Project", "key_442": "Can Extend Interceptors, Global Loading, Auto Retry, etc. as Needed", "key_443": "Can Add Other Officially Supported Values as Needed", "key_444": "Can Add More Official Facebook Fields as Needed", "key_445": "Availability", "key_446": "List of Available Ad Account IDs", "key_447": "Possible Reasons", "key_448": "Potential Issues", "key_449": "Customizable Container Style", "key_450": "Can Add Other Events", "key_451": "Can Add Other Official Events", "key_452": "Can Add Other Officially Supported Events", "key_453": "Can Record Failed Rows", "key_454": "Optionally Handle Failure", "key_455": "Configurable Viewport Trigger Conditions", "key_456": "Right Card Statistics", "key_457": "Right Detailed Information", "key_458": "Budget Usage by Platform", "key_459": "Partners", "key_460": "Partnership Type", "key_461": "Djibouti", "key_462": "Colleague", "key_463": "Set State to Control Select Component Display", "key_464": "Sync", "key_465": "Sync/Export Result Type", "key_466": "Sync Facebook Data", "key_467": "Sync Facebook Data - Directly Call Facebook Third-party API", "key_468": "Syncing", "key_469": "Sync to Form", "key_470": "Sync Failed", "key_471": "Cards That Failed to Sync", "key_472": "Records That Failed to Sync", "key_473": "Sync Failed: {failCount} Cards", "key_474": "Sync Complete", "key_475": "Sync Complete! Spent", "key_476": "Cards Synced Successfully", "key_477": "Records Synced Successfully", "key_478": "Sync Data", "key_479": "Sync Time", "key_480": "Sync Result", "key_481": "Sync Rules", "key_482": "Sync Risk Data", "key_483": "Name", "key_484": "Backend API Response Successful", "key_485": "Backend API Response Error", "key_486": "Backend API Batch Response Successful", "key_487": "Backend API Service Exception", "key_488": "Backend API Returned Failure Status", "key_489": "Backend API Error", "key_490": "Backend Response", "key_491": "Backend Exception Details", "key_492": "Did <PERSON>end Correctly Return Token Data?", "key_493": "Backend Service", "key_494": "Contains Sensitive Words", "key_495": "Start", "key_496": "Enable", "key_497": "Enable Ads", "key_498": "List of Enabled Roles", "key_499": "Enable Warning", "key_500": "<PERSON>", "key_501": "<PERSON>ert <PERSON>", "key_502": "<PERSON><PERSON>", "key_503": "Alert <PERSON>", "key_504": "Alert <PERSON>", "key_505": "<PERSON>", "key_506": "Brand Awareness", "key_507": "Kazakhstan", "key_508": "Response Action", "key_509": "Response Interception", "key_510": "Response Interceptor: Unified Error Handling", "key_511": "Response Time", "key_512": "Response Error Details", "key_513": "Colombia", "key_514": "Merchant", "key_515": "Cameroon", "key_516": "No Change Echoed", "key_517": "Team", "key_518": "Team Created Successfully", "key_519": "Team Name", "key_520": "Team Members", "key_521": "Team Description", "key_522": "Team Type", "key_523": "Team Leader", "key_524": "Country", "key_525": "Country Code", "key_526": "Country Code Mapping, Assume COUNTRY_OPTIONS value is", "key_527": "Image Recognition", "key_528": "Icon", "key_529": "Icon Options", "key_530": "Image", "key_531": "Image Creative", "key_532": "Image Material", "key_533": "Chart Data", "key_534": "Chart Configuration", "key_535": "Turkey", "key_536": "Turkish", "key_537": "Sao Tome and Principe", "key_538": "Reset Selected Index When Search Results Change", "key_539": "Add to Component State", "key_540": "Track at Top of Component with useEffect", "key_541": "Analyze <PERSON>ken Status of Selected Accounts Before Binding", "key_542": "Hide in Menu", "key_543": "Do Something Before Request is Sent\n\t\t// Re-fetch Latest User Info", "key_544": "Address", "key_545": "Geographic Distribution", "key_546": "Location", "key_547": "Tanzania", "key_548": "Vertical Scroll Progress Value (0-1)", "key_549": "Egypt", "key_550": "Ethiopia", "key_551": "Domain", "key_552": "Domain Import Template", "key_553": "After Adding Domain, Set CNAME Resolution to", "key_554": "Domain Management", "key_555": "Domain Resolution", "key_556": "Dynamically Generate Routes Based on src/router/routes/modules File Structure", "key_557": "Senegal", "key_558": "Serbia", "key_559": "Serbian", "key_560": "Sierra Leone", "key_561": "Seychelles", "key_562": "Increase Target Audience 120", "key_563": "Enhanced Token Parsing and Validation Function - Fix <PERSON> Errors", "key_564": "Mexico", "key_565": "Process", "key_566": "Handle 401 Error, Clear User Info", "key_567": "Processing VCC Card: {cardsWithAdAccounts.length} Cards", "key_568": "Processing", "key_569": "Handle Delete User", "key_570": "Processed Selected Permissions", "key_571": "Process Image Field as URL Array", "key_572": "Process Array of Objects (e.g.,", "key_573": "Handle Ad Performance Analysis Time Range Change", "key_574": "Handle Ad Delivery Analysis Time Range Change", "key_575": "Handle Search Form Submission", "key_576": "Process Array (e.g.,", "key_577": "Process Log", "key_578": "Process Time Range", "key_579": "Process Normal Value", "key_580": "Handle Permission Tree Selection Logic", "key_581": "Handle Tree Node Selection Change", "key_582": "Handle Channel Analysis Time Range Change", "key_583": "Handle Status Change", "key_584": "Handle User Submission (Create or Update)", "key_585": "Handle Form Submission", "key_586": "Handle Table Changes (Pagination, Sorting, Filtering)", "key_587": "Process Route Path, Remove Parent Route Path Part", "key_588": "Remarks", "key_589": "Remark Information", "key_590": "Copy Address", "key_591": "Control Display When Checkbox Changes", "key_592": "Summer Sale Page", "key_593": "Summer Sale Ad Campaign", "key_594": "Togo", "key_595": "Greater Than", "key_596": "12 Months or More", "key_597": "Days Ago", "key_598": "Failed", "key_599": "Failed Sync Details", "key_600": "Avatar Upload Functionality Will Be Implemented in Future Version", "key_601": "Austria", "key_602": "Can Be Left Blank If No Special Category", "key_603": "If Already a Number, Return Directly", "key_604": "Prompt User If All IDs Are Filtered Out", "key_605": "Place at Root If Parent Node Not Found", "key_606": "If 400 Error, Try to Get More Detailed Error Info", "key_607": "If Stored Using Enum", "key_608": "If Same Level (All Root Nodes or Same Parent), Sort by Order", "key_609": "If Stored in userToken Field", "key_610": "If Stored Directly", "key_611": "If Empty String, Throw Error", "key_612": "If Edit Mode and Password Empty, Remove Password Field from Submit Data", "key_613": "Remove Parent Route Path Part If Exists", "key_614": "If Detailed Error Info Exists\n          if (backendError.extensions) {\n            console.error('Backend Error Extension Info", "key_615": "Log to Console for Debugging If Error Exists", "key_616": "Show Loading State If No Data", "key_617": "Ignore Error If JSON Parsing Fails, Meaning Not Redirect Response\n  }\n\n  // Return Original Response to Apollo Client for Handling If Not Redirect Response", "key_618": "Try Direct Clear If Parsing Fails", "key_619": "Try JSON Serialization If Still No Useful Error Info", "key_620": "Prepare Facebook Data Fetching If Selected Account Has Valid <PERSON>ken", "key_621": "e.g., 37", "key_622": "Full Name", "key_623": "Venezuela", "key_624": "Media File", "key_625": "Media Assets", "key_626": "Media Asset Company", "key_627": "Child Element Entry Animation Delay (Default: 0.05)", "key_628": "Child Element Exit Animation Delay (Default: 0.05)", "key_629": "Child Component Animation Control", "key_630": "Child Components Can Specify Own Animation Effects via variants Property", "key_631": "Add Child Nodes to Parent's Children", "key_632": "Field", "key_633": "Field JSON Parsing Failed, <PERSON><PERSON><PERSON> as <PERSON> String", "key_634": "Field Name Mismatch - Backend Expected Field Name Differs from Frontend", "key_635": "Error on Field", "key_636": "Field Format", "key_637": "Field Value", "key_638": "Store File Content", "key_639": "Exists", "key_640": "Active", "key_641": "Active Count Does Not Exceed Total", "key_642": "Active Accounts", "key_643": "Sun Ba", "key_644": "Bangladesh", "key_645": "Quarter", "key_646": "Security", "key_647": "Security Page", "key_648": "Angola", "key_649": "Install", "key_650": "Completion Rate", "key_651": "Complete", "key_652": "Complete Data", "key_653": "Complete Data Failed", "key_654": "Complete", "key_655": "Official Optimization Goal and Billing Method Linkage Table", "key_656": "Define Card Data Interface (Based on Screenshot Columns)", "key_657": "Define Tab Content, Remove Unsupported extra Attribute", "key_658": "Targeting", "key_659": "Targeting Optimization", "key_660": "Targeting Optimization Completed, Expanded Your Audience Coverage", "key_661": "Calculate Expected Balance in Real-time", "key_662": "Implement Auto Card Opening Logic\n      message.success('Auto Card Opening Function Under Development", "key_663": "Implement Risk Control Rule Table", "key_664": "Should Be Added to Database in Actual Application", "key_665": "Actual Spend", "key_666": "Should Call via Backend Proxy in Actual Deployment", "key_667": "Under Review", "key_668": "Customer", "key_669": "Check Current State Immediately During Client-Side Rendering", "key_670": "Promotional Campaign", "key_671": "Container Element Reference, Used for Container Scroll Mode", "key_672": "Fault Tolerance, Ensure Object Has", "key_673": "<PERSON> Period", "key_674": "Password", "key_675": "Incorrect Password", "key_676": "Password Length Must Be At Least 6 Characters", "key_677": "Apply Transformation to Entire Array", "key_678": "Object", "key_679": "Object Type, Try to Extract", "key_680": "Import Domain", "key_681": "Import Actual Tab Component", "key_682": "Import Copy", "key_683": "Export", "key_684": "Export Transaction Records", "key_685": "Export Transaction Records - Corresponds to Frontend Export Button", "key_686": "Export Failed", "key_687": "Export Successful", "key_688": "Export Successful, File URL", "key_689": "Export All Menus\n// Risk Control (Level 1 Menu", "key_690": "Export Report", "key_691": "Export Data", "key_692": "Export Logs", "key_693": "Export Result", "key_694": "Export Risk Data", "key_695": "Ban", "key_696": "Ban Count", "key_697": "Encapsulation", "key_698": "Cover Image and Avatar Area", "key_699": "Convert <PERSON><PERSON><PERSON> to", "key_700": "Convert config to media<PERSON><PERSON><PERSON>", "key_701": "Bind elementRef to Container Element", "key_702": "<PERSON> Obtained in Insights", "key_703": "Add Node to Parent's Children", "key_704": "Add Selected Valid Route IDs to Form Value", "key_705": "Less Than", "key_706": "Less Than 12 Months", "key_707": "Less Than 1 Minute", "key_708": "Less Than 1 Day", "key_709": "Less Than 1 Hour", "key_710": "Less Than 30 Days", "key_711": "Hours Ago", "key_712": "Try Different Path Variants", "key_713": "Try Reading from userStore", "key_714": "Try Getting Error Info from Different Properties", "key_715": "Try Extracting Token from Parsed Object - Fix Linter Error", "key_716": "Try Adding", "key_717": "Try Removing", "key_718": "Employment", "key_719": "Nigeria", "key_720": "Niger", "key_721": "Nepal", "key_722": "Display", "key_723": "Display Major Info Like Country, Age, Gender", "key_724": "Number of Impressions", "key_725": "Impressions", "key_726": "Impressions - Normalized to 0-10", "key_727": "Utility Functions", "key_728": "Left Personal Info", "key_729": "Used", "key_730": "Times Used", "key_731": "Fixed Issues", "key_732": "Closed", "key_733": "Deleted", "key_734": "Loaded Enabled Roles", "key_735": "Loaded Active Groups", "key_736": "Synced", "key_737": "Processed Accounts: {aggregatedData.totalAccounts}", "key_738": "<PERSON>pied", "key_739": "Copied to Clipboard", "key_740": "Completed", "key_741": "Banned", "key_742": "Archived", "key_743": "Successfully Bound", "key_744": "Authorized", "key_745": "Paused", "key_746": "Effective", "key_747": "Disabled", "key_748": "Issued", "key_749": "Bound Ad Accounts", "key_750": "Resolved", "key_751": "Pakistan", "key_752": "Brazil", "key_753": "Market Promotion", "key_754": "Marketing Manager", "key_755": "Marketing Department", "key_756": "Burkina Faso", "key_757": "Burundi", "key_758": "Hebrew", "key_759": "Greece", "key_760": "Greek", "key_761": "Platform Distribution", "key_762": "Platform Initialization Parameter Rendering", "key_763": "Platform Status", "key_764": "Average", "key_765": "Cost <PERSON> (CPC)", "key_766": "Average Click-Through Rate (CTR)", "key_767": "Average Conversion Rate (CVR)", "key_768": "Years Ago", "key_769": "Years of Ad Experience, Skilled in Social Media Ad Optimization and Data Analysis, Managed Online Marketing for Multiple Large Brands", "key_770": "Annual Marketing Summary", "key_771": "Age Distribution", "key_772": "Fetch Data for All Accounts in Parallel", "key_773": "Fetch Account Info and Insights Data in Parallel", "key_774": "Ad", "key_775": "Ad Specialist", "key_776": "Ad Creative GraphQL Query", "key_777": "Ad Creative Dropdown Data", "key_778": "Ad Publishing", "key_779": "Ad Account", "key_780": "Ad Account Active", "key_781": "Number of Active Ad Accounts", "key_782": "Total Ad Impressions", "key_783": "Ad Impression Trend", "key_784": "Ad Platform Analysis", "key_785": "Ad Platform Account ID (For Display)", "key_786": "Ad Performance Metric Cards", "key_787": "Ad Delivery Specialist", "key_788": "Ad Delivery Team", "key_789": "Ad Delivery Channel Analysis", "key_790": "Ad Delivery Management System", "key_791": "Ad Delivery Department", "key_792": "Ad Performance Analysis", "key_793": "Ad Performance Analysis Data Fetching", "key_794": "Number of Ads", "key_795": "Ad Data Analysis", "key_796": "Ad Campaign", "key_797": "Ad Campaign Calendar", "key_798": "Ad Campaign Overview", "key_799": "Ad Campaign Overview and Auto Optimization", "key_800": "Ad Spend and Conversion Trend", "key_801": "Ad Channel Distribution", "key_802": "<PERSON>lick Count Statistics", "key_803": "Ad Objective", "key_804": "Ad Management", "key_805": "Ad Management Workspace", "key_806": "Ad Campaign", "key_807": "Number of Campaigns", "key_808": "Campaign Management", "key_809": "Ad Creative", "key_810": "Creative Performance Analysis", "key_811": "Ad Set", "key_812": "Ad Set Dropdown Data", "key_813": "Ad Set Distribution", "key_814": "Ad Set List", "key_815": "Ad Set Pause", "key_816": "Ad Set Overview", "key_817": "Ad Set Overview and Audience Insights", "key_818": "Ad Set Management", "key_819": "Ad Account", "key_820": "Ad Account 001", "key_821": "Ad Account 002", "key_822": "Ad Account 003", "key_823": "Ad Account 004", "key_824": "Ad Account ID List", "key_825": "Ad Account OAuth Info Interface", "key_826": "Ad Account List", "key_827": "Ad Account Data Empty or Loading", "key_828": "Ad Account Data Empty, Cannot Set Echo Value", "key_829": "Ad Account Data Loaded, Start Setting Echo Value", "key_830": "Ad Account Data Monitoring", "key_831": "Ad Account Data Still Loading, Wait 500ms and Retry", "key_832": "Number of Ad Accounts", "key_833": "Has Ad Account Completed Facebook Authorization?", "key_834": "Ad Account Query Failed", "key_835": "Ad Account Query Successful", "key_836": "Ad Account Management", "key_837": "Ad Account Management Main Page", "key_838": "Ad Account Type", "key_839": "Ad Account Detailed Data", "key_840": "Ad Delivery Analysis", "key_841": "Ad Delivery Analysis Data Fetching", "key_842": "Ad: Insert", "key_843": "Error Object Serialization Failed", "key_844": "Sequence Number", "key_845": "App Install", "key_846": "App Promotion", "key_847": "App <PERSON>ush", "key_848": "Delay", "key_849": "Suggestion 1", "key_850": "Suggest Passing accountId as Callback Identifier", "key_851": "Suggest Syncing Failed Cards Separately or Re-authorizing", "key_852": "Items to Check", "key_853": "Suggestion: Check Backend GraphQL Schema or API Documentation", "key_854": "Starts With", "key_855": "Start Loading Role and Group Data", "key_856": "Start Batch Sync", "key_857": "<PERSON>le Drag Start", "key_858": "Start Time", "key_859": "Start Waiting for Data Load", "key_860": "Start Account Binding Process", "key_861": "Start Fetching", "key_862": "Start Fetching Facebook Ad Account Data and Save to Card", "key_863": "Exception", "key_864": "VCC Cards' Facebook Data", "key_865": "<PERSON>", "key_866": "<PERSON>", "key_867": "Cards", "key_868": "All Card Syncs Failed", "key_869": "Spring Stiffness", "key_870": "Force Refresh Select Component", "key_871": "Normalized to 0-10", "key_872": "Ownership/Grouping", "key_873": "Owning Primary Domain", "key_874": "Owner", "key_875": "Owner <PERSON> Be <PERSON>id Account Under Tenant", "key_876": "Manually Update Form Value When initValues Changes", "key_877": "System Will Automatically Send Notifications When VCC Card Balance Falls Below Warning Threshold. Set Your Threshold and Notification Method", "key_878": "Trigger Animation When Element Enters Viewport", "key_879": "Current adAccountData State", "key_880": "Current Balance", "key_881": "Current Value", "key_882": "Current Score", "key_883": "Current State: Real API Debug Mode", "key_884": "Current User Info", "key_885": "Current User Response", "key_886": "Current Form Value", "key_887": "Current Configuration", "key_888": "Current Page Number", "key_889": "Child Components Can Inherit These Animation Properties When Parent Sets variants", "key_890": "Populate Form When Editing User", "key_891": "Pending Closure", "key_892": "Pending Sync", "key_893": "Pending Review", "key_894": "Pending Authorization", "key_895": "Pending Activation", "key_896": "Pending Settlement", "key_897": "Pending Bill Supplement", "key_898": "Germany", "key_899": "German", "key_900": "Must Always Call", "key_901": "Must Be UUID Format", "key_902": "Ignore Folder Entries", "key_903": "Gender", "key_904": "Gender Distribution", "key_905": "Performance Metrics", "key_906": "Performance Metric Chart Configuration", "key_907": "Performance Metric Series Data (Use Impressions as Y-axis)", "key_908": "Total Transactions", "key_909": "Total Transaction Amount", "key_910": "Overall Health Score", "key_911": "Overall Budget Usage", "key_912": "Total Cards", "key_913": "Total Alerts", "key_914": "Total Impressions", "key_915": "Total Impressions", "key_916": "Total Delivery", "key_917": "Total Spend", "key_918": "Total Consumption", "key_919": "Total Clicks", "key_920": "Total Clicks", "key_921": "Total Clicks / Total Impressions", "key_922": "Summary Info", "key_923": "Overview", "key_924": "Total Accounts", "key_925": "Total Conversions", "key_926": "Total Conversions", "key_927": "Total Conversions / Total Clicks", "key_928": "Total Risks", "key_929": "Restore All Real API Calls", "key_930": "Your Role", "key_931": "Case 1", "key_932": "Case 2", "key_933": "Italy", "key_934": "Italian", "key_935": "Lazy Load Component", "key_936": "Success", "key_937": "Successfully Loaded User Data, Total", "key_938": "Successfully Synced", "key_939": "Successfully Synced: {successCount} Cards", "key_940": "Success Rate", "key_941": "Successful Sync Details", "key_942": "Members", "key_943": "Member Count", "key_944": "My Team", "key_945": "My Projects", "key_946": "Or Use Facebook Official", "key_947": "Or Configure Appropriate CORS Policy", "key_948": "Belonging Campaign", "key_949": "Belonging Campaign", "key_950": "Belonging Group", "key_951": "All IDs Are Valid UUID Format", "key_952": "All Ad Accounts", "key_953": "Selected Route ID Format Does Not Meet System Requirements (UUID v4), Cannot Bind Permissions", "key_954": "Flat Format", "key_955": "Manually Add Recharge Record", "key_956": "Manually Add Recharge Record - Quick Operation", "key_957": "Open Create User Mo<PERSON>", "key_958": "Open Popup", "key_959": "Open Add/Edit Popup", "key_960": "Open Add Role Dialog", "key_961": "Open Edit User Modal, User", "key_962": "Open Edit Role Dialog", "key_963": "Extend Registration Parameters, Include Confirm Password Field", "key_964": "Batch Facebook Data Sync Failed", "key_965": "Batch Facebook Data Sync Complete", "key_966": "Batch Token Analysis Started", "key_967": "Batch Token Analysis Result", "key_968": "Batch Token Detection Function", "key_969": "Batch Upload", "key_970": "Batch Upload IP Restriction", "key_971": "Batch Upload Complete, Success", "key_972": "Batch Create VCC Cards", "key_973": "Batch Refresh All Ads", "key_974": "Batch Sync Facebook Data", "key_975": "Batch Sync Facebook Data Failed", "key_976": "Batch Sync Failed", "key_977": "Batch Sync Failed! All", "key_978": "Batch Sync Complete! Success", "key_979": "Batch Sync Summary", "key_980": "Batch Sync Result", "key_981": "Batch Import", "key_982": "Batch Import Domains", "key_983": "Batch Import Template Spreadsheet", "key_984": "<PERSON><PERSON> Add", "key_985": "Batch Fetch VCC Cards' Facebook Data", "key_986": "Found Valid", "key_987": "Technical Support", "key_988": "Delivering", "key_989": "Delivery Status", "key_990": "Delivery Account", "key_991": "Report", "key_992": "Fetch All Accounts Under Current Tenant", "key_993": "Latvia", "key_994": "Latvian", "key_995": "Cardholder", "key_996": "Holder", "key_997": "Metric", "key_998": "Find and Parse by Priority", "key_999": "Find Valid by Priority", "key_1000": "Group by Type", "key_1001": "Split by Path", "key_1002": "Add Feature Modules and Tables as Needed", "key_1003": "Norway", "key_1004": "Norwegian", "key_1005": "Czech Republic", "key_1006": "Czech", "key_1007": "Authorize", "key_1008": "Authorization Info Expired", "key_1009": "Authorization Callback URL, Need<PERSON> Whitelist in Facebook Developer Settings", "key_1010": "Authorization Successful (Simulation)", "key_1011": "Authorization Method", "key_1012": "Authorization Flow Description]\n    // 1. Need to pass parameter account (ad account unique identifier, usually accountId).\n    // 2. Backend getAdAccountOAuth2Url query will generate Facebook authorization link, needs configuration", "key_1013": "Authorization Status", "key_1014": "Sort", "key_1015": "Scheduling", "key_1016": "Scheduling Type", "key_1017": "Excluded Countries", "key_1018": "Received Role Data", "key_1019": "Promote", "key_1020": "Promotion Task", "key_1021": "Promotion Task Management", "key_1022": "Promotional Image", "key_1023": "Upload at Least 3 Promotional Images", "key_1024": "Promotion Platform", "key_1025": "Promotion Data Type", "key_1026": "Promotional Copy", "key_1027": "Description", "key_1028": "Submit Recharge", "key_1029": "Submission Failed", "key_1030": "Submission Failed, Please Check Form Data", "key_1031": "Submit New Card", "key_1032": "Submitted Settings", "key_1033": "Submit Binding Account - Directly Use Current Selected Account's", "key_1034": "Submit Form", "key_1035": "Submit Form Data", "key_1036": "Provide Unified Animation State Management", "key_1037": "Increase Click-Through Rate by 8.5", "key_1038": "Extract", "key_1039": "Extract Numeric Part", "key_1040": "Extract Data", "key_1041": "Extract Filename", "key_1042": "Prompt", "key_1043": "Inserted {res.inserted}, Updated {res.updated}, Skipped", "key_1044": "Search", "key_1045": "Search ID or Name", "key_1046": "Search Name or Domain", "key_1047": "Search Alerts", "key_1048": "Search Users", "key_1049": "Search Username", "key_1050": "Search Tenant Name", "key_1051": "Search Combinations", "key_1052": "Search Group Name or Description", "key_1053": "Search Contacts", "key_1054": "Search Form", "key_1055": "Search Form Type", "key_1056": "Search Rules", "key_1057": "Morocco", "key_1058": "Operation", "key_1059": "Operator", "key_1060": "Operation Failed", "key_1061": "Action Buttons", "key_1062": "Action Button Area", "key_1063": "Operate User Failed", "key_1064": "Operating System", "key_1065": "OS Options", "key_1066": "Payment", "key_1067": "Payment Security", "key_1068": "Payment Security Rules", "key_1069": "Payment System", "key_1070": "Payment System Abnormal Transaction Pattern", "key_1071": "Spend", "key_1072": "Support", "key_1073": "Support Multiple Preset Animation Effects: fade, slide, zoom, bounce, flip, scale, rotate, etc.", "key_1074": "Support Cascading Animation Effects for Child Components", "key_1075": "Supported Standard Event Types", "key_1076": "Support USD Recharge, Minimum Recharge $1", "key_1077": "Support Custom Animation Variants", "key_1078": "Change to", "key_1079": "Politics/Elections/Social Issues", "key_1080": "Effect Comparison", "key_1081": "Sensitive Words", "key_1082": "Sensitive Word Ads", "key_1083": "Sensitive Word Detection", "key_1084": "Education", "key_1085": "Numeric", "key_1086": "Data", "key_1087": "Data and ZIP File Matching Result", "key_1088": "Data Save Failed", "key_1089": "Data Save Instructions", "key_1090": "Data Analysis", "key_1091": "Data Analysis Team", "key_1092": "Data Analyst", "key_1093": "Data Analysis Department", "key_1094": "Data Load Failed", "key_1095": "Data Load Failed, Please Refresh Page and Retry", "key_1096": "Data Load Complete", "key_1097": "Data Sync Failed", "key_1098": "Data Sync Successful", "key_1099": "Data Display", "key_1100": "Data Refreshed", "key_1101": "Data Successfully Saved to Backend Database", "key_1102": "Data Automatically Saved to VCC Card", "key_1103": "Total Data", "key_1104": "Data Interface Definition", "key_1105": "Data Source Description", "key_1106": "Data Source: Facebook Marketing API (Real-time Fetch)", "key_1107": "Data Source: Facebook Marketing API (Direct Call to Third-party API)", "key_1108": "Data Source: Facebook Marketing API (Via Backend Service)", "key_1109": "Data Type Definition", "key_1110": "Data Type Error - Some Fields Expect Number Instead of String", "key_1111": "Data Fetch Failed", "key_1112": "Data Validation Passed", "key_1113": "Array", "key_1114": "Each Element of Array", "key_1115": "Array Length", "key_1116": "Quantity", "key_1117": "Overall Entry Animation Delay (Default: 0.05)", "key_1118": "File", "key_1119": "File Upload", "key_1120": "File Content", "key_1121": "Text Upload", "key_1122": "Copy", "key_1123": "Copy Content", "key_1124": "<PERSON><PERSON> Added Successfully", "key_1125": "Copy Management", "key_1126": "Cloaking System", "key_1127": "Slovakia", "key_1128": "Slovak", "key_1129": "Slovenia", "key_1130": "Slovenian", "key_1131": "Sri Lanka", "key_1132": "New Product Launch Promotion", "key_1133": "New Group Name", "key_1134": "Singapore", "key_1135": "New Product Launch Page", "key_1136": "Add", "key_1137": "Add App List GraphQL Query", "key_1138": "Add Facebook User App Promotion Query (Use campaignId to Automatically Query Account and", "key_1139": "Add/Edit/Delete", "key_1140": "Add/Edit <PERSON>up", "key_1141": "Add/Edit Submit", "key_1142": "Add IP Restriction", "key_1143": "Add Card", "key_1144": "Add Card Popup", "key_1145": "Add Audience", "key_1146": "Add Ad", "key_1147": "Add Campaign", "key_1148": "Add Ad Set", "key_1149": "Add Successful", "key_1150": "Add Promotion", "key_1151": "Add Tenant", "key_1152": "Add Material", "key_1153": "Add Group", "key_1154": "Add Landing Page", "key_1155": "Add Rule", "key_1156": "Add Role", "key_1157": "Add Account", "key_1158": "Add: loading/No Data Prompt", "key_1159": "Add: tenantId Parameter", "key_1160": "Add: Token Invalidation Handling", "key_1161": "Add: Pass device_platforms to Backend", "key_1162": "Add: Ad Set-Creative Linkage", "key_1163": "Add: Channel code Parameter", "key_1164": "New", "key_1165": "New/Edit/Delete", "key_1166": "New/Edit Popup", "key_1167": "Create, Edit, Delete Tenants", "key_1168": "New State", "key_1169": "New Zealand", "key_1170": "New Account Large Transactions", "key_1171": "New Selected Value", "key_1172": "No Token", "key_1173": "No Size Limit, Supports", "key_1174": "No Corresponding File", "key_1175": "Invalid or Expired Token", "key_1176": "Invalid Ad Account", "key_1177": "No Data", "key_1178": "Number of Accounts Without <PERSON><PERSON>", "key_1179": "Number of Accounts Without <PERSON><PERSON>", "key_1180": "Unable to Load Enabled Roles", "key_1181": "Unable to Load Active Groups", "key_1182": "Unmatched Binding", "key_1183": "Unparseable Error", "key_1184": "No Group", "key_1185": "No Role", "key_1186": "No Details", "key_1187": "Log Link", "key_1188": "Date", "key_1189": "Date String or Date Object", "key_1190": "Date Formatting Utility Function", "key_1191": "Date Picker Disabled Date Logic", "key_1192": "Japan", "key_1193": "Japanese", "key_1194": "Daily Budget", "key_1195": "Time", "key_1196": "Time Range", "key_1197": "Time Range: Last 30 Days of Ad Data", "key_1198": "Detail Table", "key_1199": "Star Rating", "key_1200": "Mapping Table", "key_1201": "Is Empty", "key_1202": "Whether Deletion Successful", "key_1203": "Enabled", "key_1204": "Hidden", "key_1205": "Is Array", "key_1206": "Is Array?", "key_1207": "Is Video File", "key_1208": "Show", "key_1209": "Show Specific Validation Errors", "key_1210": "Show Create User Mo<PERSON>", "key_1211": "Show Loading Prompt", "key_1212": "Show Original Data Structure", "key_1213": "Show All Attempted Formats", "key_1214": "Show All Route IDs for Format Check", "key_1215": "Show Binding and Facebook Data Fetch Results", "key_1216": "Show Edit User <PERSON>", "key_1217": "Show Detailed Sync Results", "key_1218": "Show Detailed Error Diagnostic Info", "key_1219": "Smart Token Fetch and Validation Function - Simplified Version", "key_1220": "Intelligently Adjust Bidding Strategy to Maximize Conversions", "key_1221": "Pause", "key_1222": "Pausing", "key_1223": "Pause Transaction", "key_1224": "Pause Ad", "key_1225": "No Facebook Authorized Accounts, Cannot Fetch Data Immediately", "key_1226": "No Ads to Refresh", "key_1227": "No Data", "key_1228": "No Type Distribution Data", "key_1229": "No Role Data", "key_1230": "No Route Data", "key_1231": "Use Simple Validation Logic Temporarily, Should Call Backend", "key_1232": "Temporarily Banned", "key_1233": "Impressions/30 Clicks", "key_1234": "More Members", "key_1235": "More Settings (Customizable Extension)", "key_1236": "Change Avatar", "key_1237": "Change Bid", "key_1238": "Change User Status", "key_1239": "User Status Change Failed", "key_1240": "Change Budget", "key_1241": "Update", "key_1242": "Update VCC Card", "key_1243": "Update VCC Card Facebook Data (Use Standard Update Interface)", "key_1244": "Update Transaction Record", "key_1245": "Update Pagination", "key_1246": "Update to Card Transaction Count Field", "key_1247": "Update to Card Consumption Field", "key_1248": "Update to Active Status", "key_1249": "Updated User", "key_1250": "Updated Role", "key_1251": "Update Failed", "key_1252": "Update Successful", "key_1253": "Update Sorting", "key_1254": "Update Time", "key_1255": "Update User", "key_1256": "Update User\n        console.log('Update user", "key_1257": "Update User Parameters", "key_1258": "User Update Failed", "key_1259": "Update Result", "key_1260": "Update Group", "key_1261": "Update Role", "key_1262": "Update Role\n\t\t\t\tconsole.log('Update role", "key_1263": "Update Role Parameters", "key_1264": "Role Update Failed", "key_1265": "Role Status Update Failed", "key_1266": "Role Status Update Failure Details", "key_1267": "Update Request Headers", "key_1268": "Update Filter", "key_1269": "Update Expected Balance Display", "key_1270": "Replace with Actual GraphQL Query", "key_1271": "Replace with Actual GraphQL Query and", "key_1272": "Last Used Time", "key_1273": "Last Updated", "key_1274": "Maximum 65 Years Old", "key_1275": "Maximum Age", "key_1276": "Maximum Range", "key_1277": "Minimum 18 Years Old", "key_1278": "Minimum Age", "key_1279": "Minimum 3, Recommended <PERSON><PERSON> 252x360, Supports", "key_1280": "Final Form Set Value", "key_1281": "Final Error Message", "key_1282": "Last 1 Year", "key_1283": "Last 30 Days", "key_1284": "Last 7 Days", "key_1285": "Last 90 Days", "key_1286": "Recent Optimization", "key_1287": "Recent Alerts", "key_1288": "Monthly Budget Trend", "key_1289": "Number of Accounts with accessToken", "key_1290": "Number of Accounts with facebookToken", "key_1291": "Number of Accounts with oauth", "key_1292": "Number of Accounts with token", "key_1293": "Account Details with Token", "key_1294": "Number of Accounts with <PERSON>ken", "key_1295": "<PERSON><PERSON>", "key_1296": "Validity Period", "key_1297": "Number of Accounts with <PERSON><PERSON>", "key_1298": "Number of Accounts with <PERSON><PERSON>", "key_1299": "Has Issue", "key_1300": "Service", "key_1301": "Service - Via Backend API Call", "key_1302": "Default to During Server-Side Rendering", "key_1303": "No Route ID in Server-Returned Role Data, Binding May Have Failed", "key_1304": "Unused", "key_1305": "Unnamed", "key_1306": "Unnamed Account", "key_1307": "Not Started", "key_1308": "No Bound Ad Account Info Found", "key_1309": "Unauthorized", "key_1310": "Inactive", "key_1311": "Unknown", "key_1312": "Unknown Error", "key_1313": "Unbound", "key_1314": "Unsettled", "key_1315": "Failed to Get Latest Data", "key_1316": "Failed to Get User Info", "key_1317": "Failed to Get Route Data", "key_1318": "Local", "key_1319": "Local Card List Data Will Update via refetch", "key_1320": "Permission", "key_1321": "Insufficient Permissions, Ensure Ad Account Has ads_read Permission", "key_1322": "Permission Tree Structure", "key_1323": "Permission Binding Warning", "key_1324": "Permission Settings", "key_1325": "Permission Issue", "key_1326": "Permission Issue - Current User Has No Bound Permissions", "key_1327": "<PERSON>", "key_1328": "<PERSON>", "key_1329": "Condition", "key_1330": "Conditional Dynamic Form", "key_1331": "Conditional Add/Delete/Modify", "key_1332": "Records", "key_1333": "Items, Total", "key_1334": "Items, Failed", "key_1335": "Source", "key_1336": "From Facebook Real-time Data", "key_1337": "Build", "key_1338": "Build OAuth Info", "key_1339": "Build OAuth Info - Use New Direct API Service Type", "key_1340": "Build OAuth Info - Account", "key_1341": "Build Parent Route Tree Selection Data", "key_1342": "Build Account Status Map", "key_1343": "Build Route Tree Data", "key_1344": "Construct Audience Dropdown Label, Include Main Fields", "key_1345": "Find Matching Component Path", "key_1346": "View", "key_1347": "View {opt.recommendations} Optimization Suggestions", "key_1348": "View Transaction Details", "key_1349": "View Profile", "key_1350": "Query", "key_1351": "Query VCC Card List", "key_1352": "Query Transaction Record List (<PERSON><PERSON><PERSON>, Filter) - Corresponds to Frontend", "key_1353": "Query Transaction Record List (Supports Pagination and Filtering)", "key_1354": "Query Single Transaction Record Details", "key_1355": "Query Single Transaction Record Details - Corresponds to Frontend Transaction Details", "key_1356": "Query Parameters", "key_1357": "Query Variable Type", "key_1358": "Query Response Type", "key_1359": "Query Failed", "key_1360": "Query Ad List", "key_1361": "Query Campaigns", "key_1362": "Query Ad Account", "key_1363": "Query Ad Account List", "key_1364": "Query Report", "key_1365": "Query Operation", "key_1366": "Query If Contains token Field", "key_1367": "Query Tenant List", "key_1368": "Query Error", "key_1369": "Cambodia", "key_1370": "Bar Chart", "key_1371": "Standard UUID v4 Format: Third Group Starts with 4, Fourth Group Starts with 8", "key_1372": "Normalize Path", "key_1373": "Tag", "key_1374": "Tab Content Area", "key_1375": "Tab Configuration", "key_1376": "Tag Content", "key_1377": "Validate Field", "key_1378": "Check If Valid", "key_1379": "Automatically Set Based on countryCode", "key_1380": "Use Correct Format According to Backend DTO Definition", "key_1381": "According to Backend Validation Rules, Must Be UUID v4 Format String", "key_1382": "Provide More Detailed Error Info Based on Error Type\n            if (error.message.includes('Failed to fetch')) {\n                throw new Error('Network Connection Failed, Please Check If Backend Service is Running Normally", "key_1383": "Root Nodes Join Tree Directly", "key_1384": "Format Transaction Status Display", "key_1385": "Format Transaction Type Display", "key_1386": "Format Card Number Display: Separate Every 4 Digits with Space", "key_1387": "Formatted Date String", "key_1388": "Format Numeric Display", "key_1389": "Format Data to Ensure Correct Data Types", "key_1390": "Format Date to String", "key_1391": "Format Time Display", "key_1392": "Format Template, De<PERSON><PERSON>", "key_1393": "Format Relative Time (e.g., Minutes Ago, Hours Ago, etc.)", "key_1394": "Format Amount Display", "key_1395": "Georgia", "key_1396": "Desktop", "key_1397": "Check Facebook Business Manager Settings", "key_1398": "Check Token Info Using New Parsing Function", "key_1399": "Check If Token is Within Validity Period", "key_1400": "Check Which Bound IDs Exist in Current Ad Account List", "key_1401": "Check Which Selected Accounts Have", "key_1402": "Check Ad Account Data Loading Status", "key_1403": "Check All Possible Token Fields", "key_1404": "Check All Possible Token Fields, Prioritized", "key_1405": "Check All Possible Locations", "key_1406": "Check Method 1", "key_1407": "Check Method 2", "key_1408": "Check Method 3", "key_1409": "Check If Date is Valid", "key_1410": "Check If Ends with px (Case Insensitive)", "key_1411": "Check If JSON String Format", "key_1412": "Check If Current Logged-in User", "key_1413": "Check If Parent <PERSON> of Some Node", "key_1414": "Check If Other Places Are Making Heavy Calls", "key_1415": "Check If Extension Info Exists\n        if (error.graphQLErrors[0].extensions) {\n          console.error('GraphQL Error Extension Info", "key_1416": "Check Final Result", "key_1417": "Check Each Role Has Bound Route", "key_1418": "Check Result", "key_1419": "Check Current Form State", "key_1420": "Detected Multiple Large Transactions in Short Time", "key_1421": "Detected Account Multiple Failed <PERSON>gins from Different Regions", "key_1422": "Detect Abnormal Payment Behavior", "key_1423": "Detect User-Uploaded Sensitive Content", "key_1424": "Detect Abnormal Account <PERSON>gin <PERSON>vior", "key_1425": "Modal Content", "key_1426": "Modal Properties", "key_1427": "Simulate", "key_1428": "Simulate VCC Card Data", "key_1429": "Simulated Transaction Count", "key_1430": "Simulated Transaction Record Data", "key_1431": "Simulated Team Member Data", "key_1432": "Simulated Team Data", "key_1433": "Simulated Country", "key_1434": "Simulated Country Code", "key_1435": "Simulated Remarks", "key_1436": "Simulated Times Used", "key_1437": "Simulated Cardholder", "key_1438": "Simulated Data - Adapt Existing Data to New Interface Format", "key_1439": "Simulated Data - System Health Status", "key_1440": "Simulated Data - Risk Control Rules", "key_1441": "Simulated Data - Risk Alert Data", "key_1442": "Simulated Consumption Amount", "key_1443": "Simulated Channel", "key_1444": "Simulated User Data", "key_1445": "Simulated Binding Count", "key_1446": "Simulated Expiration Year", "key_1447": "Simulated Expiration Month", "key_1448": "Simulated Connection Data", "key_1449": "Simulated Postal Code", "key_1450": "Simulated Limit Count", "key_1451": "Simulated Project Data", "key_1452": "Template", "key_1453": "Template Download", "key_1454": "Arrears", "key_1455": "Impressions", "key_1456": "Regex Validation - Fix to Comply with UUID v4 Format Requirements", "key_1457": "Optimizing", "key_1458": "Saving Facebook Data to Backend", "key_1459": "Saving Bound Facebook Data to Backend", "key_1460": "Loading User Data, Page", "key_1461": "Loading Group Data", "key_1462": "Fetching Data Directly from Facebook", "key_1463": "Fetching", "key_1464": "Calling", "key_1465": "Normal", "key_1466": "Every Half Hour", "key_1467": "Daily", "key_1468": "Multiple Time Points Daily, All Times US West Pacific Time", "key_1469": "Hourly", "key_1470": "Per Impression", "key_1471": "<PERSON>", "key_1472": "Items Per Page", "key_1473": "Belgium", "key_1474": "Mauritania", "key_1475": "Horizontal Scroll Progress Value (0-1)", "key_1476": "Saudi Arabia", "key_1477": "No Token Case", "key_1478": "No Transaction Records, Cannot Sync", "key_1479": "No VCC Cards to Sync", "key_1480": "No Available Enabled Roles", "key_1481": "No Available Active Groups", "key_1482": "No Enabled Roles Found", "key_1483": "No Valid Found", "key_1484": "No Valid OAuth Authorization Info Found, Please Go to Ad Account Management Page for Authorization First", "key_1485": "No Active Groups Found", "key_1486": "No Ad Account Info Provided", "key_1487": "Return Default Value If No Data", "key_1488": "No Valid Accounts", "key_1489": "VCC Cards Not Bound to Facebook Ad Account", "key_1490": "No Bound Route", "key_1491": "France", "key_1492": "French", "key_1493": "Poland", "key_1494": "Polish", "key_1495": "Register", "key_1496": "Registration Parameters", "key_1497": "Registration Mutation", "key_1498": "Registration Response", "key_1499": "Registration Failed", "key_1500": "Registration Success Prompt\n\t\t\ttoast.success(\"Registration Successful! Please Login with New Account", "key_1501": "Registration Time", "key_1502": "Registration Request Parameters", "key_1503": "Note: Binding Operation Will Completely Replace Current Bound Account, Not Append", "key_1504": "Thailand", "key_1505": "Thai", "key_1506": "Zimbabwe", "key_1507": "Active", "key_1508": "Active/Total", "key_1509": "Active Alerts", "key_1510": "Active Ad Campaigns", "key_1511": "List of Active Groups", "key_1512": "Active Rules", "key_1513": "Traffic", "key_1514": "Traffic Distribution and Landing Page", "key_1515": "Test", "key_1516": "Browser CORS Restrictions", "key_1517": "Browser Blocks Direct Calls", "key_1518": "Browser Blocks Direct Calls to Facebook API (This is Normal)", "key_1519": "Spend", "key_1520": "Auto Pause When Spend Exceeds 100", "key_1521": "Consumption", "key_1522": "Consumption Amount", "key_1523": "Add", "key_1524": "Add /src Prefix", "key_1525": "Added Export Button Loading State", "key_1526": "Add Group", "key_1527": "Add Action", "key_1528": "Add Card Failed", "key_1529": "Add Partner", "key_1530": "Add Colleague", "key_1531": "<PERSON><PERSON>", "key_1532": "Add Domain", "key_1533": "Add Customer", "key_1534": "Add Successful", "key_1535": "Add or Edit Role", "key_1536": "Add Copy", "key_1537": "Add Time Point", "key_1538": "Add Condition", "key_1539": "Add Landing Page", "key_1540": "Add Form Validation Error Handling", "key_1541": "Add Rule", "key_1542": "Add <PERSON>", "key_1543": "Add Route", "key_1544": "Add Risk Control Rule", "key_1545": "Add Risk Alert", "key_1546": "Add Default Sort", "key_1547": "Add Default Redirect to First Child Route", "key_1548": "<PERSON> <PERSON>", "key_1549": "Cleanup Function", "key_1550": "Cleanup Listeners", "key_1551": "Clear Related Fields", "key_1552": "Clear Material Selection", "key_1553": "Clear Previous Timer", "key_1554": "Clear Local Token and User Info", "key_1555": "Failed to Clear User Info", "key_1556": "Channel", "key_1557": "Channel ROI Comparison Chart Configuration", "key_1558": "Channel Distribution", "key_1559": "Channel Distribution Pie Chart Configuration", "key_1560": "Channel Analysis", "key_1561": "Channel Analysis Data Fetching", "key_1562": "Channel Comparison", "key_1563": "Channel Performance Comparison", "key_1564": "Channel Effectiveness Comparison Radar Chart Configuration", "key_1565": "Channel Code", "key_1566": "Render", "key_1567": "Scroll Target Type", "key_1568": "Scroll Target Type, Options: \"document\" or \"container\", Default", "key_1569": "Scroll Progress Value", "key_1570": "Scroll Progress Value, Range 0-1", "key_1571": "Scroll Progress Bar Component", "key_1572": "Funnel Analysis", "key_1573": "Funnel Chart Data", "key_1574": "Funnel Chart Configuration", "key_1575": "<PERSON>", "key_1576": "Lead", "key_1577": "Potential Increase", "key_1578": "Potential Savings", "key_1579": "Potential Reach", "key_1580": "Australia", "key_1581": "Click", "key_1582": "Click to <PERSON><PERSON>", "key_1583": "Cost <PERSON> (CPC)", "key_1584": "Cost Per <PERSON> - Inverse Metric, Lower is Better, Normalized to 0-10", "key_1585": "Click Count", "key_1586": "Click to View Details", "key_1587": "Click-Through Rate (CTR)", "key_1588": "Click-Through Rate - Normalized to 0-10", "key_1589": "CTR Analysis", "key_1590": "<PERSON>licks", "key_1591": "Clicks - Normalized to 0-10", "key_1592": "Ireland", "key_1593": "Estonia", "key_1594": "Estonian", "key_1595": "Parent Route", "key_1596": "Material Name", "key_1597": "Material Management", "key_1598": "Material Management Page Reuses Original", "key_1599": "Specially Handle Response Info in Network Errors\n      if (error.networkError) {\n        console.error('Network Error Status Code", "key_1600": "Specific IP Address Sending Numerous Requests in Short Time", "key_1601": "Special Ad Category", "key_1602": "Status", "key_1603": "Status/Tag", "key_1604": "State Object", "key_1605": "Status Map", "key_1606": "Status Update Failed", "key_1607": "Status Update Successful", "key_1608": "Status Update Result", "key_1609": "State Management", "key_1610": "Status Color Map", "key_1611": "Status: Normal, Frozen, Invalid, etc.", "key_1612": "<PERSON>", "key_1613": "<PERSON>", "key_1614": "Now validAccountIds Contains Internal UUIDs, All Should Be Valid UUID Format", "key_1615": "Sweden", "key_1616": "Swedish", "key_1617": "Switzerland", "key_1618": "Used to Force Refresh Select Component", "key_1619": "User", "key_1620": "User-Uploaded Content May Contain Sensitive Info", "key_1621": "User Token Type", "key_1622": "User Info", "key_1623": "User Info Type", "key_1624": "User List Response", "key_1625": "User Created Successfully", "key_1626": "User Deleted Successfully", "key_1627": "Username", "key_1628": "Username Only Supports Letters, Numbers, Underscores", "key_1629": "Username Length Must Be 3-20 Characters", "key_1630": "User Growth", "key_1631": "User Growth Strategy Analysis", "key_1632": "User Avatar", "key_1633": "User Data", "key_1634": "User Updated Successfully", "key_1635": "User Update Data", "key_1636": "User Query Parameters", "key_1637": "User Registration", "key_1638": "User Status Changed Successfully", "key_1639": "User Status Enum", "key_1640": "User Login", "key_1641": "User Management", "key_1642": "User Authentication System", "key_1643": "User Item", "key_1644": "E-commerce", "key_1645": "Email", "key_1646": "TV", "key_1647": "Phone", "key_1648": "Login Parameters", "key_1649": "Login Mutation", "key_1650": "Login Response", "key_1651": "Login Failed", "key_1652": "Login Failed, Please Check Username and Password", "key_1653": "Login Security Rules", "key_1654": "Login Success Prompt\n\t\t\t\t\t\ttoast.success(\"Login Successful", "key_1655": "Login Successful, Retrieved", "key_1656": "Login Request Parameters", "key_1657": "Belarus", "key_1658": "Percentage", "key_1659": "onCheck May Return String Array or", "key_1660": "Card? This Operation Cannot Be Undone", "key_1661": "Case", "key_1662": "Minimum Movement Distance Before Triggering Drag", "key_1663": "Listen for href Changes", "key_1664": "Listen for popstate Event (<PERSON><PERSON> Forward/Back)", "key_1665": "Listen to GraphQL Return, Update Creative Dropdown", "key_1666": "Listen for Changes", "key_1667": "Listen to Container <PERSON><PERSON>", "key_1668": "Listen to Campaign Changes, Automatically Get Ad Account ID and accessToken, Fetch Facebook User", "key_1669": "Listen to Ad Set Selection Change, Automatically Request Creatives", "key_1670": "Listen to <PERSON><PERSON> of Specified Container", "key_1671": "Listen to <PERSON><PERSON> of Entire Document", "key_1672": "Monitor Ad Account Data Changes - Add Detailed Token Debugging", "key_1673": "Monitoring Ended", "key_1674": "Target", "key_1675": "Target Audience Optimization", "key_1676": "Read Directly from localStorage", "key_1677": "Use All Data Directly\n    // Pie Chart Statistics", "key_1678": "Use String Value Directly", "key_1679": "Directly a String", "key_1680": "Direct Call", "key_1681": "Directly Call Facebook Third-party API to Get Ad Data", "key_1682": "Directly Call Service", "key_1683": "Relative Time String", "key_1684": "Real Data (<PERSON><PERSON>)", "key_1685": "SMS", "key_1686": "Multiple Failed Logins in Short Time", "key_1687": "Multiple Large Transactions in Short Time", "key_1688": "Ensure roleIds and groupIds Are Correct Format", "key_1689": "Ensure tenantId Correctly Set - Use Existing in Database", "key_1690": "Ensure User Info Contains Route Data", "key_1691": "Ensure Role and Group IDs Are Arrays", "key_1692": "Ensure Path Ends with .tsx", "key_1693": "Confirm", "key_1694": "Confirm Delete", "key_1695": "Confirm Delete This User", "key_1696": "Confirm Delete This Tenant", "key_1697": "Confirm Delete Card Number", "key_1698": "Confirm Delete?", "key_1699": "Confirm Delete This Role?", "key_1700": "Confirm Delete This Domain?", "key_1701": "Confirm Delete Route", "key_1702": "Confirm Delete This Project? This Operation Cannot Be Undone", "key_1703": "Confirm", "key_1704": "Confirm Token Has ads_read Permission", "key_1705": "Confirm Recharge", "key_1706": "Confirm Delete", "key_1707": "Confirm Delete This Audience", "key_1708": "Confirm Delete This Campaign", "key_1709": "Confirm Delete This Ad Set", "key_1710": "Confirm Delete This Ad Account", "key_1711": "Confirm Ad Account Has ads_read Permission", "key_1712": "Example Type, Can Adjust Based on Actual Interface", "key_1713": "Example: Create Dashboard Route", "key_1714": "Disable", "key_1715": "Disable Original Ad Delivery System Menu, Its Submenus Promoted to Level 1", "key_1716": "Kuwait", "key_1717": "Technology", "key_1718": "Comoros", "key_1719": "Cote d'Ivoire", "key_1720": "Seconds Ago", "key_1721": "Seconds of Continuous Video Playback", "key_1722": "Peru", "key_1723": "Tenant", "key_1724": "Tenant Created Successfully", "key_1725": "Tenant Deleted Successfully", "key_1726": "Tenant Name", "key_1727": "Tenant Name Cannot Be Empty", "key_1728": "Tenant Submission Failed", "key_1729": "Number of Tenants", "key_1730": "Tenant Updated Successfully", "key_1731": "Tenant Management", "key_1732": "Tenant Administrator", "key_1733": "Tenant Resource Details", "key_1734": "Mobile", "key_1735": "Remove Protocol and Path", "key_1736": "Remove Leading Slash", "key_1737": "Remove All Spaces", "key_1738": "Null Values Controlled by required", "key_1739": "Tunisia", "key_1740": "Optimize Now", "key_1741": "Immediately Update Data in Apollo Client Cache, User Sees Update Instantly", "key_1742": "Buy Now", "key_1743": "Lithuania", "key_1744": "Lithuanian", "key_1745": "Bid", "key_1746": "Bid Optimization", "key_1747": "Bid Optimization Completed, Improved Your Ad Performance", "key_1748": "<PERSON><PERSON> (Yuan)", "key_1749": "Page {page} / {pageCount}", "key_1750": "Step 1: Create Mapping for All Nodes", "key_1751": "Step 1: Create Mapping for All Menu Nodes", "key_1752": "Step 3: Recursively Sort All Levels", "key_1753": "Step 2: Build Tree Structure", "key_1754": "Equal To", "key_1755": "Retry After Waiting a Few Minutes", "key_1756": "Set Bound Account Value After Ad Account Data Finishes Loading", "key_1757": "Wait for Use<PERSON> Login to Test Function", "key_1758": "etc.)\n    // 3. After User Authorization, Facebook Calls Back redirect_uri with code and state\n    // 4. Backend Callback Interface Uses code to Exchange access_token, Finds Corresponding Ad Account Based on state, Saves access_token\n    // 5. Frontend Doesn't Need to Care About client_id, redirect_uri, etc., Only Pass accountId", "key_1759": "Filter", "key_1760": "Issuing", "key_1761": "Manage", "key_1762": "Manage Main Component", "key_1763": "Manage Selected Ad Accounts", "key_1764": "Management Page Reuses Original", "key_1765": "Type", "key_1766": "Type Icon Map", "key_1767": "Type Multi-Select Filter", "key_1768": "Type Definition", "key_1769": "Type Definition - <PERSON><PERSON>rre<PERSON>onds to Backend", "key_1770": "Campaign Pending Review", "key_1771": "Campaign Paused", "key_1772": "Campaign Has Issue", "key_1773": "Campaign: Insert", "key_1774": "System", "key_1775": "Valid Routes in System", "key_1776": "System Health Status", "key_1777": "System Detected 3 High-Risk Items Requiring Immediate Action, Please Review and Take Corresponding Measures", "key_1778": "System Management", "key_1779": "System Administrator", "key_1780": "System Settings", "key_1781": "System Risk Reminder", "key_1782": "Creative", "key_1783": "Creative CTR Comparison Chart Configuration", "key_1784": "Creative CVR Comparison Chart Configuration", "key_1785": "Creative Library", "key_1786": "Number of Creatives", "key_1787": "Creative Management", "key_1788": "Creative Type", "key_1789": "Creative Performance Comparison Chart Data", "key_1790": "Creative Performance Comparison Chart Configuration", "key_1791": "Somalia", "key_1792": "<PERSON><PERSON>", "key_1793": "<PERSON><PERSON>", "key_1794": "Jordan", "key_1795": "Level Color Map", "key_1796": "Namibia", "key_1797": "Line Chart", "key_1798": "Lead Generation", "key_1799": "Component - Used to Create Viewport Scroll-Based Animation Effects", "key_1800": "Clear Timer When Component Unmounts", "key_1801": "Component Properties", "key_1802": "Component Properties Interface", "key_1803": "Component Renders Without Errors", "key_1804": "Component Path", "key_1805": "Combination", "key_1806": "Combination Name", "key_1807": "Ad Set Pending Review", "key_1808": "Ad Set Has Issue", "key_1809": "Assemble Parameters, Compatible with Backend and", "key_1810": "Ad Set: Insert", "key_1811": "Bound", "key_1812": "Bound Facebook Data Successfully Saved to Backend Database", "key_1813": "Binding Echo Debug Info", "key_1814": "Binding Echo Debug Info End", "key_1815": "Binding Failed", "key_1816": "Binding Failure Details", "key_1817": "Binding and Data Fetching Complete! Spent", "key_1818": "Total Bound Ad Accounts", "key_1819": "Bind Ad Account", "key_1820": "Bind Ad Account to VCC Card", "key_1821": "Binding Successful", "key_1822": "After Binding Successfully, If Account Has <PERSON>ken, Immediately Fetch Facebook Data and Save to Card", "key_1823": "Binding Successful and Facebook Data Fetched", "key_1824": "Binding Successful, Facebook Data Fetch Failed", "key_1825": "Binding Successful, But Data Save Failed", "key_1826": "Binding Successful, Fetching Facebook Data", "key_1827": "Binding Submission Start", "key_1828": "Binding Submission End", "key_1829": "Binding Time", "key_1830": "Binding Count", "key_1831": "Binding Status", "key_1832": "Bound Ad Account ID Array", "key_1833": "Bound Ad Account Has No Valid Facebook Access Token, Please Go to Ad Account Management Page for Authorization First", "key_1834": "Bind Account", "key_1835": "Bind Account <PERSON><PERSON>", "key_1836": "Bind Account (Current Selection Will Completely Replace Bound Account)", "key_1837": "Bind Account (Replacement Mode)", "key_1838": "Bind Account", "key_1839": "Number of Bound Accounts", "key_1840": "Handle Drag End", "key_1841": "End Time", "key_1842": "End Time Must Be After Start Time", "key_1843": "Count <PERSON>", "key_1844": "Statistics", "key_1845": "Stats Card", "key_1846": "Stats Card Data", "key_1847": "Count Sync Results", "key_1848": "Statistical Data", "key_1849": "Statistical Overview", "key_1850": "Inherited from Framer Motion div Element Properties", "key_1851": "Myanmar", "key_1852": "Encoding", "key_1853": "Edit", "key_1854": "Edit Audience", "key_1855": "Edit Ad", "key_1856": "Edit Campaign", "key_1857": "Edit Ad Set", "key_1858": "Edit Successful", "key_1859": "Edit Promotion", "key_1860": "<PERSON><PERSON> Latest Details When Editing", "key_1861": "Edit Existing Project", "key_1862": "Edit User", "key_1863": "Edit Tenant", "key_1864": "Edit Group", "key_1865": "Edit Landing Page", "key_1866": "Edit Role", "key_1867": "Edit Route", "key_1868": "Edit Project", "key_1869": "Missing Required Fields", "key_1870": "Missing Required Fields - <PERSON><PERSON> Needs Additional Fields", "key_1871": "Gateway", "key_1872": "Network or Other Exception", "key_1873": "Network Request Error", "key_1874": "Network Connection Error", "key_1875": "Network Connection Issue", "key_1876": "Network Error", "key_1877": "Network Error Response", "key_1878": "Network Error Details", "key_1879": "Romania", "key_1880": "Romanian", "key_1881": "USD", "key_1882": "USA", "key_1883": "Group", "key_1884": "Group List", "key_1885": "Group List Return Interface, Unified Format with Role Return", "key_1886": "Group Name", "key_1887": "Number of Groups", "key_1888": "Group Data Raw Response", "key_1889": "Group Management", "key_1890": "Group Item Interface", "key_1891": "Laos", "key_1892": "Position", "key_1893": "Link Billing Method", "key_1894": "Link Conversion Event", "key_1895": "Contact Added", "key_1896": "Contact Information", "key_1897": "Aggregated Data", "key_1898": "Aggregated Data Fetch Successful", "key_1899": "Kenya", "key_1900": "Background Image", "key_1901": "Matched Binding", "key_1902": "Auto Optimization", "key_1903": "Auto Optimization Management", "key_1904": "Automated Tasks", "key_1905": "Auto Echo Landing Page Address", "key_1906": "Auto Review", "key_1907": "Auto Card Opening", "key_1908": "Auto Card Opening Failed", "key_1909": "Auto Concatenate Landing Page Address", "key_1910": "Auto Inject platform Filter (Expandable If Needed)", "key_1911": "Automatically Refine Target Audience to Improve Ad Relevance", "key_1912": "Auto Rule Related Constants", "key_1913": "Auto Rule Type", "key_1914": "Automatically Adjust Ad Budget Allocation to Improve", "key_1915": "Custom", "key_1916": "Custom Hook for Getting Scroll Progress", "key_1917": "Custom Download Filename", "key_1918": "Custom Event", "key_1919": "Custom Animation Parameters", "key_1920": "Custom Audience Options", "key_1921": "Custom Time Points (Daily)", "key_1922": "Custom Debounce Search Function", "key_1923": "Custom Optimization", "key_1924": "Save Budget", "key_1925": "Finland", "key_1926": "Finnish", "key_1927": "Spend", "key_1928": "Sudan", "key_1929": "UK", "key_1930": "English (US)", "key_1931": "English (UK)", "key_1932": "Netherlands", "key_1933": "Dutch", "key_1934": "Mozambique", "key_1935": "Get", "key_1936": "Failed to Get Facebook Ad Account List", "key_1937": "Failed to Get Facebook Data", "key_1938": "Get VCC Card List", "key_1939": "Get Primary Domain (Simple Handling)", "key_1940": "Get Transaction Statistics", "key_1941": "Get Transaction Statistics - For Frontend", "key_1942": "Got User Info", "key_1943": "Get Single VCC Card Details", "key_1944": "Get Single Ad Account's Complete Data", "key_1945": "Get Single User", "key_1946": "Get Single Group", "key_1947": "Get Single Role", "key_1948": "Get Audience List", "key_1949": "Get Only Active Groups List", "key_1950": "Get Enabled Roles List", "key_1951": "Get Aggregated Data for Multiple Ad Accounts", "key_1952": "Failed to Get Ad Performance Analysis Data", "key_1953": "Get Pixel List Under Campaign's Ad Account", "key_1954": "Get Campaign List", "key_1955": "Get Ad Account", "key_1956": "Get Ad Account Insights Data", "key_1957": "Get Ad Account Info", "key_1958": "Failed to Get Ad Account List", "key_1959": "Get Ad Account List (Including", "key_1960": "Get Ad Account Basic Info", "key_1961": "Failed to Get Ad Delivery Analysis Data", "key_1962": "Get Current Matched Route", "key_1963": "Failed to Get Current User Info", "key_1964": "Get Current Logged-in User Info", "key_1965": "Get Current Tenant", "key_1966": "Get Current Authenticated User Info", "key_1967": "Get Currently Selected Creative Data", "key_1968": "Get All Matched Routes", "key_1969": "Get All Files and Their Paths", "key_1970": "Get All Role List (Non-paginated)", "key_1971": "Get All Page Components", "key_1972": "Get Flattened Route Menu", "key_1973": "Failed to Get Authorization Link", "key_1974": "Failed to Get Data", "key_1975": "Get File Content", "key_1976": "Get File Format", "key_1977": "Get File Thumbnail", "key_1978": "Method to Get Local Token (Can Adjust Based on Actual Project)", "key_1979": "Failed to Get Channel Analysis Data", "key_1980": "Failed to Get User Info", "key_1981": "Failed to Get User Info, Please Retry", "key_1982": "Get User List", "key_1983": "Failed to Get User List", "key_1984": "Get User's Permitted Ad Account List (Via Backend Call)", "key_1985": "Failed to Get User Details", "key_1986": "Get User Detailed Info (Including Route Data)", "key_1987": "Get Bound Ad Account OAuth Info", "key_1988": "Get Bound Ad Account Data, Including Token Info", "key_1989": "Get Group List", "key_1990": "Failed to Get Group List", "key_1991": "Group List Fetch Exception", "key_1992": "Group List Fetch Successful", "key_1993": "Get Group Statistics", "key_1994": "Get Aggregated Facebook Data", "key_1995": "Failed to Get Aggregated Facebook Data", "key_1996": "Failed to Get Aggregated Data", "key_1997": "Aggregated Data Fetch Successful", "key_1998": "Convenience Method to Get Aggregated Data", "key_1999": "Get Role List", "key_2000": "Failed to Get Role List", "key_2001": "Failed to Get Role Details", "key_2002": "Get Account", "key_2003": "Failed to Get Account Info", "key_2004": "Get Sufficiently Large Number of Roles", "key_2005": "Get Route Component Instance", "key_2006": "<PERSON><PERSON>", "key_2007": "Philippines", "key_2008": "Filipino", "key_2009": "<PERSON>", "key_2010": "Landing Page URL Library", "key_2011": "Landing Page URL Added Successfully", "key_2012": "Landing Page Name", "key_2013": "Landing Page URL", "key_2014": "Landing Page Domain", "key_2015": "Landing Page Management", "key_2016": "Portugal", "key_2017": "Portuguese", "key_2018": "Mongolia", "key_2019": "Although Facebook Data Fetch Failed, Account Binding Was Successful. You Can Manually Click \"Sync Facebook Data\" But<PERSON> Later to <PERSON><PERSON>, or Re-authorize at Ad Account Management Page", "key_2020": "Industry", "key_2021": "Behavior", "key_2022": "Behavior Options", "key_2023": "Zero-Padding Function", "key_2024": "Form", "key_2025": "Form Field Actual Value", "key_2026": "Form Submission Content", "key_2027": "Form Submission Successful", "key_2028": "Form Data", "key_2029": "Form Lead", "key_2030": "Form Validation Failed", "key_2031": "Form Validation Failed, Please Check Input", "key_2032": "Form Validation Successful", "key_2033": "Form Validation or Submission Failed", "key_2034": "Table Columns", "key_2035": "Table Columns - Risk Control Rules", "key_2036": "Table Columns - Risk Alerts", "key_2037": "Table Column Definition", "key_2038": "Table Column Configuration", "key_2039": "Table Column Configuration - Designed Based on Screenshot", "key_2040": "Rejected", "key_2041": "Spain", "key_2042": "Spanish", "key_2043": "Override", "key_2044": "Reach", "key_2045": "Observe Entire Document Changes", "key_2046": "Rule", "key_2047": "Rule Information", "key_2048": "Rule Modified Successfully", "key_2049": "Rule Name", "key_2050": "Rule Name Cannot Be Empty", "key_2051": "Rule Deleted", "key_2052": "Rule Application Object", "key_2053": "Rule Description", "key_2054": "Rule Evaluated Every 30 Minutes (Suitable for Very High Frequency Monitoring)", "key_2055": "Rule Evaluated Daily (Facebook Recommended, Suitable for Most Scenarios)", "key_2056": "Rule Evaluated Hourly (Suitable for Scenarios Requiring High Frequency Monitoring)", "key_2057": "Rule Added Successfully", "key_2058": "Rule Management", "key_2059": "Rule Management Related", "key_2060": "Rule Type", "key_2061": "Viewport Configuration Description", "key_2062": "Video", "key_2063": "Video Creative", "key_2064": "Video Playback", "key_2065": "Video Playback Complete", "key_2066": "Video Material", "key_2067": "Role", "key_2068": "Role Information", "key_2069": "Role List Response", "key_2070": "Role Created Successfully", "key_2071": "Role Deleted Successfully", "key_2072": "Role Name", "key_2073": "Role Data", "key_2074": "Role Data Raw Response", "key_2075": "Role Data Format Error", "key_2076": "Role Data Structure", "key_2077": "Role Updated Successfully", "key_2078": "Role Update Data", "key_2079": "Role Query Parameters", "key_2080": "Role Status Updated Successfully", "key_2081": "Role Status Enum", "key_2082": "Role Management", "key_2083": "When Role Selection Changes, Determine If System Admin and Tenant Admin", "key_2084": "Role Item", "key_2085": "Resolution Suggestions", "key_2086": "Resolution Steps", "key_2087": "Resolve Cache Key Conflict Issue", "key_2088": "Parse", "key_2089": "Failed to Parse ZIP", "key_2090": "Parse Insights Data", "key_2091": "Parsing Failed", "key_2092": "Parsing Failed, Please <PERSON>try", "key_2093": "Parsing Successful", "key_2094": "Failed to Parse User Info", "key_2095": "Parsing Result Itself is String", "key_2096": "Parse Account", "key_2097": "Parse Parameter Names in Route Path", "key_2098": "Trigger Condition", "key_2099": "Warning", "key_2100": "Warning: The Following Bound Account IDs Do Not Exist in Current Ad Account List", "key_2101": "Warning: All Ad Accounts Have No Valid Found", "key_2102": "Calculate Average CTR and CVR - Use Correct Formula", "key_2103": "Calculate New Balance", "key_2104": "Calculate Time Range", "key_2105": "Calculate Statistical Data", "key_2106": "Calculate Aggregated Data", "key_2107": "Billing Method", "key_2108": "Billing Method Linkage Logic", "key_2109": "Authentication Link", "key_2110": "Authentication: <PERSON><PERSON><PERSON> <PERSON> (Requires Login)", "key_2111": "Device Platform", "key_2112": "Device Platform Options (Officially Supported by Facebook)", "key_2113": "Settings", "key_2114": "Set a Large Order Value to Ensure Not Displayed", "key_2115": "Set Pagination Parameters to Ensure Fetching Enough Data", "key_2116": "Set Transform Origin to Left", "key_2117": "Settings Saved", "key_2118": "Set Existing Permissions", "key_2119": "Set New Timer", "key_2120": "Set Time Range - Last 30 Days", "key_2121": "Set Form Value for Bound Account", "key_2122": "Set Edit Form Initial Values", "key_2123": "Set Background Color", "key_2124": "Set Request Headers", "key_2125": "Set Progress Bar Color, Prioritize Passed Color, Otherwise Use Theme Color", "key_2126": "Set Height", "key_2127": "Designer", "key_2128": "Access Token Invalid or Expired", "key_2129": "Access Token Invalid, Please Re-authorize Ad Account", "key_2130": "Access Token (Backup Field)", "key_2131": "Access Restriction Rules", "key_2132": "Certificate", "key_2133": "Certificate Will Be Automatically Issued. Accessing This Domain Will Automatically Redirect to Main Site After Taking Effect", "key_2134": "This VCC Card is Not Bound to Any Facebook Ad Account", "key_2135": "This Component Displays Page Scroll Progress, Creating a Smooth Progress Bar Animation Effect", "key_2136": "Details", "key_2137": "View Details", "key_2138": "Detailed Token Debug Info", "key_2139": "Detailed Ad Account and Token Analysis", "key_2140": "Detailed Error Handling", "key_2141": "Detailed Account Status", "key_2142": "Language", "key_2143": "Description", "key_2144": "Please Upload One Excel and One ZIP File", "key_2145": "Please Upload zip Format, Image/Video Names Must Match Template id", "key_2146": "Please Upload App Icon", "key_2147": "Please Upload Excel File", "key_2148": "Please Upload Excel File Containing ip, status Fields", "key_2149": "Please Upload Image File", "key_2150": "Please Upload Promotional Images, At Least 3", "key_2151": "Please Upload Background Image", "key_2152": "Please Parse First", "key_2153": "Please Go to Ad Account Management Page for Facebook Authorization, Then Return to Sync Data Manually", "key_2154": "Please Fill Name and Upload File", "key_2155": "Please Set Your Domain", "key_2156": "Please Check", "key_2157": "Request Body May Have Issue, Here Are All Formats We Tried", "key_2158": "Request Parameters", "key_2159": "Request Interception", "key_2160": "Request Interceptor: Automatically Attach", "key_2161": "Do Something When Request Errors", "key_2162": "Abnormal Request Frequency", "key_2163": "Please Add Tag", "key_2164": "Please Contact Administrator", "key_2165": "Please Add At Least One Time Point", "key_2166": "Please Add At Least One Condition", "key_2167": "Please Enter", "key_2168": "Please Enter App Store Link", "key_2169": "Please Enter 11-digit Phone Number", "key_2170": "Please Enter App Description", "key_2171": "Please Enter Balance", "key_2172": "Please Enter Recharge Amount", "key_2173": "Please Enter Company", "key_2174": "Please Enter Keywords to Search Interests", "key_2175": "Please Enter Keywords to Search Behaviors", "key_2176": "Please Enter Other Relevant Info", "key_2177": "Please Enter Group Name", "key_2178": "Please Enter Action", "key_2179": "Please Enter Card Number", "key_2180": "Please Enter Affected User Count", "key_2181": "Please Enter Name", "key_2182": "Please Enter Alert <PERSON>", "key_2183": "Please Enter Alert Source", "key_2184": "Please Enter Alert <PERSON>", "key_2185": "Please Enter Team Name", "key_2186": "Please Enter Team Description", "key_2187": "Please Enter Country Code", "key_2188": "Please Enter Domain List", "key_2189": "Please Enter Domains, One Per Line", "key_2190": "Please Enter Remarks", "key_2191": "Please Enter Remark Info", "key_2192": "Please Enter Full Name", "key_2193": "Please Enter Password", "key_2194": "Please Enter Ad Name", "key_2195": "Please Enter Campaign Name", "key_2196": "Please Enter Ad Set Name", "key_2197": "Please Enter Belonging Group", "key_2198": "Please Enter Cardholder", "key_2199": "Please Enter Sort Order", "key_2200": "Please Enter Sort Value", "key_2201": "Please Enter Promotional Copy", "key_2202": "Please Enter Description", "key_2203": "Please Enter Copy Content", "key_2204": "Please Enter Daily Budget", "key_2205": "Please Enter Time Range", "key_2206": "Please Enter Star Rating, 0-5, Step 0.5", "key_2207": "Please Enter Valid Recharge Amount", "key_2208": "Please Enter Valid Number", "key_2209": "Please Enter <PERSON><PERSON> Email Address", "key_2210": "Please Enter <PERSON><PERSON>", "key_2211": "Please Enter Condition", "key_2212": "Please Enter Tag", "key_2213": "Please Enter Channel", "key_2214": "Please Enter Channel Code", "key_2215": "Please Enter Status", "key_2216": "Please Enter Username", "key_2217": "Please Enter User Account", "key_2218": "Please Enter Phone", "key_2219": "Please Enter Related IP Address", "key_2220": "Please Enter Tenant Name", "key_2221": "Please Enter Bid Amount", "key_2222": "Please Enter Material Name", "key_2223": "Please Enter Component Path, e.g.", "key_2224": "Please Enter Combination Name", "key_2225": "Please Enter Group Name", "key_2226": "Please Enter Group Description", "key_2227": "Please Enter Position", "key_2228": "Please Enter Contact Information", "key_2229": "Please Enter At Least One Email", "key_2230": "Please Enter Landing Page", "key_2231": "Please Enter Domain to Bind", "key_2232": "Please Enter Rule Name", "key_2233": "Please Enter Rule Description", "key_2234": "Please Enter Role Name", "key_2235": "Please Enter Role Description", "key_2236": "Please Enter Route Name", "key_2237": "Please Enter Route Path", "key_2238": "Please Enter Route Path, e.g.", "key_2239": "Please Enter Email", "key_2240": "Please Enter Postal Code", "key_2241": "Please Enter Department", "key_2242": "Please Enter Project Name", "key_2243": "Please Enter Warning Threshold", "key_2244": "Please Select", "key_2245": "Please Select Excel File", "key_2246": "Please Select Facebook Page", "key_2247": "Please Select Facebook Ad Account", "key_2248": "Please Select Facebook Account", "key_2249": "Please Select Optimization Goal", "key_2250": "Please Select Action Type", "key_2251": "Please Select Audience", "key_2252": "Please Select Partnership Type", "key_2253": "Please Select Alert Type", "key_2254": "Please Select Team Type", "key_2255": "Please Select Country", "key_2256": "Please Select Icon", "key_2257": "Please Select Domain with Issued SSL", "key_2258": "Please Select Ad Objective", "key_2259": "Please Select Campaign", "key_2260": "Please Select Ad Set", "key_2261": "Please Select Application Object", "key_2262": "Please Select Start Time", "key_2263": "Please Select Gender", "key_2264": "Please Select or Create", "key_2265": "Please Select or Enter", "key_2266": "Please Select or Enter App Name or", "key_2267": "Please Select Belonging Group", "key_2268": "Please Select Delivery Account", "key_2269": "Please Select Promotion Platform", "key_2270": "Please Select Operating System", "key_2271": "Please Select Time", "key_2272": "Please Select Parent Route", "key_2273": "Please Select Status", "key_2274": "Please Select Tenant", "key_2275": "Please Select Material", "key_2276": "Please Select At Least One Valid Ad Account", "key_2277": "Please Select Landing Page", "key_2278": "Please Select Landing Page Domain", "key_2279": "Please Select Industry", "key_2280": "Please Select Ad Account to Bind", "key_2281": "Please Select Account to Bind", "key_2282": "Please Select Rule", "key_2283": "Please Select Rule Application Object", "key_2284": "Please Select Rule Type", "key_2285": "Please Select Role", "key_2286": "Please Select Billing Method", "key_2287": "Please Select Device Platform", "key_2288": "Please Select Language", "key_2289": "Please Select Responsible Team", "key_2290": "Please Select Account, <PERSON>, <PERSON>", "key_2291": "Please Select Route Type", "key_2292": "Please Select Conversion Event", "key_2293": "Please Select Expiration Year", "key_2294": "Please Select Expiration Month", "key_2295": "Please Select Project Time Range", "key_2296": "Please Select Project Type", "key_2297": "Please Select Risk Level", "key_2298": "Under Investigation", "key_2299": "Call GraphQL Registration Interface", "key_2300": "Call Backend API to Batch Update VCC Card Facebook Data\n  //     console.log('Data to be batch updated to backend", "key_2301": "Call Backend API to Batch Fetch VCC Facebook Data", "key_2302": "Call Backend API to Update VCC Card Facebook Data and Update Local State", "key_2303": "Call Backend API to Get Facebook Data", "key_2304": "Failed to Call Backend Facebook API", "key_2305": "Failed to Call Backend Facebook Batch API", "key_2306": "Call Backend Batch Sync", "key_2307": "Call Successful", "key_2308": "Call Interface", "key_2309": "Call Add Interface", "key_2310": "Call Login Interface to Get", "key_2311": "Call Frequency Too High", "key_2312": "Call Frequency Too High, Please Try Again Later", "key_2313": "Debug name Field", "key_2314": "Debug Info", "key_2315": "Debug Info End", "key_2316": "Debug Output\n    console.log('Current logged-in user", "key_2317": "Debug: Print All Available Component Paths", "key_2318": "Benin", "key_2319": "Responsible Team", "key_2320": "Account", "key_2321": "Account Password", "key_2322": "Number of Accounts", "key_2323": "Account", "key_2324": "Account List", "key_2325": "Account Name", "key_2326": "Account Security", "key_2327": "Abnormal Account <PERSON>gin Attempts", "key_2328": "Number of Accounts", "key_2329": "Account <PERSON><PERSON><PERSON> Length", "key_2330": "Insufficient Account Permissions", "key_2331": "Account Status", "key_2332": "Account Status Details", "key_2333": "Account Management", "key_2334": "Account Details", "key_2335": "Account: <PERSON><PERSON><PERSON>", "key_2336": "Purchase", "key_2337": "Zambia", "key_2338": "Equatorial Guinea", "key_2339": "<PERSON>", "key_2340": "Super Administrator", "key_2341": "Vietnam", "key_2342": "Vietnamese", "key_2343": "Trend Analysis", "key_2344": "Trend Chart/Distribution Chart", "key_2345": "Route", "key_2346": "Route Name", "key_2347": "Route Data Loaded, Preparing to Redirect", "key_2348": "Route Permission", "key_2349": "Route Management", "key_2350": "Route Type", "key_2351": "Route Path", "key_2352": "Redirect to Login Page (Hash Route)", "key_2353": "Redirect to Homepage", "key_2354": "<PERSON><PERSON>", "key_2355": "Skip Current Edited Node and Its Children", "key_2356": "Skip Non-Menu Type Routes", "key_2357": "Conversion", "key_2358": "Conversion Event", "key_2359": "Conversion Rate (CVR)", "key_2360": "Conversion Rate - Normalized to 0-10", "key_2361": "Conversion Rate Analysis", "key_2362": "Conversions", "key_2363": "Conversions - Normalized to 0-10", "key_2364": "Convert to Number", "key_2365": "Convert to Tree Structure", "key_2366": "Convert to Seconds", "key_2367": "Convert to Unified Format", "key_2368": "Convert Single Object Field", "key_2369": "Convert Account Status", "key_2370": "Carousel/Other", "key_2371": "Carousel Creative", "key_2372": "Carousel Material", "key_2373": "Helper Functions", "key_2374": "Automatically Calculate After Entering Recharge Amount", "key_2375": "Enter Card Number", "key_2376": "Enter Merchant Name", "key_2377": "Enter Phone Number", "key_2378": "Enter New Pixel Name", "key_2379": "Enter Email Address", "key_2380": "<PERSON><PERSON> <PERSON><PERSON> Amount", "key_2381": "Expiration Year", "key_2382": "Expiration Time", "key_2383": "Expiration Month", "key_2384": "Filter undefined, Convert Empty String to", "key_2385": "Filter Dropdown Roles (Compatible with Multiple Formats)", "key_2386": "Filtered", "key_2387": "Valid After Filtering", "key_2388": "Valid Echo After Filtering", "key_2389": "Valid Routes After Filtering", "key_2390": "Roles After Filtering", "key_2391": "Filter Out null, undefined, etc. Invalid Values, Ensure Only Valid Strings Passed", "key_2392": "Filter Out null Values, Ensure Only Valid Accounts Echoed", "key_2393": "Filter Out Failed Uploads", "key_2394": "Filter Out Confirm Password Field", "key_2395": "Filter Data", "key_2396": "Filter VCC Cards Bound to Ad Account", "key_2397": "Operator", "key_2398": "Running", "key_2399": "Recent Ad Campaigns", "key_2400": "Return a Response Not Handled by Apollo Client, or Throw Error\n      // Here Return an Empty, Successful Response to Avoid Apollo Client Error", "key_2401": "Return Object Containing Scroll Progress Value and Element Reference", "key_2402": "Return Team List", "key_2403": "Return Current Route Meta Info", "key_2404": "Return Flattened Menu Routes", "key_2405": "Return Format", "key_2406": "Return to Login Page", "key_2407": "Returned Aggregated Data", "key_2408": "Return Type Definition, Contains Scroll Progress Value and Element Reference", "key_2409": "Return Result", "key_2410": "Return Detailed Data", "key_2411": "Return Error Status Account Data", "key_2412": "This is a Universal Animation Container Component Based on Framer Motion, Used to Manage Child Component Animation States and Transitions", "key_2413": "This is Normal, Browser Blocks Cross-Origin Requests", "key_2414": "This is Browser Security Mechanism, Normal", "key_2415": "Here userId Can Be Obtained from Actual Logged-in User, Placeholder \"test-user\" Used Here", "key_2416": "Here You Can Get Routes from Your State Management or API", "key_2417": "Here Can Add Time Filter as Needed", "key_2418": "Here Can Use Global message/notification Component Popup", "key_2419": "Here Will Display Auto Optimization Management Functions, Including Auto Bidding, Material Optimization, Performance Tracking, etc.", "key_2420": "Here Should Call API to Save Settings", "key_2421": "After Specifying initial, animate, and exit Property Names Here, Child Components Don't Need to Repeat", "key_2422": "This is the Auto Optimization Management Function Page", "key_2423": "Need to Select Card ID, Temporarily Use First Card", "key_2424": "Animation State After Entering Viewport", "key_2425": "Progress", "key_2426": "Progress Bar Style Configuration", "key_2427": "Progress Bar Color, Optional", "key_2428": "Progress Bar Color, Default Uses Theme Color", "key_2429": "Progress Bar Height", "key_2430": "Progress Bar Height, Default 4", "key_2431": "In Progress", "key_2432": "Connection Normal", "key_2433": "Exit State", "key_2434": "Refund", "key_2435": "Applicable To", "key_2436": "Compatible with Chrome, Safari, and", "key_2437": "Adapt to New Data Structure", "key_2438": "Adaptation Status", "key_2439": "Selected Card Records", "key_2440": "Selected Permissions", "key_2441": "Selected Permissions Change Raw Data", "key_2442": "Selected Permissions Additional Info", "key_2443": "Selected Account Details", "key_2444": "Selected Account Facebook Token Status Analysis", "key_2445": "Total Selected Accounts", "key_2446": "Select Excel File", "key_2447": "Select Facebook Account", "key_2448": "Select Year", "key_2449": "Select File", "key_2450": "Select Text", "key_2451": "Select Time Range", "key_2452": "Select Month", "key_2453": "Select Status", "key_2454": "Select Type", "key_2455": "Select Material", "key_2456": "Select Landing Page", "key_2457": "Option Library (Dynamic Interface)", "key_2458": "General", "key_2459": "General DELETE Request", "key_2460": "General GET Request", "key_2461": "General POST Request", "key_2462": "General PUT Request", "key_2463": "General <PERSON> (Backup Field)", "key_2464": "General Audience List Hooks, Support platform Parameter", "key_2465": "General Custom Upload", "key_2466": "General Request Utility, Supports REST/GraphQL, Automatically Handles baseURL, token, Error Prompting, etc.", "key_2467": "Notification", "key_2468": "Notification Phone Number", "key_2469": "Notification Method", "key_2470": "Notification Email", "key_2471": "Email", "key_2472": "Email Format Incorrect", "key_2473": "Postal Code", "key_2474": "<PERSON>", "key_2475": "Department", "key_2476": "Configuration", "key_2477": "Configure Drag Sensor", "key_2478": "Configure Correct GraphQL Endpoint", "key_2479": "Redirect Handling Link", "key_2480": "Reload User List\n    } catch (error) {\n      console.error('Operate user failed", "key_2481": "Refetch Data", "key_2482": "Re-authorize Facebook OAuth", "key_2483": "Reset", "key_2484": "Reset to First Page", "key_2485": "Reset to First Page\n    loadUsers(1", "key_2486": "Reset Selected Accounts", "key_2487": "Finance", "key_2488": "Amount", "key_2489": "Hook", "key_2490": "<PERSON><PERSON>", "key_2491": "Link", "key_2492": "Link\n// Custom fetch Function Handles Redirect", "key_2493": "Link, Use Custom fetch Function", "key_2494": "Sales Conversion", "key_2495": "Lock Account", "key_2496": "Error", "key_2497": "Error Message", "key_2498": "Error Value", "key_2499": "<PERSON><PERSON><PERSON>", "key_2500": "Error <PERSON>", "key_2501": "Error Field", "key_2502": "Error O<PERSON>", "key_2503": "Error Object Type", "key_2504": "Error Object Key", "key_2505": "<PERSON><PERSON><PERSON>", "key_2506": "Error Message", "key_2507": "Error Status", "key_2508": "Error Rate", "key_2509": "Error Type", "key_2510": "<PERSON><PERSON><PERSON>", "key_2511": "Error Detailed Analysis", "key_2512": "Length", "key_2513": "Long Term", "key_2514": "Issue", "key_2515": "Anti-Ban Domain Management", "key_2516": "Prevent", "key_2517": "Prevent Passing String \"[]\"", "key_2518": "Damping Coefficient", "key_2519": "Azerbaijan", "key_2520": "Afghanistan", "key_2521": "Algeria", "key_2522": "Arabic", "key_2523": "Oman", "key_2524": "Argentina", "key_2525": "UAE", "key_2526": "<PERSON>", "key_2527": "Restriction", "key_2528": "Restrict API Request Frequency", "key_2529": "Restrict Pagination Options", "key_2530": "Restrict Maximum Page Size to 50", "key_2531": "Limit Count", "key_2532": "Restriction Management", "key_2533": "Restrict Request Frequency", "key_2534": "Radar Chart Data - Channel Effectiveness Comparison\n  // Note: Data Normalized for Reasonable Display on Radar Chart", "key_2535": "Need to Set in .env or Configuration File", "key_2536": "Permissions Required (e.g.,", "key_2537": "Inactive", "key_2538": "South Korea", "key_2539": "Korean", "key_2540": "Page", "key_2541": "Page View", "key_2542": "Page Like", "key_2543": "Page Type Route Required, Menu Type Optional", "key_2544": "Page, Per Page", "key_2545": "Top Background Area", "key_2546": "Project", "key_2547": "Project Name", "key_2548": "Project Created", "key_2549": "Project Deleted", "key_2550": "Project Updated", "key_2551": "Number of Projects", "key_2552": "Project Time Range", "key_2553": "Project Type", "key_2554": "Pre-Approved", "key_2555": "Reserve More Dimensions for Display", "key_2556": "Budget", "key_2557": "Budget Optimization", "key_2558": "Budget Optimization Completed, Saved You Some Budget", "key_2559": "Budget Usage", "key_2560": "Budget Usage Status", "key_2561": "Budget Auto Optimization", "key_2562": "Budget Planning", "key_2563": "Budget Amount", "key_2564": "Preview", "key_2565": "Preview Image", "key_2566": "Warning Settings", "key_2567": "Warning Settings Type", "key_2568": "Warning Threshold", "key_2569": "Frequency", "key_2570": "Frequency Limit", "key_2571": "Risk Control Dashboard", "key_2572": "Risk Control Sync Response", "key_2573": "Risk Control Review", "key_2574": "Risk Control Export Response", "key_2575": "Risk Control Overview", "key_2576": "Risk Control Type", "key_2577": "Risk Control Rules", "key_2578": "Style Audience Form Type", "key_2579": "Risk", "key_2580": "Risk Alert", "key_2581": "Risk Control", "key_2582": "Risk Data Sync Successful", "key_2583": "Risk Data Export Failed", "key_2584": "Risk Data Export Successful", "key_2585": "Risk Status Map", "key_2586": "Risk Level", "key_2587": "Risk Level Map", "key_2588": "Risk Type", "key_2589": "Risk Level", "key_2590": "Load Data on Initial Load and Pagination Change", "key_2591": "Macedonian", "key_2592": "Malawi", "key_2593": "Malaysia", "key_2594": "Malay", "key_2595": "Madagascar", "key_2596": "Mali", "key_2597": "Validate IP Address", "key_2598": "Validate VCC Card ID Format (Must Be", "key_2599": "Return Directly If Validation Fails\n      }\n\n      console.log('Form raw value", "key_2600": "Validate and Log", "key_2601": "Validate and Filter routeIds, Ensure Only Valid Included", "key_2602": "Validate Ad Account ID Format Correctness", "key_2603": "Validate If Valid UUID v4 Format", "key_2604": "Validate User's Role in Ad Account", "key_2605": "Validate If Result is Valid Number", "key_2606": "Validate Form Fields Correctly Set", "key_2607": "Validate Parsed", "key_2608": "Validate If Settings Successful", "key_2609": "Validate Access Token", "key_2610": "Validate Access Token Validity (Via Backend Validation)", "key_2611": "Validate Returned Result Contains Route", "key_2612": "Error Validation Details", "key_2613": "Senior Ad Specialist", "key_2614": "High Conversion", "key_2615": "High Risk", "key_2616": "Lebanon", "key_2617": "De<PERSON><PERSON> from localStorage", "key_2618": "Default Group", "key_2619": "Default Warning Settings", "key_2620": "Although Facebook Data Fetch Failed, Account Binding Was Successful. You Can Manually Click t('pages.key_466') But<PERSON> Later to <PERSON><PERSON>, or Re-authorize at Ad Account Management Page", "vcc_form_channel": "Channel", "vcc_form_countryCode": "Country Code", "vcc_form_cardHolder": "Cardholder", "vcc_form_balance": "Balance", "vcc_form_cardNumber": "Card Number", "vcc_form_expiryMonth": "Expiry Month", "vcc_form_expiryYear": "Expiry Year", "vcc_form_cvv": "CVV", "vcc_form_zipCode": "ZIP Code", "vcc_form_usedCount": "Times Used", "vcc_form_bindCount": "Bind Count", "vcc_form_status": "Status", "vcc_form_remark": "Remarks", "vcc_form_remainingBindCount": "Remaining Bind Count", "vcc_form_currentBalance": "Current Balance", "vcc_form_rechargeAmount": "Recharge Amount (USD)", "vcc_form_expectedBalance": "Expected Balance", "key_2621": "Create rules to automatically update settings for selected campaigns, ad groups, or ads.", "key_2622": "Your rules will apply to ads that are running when the rule executes.", "key_2623": "Campaign A", "key_2624": "Campaign B", "key_2625": "Clicks (CLICKS)", "key_2626": "Impressions (IMPRESSIONS)", "key_2627": "Reach (REACH)", "key_2628": "Frequency (FREQUENCY)", "key_2629": "CPM (Cost Per Mille)", "key_2630": "CTR (Click-Through Rate)", "key_2631": "Spend (SPEND)", "key_2632": "More settings (customizable)", "key_2633": "Custom Time Points (Daily)", "key_2634": "Pause Ad (PAUSE)", "key_2635": "Enable Ad (UNPAUSE)", "key_2636": "Notification (NOTIFICATION)", "key_2637": "Change Budget (CHANGE_BUDGET)", "key_2638": "Change Bid (CHANGE_BID)", "key_2639": "Rule evaluated daily (Facebook recommended, suitable for most scenarios).", "key_2640": "Rule evaluated hourly (for high-frequency monitoring scenarios).", "key_2641": "Rule evaluated every 30 minutes (for ultra-high-frequency monitoring).", "key_2642": "All refreshed, {success} succeeded, {fail} failed", "key_2643": "Refresh failed: ", "key_2665": "Excel is missing required fields: ip, status", "key_2666": "Please parse the Excel file first", "key_2667": "IP Restriction Management", "key_2668": "Search IP", "key_2669": "Please enter IP"}}