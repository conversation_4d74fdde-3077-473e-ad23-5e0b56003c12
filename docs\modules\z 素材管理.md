# 素材管理模块

## 重要说明：两种素材管理实现

本系统包含两种不同的素材管理实现：

1. **用户资料页面中的素材管理 Tab** - `/pages/management/user/profile/index.tsx`
2. **独立的素材管理页面** - `/pages/management/material/index.tsx`

## 前端

### 用户资料页面中的素材管理（主要实现）

- **实际路径**：slash-admin/src/pages/management/user/profile/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，包含多个 Tab 页）
  - MaterialTab（素材列表管理）
  - CopywritingTab（文案管理）
  - LandingPageTab（落地页管理）
  - CreativeComboTab（创意组合管理）
- **主要依赖 API 定义**：@/api/graphql/materialManagement.graphql

### 独立素材管理页面

- **实际路径**：slash-admin/src/pages/management/material/index.tsx
- **主要依赖 API 定义**：@/api/material.graphql.ts

#### materialManagement 相关接口（主要实现）

| API 名称     | 方法     | GraphQL 名/路径             | 参数类型/说明                                                                              | 返回字段                                                                                                                                                          | 说明/示例               |
| ------------ | -------- | --------------------------- | ------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------- |
| 获取素材列表 | query    | materialManagements         | paging: OffsetPaging, filter: materialManagementFilter, sorting: [materialManagementSort!] | id, fileName, tags, group, adCount, activeAdCount, bannedCount, spend, conversion, commission, file, notes, facebookImageId, facebookVedioId, tenantId, updatedAt | 支持分页、搜索、过滤    |
| 新增素材     | mutation | createOneMaterialManagement | input: CreateOneMaterialManagementInput!                                                   | id, tenantId, fileName, tags, notes, file                                                                                                                         | 新建素材，id 为自增数字 |
| 删除素材     | mutation | deleteOneMaterialManagement | input: DeleteOneMaterialManagementInput!                                                   | id                                                                                                                                                                | 删除指定素材            |
| 创意组合创建 | mutation | createOneMateriaCreate      | input: CreateOneMateriaCreateInput!                                                        | id, name, materials, pageLading, slogan, account, pageId, createId                                                                                                | 创建创意组合            |
| 删除创意组合 | mutation | deleteOneMateriaCreate      | input: DeleteOneMateriaCreateInput!                                                        | id                                                                                                                                                                | 删除创意组合            |

#### Material 相关接口（独立页面）

| API 名称     | 方法  | GraphQL 名/路径 | 参数类型/说明                                                          | 返回字段                             | 说明/示例      |
| ------------ | ----- | --------------- | ---------------------------------------------------------------------- | ------------------------------------ | -------------- |
| 获取素材列表 | query | materials       | filter: MaterialFilter, paging: CursorPaging, sorting: [MaterialSort!] | id, type, name, url, content, status | 支持分页、过滤 |

#### 主要参数结构说明

##### materialManagement 相关参数

- **CreateOneMaterialManagementInput**（用于新增素材）

  ```ts
  {
    materialManagement: {
      fileName: string; // 文件名（必填）
      tags?: string; // 标签
      group?: string; // 所属群组
      notes?: string; // 备注
      file: string; // 文件URL（必填）
      tenantId: string; // 租户ID（必填）
      facebookImageId?: string; // Facebook图片ID
      facebookVedioId?: string; // Facebook视频ID
    }
  }
  ```

- **CreateOneMateriaCreateInput**（用于创建创意组合）
  ```ts
  {
    materiaCreate: {
      name: string; // 组合名称
      materials: number; // 关联素材ID
      pageLading: string; // 落地页
      slogan?: string; // 标语
      account: string; // 账户
      pageId: string; // 页面ID
      createId: string; // 创建者ID
    }
  }
  ```

##### Material 相关参数

- **MaterialFilter**（用于独立页面过滤）
  ```ts
  {
    tenantId?: string; // 租户ID过滤
    type?: string; // 类型过滤
    status?: string; // 状态过滤
  }
  ```

#### 返回字段说明

##### materialManagement 字段

| 字段名          | 类型   | 说明                 |
| --------------- | ------ | -------------------- |
| id              | number | 主键，自增数字       |
| fileName        | string | 文件名               |
| tags            | string | 标签                 |
| group           | string | 所属群组             |
| adCount         | number | 广告数               |
| activeAdCount   | number | 投放中广告数         |
| bannedCount     | number | 封禁广告数           |
| spend           | number | 消耗（decimal 类型） |
| conversion      | number | 转化（decimal 类型） |
| commission      | number | 佣金（decimal 类型） |
| file            | string | 文件 URL             |
| notes           | string | 备注                 |
| facebookImageId | string | Facebook 图片 ID     |
| facebookVedioId | string | Facebook 视频 ID     |
| tenantId        | string | 租户 ID              |
| createdAt       | Date   | 创建时间             |
| updatedAt       | Date   | 更新时间             |

##### Material 字段（独立页面）

| 字段名  | 类型   | 说明                            |
| ------- | ------ | ------------------------------- |
| id      | string | 主键，UUID                      |
| type    | string | 物料类型（image/video/text 等） |
| name    | string | 物料名称                        |
| url     | string | 物料 URL                        |
| content | string | 物料内容                        |
| status  | string | 状态（默认 active）             |

### 前端表单/组件说明

- 支持素材的增删改查，表单字段与上方 MaterialInput 结构一致
- 支持批量 Excel/图片/视频上传，自动校验字段
- 支持素材分组、标签、备注等管理
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）

---

## 后端

- **主要接口**：GraphQL 接口与前端一致，详见上表

### 数据库表结构

#### materialManagement 表（主要实现）

详见 ad_backend/src/modules/material-management/materialManagement.entity.ts

| 字段名          | 类型                     | 说明             | 约束                   |
| --------------- | ------------------------ | ---------------- | ---------------------- |
| id              | int                      | 主键，自增数字   | PRIMARY KEY            |
| fileName        | varchar(255)             | 文件名           | 可空                   |
| tags            | varchar(255)             | 标签             | 可空                   |
| group           | varchar(255)             | 所属群组         | 可空                   |
| facebookImageId | varchar(255)             | Facebook 图片 ID | 可空                   |
| facebookVedioId | varchar(255)             | Facebook 视频 ID | 可空                   |
| adCount         | int                      | 广告数           | 默认 0                 |
| activeAdCount   | int                      | 投放中广告数     | 默认 0                 |
| bannedCount     | int                      | 封禁广告数       | 默认 0                 |
| spend           | decimal                  | 消耗             | 默认 0                 |
| conversion      | decimal                  | 转化             | 默认 0                 |
| commission      | decimal                  | 佣金             | 默认 0                 |
| notes           | text                     | 备注             | 可空                   |
| file            | text                     | 文件 URL         | 可空                   |
| tenantId        | varchar(255)             | 租户 ID          | NOT NULL               |
| createdAt       | timestamp with time zone | 创建时间         | 默认 CURRENT_TIMESTAMP |
| updatedAt       | timestamp with time zone | 更新时间         | 自动更新               |

#### Material 表（独立页面）

详见 ad_backend/src/modules/ad-platform/entities/material.entity.ts

| 字段名  | 类型         | 说明                            | 约束         |
| ------- | ------------ | ------------------------------- | ------------ |
| id      | uuid         | 主键，UUID                      | PRIMARY KEY  |
| type    | varchar(32)  | 物料类型（image/video/text 等） | NOT NULL     |
| name    | varchar(128) | 物料名称                        | NOT NULL     |
| url     | varchar(512) | 物料 URL                        | 可空         |
| content | text         | 物料内容                        | 可空         |
| status  | varchar(32)  | 状态                            | 默认'active' |
| tenant  | 关联关系     | 租户关联（外键到 tenant 表）    | NOT NULL     |
| raw     | jsonb        | 原始数据                        | 可空         |

#### materiaCreate 表（创意组合）

详见 ad_backend/src/modules/material-create/materiaCreate.entity.ts

| 字段名     | 类型                     | 说明           | 约束                   |
| ---------- | ------------------------ | -------------- | ---------------------- |
| id         | int                      | 主键，自增数字 | PRIMARY KEY            |
| name       | varchar(255)             | 组合名称       | NOT NULL               |
| userId     | varchar(255)             | 用户 ID        | NOT NULL               |
| materials  | int                      | 关联素材 ID    | NOT NULL               |
| pageLading | varchar(255)             | 落地页         | NOT NULL               |
| slogan     | varchar(255)             | 标语           | 可空                   |
| account    | varchar(255)             | 账户           | NOT NULL               |
| pageId     | varchar(255)             | 页面 ID        | NOT NULL               |
| createId   | varchar(255)             | 创建者 ID      | NOT NULL               |
| tenantId   | varchar(64)              | 租户 ID        | NOT NULL               |
| createdAt  | timestamp with time zone | 创建时间       | 默认 CURRENT_TIMESTAMP |
| updatedAt  | timestamp with time zone | 更新时间       | 自动更新               |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query materialManagementList / mutation createMaterialManagement(input) / ...
    后端->>数据库: 操作 material_management 表
    数据库-->>后端: 返回素材数据
    后端-->>前端: 返回素材数据
```

---

## 变更历史/注意事项

### materialManagement 相关注意事项
- 素材文件 URL 需唯一，所有操作需校验唯一性
- 支持多类型素材（图片、视频、文案等）
- 上传时会同时上传到 Cloudflare 和 Facebook，获取对应的 ID
- facebookImageId 和 facebookVedioId 用于 Facebook 广告投放
- 删除素材时需校验是否被创意组合等引用
- 支持多租户隔离，所有操作需校验租户权限

### Material 相关注意事项
- 独立的素材管理实现，主要用于通用物料管理
- 支持 image/video/text 等多种物料类型
- 与租户表关联，支持多租户隔离
- raw 字段用于存储原始数据，支持扩展

### 创意组合相关注意事项
- materiaCreate 表用于管理创意组合
- materials 字段关联 materialManagement 表的 id
- 支持落地页、标语、账户等完整的创意信息
- 创建时需校验关联的素材是否存在

### 两种实现的使用场景
- **materialManagement**：主要用于 Facebook 广告素材管理，包含 Facebook 特有字段
- **Material**：用于通用物料管理，支持多平台扩展
- 前端可根据业务需求选择使用对应的实现
