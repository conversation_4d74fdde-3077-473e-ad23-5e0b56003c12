# 素材管理模块

## 重要说明：两种素材管理实现

本系统包含两种不同的素材管理实现：

1. **用户资料页面中的素材管理 Tab** - `/pages/management/user/profile/index.tsx`
2. **独立的素材管理页面** - `/pages/management/material/index.tsx`

## 前端

### 用户资料页面中的素材管理（主要实现）

- **实际路径**：slash-admin/src/pages/management/user/profile/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，包含多个 Tab 页）
  - MaterialTab（素材列表管理）
  - CopywritingTab（文案管理）
  - LandingPageTab（落地页管理）
  - CreativeComboTab（创意组合管理）
- **主要依赖 API 定义**：@/api/graphql/materialManagement.graphql

### 独立素材管理页面

- **实际路径**：slash-admin/src/pages/management/material/index.tsx
- **主要依赖 API 定义**：@/api/material.graphql.ts

### API 调用列表与详细说明

| API 名称     | 方法     | GraphQL 名/路径          | 参数类型/说明           | 返回字段                                      | 说明/示例                  |
| ------------ | -------- | ------------------------ | ----------------------- | --------------------------------------------- | -------------------------- |
| 获取素材列表 | query    | materialManagementList   | filter, paging, sorting | id, file, fileName, tags, group, adCount, ... | 支持分页、搜索、过滤       |
| 新增素材     | mutation | createMaterialManagement | input: MaterialInput    | id, file, fileName, ...                       | 新建素材，id 为 UUID       |
| 删除素材     | mutation | deleteMaterialManagement | input: { id }           | id                                            | 删除指定素材               |
| 批量上传     | mutation | batchUploadMaterial      | files: [File!]          | id, fileName, ...                             | 批量上传 Excel/图片/视频等 |

#### 主要参数结构说明

- **MaterialInput**（用于新增/编辑）

  ```ts
  {
    id?: string; // 素材ID，编辑时必填
    file: string; // 文件URL
    fileName: string; // 文件名
    tags?: string; // 标签
    group?: string; // 所属群组
    notes?: string; // 备注
    ... // 其他业务字段
  }
  ```

- **返回字段说明**  
  | 字段名 | 类型 | 说明 |
  |-------------|-----------|----------------------------|
  | id | string | 主键，UUID |
  | file | string | 文件 URL |
  | fileName | string | 文件名 |
  | tags | string | 标签 |
  | group | string | 所属群组 |
  | adCount | number | 广告数 |
  | activeAdCount | number | 投放中广告数 |
  | bannedCount | number | 封禁广告数 |
  | spend | number | 消耗 |
  | conversion | number | 转化 |
  | commission | number | 佣金 |
  | notes | string | 备注 |
  | updatedAt | Date | 更新时间 |

### 前端表单/组件说明

- 支持素材的增删改查，表单字段与上方 MaterialInput 结构一致
- 支持批量 Excel/图片/视频上传，自动校验字段
- 支持素材分组、标签、备注等管理
- 支持分页、模糊搜索、过滤
- 支持批量删除（如有实现）

---

## 后端

- **主要接口**：GraphQL 接口与前端一致，详见上表
- **数据库表结构**（material_management，需结合实际后端实现）：

| 字段名        | 类型      | 说明               | 约束        |
| ------------- | --------- | ------------------ | ----------- |
| id            | uuid      | 主键，素材唯一标识 | PRIMARY KEY |
| file          | varchar   | 文件 URL           | NOT NULL    |
| fileName      | varchar   | 文件名             | NOT NULL    |
| tags          | varchar   | 标签               | 可空        |
| group         | varchar   | 所属群组           | 可空        |
| adCount       | int       | 广告数             | 可空        |
| activeAdCount | int       | 投放中广告数       | 可空        |
| bannedCount   | int       | 封禁广告数         | 可空        |
| spend         | numeric   | 消耗               | 可空        |
| conversion    | numeric   | 转化               | 可空        |
| commission    | numeric   | 佣金               | 可空        |
| notes         | varchar   | 备注               | 可空        |
| updatedAt     | timestamp | 更新时间           | 自动生成    |
| ...           | ...       | 其他业务字段       | ...         |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query materialManagementList / mutation createMaterialManagement(input) / ...
    后端->>数据库: 操作 material_management 表
    数据库-->>后端: 返回素材数据
    后端-->>前端: 返回素材数据
```

---

## 变更历史/注意事项

- 素材文件 URL 需唯一，所有操作需校验唯一性
- 支持多类型素材（图片、视频、文案等）
- 批量上传需严格校验字段格式
- 删除素材时需校验是否被广告等引用
- 复杂业务字段建议在表结构中用 jsonb 扩展
