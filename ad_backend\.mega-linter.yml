# Configuration file for Mega-Linter
# See all available variables at https://oxsecurity.github.io/megalinter/#configuration and in linters documentation

APPLY_FIXES: 'all'
APPLY_FIXES_EVENT: 'pull_request'
APPLY_FIXES_MODE: 'pull_request'
FILTER_REGEX_EXCLUDE: (lib/example|\.github|\.vscode|pull_request_template|docs/javascripts)
FLAVOR_SUGGESTIONS: false
JAVASCRIPT_DEFAULT_STYLE: prettier
JAVASCRIPT_ES_FILE_NAME: 'LINTER_DEFAULT'
TYPESCRIPT_DEFAULT_STYLE: prettier
TYPESCRIPT_PRETTIER_CONFIG_FILE: .prettierrc
PRINT_ALL_FILES: false
GITHUB_STATUS_REPORTER: true
DISABLE_ERRORS: true
SHOW_ELAPSED_TIME: true
REPORT_OUTPUT_FOLDER: none
VALIDATE_ALL_CODEBASE: true
FILEIO_REPORTER: true
FILEIO_REPORTER_SEND_SUCCESS: true
