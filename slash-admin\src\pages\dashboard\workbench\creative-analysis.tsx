import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { useQuery } from '@apollo/client';
import { GET_CREATIVE_ANALYSIS } from '@/api/creativeAnalysis.graphql';

type CreativeAnalysisProps = {
  startDate: string;
  endDate: string;
};

const CreativeAnalysis = ({ startDate, endDate }: CreativeAnalysisProps) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(GET_CREATIVE_ANALYSIS, {
    variables: { startDate, endDate },
    fetchPolicy: 'network-only',
  });

  // 真实数据（判空处理）
  const typeTrends = data?.creativeAnalysis?.typeTrends || [];
  const sizePerformanceData = data?.creativeAnalysis?.sizePerformance || [];
  const elementPerformanceData = data?.creativeAnalysis?.elementPerformance || [];
  const labels = typeTrends[0]?.data?.length
    ? Array.from({ length: typeTrends[0].data.length }, (_, i) => `${i + 1}月`)
    : ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];

  // hooks必须始终调用
  const trendChartOptions = useChart({
    xaxis: {
      categories: labels,
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
    stroke: {
      curve: 'smooth',
      width: 2,
    },
    colors: ['#4ade80', '#3b82f6', '#f59e0b'],
    legend: {
      position: 'top',
      horizontalAlign: 'right',
    },
  });

  const trendSeries = typeTrends.length > 0
    ? typeTrends.map((trend: any) => ({ name: trend.type, data: trend.data }))
    : [
      { name: t('pages.key_531'), data: [] },
      { name: t('pages.key_2063'), data: [] },
      { name: t('pages.key_2371'), data: [] },
    ];

  const pieChartOptions = useChart({
    labels: sizePerformanceData.map((item: any) => item.name),
    legend: {
      position: 'bottom',
    },
    tooltip: {
      fillSeriesColor: false,
    },
    stroke: {
      show: false,
    },
    colors: ['#4ade80', '#3b82f6', '#f59e0b', '#a855f7', '#ec4899'],
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
        },
      },
    },
  });

  const elementPieChartOptions = useChart({
    labels: elementPerformanceData.map((item: any) => item.name),
    legend: {
      position: 'bottom',
    },
    tooltip: {
      fillSeriesColor: false,
    },
    stroke: {
      show: false,
    },
    colors: ['#4ade80', '#3b82f6', '#f59e0b', '#a855f7', '#ec4899', '#94a3b8'],
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
        },
      },
    },
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">{t('pages.key_267')}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="trend">
          <TabsList className="mb-4">
            <TabsTrigger value="trend">{t('pages.key_271')}</TabsTrigger>
            <TabsTrigger value="size">{t('pages.key_269')}</TabsTrigger>
            <TabsTrigger value="element">{t('pages.key_266')}</TabsTrigger>
          </TabsList>

          <TabsContent value="trend" className="h-[300px]">
            <Chart
              type="line"
              series={trendSeries}
              options={trendChartOptions}
              height={300}
            />
          </TabsContent>

          <TabsContent value="size" className="h-[300px]">
            <Chart
              type="donut"
              series={sizePerformanceData.map((item: any) => item.value)}
              options={pieChartOptions}
              height={300}
            />
          </TabsContent>

          <TabsContent value="element" className="h-[300px]">
            <Chart
              type="donut"
              series={elementPerformanceData.map((item: any) => item.value)}
              options={elementPieChartOptions}
              height={300}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CreativeAnalysis;
