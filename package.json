{"private": true, "workspaces": ["ad_backend", "slash-admin"], "scripts": {"doc:detect": "node scripts/doc-sync/detect-changes.js", "doc:update": "node scripts/doc-sync/update-docs.js", "doc:validate": "node scripts/doc-sync/validate-docs.js", "doc:sync": "npm run doc:detect && npm run doc:update", "doc:setup": "bash scripts/doc-sync/setup-hooks.sh"}, "devDependencies": {"csstype": "^3.1.3"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^7.2.2", "openai": "^5.3.0", "react-i18next": "^13.5.0"}}