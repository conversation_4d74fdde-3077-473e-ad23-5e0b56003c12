import { Resolver, Query, Args, Context, Mutation } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdSetDTO } from '../entities/adset.dto';
import { AdSet } from '../entities/adset.entity';
import { UseGuards, UnauthorizedException } from '@nestjs/common';
import { TenantGuard } from '../../../guards/tenant.guard';
import { plainToInstance } from 'class-transformer';
import { AdSetFilterInput, CreateAdSetInput, UpdateAdSetInput } from '../entities/adset.input';
import { Audience } from '../entities/audience.entity';
import { Campaign } from '../entities/campaign.entity';
import { Tenant } from '../../../entity/tenant.entity';
import { FacebookApiService } from '../services/facebook-api.service';
import { ID, Float } from '@nestjs/graphql';
import { JwtAuthGuard } from '../../../modules/auth/jwt-auth.guard';
import GraphQLJSON from 'graphql-type-json';
import { AdCreativeDTO } from '../entities/ad-creative.dto';
import { FacebookService } from '../facebook/facebook.service';
import { AdAccount } from '../entities/ad-account.entity';

@Resolver(() => AdSetDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class AdSetResolver {
    constructor(
        @InjectRepository(AdSet)
        private readonly adSetRepo: Repository<AdSet>,
        @InjectRepository(Campaign)
        private readonly campaignRepo: Repository<Campaign>,
        @InjectRepository(Audience)
        private readonly audienceRepo: Repository<Audience>,
        @InjectRepository(Tenant)
        private readonly tenantRepo: Repository<Tenant>,
        private readonly facebookApi: FacebookApiService,
        @InjectRepository(AdAccount)
        private readonly adAccountRepo: Repository<AdAccount>,
        private readonly facebookService: FacebookService,
    ) { }

    @Query(() => [AdSetDTO], { name: 'adSetsCustom' })
    async adSetsCustom(
        @Args('filter', { type: () => AdSetFilterInput, nullable: true }) filter: AdSetFilterInput,
        @Context() ctx,
    ): Promise<AdSetDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const qb = this.adSetRepo.createQueryBuilder('ad_set')
            .leftJoinAndSelect('ad_set.adAccount', 'adAccount')
            .leftJoinAndSelect('ad_set.adCampaign', 'adCampaign')
            .leftJoinAndSelect('ad_set.tenant', 'tenant')
            .leftJoinAndSelect('ad_set.audience', 'audience');
        if (!isSuperAdmin) {
            qb.where('ad_set."tenantId" = :tenantId', { tenantId });
        }
        if (filter?.id) {
            qb.andWhere('ad_set."id" = :id', { id: filter.id });
        }
        if (filter?.name) {
            qb.andWhere('ad_set."name" = :name', { name: filter.name });
        }
        if (filter?.platform) {
            qb.andWhere('ad_set."platform" = :platform', { platform: filter.platform });
        }
        const entities = await qb.getMany();
        return plainToInstance(AdSetDTO, entities);
    }

    @Mutation(() => AdSetDTO)
    async createAdSet(
        @Args('name') name: string,
        @Args('campaignId', { type: () => ID }) campaignId: string,
        @Args('audienceId', { type: () => ID }) audienceId: string,
        @Args('dailyBudget') dailyBudget: number,
        @Args('startTime') startTime: string,
        @Args('endTime', { nullable: true }) endTime: string,
        @Args('optimizationGoal') optimizationGoal: string,
        @Args('billingEvent') billingEvent: string,
        @Args('status') status: string,
        @Context() ctx,
        @Args('bid_amount', { type: () => Float, nullable: true }) bid_amount?: number,
        @Args('promoted_object', { type: () => GraphQLJSON, nullable: true }) promoted_object?: any,
        @Args('device_platforms', { type: () => [String], nullable: true }) device_platforms?: string[],
        @Args('user_os', { type: () => [String], nullable: true }) user_os?: string[],
    ): Promise<AdSetDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const tenant = isSuperAdmin
            ? await this.tenantRepo.findOne({ where: { id: tenantId } })
            : await this.tenantRepo.findOne({ where: { id: tenantId } });
        const campaign = isSuperAdmin
            ? await this.campaignRepo.findOne({ where: { id: campaignId }, relations: ['adAccount'] })
            : await this.campaignRepo.findOne({ where: { id: campaignId, adAccount: { tenant: { id: tenantId } } }, relations: ['adAccount'] });
        const audience = isSuperAdmin
            ? await this.audienceRepo.findOne({ where: { id: audienceId } })
            : await this.audienceRepo.findOne({ where: { id: audienceId, tenant: { id: tenantId } } });
        if (!name) throw new Error('name 不能为空');
        if (!campaignId) throw new Error('campaignId 不能为空');
        if (!audienceId) throw new Error('audienceId 不能为空');
        if (dailyBudget === null || dailyBudget === undefined) throw new Error('dailyBudget 不能为空');
        if (!startTime) throw new Error('startTime 不能为空');
        if (!optimizationGoal) throw new Error('optimizationGoal 不能为空');
        if (!billingEvent) throw new Error('billingEvent 不能为空');
        if (!status) throw new Error('status 不能为空');
        // 提前打印外键对象
        console.log('tenant:', tenantId, tenant);
        console.log('campaign:', campaignId, campaign);
        console.log('adAccount:', campaign?.adAccount);
        console.log('audience:', audienceId, audience);
        if (!tenant) throw new Error('租户不存在: tenantId=' + tenantId);
        if (!campaign) throw new Error('广告系列不存在: campaignId=' + campaignId);
        if (!campaign.adAccount) throw new Error('广告账户不存在: adAccount.accountId=' + campaign?.adAccount?.accountId);
        if (!audience) throw new Error('受众不存在: audienceId=' + audienceId);
        if (!campaign.adAccount?.accessToken) throw new Error('广告账户未配置 accessToken');
        const accessToken = campaign.adAccount.accessToken;
        // targeting 组装
        const targeting: any = {
            geo_locations: audience.geo_locations ? { ...audience.geo_locations } : undefined,
            excluded_geo_locations: audience.excluded_geo_locations ? { ...audience.excluded_geo_locations } : undefined,
            locales: audience.locales,
            age_min: audience.age_min,
            age_max: audience.age_max,
            interests: audience.interests,
            behaviors: audience.behaviors,
        };
        if (device_platforms && device_platforms.length > 0) {
            targeting.device_platforms = device_platforms;
        }
        if (user_os && user_os.length > 0) {
            targeting.user_os = user_os;
        }
        // 性别处理：1=男，2=女，不限不传
        if (audience && (audience.gender === 1 || audience.gender === 2)) {
            targeting.genders = [audience.gender];
        }
        // 强制加上 targeting_automation.advantage_audience 字段，默认关闭
        targeting.targeting_automation = { advantage_audience: 0 };
        const fbPayload = {
            name,
            campaign_id: campaign.facebookCampaignId,
            daily_budget: dailyBudget,
            start_time: startTime,
            end_time: endTime,
            optimization_goal: optimizationGoal,
            billing_event: billingEvent,
            status,
            targeting,
            account_id: campaign.adAccount.accountId,
            ...(typeof bid_amount === 'number' ? { bid_amount } : {}),
            ...(promoted_object ? { promoted_object } : {}),
        };
        let fbResult;
        try {
            fbResult = await this.facebookApi.createAdSet(fbPayload, accessToken);
        } catch (e) {
            console.error('createAdSet error:', e);
            // Facebook API 错误透传
            const fbError = e?.response?.data?.error;
            if (fbError) {
                const userTitle = fbError.error_user_title || '';
                const userMsg = fbError.error_user_msg || fbError.message || '';
                const msg = userTitle ? `${userTitle}: ${userMsg}` : userMsg;
                throw new Error(msg || 'Facebook 广告组创建失败');
            }
            throw new Error(e.message || 'Facebook 广告组创建失败');
        }
        const safeInput = Array.isArray(fbPayload) ? fbPayload[0] : fbPayload;
        // 去除外键 id 字段，避免和对象引用冲突
        const { campaignId: _campaignId, audienceId: _audienceId, ...restInput } = safeInput;
        // 新增日志，打印插入ad_set时adAccountId的值
        console.log('插入ad_set时adAccountId的值:', campaign.adAccount.accountId);
        // 新增日志，打印插入ad_set时adCampaignId的值
        console.log('插入ad_set时adCampaignId的值:', campaign.id);
        const entity = this.adSetRepo.create({
            ...restInput,
            startTime: restInput.start_time,
            endTime: restInput.end_time,
            adCampaign: campaign,
            adAccount: campaign.adAccount,
            audience,
            tenant,
            platform: 'facebook',
            adsetId: fbResult.id,
        });
        const realEntity = Array.isArray(entity) ? entity[0] : entity;
        const saved = await this.adSetRepo.save(realEntity);
        if (typeof saved.startTime === 'string') {
            saved.startTime = new Date(saved.startTime);
        }
        if (saved.endTime && typeof saved.endTime === 'string') {
            saved.endTime = new Date(saved.endTime);
        }
        if (!saved.optimizationGoal) {
            saved.optimizationGoal = realEntity.optimizationGoal || safeInput.optimizationGoal || 'CONVERSIONS';
        }
        // 调试输出，检查外键对象
        console.log('campaign:', campaign?.id, 'adAccount:', campaign?.adAccount?.id, campaign?.adAccount);
        console.log('audience:', audience?.id, audience);
        console.log('tenant:', tenant?.id, tenant);
        return plainToInstance(AdSetDTO, saved);
    }

    @Mutation(() => AdSetDTO)
    async updateAdSet(
        @Args('input', { type: () => UpdateAdSetInput }) input: UpdateAdSetInput,
        @Context() ctx,
    ): Promise<AdSetDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const adSet = isSuperAdmin
            ? await this.adSetRepo.findOne({ where: { id: input.id } })
            : await this.adSetRepo.findOne({ where: { id: input.id, tenant: { id: tenantId } } });
        if (!adSet) throw new Error('广告组不存在');
        Object.assign(adSet, input);
        const saved = await this.adSetRepo.save(adSet);
        return plainToInstance(AdSetDTO, saved);
    }

    @Mutation(() => Boolean)
    async deleteAdSet(
        @Args('id', { type: () => ID }) id: string,
        @Context() ctx,
    ): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx;
        const adSet = isSuperAdmin
            ? await this.adSetRepo.findOne({ where: { id } })
            : await this.adSetRepo.findOne({ where: { id, tenant: { id: tenantId } } });
        if (!adSet) throw new Error('广告组不存在');
        await this.adSetRepo.delete({ id });
        return true;
    }

    @Query(() => [AdCreativeDTO], { name: 'facebookCreativesByAdSet' })
    async facebookCreativesByAdSet(
        @Args('adSetId', { type: () => ID }) adSetId: string,
        @Context() ctx,
    ): Promise<AdCreativeDTO[]> {
        // 1. 查找广告组
        const adSet = await this.adSetRepo.findOne({ where: { id: adSetId }, relations: ['adAccount', 'tenant'] });
        if (!adSet) throw new Error('广告组不存在');
        if (!adSet.adAccount) throw new Error('广告账户不存在');
        if (!adSet.adAccount.accessToken) throw new Error('广告账户未配置accessToken');
        // 2. 拉取Facebook创意
        const { creatives } = await this.facebookService.getAdCreatives({
            accessToken: adSet.adAccount.accessToken,
            accountId: adSet.adAccount.accountId,
        });
        // 3. 映射为AdCreativeDTO（只返回部分字段，raw存原始数据）
        return (creatives || []).map((item: any) => ({
            id: item.id,
            platform: 'facebook',
            creativeId: item.id,
            name: item.name,
            adAccount: adSet.adAccount,
            adCampaign: null,
            tenant: adSet.tenant,
            raw: item,
            status: item.status,
        }));
    }
} 