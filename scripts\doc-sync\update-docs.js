#!/usr/bin/env node

/**
 * 文档同步 - 自动更新工具
 * 根据代码变更自动更新模块文档
 */

const fs = require('fs');
const path = require('path');

class DocumentUpdater {
  constructor() {
    this.projectRoot = process.cwd();
    this.docsPath = path.join(this.projectRoot, 'docs/modules');
    this.changesPath = path.join(this.projectRoot, 'scripts/doc-sync/changes.json');
    
    // 模块映射表 - 将代码模块映射到文档文件
    this.moduleMapping = {
      'ad-platform': ['z 广告账户.md', 'z 广告系列.md', 'z 受众管理.md'],
      'vcc': ['z 卡片管理.md'],
      'material-management': ['z 素材管理.md'],
      'material-create': ['z 素材管理.md'],
      'user': ['z 账户管理.md'],
      'group': ['z 群组管理.md'],
      'tenant': ['z 租户管理.md']
    };
  }

  /**
   * 加载变更数据
   */
  loadChanges() {
    if (!fs.existsSync(this.changesPath)) {
      console.log('❌ 未找到变更数据文件');
      return null;
    }
    
    const changes = JSON.parse(fs.readFileSync(this.changesPath, 'utf8'));
    console.log('📖 已加载变更数据');
    return changes;
  }

  /**
   * 更新实体相关文档
   */
  updateEntityDocs(entities) {
    entities.forEach(entity => {
      const docFiles = this.getDocumentFiles(entity.moduleName);
      
      docFiles.forEach(docFile => {
        const docPath = path.join(this.docsPath, docFile);
        if (fs.existsSync(docPath)) {
          console.log(`📝 更新文档: ${docFile} (Entity: ${entity.tableName})`);
          this.updateEntityInDocument(docPath, entity);
        }
      });
    });
  }

  /**
   * 更新文档中的实体信息
   */
  updateEntityInDocument(docPath, entity) {
    let content = fs.readFileSync(docPath, 'utf8');
    
    // 更新数据库表结构部分
    content = this.updateDatabaseTableSection(content, entity);
    
    // 更新字段说明部分
    content = this.updateFieldDescriptionSection(content, entity);
    
    // 保存更新后的文档
    fs.writeFileSync(docPath, content);
  }

  /**
   * 更新数据库表结构部分
   */
  updateDatabaseTableSection(content, entity) {
    const tableName = entity.tableName;
    
    // 查找表结构部分
    const tableHeaderRegex = new RegExp(`#### ${tableName} 表[\\s\\S]*?\\n\\n(?=####|---|\$)`, 'i');
    const tableMatch = content.match(tableHeaderRegex);
    
    if (tableMatch) {
      // 生成新的表结构
      const newTableSection = this.generateTableStructure(entity);
      content = content.replace(tableHeaderRegex, newTableSection);
      console.log(`  ✅ 已更新 ${tableName} 表结构`);
    } else {
      console.log(`  ⚠️  未找到 ${tableName} 表结构部分`);
    }
    
    return content;
  }

  /**
   * 生成表结构 Markdown
   */
  generateTableStructure(entity) {
    const { tableName, fields, relations } = entity;
    
    let markdown = `#### ${tableName} 表\n\n`;
    markdown += `详见 ad_backend/src/modules/${entity.moduleName}/entities/${entity.fileName}.entity.ts\n\n`;
    
    // 生成字段表格
    markdown += '| 字段名 | 类型 | 说明 | 约束 |\n';
    markdown += '|--------|------|------|------|\n';
    
    fields.forEach(field => {
      const constraints = this.generateFieldConstraints(field);
      const description = this.generateFieldDescription(field);
      
      markdown += `| ${field.name} | ${field.type} | ${description} | ${constraints} |\n`;
    });
    
    // 添加关联关系
    if (relations.length > 0) {
      relations.forEach(relation => {
        markdown += `| ${relation.field} | 关联关系 | ${relation.type}关联 | NOT NULL |\n`;
      });
    }
    
    markdown += '\n';
    return markdown;
  }

  /**
   * 生成字段约束描述
   */
  generateFieldConstraints(field) {
    const constraints = [];
    
    if (field.options.primary) {
      constraints.push('PRIMARY KEY');
    }
    if (field.options.unique) {
      constraints.push('UNIQUE');
    }
    if (field.options.nullable === false) {
      constraints.push('NOT NULL');
    }
    if (field.options.default !== undefined) {
      constraints.push(`默认${field.options.default}`);
    }
    
    return constraints.length > 0 ? constraints.join(', ') : '可空';
  }

  /**
   * 生成字段描述
   */
  generateFieldDescription(field) {
    // 根据字段名生成描述
    const descriptions = {
      'id': '主键，唯一标识',
      'tenantId': '租户ID',
      'createdAt': '创建时间',
      'updatedAt': '更新时间',
      'name': '名称',
      'status': '状态',
      'type': '类型',
      'platform': '平台类型',
      'accountId': '账户ID',
      'fileName': '文件名',
      'file': '文件URL',
      'tags': '标签',
      'group': '分组',
      'notes': '备注',
      'remark': '备注'
    };
    
    return descriptions[field.name] || field.name;
  }

  /**
   * 更新 GraphQL 相关文档
   */
  updateGraphQLDocs(schemas) {
    schemas.forEach(schema => {
      // 根据文件名推断对应的文档
      const docFiles = this.getDocumentFilesByGraphQL(schema.fileName);
      
      docFiles.forEach(docFile => {
        const docPath = path.join(this.docsPath, docFile);
        if (fs.existsSync(docPath)) {
          console.log(`📝 更新文档: ${docFile} (GraphQL: ${schema.fileName})`);
          this.updateGraphQLInDocument(docPath, schema);
        }
      });
    });
  }

  /**
   * 更新文档中的 GraphQL 信息
   */
  updateGraphQLInDocument(docPath, schema) {
    let content = fs.readFileSync(docPath, 'utf8');
    
    // 更新 API 调用列表
    content = this.updateAPIListSection(content, schema);
    
    // 保存更新后的文档
    fs.writeFileSync(docPath, content);
  }

  /**
   * 更新 API 调用列表部分
   */
  updateAPIListSection(content, schema) {
    // 查找 API 调用列表部分
    const apiSectionRegex = /### API 调用列表与详细说明[\s\S]*?(?=####|---|\n## )/;
    const apiMatch = content.match(apiSectionRegex);
    
    if (apiMatch) {
      const newAPISection = this.generateAPISection(schema);
      content = content.replace(apiSectionRegex, newAPISection);
      console.log(`  ✅ 已更新 API 调用列表`);
    } else {
      console.log(`  ⚠️  未找到 API 调用列表部分`);
    }
    
    return content;
  }

  /**
   * 生成 API 部分 Markdown
   */
  generateAPISection(schema) {
    let markdown = '### API 调用列表与详细说明\n\n';
    
    markdown += '| API名称 | 方法 | GraphQL名/路径 | 参数类型/说明 | 返回字段 | 说明/示例 |\n';
    markdown += '|---------|------|----------------|---------------|----------|----------|\n';
    
    // 添加查询
    schema.queries.forEach(query => {
      markdown += `| ${this.formatOperationName(query.operationName)} | query | ${query.operationName} | ${query.params} | ${this.formatFields(query.fields)} | 查询操作 |\n`;
    });
    
    // 添加变更
    schema.mutations.forEach(mutation => {
      markdown += `| ${this.formatOperationName(mutation.operationName)} | mutation | ${mutation.operationName} | ${mutation.params} | ${this.formatFields(mutation.fields)} | 变更操作 |\n`;
    });
    
    markdown += '\n';
    return markdown;
  }

  /**
   * 格式化操作名称
   */
  formatOperationName(operationName) {
    // 将驼峰命名转换为中文描述
    const nameMap = {
      'createOne': '新增',
      'updateOne': '更新',
      'deleteOne': '删除',
      'get': '获取',
      'list': '列表'
    };
    
    for (const [key, value] of Object.entries(nameMap)) {
      if (operationName.includes(key)) {
        return operationName.replace(key, value);
      }
    }
    
    return operationName;
  }

  /**
   * 格式化字段列表
   */
  formatFields(fields) {
    // 简化字段显示
    const fieldList = fields.split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.includes('{') && !line.includes('}'))
      .slice(0, 5); // 只显示前5个字段
    
    return fieldList.join(', ') + (fieldList.length >= 5 ? ', ...' : '');
  }

  /**
   * 根据模块名获取对应的文档文件
   */
  getDocumentFiles(moduleName) {
    return this.moduleMapping[moduleName] || [];
  }

  /**
   * 根据 GraphQL 文件名获取对应的文档文件
   */
  getDocumentFilesByGraphQL(fileName) {
    const mapping = {
      'adAccount': ['z 广告账户.md'],
      'campaign': ['z 广告系列.md'],
      'audience': ['z 受众管理.md'],
      'vccCard': ['z 卡片管理.md'],
      'materialManagement': ['z 素材管理.md'],
      'material': ['z 素材管理.md'],
      'user': ['z 账户管理.md'],
      'group': ['z 群组管理.md'],
      'tenant': ['z 租户管理.md']
    };
    
    return mapping[fileName] || [];
  }

  /**
   * 创建更新报告
   */
  generateUpdateReport(changes) {
    const reportPath = path.join(this.projectRoot, 'scripts/doc-sync/update-report.md');
    
    let report = '# 文档自动更新报告\n\n';
    report += `更新时间: ${new Date().toLocaleString()}\n\n`;
    
    // Entity 更新
    if (changes.entities.length > 0) {
      report += '## Entity 更新\n\n';
      changes.entities.forEach(entity => {
        report += `- **${entity.tableName}** (${entity.moduleName})\n`;
        report += `  - 文件: ${entity.filePath}\n`;
        report += `  - 字段数: ${entity.fields.length}\n`;
        report += `  - 关联关系: ${entity.relations.length}\n\n`;
      });
    }
    
    // GraphQL 更新
    if (changes.graphqlSchemas.length > 0) {
      report += '## GraphQL 更新\n\n';
      changes.graphqlSchemas.forEach(schema => {
        report += `- **${schema.fileName}**\n`;
        report += `  - 查询: ${schema.queries.length}\n`;
        report += `  - 变更: ${schema.mutations.length}\n`;
        report += `  - 订阅: ${schema.subscriptions.length}\n\n`;
      });
    }
    
    fs.writeFileSync(reportPath, report);
    console.log(`📊 更新报告已生成: ${reportPath}`);
  }

  /**
   * 执行文档更新
   */
  async update() {
    console.log('🔄 开始更新文档...');
    
    const changes = this.loadChanges();
    if (!changes) {
      return;
    }
    
    // 更新实体相关文档
    if (changes.entities.length > 0) {
      console.log(`📋 更新 ${changes.entities.length} 个实体的文档`);
      this.updateEntityDocs(changes.entities);
    }
    
    // 更新 GraphQL 相关文档
    if (changes.graphqlSchemas.length > 0) {
      console.log(`🔗 更新 ${changes.graphqlSchemas.length} 个 GraphQL 的文档`);
      this.updateGraphQLDocs(changes.graphqlSchemas);
    }
    
    // 生成更新报告
    this.generateUpdateReport(changes);
    
    console.log('✅ 文档更新完成');
  }
}

// 主函数
async function main() {
  const updater = new DocumentUpdater();
  await updater.update();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = DocumentUpdater;
