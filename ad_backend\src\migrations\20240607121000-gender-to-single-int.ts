import { MigrationInterface, QueryRunner } from "typeorm";

export class GenderToSingleInt20240607121000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // 删除 genders 字段（如存在）
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "genders"`);
        // 添加 gender 字段
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "gender" int`);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回滚时恢复 genders 字段
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN IF NOT EXISTS "genders" int[]`);
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "gender"`);
    }
} 