import { gql } from '@apollo/client';

// 登录变更
export const LOGIN_MUTATION = gql`
  mutation Login($input: LoginInput!) {
    login(input: $input) {
      userId
      accessToken
      username
      roles
    }
  }
`;

// 注册变更
export const REGISTER_MUTATION = gql`
  mutation Register($input: RegisterInput!) {
    register(input: $input) {
      userId
      accessToken
      username
    }
  }
`;

// 获取当前认证用户信息
export const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    me {
      user {
        id
        username
        email
        avatar
        fullName
        phone
        address
        status
        roles {
          id
          name
          routeIds
          routeList {
            id
            parentId
            name
            path
            component
            icon
            order
            type
            status
            isHidden
            description
            children {
              id
              parentId
              name
              path
              component
              icon
              order
              type
              status
              isHidden
              description
            }
          }
        }
        groups {
          id
          name
        }
        createdAt
        updatedAt
      }
      accessToken
    }
  }
`; 