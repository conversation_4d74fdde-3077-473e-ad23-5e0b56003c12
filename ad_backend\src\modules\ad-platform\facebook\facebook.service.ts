import { Injectable } from '@nestjs/common';
import { IAdPlatformService } from '../ad-platform.interface';
import { axiosProxyGet, axiosProxyPost } from '../../../utils/axios-proxy';
import { FacebookApp } from '../entities/facebook-app.entity';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const adsSdk = require('facebook-nodejs-business-sdk');
const { AdAccount, Campaign, Ad, AdCreative, AdSet, AdPixel, CustomAudience, FacebookAdsApi } = adsSdk;

@Injectable()
export class FacebookService implements IAdPlatformService {
  private api: any;

  /**
   * 初始化 Facebook API
   * @param accessToken Facebook 用户/系统 access token
   */
  private initApi(accessToken: string) {
    if (!this.api || this.api._accessToken !== accessToken) {
      this.api = FacebookAdsApi.init(accessToken);
    }
  }

  /**
   * 授权入口（OAuth URL 生成）
   */
  async authorize(params: any): Promise<any> {
    // TODO: 实现 Facebook OAuth 授权逻辑，返回授权 URL
    return { url: 'https://www.facebook.com/v18.0/dialog/oauth?...', params };
  }

  /**
   * 获取广告账户列表
   */
  async getAccounts(params: { accessToken: string }): Promise<any> {
    // 通过代理请求 Facebook Graph API
    const url = 'https://graph.facebook.com/v23.0/me/adaccounts';
    const fields = 'id,name,account_status';
    const { data } = await axiosProxyGet(url, {
      params: {
        access_token: params.accessToken,
        fields,
        limit: 100,
      },
    });
    // data.data 是账户列表
    return { accounts: data.data || [] };
  }

  /**
   * 获取广告系列（Campaign）
   */
  async getCampaigns(params: { accessToken: string; accountId: string }): Promise<any> {
    this.initApi(params.accessToken);
    const account = new AdAccount(params.accountId);
    const campaigns = await account.getCampaigns([
      Campaign.Fields.id,
      Campaign.Fields.name,
      Campaign.Fields.status,
      Campaign.Fields.objective,
      Campaign.Fields.effective_status,
    ]);
    return { campaigns };
  }

  /**
   * 获取广告组（AdSet）
   */
  async getAdSets(params: { accessToken: string; accountId: string }): Promise<any> {
    this.initApi(params.accessToken);
    const account = new AdAccount(params.accountId);
    const adsets = await account.getAdSets([
      AdSet.Fields.id,
      AdSet.Fields.name,
      AdSet.Fields.status,
      AdSet.Fields.lifetime_budget,
      AdSet.Fields.daily_budget,
      AdSet.Fields.campaign_id,
      AdSet.Fields.effective_status,
    ]);
    return { adsets };
  }

  /**
   * 获取广告（Ad）
   */
  async getAds(params: { accessToken: string; accountId: string }): Promise<any> {
    this.initApi(params.accessToken);
    const account = new AdAccount(params.accountId);
    const ads = await account.getAds([
      Ad.Fields.id,
      Ad.Fields.name,
      Ad.Fields.status,
      Ad.Fields.adset_id,
      Ad.Fields.campaign_id,
      Ad.Fields.effective_status,
    ]);
    return { ads };
  }

  /**
   * 获取广告创意（AdCreative）
   */
  async getAdCreatives(params: { accessToken: string; accountId: string }): Promise<any> {
    const url = `https://graph.facebook.com/v18.0/act_${params.accountId}/adcreatives`;
    const { data } = await axiosProxyGet(url, {
      params: {
        access_token: params.accessToken,
        fields: 'id,name,object_story_spec,status',
        limit: 100,
      },
    });
    return { creatives: data.data || [] };
  }

  /**
   * 获取像素列表
   */
  async getPixels(params: { accessToken: string; accountId: string }): Promise<any> {
    // 直接用 Facebook Graph API REST 获取 Pixel
    const url = `https://graph.facebook.com/v23.0/act_${params.accountId}/adspixels`;
    const fields = 'id,name,code,last_fired_time,creation_time';
    const { data } = await axiosProxyGet(url, {
      params: {
        access_token: params.accessToken,
        fields,
        limit: 50,
      },
    });
    // data.data 是 pixel 列表
    return { pixels: data.data || [] };
  }

  /**
   * 获取自定义受众列表
   */
  async getAudiences(params: { accessToken: string; accountId: string }): Promise<any> {
    this.initApi(params.accessToken);
    const account = new AdAccount(params.accountId);
    const audiences = await account.getCustomAudiences([
      CustomAudience.Fields.id,
      CustomAudience.Fields.name,
      CustomAudience.Fields.subtype,
      CustomAudience.Fields.description,
      CustomAudience.Fields.approximate_count,
    ]);
    return { audiences };
  }

  /**
   * 获取广告洞察数据（Insights）
   */
  async getInsights(params: { accessToken: string; accountId: string; level?: string; fields?: string[]; date_preset?: string }): Promise<any> {
    this.initApi(params.accessToken);
    const account = new AdAccount(params.accountId);
    const fields = params.fields || ['impressions', 'clicks', 'spend', 'cpc', 'ctr', 'actions'];
    const insights = await account.getInsights(fields, {
      level: params.level || 'account',
      date_preset: params.date_preset || 'last_7d',
    });
    return { insights };
  }

  /**
   * 获取 Facebook 用户管理的 App 列表
   */
  async getUserApps(accessToken: string): Promise<any[]> {
    const url = 'https://graph.facebook.com/v23.0/me';
    const fields = 'id,name,apps{name,id,link}';
    try {
      const { data } = await axiosProxyGet(url, {
        params: {
          access_token: accessToken,
          fields,
        },
      });
      // data.apps.data 是 app 列表
      return data.apps?.data || [];
    } catch (e) {
      console.error('Facebook API 获取用户App失败:', e?.response?.data || e);
      throw e;
    }
  }

  /**
   * 创建广告系列
   */
  async createCampaignOnFacebook(input: any, accessToken: string, adAccountId: string) {
    const url = `https://graph.facebook.com/v23.0/act_${adAccountId}/campaigns`;
    const payload = {
      name: input.name,
      objective: input.objective,
      status: input.status,
      special_ad_categories: input.specialAdCategories || [],
      access_token: accessToken,
    };
    try {
      const { data } = await axiosProxyPost(url, payload);
      return data; // { id: 'facebook_campaign_id' }
    } catch (e) {
      console.error('Facebook API 创建广告系列失败:', e?.response?.data || e);
      throw e;
    }
  }

  /**
   * 更新广告系列
   */
  async updateCampaignOnFacebook(
    facebookCampaignId: string,
    update: { name?: string; objective?: string; status?: string; special_ad_categories?: string[] },
    accessToken: string
  ) {
    const url = `https://graph.facebook.com/v23.0/${facebookCampaignId}`;
    const payload = {
      ...update,
      access_token: accessToken,
    };
    try {
      const { data } = await axiosProxyPost(url, payload);
      return data;
    } catch (e) {
      console.error('Facebook API 更新广告系列失败:', e?.response?.data || e);
      throw e;
    }
  }

  /**
   * 删除广告系列
   */
  async deleteCampaignOnFacebook(facebookCampaignId: string, accessToken: string) {
    const url = `https://graph.facebook.com/v23.0/${facebookCampaignId}`;
    const payload = {
      status: 'DELETED',
      access_token: accessToken,
    };
    try {
      const { data } = await axiosProxyPost(url, payload);
      return data;
    } catch (e) {
      console.error('Facebook API 删除广告系列失败:', e?.response?.data || e);
      throw e;
    }
  }

  /**
   * 获取广告账户下投放过的 App Promotion 应用（返回 id、name、link）
   */
  async getPromotedAppsByAdAccount(adAccountId: string, accessToken: string): Promise<FacebookApp[]> {
    // 1. 获取所有 AdSet（直接用 Graph API）
    const adAccountIdWithPrefix = adAccountId.startsWith('act_') ? adAccountId : `act_${adAccountId}`;
    const adsetsUrl = `https://graph.facebook.com/v23.0/${adAccountIdWithPrefix}/adsets`;
    const adsetsRes = await axiosProxyGet(adsetsUrl, {
      params: {
        access_token: accessToken,
        fields: 'id,name,promoted_object',
        limit: 100,
      },
    });
    const adsets = adsetsRes.data?.data || [];
    // 2. 收集所有 application_id
    const appIdSet = new Set<string>();
    adsets.forEach((adset: any) => {
      if (adset.promoted_object && adset.promoted_object.application_id) {
        appIdSet.add(adset.promoted_object.application_id);
      }
    });
    const appIds = Array.from(appIdSet);
    // 3. 批量查询 App 详情
    const apps: FacebookApp[] = [];
    for (const appId of appIds) {
      try {
        const url = `https://graph.facebook.com/v23.0/${appId}`;
        const fields = 'id,name,link';
        const { data } = await axiosProxyGet(url, {
          params: {
            access_token: accessToken,
            fields,
          },
        });
        apps.push({
          id: data.id,
          name: data.name,
          link: data.link,
        });
      } catch (e) {
        // 获取单个 App 失败时跳过
      }
    }
    return apps;
  }

  async getBusinessIdByAdAccountId(adAccountId: string, accessToken: string): Promise<string | undefined> {
    const url = `https://graph.facebook.com/v23.0/act_${adAccountId}`;
    console.log('[getBusinessIdByAdAccountId] 请求参数:', { adAccountId, accessToken, url });
    try {
      const res = await axiosProxyGet(url, {
        params: {
          fields: 'business',
          access_token: accessToken,
        },
      });
      console.log('[getBusinessIdByAdAccountId] Facebook 返回:', res.data);
      return res.data?.business?.id;
    } catch (e) {
      console.error('[getBusinessIdByAdAccountId] Facebook API 获取 businessId 失败:', e?.response?.data || e);
      throw e;
    }
  }
}