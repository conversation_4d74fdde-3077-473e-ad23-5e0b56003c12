# 前端项目文档（slash-admin）

## 技术栈
- React 18 + TypeScript
- Apollo Client（GraphQL 数据管理）
- antd（UI 组件库）
- React Router

## 主要页面
- 广告组管理、广告管理、受众管理
- 风险控制页面（已集成至侧边栏）

## 数据流与接口
- 所有数据均通过 Apollo Client 访问后端 GraphQL API
- 已实现分页、搜索、增删改查等功能
- 推荐统一使用 hooks 封装 GraphQL 查询与 mutation

## 开发建议
- 新增页面请参考现有 hooks/useQuery/useMutation 模式
- 组件复用与样式风格需保持一致
- 前后端接口类型建议用 codegen 自动生成

---

# 联调说明

1. 后端启动后自动生成 schema.graphql，前端根据 schema 进行接口开发。
2. 推荐使用 GraphQL Playground/Postman 联调接口。
3. 接口有变动时，前端可使用 codegen 工具同步生成最新类型。

---

如需详细接口文档和后端说明，请查阅后端 README 或 docs 目录。

---

# 页面结构与核心导航

- 侧边栏菜单：广告组管理、广告管理、受众管理、风险控制、日历、看板等
- 路由配置支持 permission（权限动态生成）与 module（静态模块）两种模式，详见 src/router 及 others.tsx
- 风险控制页面已通过 module 路由模式集成，路径和 meta.key 配置需与设计规范一致

# 与后端对接范例

- 数据统一通过 Apollo Client 调用 GraphQL API
- 推荐所有数据请求封装为 hooks，如 useAdSets/useVccs/useEmployees

```typescript
// 示例：获取广告组列表
import { useQuery, gql } from '@apollo/client';
const GET_ADSETS = gql`
  query GetAdSets($filter: AdSetFilter, $paging: OffsetPaging) {
    adSets(filter: $filter, paging: $paging) {
      nodes { id name status }
      totalCount
    }
  }
`;
const { data, loading } = useQuery(GET_ADSETS, { variables: { filter: {}, paging: { limit: 10, offset: 0 } } });
```

- 类型建议用 graphql-codegen 自动生成，避免手写

# mock 与测试建议
- 开发阶段可用 MSW/mockjs 拦截 GraphQL 请求，前后端独立开发更高效
- 单元测试建议用 jest + react-testing-library
- 组件测试建议覆盖交互逻辑、边界条件

# 组件与样式规范
- 组件统一使用 antd，禁止自造轮子
- 样式优先用 CSS Modules 或 styled-components，避免全局污染
- 组件 props、类型、默认值需注明，建议写 JSDoc 注释
- 表单、弹窗、表格等建议抽象为通用组件

# 常见问题与排查
- 类型报错：检查 codegen 类型是否同步 schema
- 页面空白：检查 hooks 查询、权限、路由配置
- 样式冲突：优先用局部样式，避免全局污染
- GraphQL 报错：用 Playground 检查接口返回
- 路由不生效：检查路由模式与配置文件

# 团队协作与开发建议
- 新页面/功能建议先用 mock 数据开发，接口联调前可用 MSW/mockjs
- 提交代码前请自查 lint、类型、UT
- 变更接口需同步更新接口文档和 codegen 类型
- 复杂流程建议提前画好流程图/接口时序图

---

<div align="center"> 
<br> 
<br>
<img src="./src/assets/images/logo.png" height="140" />
<h3> Slash Admin </h3>
  <p>
    <p style="font-size: 14px">
      Slash Admin is a modern admin dashboard template built with React 18, Vite, Ant Design, and TypeScript. It is designed to help developers quickly create powerful admin management systems.
    </p>
    <br />
    <br />
    <a href="https://admin.slashspaces.com/">Preview</a>
    ·
    <a href="https://discord.gg/fXemAXVNDa">Discord</a>
    ·
    <a href="https://docs-admin.slashspaces.com/">Document</a>
    <br />
    <br />
    <a href="https://trendshift.io/repositories/6387" target="_blank"><img src="https://trendshift.io/api/badge/repositories/6387" alt="d3george%2Fslash-admin | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>
</div>

**English** | [中文](./README.zh-CN.md)

##  Sponsor
<div style="display: flex; gap: 50px"> 
  <img style="width:300px" src="https://d3george.github.io/github-static/pay/weixin.jpg" >
  <img style="width:280px" src="https://d3george.github.io/github-static/pay/buymeacoffee.png" />
</div>

## Preview
+ https://admin.slashspaces.com/

|![login.png](https://d3george.github.io/github-static/slash-admin/login.jpeg)|![login_dark.png](https://d3george.github.io/github-static/slash-admin/login_dark.jpeg)
| ----------------------------------------------------------------- | ------------------------------------------------------------------- |
|![analysis.png](https://d3george.github.io/github-static/slash-admin/analysis.png)|![workbench.png](https://d3george.github.io/github-static/slash-admin/workbench.png)

## Features

- Built using React 18 hooks.
- Powered by Vite for rapid development and hot module replacement.
- Integrates Ant Design, providing a rich set of UI components and design patterns.
- Written in TypeScript, offering type safety and an improved development experience.
- Responsive design, adapting to various screen sizes and devices.
- Flexible routing configuration, supporting nested routes.
- Integrated access control based on user roles.
- Supports internationalization for easy language switching.
- Includes common admin features like user management, role management, and permission management.
- Customizable themes and styles to meet your branding needs.
- Mocking solution based on MSW and Faker.js.
- State management using Zustand.
- Data fetching using React-Query.

## Quick Start

### Get the Project Code

```bash
git clone https://github.com/d3george/slash-admin.git
```

### Install Dependencies

In the project's root directory, run the following command to install project dependencies:

```bash
pnpm install
```

### Start the Development Server

Run the following command to start the development server:

```bash
pnpm dev
```

Visit [http://localhost:3001](http://localhost:3001) to view your application.

### Build for Production

Run the following command to build the production version:

```bash
pnpm build
```

## Docker deployment


### Build image and Run container
#### build image
Enter the project root directory in the terminal and execute the following command to build the Docker image:
```
docker build -t your-image-name .
```
Make sure to replace `your-image-name` with your own image name 

#### run container
Run your application in the Docker container using the following command:
```
docker run -p 3001:80 your-image-name
```
This will run your application on port `80`(exposed in `Dockerfile`) of the container and map it to port `3001` on your host.

Now you can access http://localhost:3001 to view the deployed applications.

### use docker-compose.yaml
Enter the project root directory in the terminal and execute the following command to start Docker Compose:
```
docker-compose up -d
```
Docker Compose will build an image based on the configuration defined by 'docker-compose.yaml' and run the container in the background.

After the container runs successfully, it can also be accessed through http://localhost:3001 To view the deployed applications.


## Git Contribution submission specification

reference[.commitlint.config.js](./commitlint.config.js)

- `feat` new features
- `fix`  fix the
- `docs` documentation or comments
- `style` code format (changes that do not affect code execution)
- `refactor` refactor
- `perf` performance optimization
- `revert` revert commit
- `test` test related
- `chore` changes in the construction process or auxiliary tools
- `ci` modify CI configuration and scripts
- `types` type definition file changes
- `wip` in development
