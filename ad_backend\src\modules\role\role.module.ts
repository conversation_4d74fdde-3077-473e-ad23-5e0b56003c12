import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from './role.entity';
import { RoleService } from './role.service';
import { RoleResolver } from './resolvers/role.resolver';
import { RoleController } from './role.controller';
import { Route } from '../route/route.entity';
import { RoleSeedService } from './role.seed';
import { AuthModule } from '../auth/auth.module';
import { UserModule } from '../user/user.module';

@Module({
    imports: [
        TypeOrmModule.forFeature([Role, Route]),
        AuthModule,
        UserModule,
    ],
    providers: [
        RoleService,
        RoleResolver,
        RoleSeedService
    ],
    controllers: [RoleController],
    exports: [RoleService, RoleSeedService],
})
export class RoleModule { } 