import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { AnalysisService } from './analysis.service';
import {
  TimeRangeEnum,
  OverviewCardsResponseDto,
  AdPerformanceAnalysisResponseDto,
  AdTrafficAnalysisResponseDto,
  CreativePerformanceResponseDto,
  ChannelTimeRangeEnum,
  ChannelAnalysisResponseDto,
} from './dto/analysis.dto';

@ApiTags('数据分析')
@Controller('api/analysis')
export class AnalysisController {
  constructor(private readonly analysisService: AnalysisService) {}

  @Get('overview-cards')
  @ApiOperation({ summary: '获取总览卡片数据' })
  @ApiQuery({
    name: 'tenantId',
    required: false,
    description: '租户ID，如果不传则获取全局数据',
  })
  @ApiResponse({
    status: 200,
    description: '成功获取总览数据',
    type: OverviewCardsResponseDto,
  })
  async getOverviewCards(@Query('tenantId') tenantId?: string) {
    const data = await this.analysisService.getOverviewCards(tenantId);
    return {
      success: true,
      status: 0,
      data
    };
  }

  @Get('ad-performance')
  @ApiOperation({ summary: '获取广告效果分析数据' })
  @ApiQuery({
    name: 'timeRange',
    enum: TimeRangeEnum,
    required: false,
    description: '时间范围',
  })
  @ApiQuery({
    name: 'tenantId',
    required: false,
    description: '租户ID，如果不传则获取全局数据',
  })
  @ApiResponse({
    status: 200,
    description: '成功获取广告效果分析数据',
    type: AdPerformanceAnalysisResponseDto,
  })
  async getAdPerformanceAnalysis(
    @Query('timeRange') timeRange: TimeRangeEnum = TimeRangeEnum.DAY,
    @Query('tenantId') tenantId?: string
  ) {
    const data = await this.analysisService.getAdPerformanceAnalysis(timeRange, tenantId);
    return {
      success: true,
      status: 0,
      data
    };
  }

  @Get('ad-traffic')
  @ApiOperation({ summary: '获取广告跑量分析数据' })
  @ApiQuery({
    name: 'timeRange',
    enum: TimeRangeEnum,
    required: false,
    description: '时间范围',
  })
  @ApiQuery({
    name: 'tenantId',
    required: false,
    description: '租户ID，如果不传则获取全局数据',
  })
  @ApiResponse({
    status: 200,
    description: '成功获取广告跑量分析数据',
    type: AdTrafficAnalysisResponseDto,
  })
  async getAdTrafficAnalysis(
    @Query('timeRange') timeRange: TimeRangeEnum = TimeRangeEnum.DAY,
    @Query('tenantId') tenantId?: string
  ) {
    const data = await this.analysisService.getAdTrafficAnalysis(timeRange, tenantId);
    return {
      success: true,
      status: 0,
      data
    };
  }

  @Get('creative-performance')
  @ApiOperation({ summary: '获取广告素材效果分析数据' })
  @ApiQuery({
    name: 'tenantId',
    required: false,
    description: '租户ID，如果不传则获取全局数据',
  })
  @ApiResponse({
    status: 200,
    description: '成功获取广告素材效果分析数据',
    type: CreativePerformanceResponseDto,
  })
  async getCreativePerformance(@Query('tenantId') tenantId?: string) {
    const data = await this.analysisService.getCreativePerformance(tenantId);
    return {
      success: true,
      status: 0,
      data
    };
  }

  @Get('channel-analysis')
  @ApiOperation({ summary: '获取广告投放渠道分析数据' })
  @ApiQuery({
    name: 'timeRange',
    enum: ChannelTimeRangeEnum,
    required: false,
    description: '时间范围：week-周，month-月，quarter-季度',
  })
  @ApiResponse({
    status: 200,
    description: '成功获取广告投放渠道分析数据',
    type: ChannelAnalysisResponseDto,
  })
  async getChannelAnalysis(
    @Query('timeRange') timeRange: ChannelTimeRangeEnum = ChannelTimeRangeEnum.MONTH
  ) {
    const data = await this.analysisService.getChannelAnalysis(timeRange);
    return {
      success: true,
      status: 0,
      data
    };
  }
} 