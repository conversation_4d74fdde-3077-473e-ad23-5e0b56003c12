# 组件路径 /pages/dashboard/tenant-overview/index	

## 前端

- **实际路径**：slash-admin/src/pages/dashboard/tenant-overview/index.tsx
- **主要页面/组件**：
  - index.tsx（主入口，租户概览页，含租户统计、趋势、分布等卡片）
- **主要依赖API定义**：@/api/graphql/tenant.graphql

### 主要功能与数据结构

- 支持租户总览、统计卡片、趋势图、分布图等多维度展示
- 支持按时间、类型等多条件筛选
- 支持分页、模糊搜索、过滤
- 组件化拆分，便于扩展

- **典型数据结构**：
  ```ts
  interface TenantOverview {
    total: number; // 租户总数
    active: number; // 活跃租户数
    typeStats: Array<{ type: string; count: number }>;
    trend: Array<{ date: string; count: number }>;
    topTenants: Array<{ id: string; name: string; type: string; logo?: string }>;
    ... // 其他业务字段
  }
  ```

---

## 后端

- **主要接口**：GraphQL接口与前端一致，详见租户管理模块
- **数据库表结构**（tenant，需结合实际后端实现）：

| 字段名      | 类型         | 说明                       | 约束         |
|-------------|-------------|----------------------------|--------------|
| id          | uuid        | 主键，租户唯一标识         | PRIMARY KEY  |
| name        | varchar     | 租户名称                   | NOT NULL     |
| type        | varchar     | 类型                       | 可空         |
| logo        | varchar     | logo图片URL                | 可空         |
| createdAt   | timestamp   | 创建时间                   | 默认now()    |
| updatedAt   | timestamp   | 更新时间                   | 自动生成     |
| ...         | ...         | 其他业务字段               | ...          |

---

## 前后端数据流

```mermaid
sequenceDiagram
    前端->>后端: query tenantOverview / tenants / ...
    后端->>数据库: 查询 tenant 表及相关统计
    数据库-->>后端: 返回租户统计/概览数据
    后端-->>前端: 返回租户概览数据
```

---

## 变更历史/注意事项

- 租户名称需唯一，所有操作需校验唯一性
- 支持多类型租户扩展（type 字段）
- 删除租户时需校验是否被用户、资源等引用
- logo字段为图片URL，前端需校验格式
- 复杂业务字段建议在表结构中用jsonb扩展	