const { Client } = require('pg');

const client = new Client({
    host: "pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com",
    port: 5432,
    user: "yeeu",
    password: "Yy&20240505",
    database: "ad_auto",
});

async function main() {
    try {
        await client.connect();
        // 清空表数据
        await client.query('DELETE FROM materia_create');
        console.log('materia_create 表数据已全部清除。');
    } catch (err) {
        console.error('清除出错:', err);
    } finally {
        await client.end();
    }
}

main();