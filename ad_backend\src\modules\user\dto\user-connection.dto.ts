import { ObjectType, Field, Int } from '@nestjs/graphql';
import { UserDTO } from './user.dto';

@ObjectType('UserPageInfo')
export class UserPageInfo {
    @Field(() => Boolean)
    hasNextPage: boolean;

    @Field(() => Boolean)
    hasPreviousPage: boolean;
}

@ObjectType()
export class UserConnection {
    @Field(() => [UserDTO])
    nodes: UserDTO[];

    @Field(() => Int)
    totalCount: number;

    @Field(() => UserPageInfo)
    pageInfo: UserPageInfo;
} 