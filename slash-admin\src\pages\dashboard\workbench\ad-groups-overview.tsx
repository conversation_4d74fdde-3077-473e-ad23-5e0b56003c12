import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { Icon } from "@/components/icon";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Progress } from "@/ui/progress";
import { cn } from "@/utils";
import { useQuery } from '@apollo/client';
import { GET_AD_GROUPS_OVERVIEW } from '@/api/adGroupsOverview.graphql';

interface AdGroupItemProps {
  name: string;
  status: "active" | "paused" | "completed";
  budget: string;
  spent: string;
  progress: number;
  ctr: number;
  cvr: number;
  roi: number;
}

const AdGroupItem = ({
  name, status, budget, spent, progress, ctr, cvr, roi
}: AdGroupItemProps) => {
  const { t } = useTranslation();
  const statusColors = {
    active: "bg-green-500",
    paused: "bg-amber-500",
    completed: "bg-blue-500"
  };

  const statusText = {
    active: t('pages.key_2431'),
    paused: t('pages.key_745'),
    completed: t('pages.key_740')
  };

  return (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={cn("w-2 h-2 rounded-full", statusColors[status])} />
          <h3 className="font-medium">{name}</h3>
        </div>
        <span className="text-xs px-2 py-1 rounded-full bg-muted">{statusText[status]}</span>
      </div>

      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">{t('pages.key_2559')}</span>
          <span>{spent} / {budget}</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div>
          <p className="text-xs text-muted-foreground">{t('pages.key_1587')}</p>
          <p className="font-medium">{ctr.toFixed(2)}%</p>
        </div>
        <div>
          <p className="text-xs text-muted-foreground">{t('pages.key_2359')}</p>
          <p className="font-medium">{cvr.toFixed(2)}%</p>
        </div>
        <div>
          <p className="text-xs text-muted-foreground">ROI</p>
          <p className="font-medium">{roi.toFixed(2)}%</p>
        </div>
      </div>
    </div>
  );
};

// 为AdGroupsOverview添加props类型
type AdGroupsOverviewProps = {
  startDate: string;
  endDate: string;
};

const AdGroupsOverview = ({ startDate, endDate }: AdGroupsOverviewProps) => {
  const { t } = useTranslation();
  const { data, loading, error } = useQuery(GET_AD_GROUPS_OVERVIEW, {
    variables: { startDate, endDate },
    fetchPolicy: 'network-only',
  });

  const adGroups = data?.adGroupsOverview?.adGroups || [];
  const distributionData = data?.adGroupsOverview?.distribution || [];

  const pieChartOptions = useChart({
    labels: distributionData.map((item: any) => item.name),
    legend: {
      position: 'bottom',
    },
    tooltip: {
      fillSeriesColor: false,
    },
    stroke: {
      show: false,
    },
    colors: ['#4ade80', '#3b82f6', '#f59e0b', '#a855f7', '#ec4899'],
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
        },
      },
    },
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>{t('pages.key_331')}</div>;

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">{t('pages.key_816')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h3 className="font-medium">{t('pages.key_814')}</h3>
            <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
              {adGroups.map((group: any, index: number) => (
                <AdGroupItem key={index} {...group} />
              ))}
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="font-medium">{t('pages.key_813')}</h3>
            <div className="h-[300px]">
              <Chart
                type="donut"
                series={distributionData.map((item: any) => item.value)}
                options={pieChartOptions}
                height={300}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdGroupsOverview;
