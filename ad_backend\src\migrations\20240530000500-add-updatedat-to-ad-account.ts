import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUpdatedAtToAdAccount20240530000500 implements MigrationInterface {
    name = 'AddUpdatedAtToAdAccount20240530000500'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "updatedAt" TIMESTAMP NOT NULL DEFAULT now();`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "updatedAt";`);
    }
} 