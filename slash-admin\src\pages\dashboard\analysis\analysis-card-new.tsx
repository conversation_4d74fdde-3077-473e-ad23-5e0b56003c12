import { Icon } from "@/components/icon";
import { Card, CardContent } from "@/ui/card";
import { cn } from "@/utils";

interface AnalysisCardProps {
  title: string;
  value: string;
  percentage: number;
  color: string;
  icon: string;
}

export const AnalysisCard = ({ title, value, percentage, color, icon }: AnalysisCardProps) => {
  const isPositive = percentage >= 0;
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">{title}</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-semibold">{value}</p>
              <span className={cn(
                "flex items-center text-xs font-medium",
                isPositive ? "text-green-600" : "text-red-600"
              )}>
                <Icon 
                  icon={isPositive ? "solar:arrow-up-linear" : "solar:arrow-down-linear"} 
                  className="mr-1" 
                  size={14} 
                />
                {Math.abs(percentage)}%
              </span>
            </div>
          </div>
          <div className={cn("p-2 rounded-full", color)}>
            <Icon icon={icon} size={24} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AnalysisCard;
