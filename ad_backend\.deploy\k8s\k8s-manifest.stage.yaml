kind: Service
apiVersion: v1
metadata:
    name: ever-api-starter-kit-stage-api-lb
    annotations:
        # See https://github.com/digitalocean/digitalocean-cloud-controller-manager/blob/master/docs/controllers/services/annotations.md
        service.beta.kubernetes.io/do-loadbalancer-name: 'apistarterkitstage.ever.dev'
        service.beta.kubernetes.io/do-loadbalancer-protocol: 'http2'
        service.beta.kubernetes.io/do-loadbalancer-http2-ports: '443'
        # Replace with your Certificate Id. You can get a list of Ids with 'doctl compute certificate list'
        service.beta.kubernetes.io/do-loadbalancer-certificate-id: 'f7269c22-3c4e-4160-8cab-1f7ec00b11b2'
        service.beta.kubernetes.io/do-loadbalancer-size-slug: 'lb-small'
        service.beta.kubernetes.io/do-loadbalancer-hostname: 'apistarterkitstage.ever.dev'
        service.beta.kubernetes.io/do-loadbalancer-http-idle-timeout-seconds: '600'
        service.beta.kubernetes.io/do-loadbalancer-redirect-http-to-https: 'true'
spec:
    type: LoadBalancer
    selector:
        app: ever-api-starter-kit-stage-api
    ports:
        - name: http
          protocol: TCP
          port: 443
          targetPort: 3005
---
apiVersion: apps/v1
kind: Deployment
metadata:
    name: ever-api-starter-kit-stage-api
spec:
    replicas: 1
    selector:
        matchLabels:
            app: ever-api-starter-kit-stage-api
    template:
        metadata:
            labels:
                app: ever-api-starter-kit-stage-api
        spec:
            containers:
                - name: ever-api-starter-kit-stage-api
                  image: registry.digitalocean.com/ever/ever-api-starter-kit-api-stage:latest
                  env:
                      - name: API_HOST
                        value: 0.0.0.0
                      - name: DEMO
                        value: 'false'
                      - name: NODE_ENV
                        value: 'production'
                      - name: LOG_LEVEL
                        value: 'info'
                      - name: SENTRY_DSN
                        value: '$SENTRY_DSN'
                      - name: API_BASE_URL
                        value: 'https://apistarterkitstage.ever.dev'
                      - name: DB_URI
                        value: '$DB_URI'
                      - name: DB_HOST
                        value: '$DB_HOST'
                      - name: DB_SSL_MODE
                        value: '$DB_SSL_MODE'
                      - name: DB_CA_CERT
                        value: '$DB_CA_CERT'
                      - name: DB_USER
                        value: '$DB_USER'
                      - name: DB_PASS
                        value: '$DB_PASS'
                      - name: DB_TYPE
                        value: '$DB_TYPE'
                      - name: DB_NAME
                        value: '$DB_NAME'
                      - name: DB_PORT
                        value: '$DB_PORT'
                      - name: AWS_ACCESS_KEY_ID
                        value: '$AWS_ACCESS_KEY_ID'
                      - name: AWS_SECRET_ACCESS_KEY
                        value: '$AWS_SECRET_ACCESS_KEY'
                      - name: AWS_REGION
                        value: '$AWS_REGION'
                      - name: AWS_S3_BUCKET
                        value: '$AWS_S3_BUCKET'
                      - name: WASABI_ACCESS_KEY_ID
                        value: '$WASABI_ACCESS_KEY_ID'
                      - name: WASABI_SECRET_ACCESS_KEY
                        value: '$WASABI_SECRET_ACCESS_KEY'
                      - name: WASABI_REGION
                        value: '$WASABI_REGION'
                      - name: WASABI_SERVICE_URL
                        value: '$WASABI_SERVICE_URL'
                      - name: WASABI_S3_BUCKET
                        value: '$WASABI_S3_BUCKET'
                      - name: EXPRESS_SESSION_SECRET
                        value: '$EXPRESS_SESSION_SECRET'
                      - name: JWT_SECRET
                        value: '$JWT_SECRET'
                      - name: JWT_REFRESH_TOKEN_SECRET
                        value: '$JWT_REFRESH_TOKEN_SECRET'
                      - name: JWT_REFRESH_TOKEN_EXPIRATION_TIME
                        value: '$JWT_REFRESH_TOKEN_EXPIRATION_TIME'
                      - name: MAIL_FROM_ADDRESS
                        value: '$MAIL_FROM_ADDRESS'
                      - name: MAIL_HOST
                        value: '$MAIL_HOST'
                      - name: MAIL_PORT
                        value: '$MAIL_PORT'
                      - name: MAIL_USERNAME
                        value: '$MAIL_USERNAME'
                      - name: MAIL_PASSWORD
                        value: '$MAIL_PASSWORD'
                      - name: FILE_PROVIDER
                        value: '$FILE_PROVIDER'
                      - name: UNLEASH_APP_NAME
                        value: '$UNLEASH_APP_NAME'
                      - name: UNLEASH_API_URL
                        value: '$UNLEASH_API_URL'
                      - name: UNLEASH_INSTANCE_ID
                        value: '$UNLEASH_INSTANCE_ID'
                      - name: UNLEASH_REFRESH_INTERVAL
                        value: '$UNLEASH_REFRESH_INTERVAL'
                      - name: UNLEASH_METRICS_INTERVAL
                        value: '$UNLEASH_METRICS_INTERVAL'
                      - name: UNLEASH_API_KEY
                        value: '$UNLEASH_API_KEY'
                      - name: IS_DISABLE_AUTH
                        value: '$IS_DISABLE_AUTH'

                  ports:
                      - containerPort: 3005
                        protocol: TCP
---
kind: Service
apiVersion: v1
metadata:
    name: ever-api-starter-kit-stage-auth-lb
    annotations:
        # See https://github.com/digitalocean/digitalocean-cloud-controller-manager/blob/master/docs/controllers/services/annotations.md
        service.beta.kubernetes.io/do-loadbalancer-name: 'authstarterkitstage.ever.dev'
        service.beta.kubernetes.io/do-loadbalancer-protocol: 'http2'
        service.beta.kubernetes.io/do-loadbalancer-http2-ports: '443'
        # Replace with your Certificate Id. You can get a list of Ids with 'doctl compute certificate list'
        service.beta.kubernetes.io/do-loadbalancer-certificate-id: 'f7269c22-3c4e-4160-8cab-1f7ec00b11b2'
        service.beta.kubernetes.io/do-loadbalancer-size-slug: 'lb-small'
        service.beta.kubernetes.io/do-loadbalancer-hostname: 'authstarterkitstage.ever.dev'
        service.beta.kubernetes.io/do-loadbalancer-http-idle-timeout-seconds: '60'
        service.beta.kubernetes.io/do-loadbalancer-redirect-http-to-https: 'true'
spec:
    type: LoadBalancer
    selector:
        app: ever-api-starter-kit-stage-auth
    ports:
        - name: http
          protocol: TCP
          port: 443
          targetPort: 3567
---
apiVersion: apps/v1
kind: Deployment
metadata:
    name: ever-api-starter-kit-stage-auth
spec:
    replicas: 1
    selector:
        matchLabels:
            app: ever-api-starter-kit-stage-auth
    template:
        metadata:
            labels:
                app: ever-api-starter-kit-stage-auth
        spec:
            containers:
                - name: ever-api-starter-kit-stage-auth
                  image: supertokens/supertokens-postgresql:5.0
                  # If POSTGRESQL_USER, POSTGRESQL_PASSWORD, POSTGRESQL_PASSWORD_FILE and POSTGRESQL_CONNECTION_URI are not provided,
                  # then SuperTokens will use an in memory database.
                  # https://github.com/supertokens/supertokens-docker-postgresql
                  env:
                      - name: POSTGRESQL_CONNECTION_URI
                        value: '$AUTH_DB_URI'
                  #                      - name: POSTGRESQL_DATABASE_NAME
                  #                        value: 'ever_api_starter_kit_auth'
                  #                      - name: POSTGRESQL_HOST
                  #                        value: '$DB_HOST'
                  #                      - name: POSTGRESQL_SSL_MODE
                  #                        value: '$DB_SSL_MODE'
                  #                      - name: POSTGRESQL_CA_CERT
                  #                        value: '$DB_CA_CERT'
                  #                      - name: POSTGRESQL_USER
                  #                        value: '$DB_USER'
                  #                      - name: POSTGRESQL_PASSWORD
                  #                        value: '$DB_PASS'
                  #                      - name: POSTGRESQL_PORT
                  #                        value: '$DB_PORT'

                  ports:
                      - containerPort: 3567
                        protocol: TCP
