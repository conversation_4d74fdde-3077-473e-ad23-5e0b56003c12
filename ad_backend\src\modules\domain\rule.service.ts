import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Rule } from './rule.entity';

@Injectable()
export class RuleService {
    constructor(
        @InjectRepository(Rule)
        private readonly ruleRepository: Repository<Rule>,
    ) { }

    findAll(): Promise<Rule[]> {
        return this.ruleRepository.find();
    }

    findById(id: string): Promise<Rule> {
        return this.ruleRepository.findOneBy({ id });
    }

    create(input: Partial<Rule>): Promise<Rule> {
        const rule = this.ruleRepository.create(input);
        return this.ruleRepository.save(rule);
    }

    async delete(id: string): Promise<boolean> {
        await this.ruleRepository.delete(id);
        return true;
    }

    async update(data: { id: string } & Partial<Rule>) {
        const rule = await this.ruleRepository.findOneBy({ id: data.id });
        if (!rule) throw new Error('Rule not found');
        Object.assign(rule, data);
        return this.ruleRepository.save(rule);
    }
} 