import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';

@Entity('report')
export class Report {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '报表类型（account/campaign/adset/ad/audience等）' })
  @Column({ length: 32 })
  type: string;

  @ApiProperty({ description: '报表日期' })
  @Column({ length: 16 })
  date: string;

  @ApiProperty({ description: '关联对象ID' })
  @Column({ length: 64 })
  objectId: string;

  @ApiProperty({ description: '数据内容（JSON）' })
  @Column({ type: 'jsonb' })
  data: any;

  @ApiProperty({ description: '租户' })
  @ManyToOne(() => Tenant, { nullable: false })
  tenant: Tenant;
}
