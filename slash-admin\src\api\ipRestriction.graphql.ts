import { gql } from '@apollo/client';

export const GET_IPRESTRICTION = gql`
query GetipRestriction(
  $paging: OffsetPaging
  $filter: ipRestrictionFilter
  $sorting: [ipRestrictionSort!]
) {
  ipRestrictions(
    paging: $paging
    filter: $filter
    sorting: $sorting
  ) {
    nodes {
      id
      ip
      status
      operator
      updatedAt
    }
    totalCount  
  }
}
`;

export const CREATE_IPRESTRICTION_MUTATION = gql`
mutation CreateOneIpRestriction($ip: String, $status: String, $operator: String, $userId: String) {
  createOneIpRestriction(ip: $ip, status: $status, operator: $operator, userId: $userId) {
      id
      ip
      userId
      status
      operator
  }
}
`;

export const UPDATE_IP_RESTRICTION = gql`
  mutation UpdateIpRestriction($input: UpdateOneIpRestrictionInput!) {
    updateOneIpRestriction(input: $input) {
      id
      ip
      userId
      operator
      status
      updatedAt
    }
  }
`;

export const DELETE_IP_RESTRICTION = gql`
  mutation deleteOneIpRestriction($input: DeleteOneIpRestrictionInput!) {
    deleteOneIpRestriction(input: $input) {
      id
    }
  }
`;

