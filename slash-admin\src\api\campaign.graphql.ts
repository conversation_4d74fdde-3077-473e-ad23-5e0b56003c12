import { gql } from '@apollo/client';

export const GET_CAMPAIGN_LIST = gql`
  query GetCampaigns {
    campaigns {
      id
      name
      objective
      status
      specialAdCategories
      accountId
      facebookCampaignId
      syncStatus
      syncError
      createdAt
      updatedAt
    }
  }
`;

export const CREATE_CAMPAIGN = gql`
  mutation CreateCampaign($input: CreateCampaignInputX!) {
    createCampaign(input: $input) {
      id
    }
  }
`;

export const UPDATE_CAMPAIGN = gql`
  mutation UpdateCampaign($id: ID!, $input: CreateCampaignInputX!) {
    updateCampaign(id: $id, input: $input) {
      id
    }
  }
`;

export const DELETE_CAMPAIGN = gql`
  mutation DeleteCampaign($id: ID!) {
    deleteCampaign(id: $id)
  }
`;

export const GET_AD_CAMPAIGN_LIST = gql`
  query GetAdCampaigns {
    adCampaigns {
      edges {
        node {
          id
          name
          platform
          status
          campaignId
          adAccount { accountId account }
          tenant { id }
          budget
          startDate
          endDate
          tags
          description
        }
      }
    }
  }
`;

export const REFRESH_CAMPAIGN = gql`
  mutation RefreshCampaign($id: ID!) {
    refreshCampaignFromFacebook(id: $id) {
      id
      name
      objective
      status
      specialAdCategories
      accountId
      facebookCampaignId
      syncStatus
      syncError
      createdAt
      updatedAt
    }
  }
`; 