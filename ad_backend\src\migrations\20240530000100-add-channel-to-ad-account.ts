import { MigrationInterface, QueryRunner } from "typeorm";

export class AddChannelToAdAccount20240530000100 implements MigrationInterface {
    name = 'AddChannelToAdAccount20240530000100'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" ADD COLUMN "channel" character varying(128);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ad_account" DROP COLUMN "channel";`);
    }
} 