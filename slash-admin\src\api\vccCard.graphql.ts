import { gql } from '@apollo/client';

// 获取VCC卡片列表
export const GET_VCC_CARDS = gql`
  query GetVccCards($filter: VccCardFilterInput) {
    vccCards(filter: $filter) {
      id
      channel
      country
      cardHolder
      countryCode
      cardNumber
      balance
      consumption
      boundAdAccount
      boundAdAccountIds
      group
      transactionCount
      adAccountStatus
      expiryMonth
      expiryYear
      cvv
      zipCode
      usedCount
      bindCount
      totalAdAccounts
      limitCount
      status
      remark
      createdAt
      updatedAt
    }
  }
`;

// 获取单个VCC卡片详情
export const GET_VCC_CARD = gql`
  query GetVccCard($id: ID!) {
    vccCard(id: $id) {
      id
      channel
      country
      cardHolder
      countryCode
      cardNumber
      balance
      consumption
      boundAdAccount
      boundAdAccountIds
      group
      transactionCount
      adAccountStatus
      expiryMonth
      expiryYear
      cvv
      zipCode
      usedCount
      bindCount
      totalAdAccounts
      limitCount
      status
      remark
      createdAt
      updatedAt
    }
  }
`;

// 创建VCC卡片
export const CREATE_VCC_CARD = gql`
  mutation CreateVccCard($input: CreateVccCardInput!) {
    createVccCard(input: $input) {
      id
      channel
      country
      cardHolder
      countryCode
      cardNumber
      balance
      consumption
      boundAdAccount
      boundAdAccountIds
      group
      transactionCount
      adAccountStatus
      expiryMonth
      expiryYear
      cvv
      zipCode
      usedCount
      bindCount
      totalAdAccounts
      limitCount
      status
      remark
      createdAt
      updatedAt
    }
  }
`;

// 更新VCC卡片
export const UPDATE_VCC_CARD = gql`
  mutation UpdateVccCard($id: ID!, $input: UpdateVccCardInput!) {
    updateVccCard(id: $id, input: $input) {
      id
      channel
      country
      cardHolder
      countryCode
      cardNumber
      balance
      consumption
      boundAdAccount
      boundAdAccountIds
      group
      transactionCount
      adAccountStatus
      expiryMonth
      expiryYear
      cvv
      zipCode
      usedCount
      bindCount
      totalAdAccounts
      limitCount
      status
      remark
      createdAt
      updatedAt
    }
  }
`;

// 删除VCC卡片
export const DELETE_VCC_CARD = gql`
  mutation DeleteVccCard($id: ID!) {
    deleteVccCard(id: $id)
  }
`;

// 绑定广告账户到VCC卡片
export const BIND_AD_ACCOUNT_TO_VCC_CARD = gql`
  mutation BindAdAccountToVccCard($input: BindAdAccountInput!) {
    bindAdAccountToVccCard(input: $input) {
      id
      channel
      country
      cardHolder
      countryCode
      cardNumber
      balance
      consumption
      boundAdAccount
      boundAdAccountIds
      group
      transactionCount
      adAccountStatus
      expiryMonth
      expiryYear
      cvv
      zipCode
      usedCount
      bindCount
      totalAdAccounts
      limitCount
      status
      remark
      createdAt
      updatedAt
    }
  }
`;

// 批量创建VCC卡片
export const CREATE_VCC_CARDS_BATCH = gql`
  mutation CreateVccCardsBatch($inputs: [CreateVccCardInput!]!) {
    createVccCardsBatch(inputs: $inputs) {
      id
      channel
      country
      cardHolder
      countryCode
      cardNumber
      balance
      consumption
      boundAdAccount
      boundAdAccountIds
      group
      transactionCount
      adAccountStatus
      expiryMonth
      expiryYear
      cvv
      zipCode
      usedCount
      bindCount
      totalAdAccounts
      limitCount
      status
      remark
      createdAt
      updatedAt
    }
  }
`;

