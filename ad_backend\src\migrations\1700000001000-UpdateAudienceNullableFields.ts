import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAudienceNullableFields1700000001000 implements MigrationInterface {
    name = 'UpdateAudienceNullableFields1700000001000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 修改字段为可空
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "name" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "platform" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "audienceId" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 恢复字段为非空
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "name" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "platform" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "audienceId" SET NOT NULL`);
    }
} 