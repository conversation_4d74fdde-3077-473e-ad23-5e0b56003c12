# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Tenant {
  name: String!
  logo: String
  type: String
  db_config: JSON
  sync_config: JSON
  plan: String
  features: JSON
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

type Group {
  id: ID
  name: String!
  belongTo: String
  contactInfo: String
  description: String
  memberCount: Int
  permissions: JSON
  status: String!
  createTime: DateTime
  updateTime: DateTime
  tenantId: String
  tenant: Tenant
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type RolePageInfo {
  page: Int!
  limit: Int!
  totalPages: Int!
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}

type Route {
  id: ID!
  parentId: ID
  name: String!
  path: String!
  component: String
  icon: String
  order: Float!
  type: String!
  status: String!
  isHidden: Boolean!
  description: String
  createdAt: DateTime!
  updatedAt: DateTime!
  children: [Route!]
}

type Role {
  id: ID!
  name: String!
  description: String
  status: String!
  order: Int!
  routeIds: [String!]
  routeList: [Route!]
  createdAt: DateTime!
  updatedAt: DateTime!
}

type RolePagination {
  items: [Role!]!
  pageInfo: RolePageInfo!
  totalCount: Int!
}

type User {
  id: ID
  username: String!
  email: String!
  password: String
  avatar: String
  fullName: String
  phone: String
  address: String
  registerIp: String
  status: UserStatus!
  roles: [Role!]
  groups: [Group!]
  roleIds: [ID!]
  groupIds: [ID!]
  createdAt: DateTime
  updatedAt: DateTime
  tenantId: String
  tenant: Tenant
  tenantName: String
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BLOCKED
}

type UserPageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}

type UserConnection {
  nodes: [User!]!
  totalCount: Int!
  pageInfo: UserPageInfo!
}

type LoginResponse {
  userId: String!
  accessToken: String!
  username: String!
  roles: [String!]
}

type RegisterResponse {
  userId: String!
  accessToken: String!
  username: String!
}

type AuthUserResponse {
  user: User!
  accessToken: String!
}

type Employee {
  id: ID
  firstName: String
  lastName: String
  name: String
  createdAt: DateTime
  updatedAt: DateTime
  isActive: Boolean!
  isArchived: Boolean!
}

type DeleteManyResponse {
  """The number of records deleted."""
  deletedCount: Int!
}

type UpdateManyResponse {
  """The number of records updated."""
  updatedCount: Int!
}

type EmployeeEdge {
  """The node containing the Employee"""
  node: Employee!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

"""Cursor for paging through collections"""
scalar ConnectionCursor

type PageInfo {
  """true if paging forward and there are more records."""
  hasNextPage: Boolean

  """true if paging backwards and there are more records."""
  hasPreviousPage: Boolean

  """The cursor of the first returned record."""
  startCursor: ConnectionCursor

  """The cursor of the last returned record."""
  endCursor: ConnectionCursor
}

type EmployeeConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [EmployeeEdge!]!

  """Fetch total count of records"""
  totalCount: Int!
}

type EmployeeAggregateGroupBy {
  id: ID
  firstName: String
  lastName: String
  createdAt(by: GroupBy! = DAY): DateTime
  updatedAt(by: GroupBy! = DAY): DateTime
  isActive: Boolean
  isArchived: Boolean
}

"""Group by"""
enum GroupBy {
  DAY
  WEEK
  MONTH
  YEAR
}

type EmployeeCountAggregate {
  id: Int
  firstName: Int
  lastName: Int
  createdAt: Int
  updatedAt: Int
  isActive: Int
  isArchived: Int
}

type EmployeeMinAggregate {
  id: ID
  firstName: String
  lastName: String
  createdAt: DateTime
  updatedAt: DateTime
}

type EmployeeMaxAggregate {
  id: ID
  firstName: String
  lastName: String
  createdAt: DateTime
  updatedAt: DateTime
}

type EmployeeAggregateResponse {
  groupBy: EmployeeAggregateGroupBy
  count: EmployeeCountAggregate
  min: EmployeeMinAggregate
  max: EmployeeMaxAggregate
}

type EmployeeDeleteResponse {
  id: ID
  firstName: String
  lastName: String
  name: String
  createdAt: DateTime
  updatedAt: DateTime
  isActive: Boolean
  isArchived: Boolean
}

type AntiBlock {
  id: Int
  userId: String
  url: String
  status: String
  name: String
  relatedDomain: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type AntiBlockDeleteResponse {
  id: Int
  userId: String
  url: String
  status: String
  name: String
  relatedDomain: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type OffsetPageInfo {
  """true if paging forward and there are more records."""
  hasNextPage: Boolean

  """true if paging backwards and there are more records."""
  hasPreviousPage: Boolean
}

type AntiBlockConnection {
  """Paging information"""
  pageInfo: OffsetPageInfo!

  """Array of nodes."""
  nodes: [AntiBlock!]!
}

type ipRestriction {
  id: Int
  ip: String
  userId: String
  status: String
  operator: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type IpRestrictionConnection {
  """Paging information"""
  pageInfo: OffsetPageInfo!

  """Array of nodes."""
  nodes: [ipRestriction!]!

  """Fetch total count of records"""
  totalCount: Int!
}

type ipRestrictionAggregateGroupBy {
  id: Int
  ip: String
  userId: String
  status: String
  tenantId: String
  createdAt(by: GroupBy! = DAY): DateTime
  updatedAt(by: GroupBy! = DAY): DateTime
}

type ipRestrictionCountAggregate {
  id: Int
  ip: Int
  userId: Int
  status: Int
  tenantId: Int
  createdAt: Int
  updatedAt: Int
}

type ipRestrictionSumAggregate {
  id: Float
}

type ipRestrictionAvgAggregate {
  id: Float
}

type ipRestrictionMinAggregate {
  id: Int
  ip: String
  userId: String
  status: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type ipRestrictionMaxAggregate {
  id: Int
  ip: String
  userId: String
  status: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type ipRestrictionAggregateResponse {
  groupBy: ipRestrictionAggregateGroupBy
  count: ipRestrictionCountAggregate
  sum: ipRestrictionSumAggregate
  avg: ipRestrictionAvgAggregate
  min: ipRestrictionMinAggregate
  max: ipRestrictionMaxAggregate
}

type IpRestrictionDeleteResponse {
  id: Int
  ip: String
  userId: String
  status: String
  operator: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type materialManagement {
  id: Int
  fileName: String
  tags: String
  group: String
  facebookImageId: String
  facebookVedioId: String
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: String
  tenantId: String
  notes: String
  createdAt: DateTime
  updatedAt: DateTime
}

type AdAccount {
  id: ID!
  platform: String!
  accountId: String!
  account: String!
  login: String
  password: String
  accessToken: String
  tenant: Tenant!
  raw: JSON
  status: String!
  riskLevel: String
  group: String
  oauth: JSON
  createdAt: DateTime
  tag: String
  channel: String
  holder: String
  adNumber: String
  remark: String
  updatedAt: DateTime
  fbStatus: String
}

type CreativeAnalysisDTO {
  typeTrends: [CreativeTrendDTO!]!
  sizePerformance: [PieDataDTO!]!
  elementPerformance: [PieDataDTO!]!
}

type CreativeTrendDTO {
  type: String!
  data: [Int!]!
}

type PieDataDTO {
  name: String!
  value: Int!
}

type CreativeAnalysisResult {
  typeTrends: [CreativeTrendDTO!]!
  sizePerformance: [PieDataDTO!]!
  elementPerformance: [PieDataDTO!]!
}

type MaterialManagementConnection {
  """Paging information"""
  pageInfo: OffsetPageInfo!

  """Array of nodes."""
  nodes: [materialManagement!]!

  """Fetch total count of records"""
  totalCount: Int!
}

type materialManagementAggregateGroupBy {
  id: Int
  fileName: String
  tags: String
  group: String
  facebookImageId: String
  facebookVedioId: String
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: String
  tenantId: String
  notes: String
  createdAt(by: GroupBy! = DAY): DateTime
  updatedAt(by: GroupBy! = DAY): DateTime
}

type materialManagementCountAggregate {
  id: Int
  fileName: Int
  tags: Int
  group: Int
  facebookImageId: Int
  facebookVedioId: Int
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: Int
  tenantId: Int
  notes: Int
  createdAt: Int
  updatedAt: Int
}

type materialManagementSumAggregate {
  id: Float
  adCount: Float
  activeAdCount: Float
  bannedCount: Float
  spend: Float
  conversion: Float
  commission: Float
}

type materialManagementAvgAggregate {
  id: Float
  adCount: Float
  activeAdCount: Float
  bannedCount: Float
  spend: Float
  conversion: Float
  commission: Float
}

type materialManagementMinAggregate {
  id: Int
  fileName: String
  tags: String
  group: String
  facebookImageId: String
  facebookVedioId: String
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: String
  tenantId: String
  notes: String
  createdAt: DateTime
  updatedAt: DateTime
}

type materialManagementMaxAggregate {
  id: Int
  fileName: String
  tags: String
  group: String
  facebookImageId: String
  facebookVedioId: String
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: String
  tenantId: String
  notes: String
  createdAt: DateTime
  updatedAt: DateTime
}

type materialManagementAggregateResponse {
  groupBy: materialManagementAggregateGroupBy
  count: materialManagementCountAggregate
  sum: materialManagementSumAggregate
  avg: materialManagementAvgAggregate
  min: materialManagementMinAggregate
  max: materialManagementMaxAggregate
}

type Campaign {
  id: ID!
  name: String!
  objective: String!
  status: String!
  specialAdCategories: [String!]!
  facebookCampaignId: String
  createdAt: DateTime!
  updatedAt: DateTime!
  adAccount: AdAccount!
  accountId: String!
  syncStatus: String
  syncError: String
}

type AudienceRegion {
  id: String
  name: String
}

type AudienceCity {
  id: String
  name: String
}

type AudienceGeoLocation {
  countries: [String!]
  regions: [AudienceRegion!]
  cities: [AudienceCity!]
}

type AudienceInterest {
  id: String!
  name: String
}

type AudienceBehavior {
  id: String!
  name: String
}

type AudienceDTO {
  id: ID!
  audienceId: String!
  name: String
  geo_locations: AudienceGeoLocation
  excluded_geo_locations: AudienceGeoLocation
  locales: [Float!]
  gender: Float
  age_min: Float
  age_max: Float
  interests: [AudienceInterest!]
  behaviors: [AudienceBehavior!]
  custom_audiences: JSON
  notes: String
  approximateCount: Float
  raw: JSON
  platform: [String!]
  tenantId: String!
  tenantName: String
}

type AudiencePageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

type AudienceCustomEdge {
  node: AudienceDTO!
  cursor: String!
}

type Option {
  id: ID!
  name: String!
}

type Pixel {
  id: ID!
  pixelId: String!
  name: String!
}

type TenantDTO {
  id: ID!
  name: String!
  logo: String
  type: String!
  db_config: JSON
  sync_config: JSON
}

type TenantPage {
  nodes: [TenantDTO!]!
  total: Int!
}

type TenantResourceStats {
  type: String!
  count: Int!
}

type TenantResourceDetail {
  type: String!
  name: String!
  status: String
  group: String
  createdAt: String
  remark: String
}

type TenantResourceDetailPage {
  total: Int!
  nodes: [TenantResourceDetail!]!
}

type TenantTrendPoint {
  date: String!
  impressions: Float
  clicks: Float
  spend: Float
  conversion: Float
}

type GlobalResourceStats {
  type: String!
  count: Int!
}

type GlobalResourceDetail {
  type: String!
  name: String!
  status: String
  group: String
  createdAt: String
  remark: String
  tenantName: String
  tenantId: String
}

type GlobalResourceDetailPage {
  total: Int!
  nodes: [GlobalResourceDetail!]!
}

type GlobalTrendPoint {
  date: String!
  spend: Float
  conversion: Float
  tenantCount: Float
  userCount: Float
  adCount: Float
}

type ChannelDistributionDTO {
  channel: String!
  channelName: String!
  count: Int!
  totalImpressions: Int
  totalClicks: Int
  totalConversion: Int
  totalSpend: Int
}

type AudienceOption {
  id: String!
  name: String
}

type AudienceAnalysisItem {
  key: String!
  label: String!
  value: Float!
  source: String
}

type AudienceAnalysisResult {
  total: Float!
  gender: [AudienceAnalysisItem!]!
  age: [AudienceAnalysisItem!]!
  platform: [AudienceAnalysisItem!]!
  region: [AudienceAnalysisItem!]!
  interest: [AudienceAnalysisItem!]!
}

type AdAccountDTO {
  """内部UUID"""
  id: ID!
  accountId: ID!
  platform: String!
  account: String!
  password: String
  accessToken: String
  tenant: TenantDTO!
  raw: JSON
  status: String!
  riskLevel: String
  group: String
  oauth: JSON
  createdAt: DateTime
  holder: String
  adNumber: String
  remark: String
  updatedAt: DateTime
  tag: String
  channel: String
  fbStatus: String
}

type AdAccountUploadResult {
  account: String!
  status: String!
}

type AdCampaign {
  id: ID!
  platform: String!
  campaignId: String!
  name: String!
  adAccount: AdAccountDTO!
  tenant: TenantDTO!
  raw: JSON
  status: String!
  budget: Float!
  startDate: DateTime!
  endDate: DateTime!
  tags: [String!]
  description: String
}

type AdCreative {
  id: ID!
  platform: String!
  creativeId: String!
  name: String!
  adAccount: AdAccountDTO!
  adCampaign: AdCampaign!
  tenant: TenantDTO!
  raw: JSON
  status: String!
}

type AdDTO {
  id: ID!
  name: String!
  adset_id: String!
  campaign_id: String
  account_id: String!
  account_name: String
  status: String!
  creative: JSON
  tracking_specs: JSON
  bid_amount: Float
  effective_status: String
  configured_status: String
  created_time: String
  updated_time: String
  adAccount: AdAccountDTO!
  adCampaign: AdCampaign!
  adCreative: AdCreative
  tenant: TenantDTO!
  raw: JSON
  insight: JSON
  adId: String
  adset_name: String
  campaign_name: String
}

type CampaignDTO {
  id: ID!
  name: String!
  objective: String!
  status: String!
  specialAdCategories: [String!]!
  facebookCampaignId: String
  accountId: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  syncStatus: String
  syncError: String
  adAccount: AdAccountDTO
}

type AdSet {
  id: ID!
  platform: String!
  adsetId: String!
  name: String!
  adAccount: AdAccountDTO!
  adCampaign: CampaignDTO!
  tenant: TenantDTO!
  raw: JSON
  status: String!
  audience: AudienceDTO!
  dailyBudget: Float!
  startTime: DateTime!
  endTime: DateTime
  optimizationGoal: String!
  billingEvent: String!
}

type Material {
  id: ID!
  type: String!
  name: String!
  url: String
  content: String
  status: String!
  tenant: TenantDTO!
  raw: JSON
}

type Vcc {
  id: ID!
  cardNumber: String!
  cardHolder: String!
  expiry: String!
  cvv: String!
  bindAccount: String
  status: String!
  tenant: TenantDTO!
  raw: JSON
}

type TestOutput {
  name: String!
  platform: String!
}

type LandingPage {
  id: ID!
  code: String!
  title: String!
  url: String!
  slogan: String!
  appIcon: [String!]
  background: [String!]
  description: String
  star: Float!
  promoImages: [String!]
  creator: String!
  createdAt: DateTime!
  tenantId: String!
}

type PromotionDto {
  id: ID!
  code: String!
  platform: String!
  landingPageCode: String!
  landingPageUrl: String!
  status: String!
  createdAt: DateTime!
  creator: String
  platformParams: JSON
  landingPageName: String
  tenantId: String!
  appDomain: String
}

type PromotionDetailDto {
  promotion: PromotionDto!
  landingPage: LandingPage
}

type FacebookApp {
  id: ID!
  name: String!
  link: String
}

type PixelOutput {
  id: String!
  name: String!
}

type RuleCondition {
  field: String!
  operator: String!
  value: String!
  unit: String
}

type Rule {
  id: ID!
  name: String!
  conditions: [RuleCondition!]!
  action: String!
  object: String!
  scheduleType: String
  timeRange: String
  status: String
  custom_schedule: [String!]
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Domain {
  id: ID!
  name: String!
  tenantId: String!
  status: String!
  sslStatus: String!
  cnameTarget: String!
  cfId: String
  error: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

type AdMetricsDTO {
  impressions: Int!
  clicks: Int!
  conversions: Int!
  cost: Float!
}

type CampaignOverviewDTO {
  activeCount: Int!
  totalSpend: Float!
  totalConversions: Int!
  avgRoi: Float!
  recentCampaigns: [CampaignOverviewItemDTO!]!
}

type CampaignOverviewItemDTO {
  name: String!
  status: String!
  budget: Float!
  spent: Float!
  progress: Int!
  impressions: Int!
  clicks: Int!
  conversions: Int!
}

type AutoOptimizationDTO {
  status: String!
  progress: Int!
  lastOptimized: String!
  recommendations: Int!
  potentialSavings: String
  potentialImprovement: String
  potentialReach: String
}

type BudgetUtilizationDTO {
  platformBudgets: [PlatformBudgetDTO!]!
  monthlyTrend: [MonthlyBudgetTrendDTO!]!
}

type PlatformBudgetDTO {
  name: String!
  allocated: Float!
  spent: Float!
  percentage: Int!
}

type MonthlyBudgetTrendDTO {
  month: String!
  allocated: Float!
  spent: Float!
}

type AdGroupOverviewItemDTO {
  name: String!
  status: String!
  budget: Float!
  spent: Float!
  progress: Int!
  ctr: Float!
  cvr: Float!
  roi: Float!
}

type AdGroupsOverviewDTO {
  adGroups: [AdGroupOverviewItemDTO!]!
  distribution: [PieDataDTO!]!
}

type PlatformDistributionItem {
  name: String!
  value: Float!
}

type PlatformPerformance {
  platforms: [String!]!
  ctr: [Float!]!
  cvr: [Float!]!
  cpc: [Float!]!
  roi: [Float!]!
}

type AdPlatformDistributionDTO {
  platformDistribution: [PlatformDistributionItem!]!
  platformPerformance: PlatformPerformance!
}

type DistributionItem {
  name: String!
  value: Int!
}

type AudienceInsightsDTO {
  ageDistribution: [DistributionItem!]!
  genderDistribution: [DistributionItem!]!
  locationDistribution: [DistributionItem!]!
  interestDistribution: [DistributionItem!]!
}

type AdDTODeleteResponse {
  id: ID
  name: String
  adset_id: String
  campaign_id: String
  account_id: String
  account_name: String
  status: String
  creative: JSON
  tracking_specs: JSON
  bid_amount: Float
  effective_status: String
  configured_status: String
  created_time: String
  updated_time: String
  adAccount: AdAccountDTO
  adCampaign: AdCampaign
  adCreative: AdCreative
  tenant: TenantDTO
  raw: JSON
  insight: JSON
  adId: String
  adset_name: String
  campaign_name: String
}

type AdDTOEdge {
  """The node containing the AdDTO"""
  node: AdDTO!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type AdDTOConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [AdDTOEdge!]!
}

type AdCampaignDeleteResponse {
  id: ID
  platform: String
  campaignId: String
  name: String
  adAccount: AdAccountDTO
  tenant: TenantDTO
  raw: JSON
  status: String
  budget: Float
  startDate: DateTime
  endDate: DateTime
  tags: [String!]
  description: String
}

type AdCampaignEdge {
  """The node containing the AdCampaign"""
  node: AdCampaign!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type AdCampaignConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [AdCampaignEdge!]!
}

type AdCreativeDeleteResponse {
  id: ID
  platform: String
  creativeId: String
  name: String
  adAccount: AdAccountDTO
  adCampaign: AdCampaign
  tenant: TenantDTO
  raw: JSON
  status: String
}

type AdCreativeEdge {
  """The node containing the AdCreative"""
  node: AdCreative!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type AdCreativeConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [AdCreativeEdge!]!
}

type AdSetDeleteResponse {
  id: ID
  platform: String
  adsetId: String
  name: String
  adAccount: AdAccountDTO
  adCampaign: CampaignDTO
  tenant: TenantDTO
  raw: JSON
  status: String
  audience: AudienceDTO
  dailyBudget: Float
  startTime: DateTime
  endTime: DateTime
  optimizationGoal: String
  billingEvent: String
}

type AdSetEdge {
  """The node containing the AdSet"""
  node: AdSet!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type AdSetConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [AdSetEdge!]!
}

type AudienceDTODeleteResponse {
  id: ID
  audienceId: String
  name: String
  geo_locations: AudienceGeoLocation
  excluded_geo_locations: AudienceGeoLocation
  locales: [Float!]
  gender: Float
  age_min: Float
  age_max: Float
  interests: [AudienceInterest!]
  behaviors: [AudienceBehavior!]
  custom_audiences: JSON
  notes: String
  approximateCount: Float
  raw: JSON
  platform: [String!]
  tenantId: String
  tenantName: String
}

type AudienceDTOEdge {
  """The node containing the AudienceDTO"""
  node: AudienceDTO!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type AudienceDTOConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [AudienceDTOEdge!]!
}

type MaterialDeleteResponse {
  id: ID
  type: String
  name: String
  url: String
  content: String
  status: String
  tenant: TenantDTO
  raw: JSON
}

type MaterialEdge {
  """The node containing the Material"""
  node: Material!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type MaterialConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [MaterialEdge!]!
}

type VccDeleteResponse {
  id: ID
  cardNumber: String
  cardHolder: String
  expiry: String
  cvv: String
  bindAccount: String
  status: String
  tenant: TenantDTO
  raw: JSON
}

type VccEdge {
  """The node containing the Vcc"""
  node: Vcc!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type VccConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [VccEdge!]!
}

type MaterialManagementDeleteResponse {
  id: Int
  fileName: String
  tags: String
  group: String
  facebookImageId: String
  facebookVedioId: String
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: String
  tenantId: String
  notes: String
  createdAt: DateTime
  updatedAt: DateTime
}

type materiaCreate {
  id: Int
  userId: String!
  name: String!
  materials: Float!
  pageLading: String!
  createId: String
  slogan: String
  account: String!
  pageId: String!
  tenantId: String!
  createdAt: DateTime
  updatedAt: DateTime
}

type MateriaCreateConnection {
  """Paging information"""
  pageInfo: OffsetPageInfo!

  """Array of nodes."""
  nodes: [materiaCreate!]!

  """Fetch total count of records"""
  totalCount: Int!
}

type materiaCreateAggregateGroupBy {
  id: Int
  userId: String
  name: String
  materials: Float
  pageLading: String
  createId: String
  slogan: String
  account: String
  pageId: String
  tenantId: String
  createdAt(by: GroupBy! = DAY): DateTime
  updatedAt(by: GroupBy! = DAY): DateTime
}

type materiaCreateCountAggregate {
  id: Int
  userId: Int
  name: Int
  materials: Int
  pageLading: Int
  createId: Int
  slogan: Int
  account: Int
  pageId: Int
  tenantId: Int
  createdAt: Int
  updatedAt: Int
}

type materiaCreateSumAggregate {
  id: Float
  materials: Float
}

type materiaCreateAvgAggregate {
  id: Float
  materials: Float
}

type materiaCreateMinAggregate {
  id: Int
  userId: String
  name: String
  materials: Float
  pageLading: String
  createId: String
  slogan: String
  account: String
  pageId: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type materiaCreateMaxAggregate {
  id: Int
  userId: String
  name: String
  materials: Float
  pageLading: String
  createId: String
  slogan: String
  account: String
  pageId: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type materiaCreateAggregateResponse {
  groupBy: materiaCreateAggregateGroupBy
  count: materiaCreateCountAggregate
  sum: materiaCreateSumAggregate
  avg: materiaCreateAvgAggregate
  min: materiaCreateMinAggregate
  max: materiaCreateMaxAggregate
}

type MateriaCreateDeleteResponse {
  id: Int
  userId: String
  name: String
  materials: Float
  pageLading: String
  createId: String
  slogan: String
  account: String
  pageId: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type MetricData {
  value: Int!
  percentage: Float!
}

type OverviewCardsResponse {
  impressions: MetricData!
  clicks: MetricData!
  conversions: MetricData!
  spend: MetricData!
}

type BarData {
  impressions: [Int!]!
  clicks: [Int!]!
  conversions: [Int!]!
}

type LineData {
  ctr: [Float!]!
  cvr: [Float!]!
}

type AdPerformanceAnalysisResponse {
  barData: BarData!
  lineData: LineData!
  timeLabels: [String!]!
}

type AdTrafficAnalysisResponse {
  spend: [Float!]!
  conversions: [Int!]!
  clicks: [Int!]!
  impressions: [Int!]!
  timeLabels: [String!]!
}

type CreativeItem {
  id: Int!
  name: String!
  impressions: Int!
  clicks: Int!
  ctr: Float!
  conversions: Int!
  cvr: Float!
}

type CreativePerformanceResponse {
  images: [CreativeItem!]!
  videos: [CreativeItem!]!
  carousels: [CreativeItem!]!
}

type ChannelMetric {
  channel: String!
  impressions: Int!
  clicks: Int!
  conversions: Int!
  cost: Float!
  ctr: Float!
  cvr: Float!
  cpc: Float!
  roi: Float!
}

type ChannelAnalysisResponse {
  channels: [ChannelMetric!]!
  channelNames: [String!]!
  impressionsData: [Int!]!
  roiData: [Float!]!
}

type GroupDeleteResponse {
  id: ID
  name: String
  belongTo: String
  contactInfo: String
  description: String
  memberCount: Int
  permissions: JSON
  status: String
  createTime: DateTime
  updateTime: DateTime
  tenantId: String
  tenant: Tenant
}

type GroupConnection {
  """Paging information"""
  pageInfo: OffsetPageInfo!

  """Array of nodes."""
  nodes: [Group!]!

  """Fetch total count of records"""
  totalCount: Int!
}

type RouteDeleteResponse {
  id: ID
  parentId: ID
  name: String
  path: String
  component: String
  icon: String
  order: Float
  type: String
  status: String
  isHidden: Boolean
  description: String
  createdAt: DateTime
  updatedAt: DateTime
  children: [Route!]
}

type RouteEdge {
  """The node containing the Route"""
  node: Route!

  """Cursor for this node."""
  cursor: ConnectionCursor!
}

type RouteConnection {
  """Paging information"""
  pageInfo: PageInfo!

  """Array of edges."""
  edges: [RouteEdge!]!

  """Fetch total count of records"""
  totalCount: Int!
}

type RouteAggregateGroupBy {
  id: ID
  parentId: ID
  name: String
  path: String
  component: String
  icon: String
  order: Float
  type: String
  status: String
  isHidden: Boolean
  description: String
  createdAt(by: GroupBy! = DAY): DateTime
  updatedAt(by: GroupBy! = DAY): DateTime
}

type RouteCountAggregate {
  id: Int
  parentId: Int
  name: Int
  path: Int
  component: Int
  icon: Int
  order: Int
  type: Int
  status: Int
  isHidden: Int
  description: Int
  createdAt: Int
  updatedAt: Int
}

type RouteSumAggregate {
  order: Float
}

type RouteAvgAggregate {
  order: Float
}

type RouteMinAggregate {
  id: ID
  parentId: ID
  name: String
  path: String
  component: String
  icon: String
  order: Float
  type: String
  status: String
  description: String
  createdAt: DateTime
  updatedAt: DateTime
}

type RouteMaxAggregate {
  id: ID
  parentId: ID
  name: String
  path: String
  component: String
  icon: String
  order: Float
  type: String
  status: String
  description: String
  createdAt: DateTime
  updatedAt: DateTime
}

type RouteAggregateResponse {
  groupBy: RouteAggregateGroupBy
  count: RouteCountAggregate
  sum: RouteSumAggregate
  avg: RouteAvgAggregate
  min: RouteMinAggregate
  max: RouteMaxAggregate
}

type VccCard {
  id: ID!

  """渠道"""
  channel: String!

  """国家"""
  country: String!

  """持卡人"""
  cardHolder: String!

  """国家代码"""
  countryCode: String!

  """卡号"""
  cardNumber: String!

  """余额"""
  balance: Float!

  """消费"""
  consumption: Float!

  """已绑定广告号"""
  boundAdAccount: String

  """绑定的广告账户ID数组"""
  boundAdAccountIds: [String!]

  """所属群组"""
  group: String

  """交易数"""
  transactionCount: Int!

  """广告号存活数量"""
  adAccountStatus: Int!

  """过期月"""
  expiryMonth: String!

  """过期年"""
  expiryYear: String!

  """CVV"""
  cvv: String!

  """邮编"""
  zipCode: String!

  """已使用次数"""
  usedCount: Int!

  """绑定次数"""
  bindCount: Int!

  """绑定广告号总数"""
  totalAdAccounts: Int!

  """限制次数"""
  limitCount: Int!

  """状态"""
  status: String!

  """备注"""
  remark: String

  """创建时间"""
  createdAt: DateTime!

  """更新时间"""
  updatedAt: DateTime!
}

type VccTransactionDTO {
  """交易ID"""
  id: ID!

  """VCC卡片ID"""
  cardId: ID!

  """卡号（脱敏显示）"""
  cardNo: String!

  """交易金额"""
  amount: Float!

  """商户名称"""
  merchant: String!

  """交易时间"""
  transactionTime: String!

  """交易状态"""
  status: TransactionStatus!

  """交易类型"""
  type: TransactionType!

  """Facebook广告账户ID"""
  facebookAccountId: String

  """Facebook交易ID"""
  facebookTransactionId: String

  """广告活动名称"""
  campaignName: String

  """交易描述"""
  description: String
}

"""交易状态"""
enum TransactionStatus {
  SUCCESS
  PENDING
  FAILED
}

"""交易类型"""
enum TransactionType {
  PAYMENT
  DEPOSIT
  REFUND
}

type PaginatedVccTransactionResult {
  """交易记录列表"""
  data: [VccTransactionDTO!]!

  """总数"""
  total: Int!

  """当前页"""
  page: Int!

  """每页条数"""
  limit: Int!

  """总页数"""
  totalPages: Int!
}

type VccTransactionStatsDTO {
  """总交易数"""
  totalCount: Int!

  """总交易金额"""
  totalAmount: Float!

  """平均交易金额"""
  averageAmount: Float!

  """成功交易数"""
  successCount: Int!

  """待处理交易数"""
  pendingCount: Int!

  """失败交易数"""
  failedCount: Int!

  """成功率（%）"""
  successRate: Float!
}

input EmployeeInput {
  id: ID
  firstName: String
  lastName: String
  name: String
  createdAt: DateTime
  updatedAt: DateTime
  isActive: Boolean!
  isArchived: Boolean!
}

input AntiBlockInput {
  id: Int
  userId: String
  url: String
  status: String
  name: String
  relatedDomain: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

input AudienceRegionInput {
  id: String
  name: String
}

input AudienceCityInput {
  id: String
  name: String
}

input AudienceGeoLocationInput {
  countries: [String!]
  regions: [AudienceRegionInput!]
  cities: [AudienceCityInput!]
}

input AudienceInterestInput {
  id: String!
  name: String
}

input AudienceBehaviorInput {
  id: String!
  name: String
}

input LandingPageInput {
  id: ID!
  code: String!
  title: String!
  url: String!
  slogan: String!
  appIcon: [String!]
  background: [String!]
  description: String
  star: Float!
  promoImages: [String!]
  creator: String!
  createdAt: DateTime!
  tenantId: String!
}

type Query {
  """获取VCC卡片列表"""
  vccCards(filter: VccCardFilterInput): [VccCard!]!

  """根据ID获取VCC卡片详情"""
  vccCard(id: ID!): VccCard!

  """查询VCC交易记录列表（支持前端所有过滤条件）"""
  vccTransactions(filter: VccTransactionFilterInputDTO, pagination: VccTransactionPaginationInputDTO): PaginatedVccTransactionResult!

  """根据ID查询单个VCC交易记录"""
  vccTransaction(id: ID!): VccTransactionDTO!

  """获取VCC交易统计数据"""
  vccTransactionStats(filter: VccTransactionFilterInputDTO): VccTransactionStatsDTO!

  """导出VCC交易记录（CSV或JSON格式）"""
  exportVccTransactions(filter: VccTransactionFilterInputDTO, format: String! = "csv"): String!
  tenants(search: String, page: Int, limit: Int): TenantPage!
  tenant(id: ID!): TenantDTO!
  tenantResourceStats(tenantId: String!): [TenantResourceStats!]!
  tenantResourceDetails(tenantId: String!, page: Int! = 1, pageSize: Int! = 10): TenantResourceDetailPage!
  tenantTrend(tenantId: String!, startDate: String, endDate: String): [TenantTrendPoint!]!
  globalResourceStats: [GlobalResourceStats!]!
  globalResourceDetails(page: Int! = 1, pageSize: Int! = 10): GlobalResourceDetailPage!
  globalTrend(startDate: String, endDate: String): [GlobalTrendPoint!]!
  channelDistribution(tenantId: String!): [ChannelDistributionDTO!]!
  audiences(filter: AudienceCustomFilter, paging: AudienceCursorPaging): [AudienceDTO!]!
  audience(id: ID!): AudienceDTO

  """兴趣选项列表"""
  audienceInterestOptions: [AudienceOption!]!

  """行为选项列表"""
  audienceBehaviorOptions: [AudienceOption!]!
  audienceAnalysis: AudienceAnalysisResult!
  adAccounts(filter: AdAccountFilterInput): [AdAccountDTO!]!
  unauthorizedAdAccounts: [AdAccountDTO!]!
  getAdAccountOAuth2Url(account: String!): String!
  adCampaigns(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: AdCampaignFilter! = {}

    """Specify to sort results."""
    sorting: [AdCampaignSort!]! = []
  ): AdCampaignConnection!
  adSetsCustom(filter: AdSetFilterInput): [AdSet!]!
  facebookCreativesByAdSet(adSetId: ID!): [AdCreative!]!
  getPromotions: [PromotionDto!]!
  getPromotionByCode(code: String!): PromotionDetailDto
  getPromotionDetail(code: String!, tenantId: String!): PromotionDetailDto
  campaigns: [Campaign!]!

  """通过广告系列ID获取Facebook用户管理的App对象列表"""
  facebookUserAppsByCampaign(campaignId: ID!): [FacebookApp!]!

  """通过广告账户ID获取投放过的App Promotion应用对象列表"""
  facebookUserAppsByAdAccount(adAccountId: String!, accessToken: String!): [FacebookApp!]!

  """通过广告系列ID获取投放过的App Promotion应用对象列表"""
  facebookUserAppsByAdAccountByCampaignId(campaignId: ID!): [FacebookApp!]!

  """Facebook国家/地区选项"""
  facebookCountries: [Option!]!

  """Facebook语言ID选项"""
  facebookLocales: [Option!]!

  """Facebook兴趣选项"""
  facebookInterests(query: String): [Option!]!

  """Facebook行为选项（API v22.0）"""
  facebookBehaviors(query: String): [Option!]!
  pixelsByCampaign(campaignId: String!): [Pixel!]!
  businessIdByCampaignId(campaignId: String!): String
  getAd(adId: String!, accessToken: String!): AdDTO
  ads(limit: Int, offset: Int): [AdDTO!]!
  adMetrics(startDate: String!, endDate: String!): AdMetricsDTO!
  campaignOverview(startDate: String!, endDate: String!): CampaignOverviewDTO!
  autoOptimization(type: String!): AutoOptimizationDTO!
  creativeAnalysis(startDate: String!, endDate: String!): CreativeAnalysisResult!
  budgetUtilization(startDate: String!, endDate: String!): BudgetUtilizationDTO!
  adGroupsOverview(startDate: String!, endDate: String!): AdGroupsOverviewDTO!
  adPlatformDistribution(startDate: String!, endDate: String!): AdPlatformDistributionDTO!
  audienceInsights(startDate: String!, endDate: String!): AudienceInsightsDTO!
  getLandingPages: [LandingPage!]!
  getLandingPage(code: String!): LandingPage
  me: AuthUserResponse!
  users(input: UserListInput, sorting: [UserSortInput!]): UserConnection!
  user(id: ID!): User!
  myDomains: [Domain!]!
  rules: [Rule!]!
  adDTO(
    """The id of the record to find."""
    id: ID!
  ): AdDTO!
  adDTOS(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: AdDTOFilter! = {}

    """Specify to sort results."""
    sorting: [AdDTOSort!]! = []
  ): AdDTOConnection!
  adCampaign(
    """The id of the record to find."""
    id: ID!
  ): AdCampaign!
  adCreative(
    """The id of the record to find."""
    id: ID!
  ): AdCreative!
  adCreatives(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: AdCreativeFilter! = {}

    """Specify to sort results."""
    sorting: [AdCreativeSort!]! = []
  ): AdCreativeConnection!
  adSet(
    """The id of the record to find."""
    id: ID!
  ): AdSet!
  adSets(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: AdSetFilter! = {}

    """Specify to sort results."""
    sorting: [AdSetSort!]! = []
  ): AdSetConnection!
  audienceDTO(
    """The id of the record to find."""
    id: ID!
  ): AudienceDTO!
  audienceDTOS(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: AudienceDTOFilter! = {}

    """Specify to sort results."""
    sorting: [AudienceDTOSort!]! = []
  ): AudienceDTOConnection!
  material(
    """The id of the record to find."""
    id: ID!
  ): Material!
  materials(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: MaterialFilter! = {}

    """Specify to sort results."""
    sorting: [MaterialSort!]! = []
  ): MaterialConnection!
  vcc(
    """The id of the record to find."""
    id: ID!
  ): Vcc!
  vccs(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: VccFilter! = {}

    """Specify to sort results."""
    sorting: [VccSort!]! = []
  ): VccConnection!
  roles(input: RoleSearchInput!): RolePagination!
  role(id: String!): Role!
  getOverviewCards: OverviewCardsResponse!
  getAdPerformanceAnalysis(timeRange: String!): AdPerformanceAnalysisResponse!
  getAdTrafficAnalysis(timeRange: String!): AdTrafficAnalysisResponse!
  getCreativePerformance: CreativePerformanceResponse!
  getChannelAnalysis(timeRange: String!): ChannelAnalysisResponse!
  materiaCreateAggregate(
    """Filter to find records to aggregate on"""
    filter: materiaCreateAggregateFilter
  ): [materiaCreateAggregateResponse!]!
  materiaCreate(
    """The id of the record to find."""
    id: ID!
  ): materiaCreate!
  materiaCreates(
    """Limit or page results."""
    paging: OffsetPaging! = {limit: 10}

    """Specify to filter the records returned."""
    filter: materiaCreateFilter! = {}

    """Specify to sort results."""
    sorting: [materiaCreateSort!]! = []
  ): MateriaCreateConnection!
  materialManagementAggregate(
    """Filter to find records to aggregate on"""
    filter: materialManagementAggregateFilter
  ): [materialManagementAggregateResponse!]!
  materialManagement(
    """The id of the record to find."""
    id: ID!
  ): materialManagement!
  materialManagements(
    """Limit or page results."""
    paging: OffsetPaging! = {limit: 10}

    """Specify to filter the records returned."""
    filter: materialManagementFilter! = {}

    """Specify to sort results."""
    sorting: [materialManagementSort!]! = []
  ): MaterialManagementConnection!
  ipRestrictionAggregate(
    """Filter to find records to aggregate on"""
    filter: ipRestrictionAggregateFilter
  ): [ipRestrictionAggregateResponse!]!
  ipRestriction(
    """The id of the record to find."""
    id: ID!
  ): ipRestriction!
  ipRestrictions(
    """Limit or page results."""
    paging: OffsetPaging! = {limit: 10}

    """Specify to filter the records returned."""
    filter: ipRestrictionFilter! = {}

    """Specify to sort results."""
    sorting: [ipRestrictionSort!]! = []
  ): IpRestrictionConnection!
  getRouteTree: [Route!]!
  getRoutes: [Route!]!
  getRoute(id: String!): Route
  routeAggregate(
    """Filter to find records to aggregate on"""
    filter: RouteAggregateFilter
  ): [RouteAggregateResponse!]!
  route(
    """The id of the record to find."""
    id: ID!
  ): Route!
  routes(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: RouteFilter! = {}

    """Specify to sort results."""
    sorting: [RouteSort!]! = []
  ): RouteConnection!
  group(
    """The id of the record to find."""
    id: ID!
  ): Group!
  groups(
    """Limit or page results."""
    paging: OffsetPaging! = {limit: 10}

    """Specify to filter the records returned."""
    filter: GroupFilter! = {}

    """Specify to sort results."""
    sorting: [GroupSort!]! = []
  ): GroupConnection!
  antiBlock(
    """The id of the record to find."""
    id: ID!
  ): AntiBlock!
  antiBlocks(
    """Limit or page results."""
    paging: OffsetPaging! = {limit: 10}

    """Specify to filter the records returned."""
    filter: AntiBlockFilter! = {}

    """Specify to sort results."""
    sorting: [AntiBlockSort!]! = []
  ): AntiBlockConnection!
  employeeAggregate(
    """Filter to find records to aggregate on"""
    filter: EmployeeAggregateFilter
  ): [EmployeeAggregateResponse!]!
  employee(
    """The id of the record to find."""
    id: ID!
  ): Employee!
  employees(
    """Limit or page results."""
    paging: CursorPaging! = {first: 10}

    """Specify to filter the records returned."""
    filter: EmployeeFilter! = {}

    """Specify to sort results."""
    sorting: [EmployeeSort!]! = []
  ): EmployeeConnection!
}

input VccCardFilterInput {
  """渠道"""
  channel: String

  """国家"""
  country: String

  """持卡人"""
  cardHolder: String

  """卡号"""
  cardNumber: String

  """状态"""
  status: String

  """租户ID"""
  tenantId: String
}

input VccTransactionFilterInputDTO {
  """VCC卡片ID"""
  cardId: ID

  """卡号模糊搜索"""
  cardNo: String

  """商户名称模糊搜索"""
  merchant: String

  """交易状态"""
  status: TransactionStatus

  """交易类型"""
  type: TransactionType

  """开始时间（日期范围）"""
  startTime: String

  """结束时间（日期范围）"""
  endTime: String

  """最小金额"""
  minAmount: Float

  """最大金额"""
  maxAmount: Float

  """租户ID"""
  tenantId: ID
}

input VccTransactionPaginationInputDTO {
  """页码"""
  page: Int = 1

  """每页条数"""
  limit: Int = 20

  """排序字段"""
  sortBy: String = "transactionTime"

  """排序方向"""
  sortOrder: String = "DESC"
}

input AudienceCustomFilter {
  id: String
  name: String
}

input AudienceCursorPaging {
  first: Float
  after: String
}

input AdAccountFilterInput {
  name: String
  platform: String
  tenantId: String
  fbStatus: String
}

input CursorPaging {
  """Paginate before opaque cursor"""
  before: ConnectionCursor

  """Paginate after opaque cursor"""
  after: ConnectionCursor

  """Paginate first"""
  first: Int

  """Paginate last"""
  last: Int
}

input AdCampaignFilter {
  and: [AdCampaignFilter!]
  or: [AdCampaignFilter!]
  id: IDFilterComparison
  platform: StringFieldComparison
  campaignId: StringFieldComparison
  name: StringFieldComparison
  raw: JSONFilterComparison
  status: StringFieldComparison
}

input IDFilterComparison {
  is: Boolean
  isNot: Boolean
  eq: ID
  neq: ID
  gt: ID
  gte: ID
  lt: ID
  lte: ID
  like: ID
  notLike: ID
  iLike: ID
  notILike: ID
  in: [ID!]
  notIn: [ID!]
}

input StringFieldComparison {
  is: Boolean
  isNot: Boolean
  eq: String
  neq: String
  gt: String
  gte: String
  lt: String
  lte: String
  like: String
  notLike: String
  iLike: String
  notILike: String
  in: [String!]
  notIn: [String!]
}

input JSONFilterComparison {
  is: Boolean
  isNot: Boolean
  eq: JSON
  neq: JSON
  gt: JSON
  gte: JSON
  lt: JSON
  lte: JSON
  like: JSON
  notLike: JSON
  iLike: JSON
  notILike: JSON
  in: [JSON!]
  notIn: [JSON!]
}

input AdCampaignSort {
  field: AdCampaignSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum AdCampaignSortFields {
  id
  platform
  campaignId
  name
  raw
  status
}

"""Sort Directions"""
enum SortDirection {
  ASC
  DESC
}

"""Sort Nulls Options"""
enum SortNulls {
  NULLS_FIRST
  NULLS_LAST
}

input AdSetFilterInput {
  id: ID
  name: String
  platform: String
}

input UserListInput {
  filter: UserFilterInput
  page: Int = 1
  limit: Int = 10
}

input UserFilterInput {
  username: String
  email: String
  fullName: String
  phone: String
  status: UserStatus
  roleIds: [ID!]
  groupIds: [ID!]
}

input UserSortInput {
  field: String!
  direction: String!
}

input AdDTOFilter {
  and: [AdDTOFilter!]
  or: [AdDTOFilter!]
  raw: JSONFilterComparison
  insight: JSONFilterComparison
}

input AdDTOSort {
  field: AdDTOSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum AdDTOSortFields {
  raw
  insight
}

input AdCreativeFilter {
  and: [AdCreativeFilter!]
  or: [AdCreativeFilter!]
  id: IDFilterComparison
  platform: StringFieldComparison
  creativeId: StringFieldComparison
  name: StringFieldComparison
  raw: JSONFilterComparison
  status: StringFieldComparison
}

input AdCreativeSort {
  field: AdCreativeSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum AdCreativeSortFields {
  id
  platform
  creativeId
  name
  raw
  status
}

input AdSetFilter {
  and: [AdSetFilter!]
  or: [AdSetFilter!]
  id: IDFilterComparison
  platform: StringFieldComparison
  adsetId: StringFieldComparison
  name: StringFieldComparison
  raw: JSONFilterComparison
  status: StringFieldComparison
}

input AdSetSort {
  field: AdSetSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum AdSetSortFields {
  id
  platform
  adsetId
  name
  raw
  status
}

input AudienceDTOFilter {
  and: [AudienceDTOFilter!]
  or: [AudienceDTOFilter!]
  id: IDFilterComparison
  audienceId: StringFieldComparison
  name: StringFieldComparison
  notes: StringFieldComparison
  approximateCount: NumberFieldComparison
  tenantId: StringFieldComparison
  tenantName: StringFieldComparison
}

input NumberFieldComparison {
  is: Boolean
  isNot: Boolean
  eq: Float
  neq: Float
  gt: Float
  gte: Float
  lt: Float
  lte: Float
  in: [Float!]
  notIn: [Float!]
  between: NumberFieldComparisonBetween
  notBetween: NumberFieldComparisonBetween
}

input NumberFieldComparisonBetween {
  lower: Float!
  upper: Float!
}

input AudienceDTOSort {
  field: AudienceDTOSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum AudienceDTOSortFields {
  id
  audienceId
  name
  notes
  approximateCount
  tenantId
  tenantName
}

input MaterialFilter {
  and: [MaterialFilter!]
  or: [MaterialFilter!]
  id: IDFilterComparison
  type: StringFieldComparison
  name: StringFieldComparison
  url: StringFieldComparison
  content: StringFieldComparison
  status: StringFieldComparison
  raw: JSONFilterComparison
}

input MaterialSort {
  field: MaterialSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum MaterialSortFields {
  id
  type
  name
  url
  content
  status
  raw
}

input VccFilter {
  and: [VccFilter!]
  or: [VccFilter!]
  id: IDFilterComparison
  cardNumber: StringFieldComparison
  cardHolder: StringFieldComparison
  expiry: StringFieldComparison
  cvv: StringFieldComparison
  bindAccount: StringFieldComparison
  status: StringFieldComparison
  raw: JSONFilterComparison
}

input VccSort {
  field: VccSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum VccSortFields {
  id
  cardNumber
  cardHolder
  expiry
  cvv
  bindAccount
  status
  raw
}

input RoleSearchInput {
  page: Int = 1
  limit: Int = 10
  search: String
  status: String
}

input materiaCreateAggregateFilter {
  and: [materiaCreateAggregateFilter!]
  or: [materiaCreateAggregateFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  name: StringFieldComparison
  materials: NumberFieldComparison
  pageLading: StringFieldComparison
  createId: StringFieldComparison
  slogan: StringFieldComparison
  account: StringFieldComparison
  pageId: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input IntFieldComparison {
  is: Boolean
  isNot: Boolean
  eq: Int
  neq: Int
  gt: Int
  gte: Int
  lt: Int
  lte: Int
  in: [Int!]
  notIn: [Int!]
  between: IntFieldComparisonBetween
  notBetween: IntFieldComparisonBetween
}

input IntFieldComparisonBetween {
  lower: Int!
  upper: Int!
}

input DateFieldComparison {
  is: Boolean
  isNot: Boolean
  eq: DateTime
  neq: DateTime
  gt: DateTime
  gte: DateTime
  lt: DateTime
  lte: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  between: DateFieldComparisonBetween
  notBetween: DateFieldComparisonBetween
}

input DateFieldComparisonBetween {
  lower: DateTime!
  upper: DateTime!
}

input OffsetPaging {
  """Limit the number of records returned"""
  limit: Int

  """Offset to start returning records from"""
  offset: Int
}

input materiaCreateFilter {
  and: [materiaCreateFilter!]
  or: [materiaCreateFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  name: StringFieldComparison
  materials: NumberFieldComparison
  pageLading: StringFieldComparison
  createId: StringFieldComparison
  slogan: StringFieldComparison
  account: StringFieldComparison
  pageId: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input materiaCreateSort {
  field: materiaCreateSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum materiaCreateSortFields {
  id
  userId
  name
  materials
  pageLading
  createId
  slogan
  account
  pageId
  tenantId
  createdAt
  updatedAt
}

input materialManagementAggregateFilter {
  and: [materialManagementAggregateFilter!]
  or: [materialManagementAggregateFilter!]
  id: IntFieldComparison
  fileName: StringFieldComparison
  tags: StringFieldComparison
  group: StringFieldComparison
  facebookImageId: StringFieldComparison
  facebookVedioId: StringFieldComparison
  adCount: IntFieldComparison
  activeAdCount: IntFieldComparison
  bannedCount: IntFieldComparison
  spend: IntFieldComparison
  conversion: IntFieldComparison
  commission: IntFieldComparison
  file: StringFieldComparison
  tenantId: StringFieldComparison
  notes: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input materialManagementFilter {
  and: [materialManagementFilter!]
  or: [materialManagementFilter!]
  id: IntFieldComparison
  fileName: StringFieldComparison
  tags: StringFieldComparison
  group: StringFieldComparison
  facebookImageId: StringFieldComparison
  facebookVedioId: StringFieldComparison
  adCount: IntFieldComparison
  activeAdCount: IntFieldComparison
  bannedCount: IntFieldComparison
  spend: IntFieldComparison
  conversion: IntFieldComparison
  commission: IntFieldComparison
  file: StringFieldComparison
  tenantId: StringFieldComparison
  notes: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input materialManagementSort {
  field: materialManagementSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum materialManagementSortFields {
  id
  fileName
  tags
  group
  facebookImageId
  facebookVedioId
  adCount
  activeAdCount
  bannedCount
  spend
  conversion
  commission
  file
  tenantId
  notes
  createdAt
  updatedAt
}

input ipRestrictionAggregateFilter {
  and: [ipRestrictionAggregateFilter!]
  or: [ipRestrictionAggregateFilter!]
  id: IntFieldComparison
  ip: StringFieldComparison
  userId: StringFieldComparison
  status: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input ipRestrictionFilter {
  and: [ipRestrictionFilter!]
  or: [ipRestrictionFilter!]
  id: IntFieldComparison
  ip: StringFieldComparison
  userId: StringFieldComparison
  status: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input ipRestrictionSort {
  field: ipRestrictionSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum ipRestrictionSortFields {
  id
  ip
  userId
  status
  tenantId
  createdAt
  updatedAt
}

input RouteAggregateFilter {
  and: [RouteAggregateFilter!]
  or: [RouteAggregateFilter!]
  id: IDFilterComparison
  parentId: IDFilterComparison
  name: StringFieldComparison
  path: StringFieldComparison
  component: StringFieldComparison
  icon: StringFieldComparison
  order: NumberFieldComparison
  type: StringFieldComparison
  status: StringFieldComparison
  isHidden: BooleanFieldComparison
  description: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input BooleanFieldComparison {
  is: Boolean
  isNot: Boolean
}

input RouteFilter {
  and: [RouteFilter!]
  or: [RouteFilter!]
  id: IDFilterComparison
  parentId: IDFilterComparison
  name: StringFieldComparison
  path: StringFieldComparison
  component: StringFieldComparison
  icon: StringFieldComparison
  order: NumberFieldComparison
  type: StringFieldComparison
  status: StringFieldComparison
  isHidden: BooleanFieldComparison
  description: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input RouteSort {
  field: RouteSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum RouteSortFields {
  id
  parentId
  name
  path
  component
  icon
  order
  type
  status
  isHidden
  description
  createdAt
  updatedAt
}

input GroupFilter {
  and: [GroupFilter!]
  or: [GroupFilter!]
  id: IDFilterComparison
  name: StringFieldComparison
  belongTo: StringFieldComparison
  contactInfo: StringFieldComparison
  description: StringFieldComparison
  memberCount: IntFieldComparison
  permissions: JSONFilterComparison
  status: StringFieldComparison
  createTime: DateFieldComparison
  updateTime: DateFieldComparison
  tenantId: StringFieldComparison
}

input GroupSort {
  field: GroupSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum GroupSortFields {
  id
  name
  belongTo
  contactInfo
  description
  memberCount
  permissions
  status
  createTime
  updateTime
  tenantId
}

input AntiBlockFilter {
  and: [AntiBlockFilter!]
  or: [AntiBlockFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  url: StringFieldComparison
  status: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input AntiBlockSort {
  field: AntiBlockSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum AntiBlockSortFields {
  id
  userId
  url
  status
  createdAt
  updatedAt
}

input EmployeeAggregateFilter {
  and: [EmployeeAggregateFilter!]
  or: [EmployeeAggregateFilter!]
  id: IDFilterComparison
  firstName: StringFieldComparison
  lastName: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
  isActive: BooleanFieldComparison
  isArchived: BooleanFieldComparison
}

input EmployeeFilter {
  and: [EmployeeFilter!]
  or: [EmployeeFilter!]
  id: IDFilterComparison
  firstName: StringFieldComparison
  lastName: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
  isActive: BooleanFieldComparison
  isArchived: BooleanFieldComparison
}

input EmployeeSort {
  field: EmployeeSortFields!
  direction: SortDirection!
  nulls: SortNulls
}

enum EmployeeSortFields {
  id
  firstName
  lastName
  createdAt
  updatedAt
  isActive
  isArchived
}

type Mutation {
  """创建VCC卡片"""
  createVccCard(input: CreateVccCardInput!): VccCard!

  """更新VCC卡片"""
  updateVccCard(id: ID!, input: UpdateVccCardInput!): VccCard!

  """删除VCC卡片"""
  deleteVccCard(id: ID!): Boolean!

  """绑定广告账户到VCC卡片"""
  bindAdAccountToVccCard(input: BindAdAccountInput!): VccCard!

  """批量创建VCC卡片"""
  createVccCardsBatch(inputs: [CreateVccCardInput!]!): [VccCard!]!

  """创建VCC交易记录"""
  createVccTransaction(input: CreateVccTransactionInputDTO!): VccTransactionDTO!

  """更新VCC交易记录"""
  updateVccTransaction(id: ID!, input: UpdateVccTransactionInputDTO!): VccTransactionDTO!

  """删除VCC交易记录"""
  deleteVccTransaction(id: ID!): Boolean!

  """从Facebook API同步VCC卡片的广告消费记录"""
  syncVccTransactionsFromFacebook(input: SyncFacebookTransactionsInputDTO!): [VccTransactionDTO!]!

  """手动添加VCC充值记录"""
  addVccRechargeTransaction(cardId: ID!, amount: Float!, description: String): VccTransactionDTO!
  createTenant(input: CreateTenantInput!): TenantDTO!
  updateTenant(input: UpdateTenantInput!): TenantDTO!
  deleteTenant(id: ID!): Boolean!
  disableTenant(id: ID!): Boolean!
  enableTenant(id: ID!): Boolean!
  createAudience(input: CreateAudience!): AudienceDTO!
  updateAudience(id: ID!, input: CreateAudience!): AudienceDTO!
  deleteAudience(id: ID!): Boolean!
  testEcho(input: TestInput!): TestOutput!
  deleteOneAdAccount(accountId: String!): AdAccountDTO!
  batchUploadAdAccounts(accounts: [AdAccountInput!]!, platform: String!): [AdAccountUploadResult!]!
  saveAdAccountOAuthToken(account: String!, accessToken: String!): Boolean!
  createOneAdCampaign(name: String!, platform: String!, budget: Float!, status: String!, startDate: DateTime!, endDate: DateTime!, tags: [String!], description: String, adAccountId: String!): AdCampaign!
  createAdSet(name: String!, campaignId: ID!, audienceId: ID!, dailyBudget: Float!, startTime: String!, endTime: String, optimizationGoal: String!, billingEvent: String!, status: String!, bid_amount: Float, promoted_object: JSON, device_platforms: [String!], user_os: [String!]): AdSet!
  updateAdSet(input: UpdateAdSetInput!): AdSet!
  deleteAdSet(id: ID!): Boolean!
  createPromotion(input: CreatePromotionDto!): PromotionDto!
  updatePromotion(id: String!, input: UpdatePromotionDto!): PromotionDto!
  deletePromotion(id: String!): Boolean!
  createCampaign(input: CreateCampaignInputX!): Campaign!
  deleteCampaign(id: ID!): Boolean!
  updateCampaign(id: ID!, input: CreateCampaignInputX!): Campaign!

  """刷新并同步单条广告系列的 Facebook 状态"""
  refreshCampaignFromFacebook(id: ID!): Campaign!
  createPixelAndAssign(campaignId: String!, pixelName: String!, businessId: String!): PixelOutput!
  createAd(adset_id: String!, name: String!, status: String!, creative: JSON, ruleId: String, bid_amount: Float, remark: String): AdDTO!
  updateAd(adId: String!, name: String, status: String, creative: JSON, tracking_specs: JSON, bid_amount: Float, adset_id: String): AdDTO!
  deleteAd(adId: String!): Boolean!

  """刷新并同步单条广告的 Facebook 状态"""
  refreshAdFromFacebook(adId: String!): Boolean!
  createLandingPage(title: String!, url: String!, slogan: String!, appIcon: [String!], background: [String!], description: String, star: Float!, promoImages: [String!], creator: String): LandingPage!
  updateLandingPage(id: String!, input: UpdateLandingPageInput!): LandingPage!
  deleteLandingPage(id: String!): Boolean!
  login(input: LoginInput!): LoginResponse!
  register(input: RegisterInput!): RegisterResponse!
  createUser(input: CreateUserInput!): User!
  updateUser(input: UpdateUserInput!): User!
  deleteUser(id: ID!): User!
  changeUserStatus(id: ID!, status: UserStatus!): User!
  addDomain(name: String!): Domain!
  deleteDomain(id: String!): Boolean!
  refreshDomainStatus(id: String!): Domain!
  createRule(name: String!, action: String!, object: String!, scheduleType: String, custom_schedule: [String!], timeRange: String, status: String, conditions: [RuleConditionInput!]!): Rule!
  updateRule(id: String!, name: String!, action: String!, object: String!, scheduleType: String, custom_schedule: [String!], timeRange: String, status: String, conditions: [RuleConditionInput!]!): Rule!
  deleteRule(id: String!): Boolean!
  updateOneAudienceDTO(input: UpdateOneAudienceDTOInput!): AudienceDTO!
  updateManyAudienceDTOS(input: UpdateManyAudienceDTOSInput!): UpdateManyResponse!
  deleteOneAudienceDTO(input: DeleteOneAudienceDTOInput!): AudienceDTODeleteResponse!
  deleteManyAudienceDTOS(input: DeleteManyAudienceDTOSInput!): DeleteManyResponse!
  createRole(input: CreateRoleInput!): Role!
  updateRole(input: UpdateRoleInput!): Role!
  deleteRole(input: DeleteRoleInput!): Boolean!
  createOneMateriaCreate(input: CreateOneMateriaCreateInput!): materiaCreate!
  createManyMateriaCreates(input: CreateManyMateriaCreatesInput!): [materiaCreate!]!
  updateOneMateriaCreate(input: UpdateOneMateriaCreateInput!): materiaCreate!
  updateManyMateriaCreates(input: UpdateManyMateriaCreatesInput!): UpdateManyResponse!
  deleteOneMateriaCreate(input: DeleteOneMateriaCreateInput!): MateriaCreateDeleteResponse!
  deleteManyMateriaCreates(input: DeleteManyMateriaCreatesInput!): DeleteManyResponse!
  createOneMaterialManagement(input: CreateOneMaterialManagementInput!): materialManagement!
  createManyMaterialManagements(input: CreateManyMaterialManagementsInput!): [materialManagement!]!
  updateOneMaterialManagement(input: UpdateOneMaterialManagementInput!): materialManagement!
  updateManyMaterialManagements(input: UpdateManyMaterialManagementsInput!): UpdateManyResponse!
  deleteOneMaterialManagement(input: DeleteOneMaterialManagementInput!): MaterialManagementDeleteResponse!
  deleteManyMaterialManagements(input: DeleteManyMaterialManagementsInput!): DeleteManyResponse!
  createOneIpRestriction(ip: String, userId: String, status: String, operator: String): ipRestriction!
  updateOneIpRestriction(input: UpdateOneIpRestrictionInput!): ipRestriction!
  updateManyIpRestrictions(input: UpdateManyIpRestrictionsInput!): UpdateManyResponse!
  deleteOneIpRestriction(input: DeleteOneIpRestrictionInput!): IpRestrictionDeleteResponse!
  deleteManyIpRestrictions(input: DeleteManyIpRestrictionsInput!): DeleteManyResponse!
  createRoute(input: CreateRouteInput!): Route!
  updateOneRoute(input: UpdateOneRouteInput!): Route!
  deleteRoute(id: String!): Boolean!
  toggleRouteStatus(id: String!): Route!
  createOneRoute(input: CreateOneRouteInput!): Route!
  createManyRoutes(input: CreateManyRoutesInput!): [Route!]!
  updateManyRoutes(input: UpdateManyRoutesInput!): UpdateManyResponse!
  deleteOneRoute(input: DeleteOneRouteInput!): RouteDeleteResponse!
  deleteManyRoutes(input: DeleteManyRoutesInput!): DeleteManyResponse!
  createOneGroup(input: CreateOneGroupInput!): Group!
  createManyGroups(input: CreateManyGroupsInput!): [Group!]!
  updateOneGroup(input: UpdateOneGroupInput!): Group!
  updateManyGroups(input: UpdateManyGroupsInput!): UpdateManyResponse!
  deleteOneGroup(input: DeleteOneGroupInput!): GroupDeleteResponse!
  deleteManyGroups(input: DeleteManyGroupsInput!): DeleteManyResponse!
  createManyAntiBlocks(antiBlocks: [AntiBlockInput!]!): [AntiBlock!]!
  createOneAntiBlock(input: CreateOneAntiBlockInput!): AntiBlock!
  updateOneAntiBlock(input: UpdateOneAntiBlockInput!): AntiBlock!
  updateManyAntiBlocks(input: UpdateManyAntiBlocksInput!): UpdateManyResponse!
  deleteOneAntiBlock(input: DeleteOneAntiBlockInput!): AntiBlockDeleteResponse!
  deleteManyAntiBlocks(input: DeleteManyAntiBlocksInput!): DeleteManyResponse!
  createOneEmployee(input: CreateOneEmployeeInput!): Employee!
  createManyEmployees(input: CreateManyEmployeesInput!): [Employee!]!
  updateOneEmployee(input: UpdateOneEmployeeInput!): Employee!
  updateManyEmployees(input: UpdateManyEmployeesInput!): UpdateManyResponse!
  deleteOneEmployee(input: DeleteOneEmployeeInput!): EmployeeDeleteResponse!
  deleteManyEmployees(input: DeleteManyEmployeesInput!): DeleteManyResponse!
}

input CreateVccCardInput {
  """渠道"""
  channel: String!

  """国家"""
  country: String!

  """持卡人"""
  cardHolder: String!

  """国家代码"""
  countryCode: String!

  """卡号"""
  cardNumber: String!

  """余额"""
  balance: Float!

  """消费"""
  consumption: Float! = 0

  """已绑定广告号"""
  boundAdAccount: String

  """绑定的广告账户ID数组"""
  boundAdAccountIds: [String!]

  """所属群组"""
  group: String

  """交易数"""
  transactionCount: Int! = 0

  """广告号存活数量"""
  adAccountStatus: Int! = 0

  """过期月"""
  expiryMonth: String!

  """过期年"""
  expiryYear: String!

  """CVV"""
  cvv: String!

  """邮编"""
  zipCode: String!

  """已使用次数"""
  usedCount: Int! = 0

  """绑定次数"""
  bindCount: Int! = 0

  """绑定广告号总数"""
  totalAdAccounts: Int! = 0

  """限制次数"""
  limitCount: Int! = 10

  """状态"""
  status: String! = "未使用"

  """备注"""
  remark: String

  """租户ID"""
  tenantId: String!
}

input UpdateVccCardInput {
  """渠道"""
  channel: String

  """国家"""
  country: String

  """持卡人"""
  cardHolder: String

  """国家代码"""
  countryCode: String

  """余额"""
  balance: Float

  """消费"""
  consumption: Float

  """已绑定广告号"""
  boundAdAccount: String

  """绑定的广告账户ID数组"""
  boundAdAccountIds: [String!]

  """所属群组"""
  group: String

  """交易数"""
  transactionCount: Int

  """广告号存活数量"""
  adAccountStatus: Int

  """过期月"""
  expiryMonth: String

  """过期年"""
  expiryYear: String

  """邮编"""
  zipCode: String

  """已使用次数"""
  usedCount: Int

  """绑定次数"""
  bindCount: Int

  """绑定广告号总数"""
  totalAdAccounts: Int

  """限制次数"""
  limitCount: Int

  """状态"""
  status: String

  """备注"""
  remark: String
}

input BindAdAccountInput {
  """VCC卡片ID"""
  vccCardId: String!

  """广告账户ID列表"""
  adAccountIds: [String!]!
}

input CreateVccTransactionInputDTO {
  """VCC卡片ID"""
  cardId: ID!

  """交易金额"""
  amount: Float!

  """商户名称"""
  merchant: String!

  """交易时间"""
  transactionTime: String!

  """交易状态"""
  status: TransactionStatus!

  """交易类型"""
  type: TransactionType!

  """Facebook广告账户ID"""
  facebookAccountId: String

  """Facebook交易ID"""
  facebookTransactionId: String

  """广告活动名称"""
  campaignName: String

  """交易描述"""
  description: String
}

input UpdateVccTransactionInputDTO {
  """交易状态"""
  status: TransactionStatus

  """交易描述"""
  description: String
}

input SyncFacebookTransactionsInputDTO {
  """VCC卡片ID"""
  cardId: ID!

  """同步开始日期"""
  startDate: String

  """同步结束日期"""
  endDate: String

  """是否强制覆盖已有记录"""
  forceOverwrite: Boolean = false
}

input CreateTenantInput {
  name: String!
  logo: String
  type: String
  db_config: JSON
  sync_config: JSON
  createAccount: Boolean
  accountUsername: String
  accountPassword: String
}

input UpdateTenantInput {
  id: ID!
  name: String
  logo: String
  type: String
  db_config: JSON
  sync_config: JSON
}

input CreateAudience {
  id: ID!
  audienceId: String
  name: String!
  geo_locations: AudienceGeoLocationInput!
  excluded_geo_locations: AudienceGeoLocationInput
  locales: [Float!]
  gender: Float
  age_min: Float
  age_max: Float
  interests: [AudienceInterestInput!]
  behaviors: [AudienceBehaviorInput!]
  custom_audiences: JSON
  notes: String
  raw: JSON
  platform: [String!]
}

input TestInput {
  name: String!
  platform: String!
}

input AdAccountInput {
  accountId: String
  account: String!
  password: String
  tag: String
  channel: String
  holder: String
  adNumber: String
  remark: String
}

input UpdateAdSetInput {
  id: ID!
  name: String!
  campaignId: ID!
  audienceId: ID!
  dailyBudget: Float!
  startTime: DateTime!
  endTime: DateTime
  optimizationGoal: String!
  billingEvent: String!
  status: String!
  bid_amount: Float
  applicationId: String
  objectStoreUrl: String
  appEventType: String
  pixelId: String
  customEventType: String
  promoted_object: JSON
}

input CreatePromotionDto {
  code: String!
  platform: String!
  landingPageCode: String!
  landingPageUrl: String!
  status: String!
  platformParams: JSON

  """由后端自动填充"""
  tenantId: String
  appDomain: String
}

input UpdatePromotionDto {
  code: String
  platform: String
  landingPageCode: String
  landingPageUrl: String
  status: String
  platformParams: JSON

  """由后端自动填充"""
  tenantId: String
  appDomain: String
}

input CreateCampaignInputX {
  name: String!
  objective: String!
  status: String!
  specialAdCategories: [String!]!
  accountId: String!
}

input UpdateLandingPageInput {
  title: String
  url: String
  slogan: String
  appIcon: [String!]
  background: [String!]
  description: String
  star: Float
  promoImages: [String!]
  creator: String

  """由后端自动填充"""
  tenantId: String
}

input LoginInput {
  username: String!
  password: String!
}

input RegisterInput {
  username: String!
  email: String!
  password: String!
  fullName: String
  roleIds: [ID!]
  groupIds: [ID!]
  tenantId: String!
}

input CreateUserInput {
  username: String!
  email: String!
  password: String!
  avatar: String
  fullName: String
  phone: String
  address: String
  registerIp: String
  status: UserStatus = ACTIVE
  roleIds: [ID!]
  groupIds: [ID!]
  tenantId: String
}

input UpdateUserInput {
  id: ID!
  username: String
  email: String
  password: String
  avatar: String
  fullName: String
  phone: String
  address: String
  registerIp: String
  status: UserStatus
  roleIds: [ID!]
  groupIds: [ID!]
  tenantId: String
}

input RuleConditionInput {
  field: String!
  operator: String!
  value: String!
  unit: String
}

input UpdateOneAudienceDTOInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: UpdateAudienceDTO!
}

input UpdateAudienceDTO {
  id: ID
  audienceId: String
  name: String
  geo_locations: AudienceGeoLocationInput
  excluded_geo_locations: AudienceGeoLocationInput
  locales: [Float!]
  gender: Float
  age_min: Float
  age_max: Float
  interests: [AudienceInterestInput!]
  behaviors: [AudienceBehaviorInput!]
  custom_audiences: JSON
  notes: String
  approximateCount: Float
  raw: JSON
  platform: [String!]
  tenantId: String
  tenantName: String
}

input UpdateManyAudienceDTOSInput {
  """Filter used to find fields to update"""
  filter: AudienceDTOUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: UpdateAudienceDTO!
}

input AudienceDTOUpdateFilter {
  and: [AudienceDTOUpdateFilter!]
  or: [AudienceDTOUpdateFilter!]
  id: IDFilterComparison
  audienceId: StringFieldComparison
  name: StringFieldComparison
  notes: StringFieldComparison
  approximateCount: NumberFieldComparison
  tenantId: StringFieldComparison
  tenantName: StringFieldComparison
}

input DeleteOneAudienceDTOInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyAudienceDTOSInput {
  """Filter to find records to delete"""
  filter: AudienceDTODeleteFilter!
}

input AudienceDTODeleteFilter {
  and: [AudienceDTODeleteFilter!]
  or: [AudienceDTODeleteFilter!]
  id: IDFilterComparison
  audienceId: StringFieldComparison
  name: StringFieldComparison
  notes: StringFieldComparison
  approximateCount: NumberFieldComparison
  tenantId: StringFieldComparison
  tenantName: StringFieldComparison
}

input CreateRoleInput {
  name: String!
  description: String
  status: String
  order: Int
  routeIds: [String!]
}

input UpdateRoleInput {
  id: ID!
  name: String
  description: String
  status: String
  order: Int
  routeIds: [String!]
}

input DeleteRoleInput {
  id: ID!
}

input CreateOneMateriaCreateInput {
  """The record to create"""
  materiaCreate: CreateMateriaCreate!
}

input CreateMateriaCreate {
  id: Int
  userId: String!
  name: String!
  materials: Float!
  pageLading: String!
  createId: String
  slogan: String
  account: String!
  pageId: String!
  tenantId: String!
  createdAt: DateTime
  updatedAt: DateTime
}

input CreateManyMateriaCreatesInput {
  """Array of records to create"""
  materiaCreates: [CreateMateriaCreate!]!
}

input UpdateOneMateriaCreateInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: UpdateMateriaCreate!
}

input UpdateMateriaCreate {
  id: Int
  userId: String
  name: String
  materials: Float
  pageLading: String
  createId: String
  slogan: String
  account: String
  pageId: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

input UpdateManyMateriaCreatesInput {
  """Filter used to find fields to update"""
  filter: materiaCreateUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: UpdateMateriaCreate!
}

input materiaCreateUpdateFilter {
  and: [materiaCreateUpdateFilter!]
  or: [materiaCreateUpdateFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  name: StringFieldComparison
  materials: NumberFieldComparison
  pageLading: StringFieldComparison
  createId: StringFieldComparison
  slogan: StringFieldComparison
  account: StringFieldComparison
  pageId: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input DeleteOneMateriaCreateInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyMateriaCreatesInput {
  """Filter to find records to delete"""
  filter: materiaCreateDeleteFilter!
}

input materiaCreateDeleteFilter {
  and: [materiaCreateDeleteFilter!]
  or: [materiaCreateDeleteFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  name: StringFieldComparison
  materials: NumberFieldComparison
  pageLading: StringFieldComparison
  createId: StringFieldComparison
  slogan: StringFieldComparison
  account: StringFieldComparison
  pageId: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input CreateOneMaterialManagementInput {
  """The record to create"""
  materialManagement: CreateMaterialManagement!
}

input CreateMaterialManagement {
  id: Int
  fileName: String
  tags: String
  group: String
  facebookImageId: String
  facebookVedioId: String
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: String
  tenantId: String
  notes: String
  createdAt: DateTime
  updatedAt: DateTime
}

input CreateManyMaterialManagementsInput {
  """Array of records to create"""
  materialManagements: [CreateMaterialManagement!]!
}

input UpdateOneMaterialManagementInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: UpdateMaterialManagement!
}

input UpdateMaterialManagement {
  id: Int
  fileName: String
  tags: String
  group: String
  facebookImageId: String
  facebookVedioId: String
  adCount: Int
  activeAdCount: Int
  bannedCount: Int
  spend: Int
  conversion: Int
  commission: Int
  file: String
  tenantId: String
  notes: String
  createdAt: DateTime
  updatedAt: DateTime
}

input UpdateManyMaterialManagementsInput {
  """Filter used to find fields to update"""
  filter: materialManagementUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: UpdateMaterialManagement!
}

input materialManagementUpdateFilter {
  and: [materialManagementUpdateFilter!]
  or: [materialManagementUpdateFilter!]
  id: IntFieldComparison
  fileName: StringFieldComparison
  tags: StringFieldComparison
  group: StringFieldComparison
  facebookImageId: StringFieldComparison
  facebookVedioId: StringFieldComparison
  adCount: IntFieldComparison
  activeAdCount: IntFieldComparison
  bannedCount: IntFieldComparison
  spend: IntFieldComparison
  conversion: IntFieldComparison
  commission: IntFieldComparison
  file: StringFieldComparison
  tenantId: StringFieldComparison
  notes: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input DeleteOneMaterialManagementInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyMaterialManagementsInput {
  """Filter to find records to delete"""
  filter: materialManagementDeleteFilter!
}

input materialManagementDeleteFilter {
  and: [materialManagementDeleteFilter!]
  or: [materialManagementDeleteFilter!]
  id: IntFieldComparison
  fileName: StringFieldComparison
  tags: StringFieldComparison
  group: StringFieldComparison
  facebookImageId: StringFieldComparison
  facebookVedioId: StringFieldComparison
  adCount: IntFieldComparison
  activeAdCount: IntFieldComparison
  bannedCount: IntFieldComparison
  spend: IntFieldComparison
  conversion: IntFieldComparison
  commission: IntFieldComparison
  file: StringFieldComparison
  tenantId: StringFieldComparison
  notes: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input UpdateOneIpRestrictionInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: UpdateIpRestriction!
}

input UpdateIpRestriction {
  id: Int
  ip: String
  userId: String
  status: String
  operator: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

input UpdateManyIpRestrictionsInput {
  """Filter used to find fields to update"""
  filter: ipRestrictionUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: UpdateIpRestriction!
}

input ipRestrictionUpdateFilter {
  and: [ipRestrictionUpdateFilter!]
  or: [ipRestrictionUpdateFilter!]
  id: IntFieldComparison
  ip: StringFieldComparison
  userId: StringFieldComparison
  status: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input DeleteOneIpRestrictionInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyIpRestrictionsInput {
  """Filter to find records to delete"""
  filter: ipRestrictionDeleteFilter!
}

input ipRestrictionDeleteFilter {
  and: [ipRestrictionDeleteFilter!]
  or: [ipRestrictionDeleteFilter!]
  id: IntFieldComparison
  ip: StringFieldComparison
  userId: StringFieldComparison
  status: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input CreateRouteInput {
  parentId: ID
  name: String!
  path: String!
  component: String
  icon: String
  order: Float!
  type: String!
  status: String!
  isHidden: Boolean!
  description: String
}

input UpdateOneRouteInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: UpdateRouteInput!
}

input UpdateRouteInput {
  parentId: ID
  name: String
  path: String
  component: String
  icon: String
  order: Float
  type: String
  status: String
  isHidden: Boolean
  description: String
}

input CreateOneRouteInput {
  """The record to create"""
  route: CreateRouteInput!
}

input CreateManyRoutesInput {
  """Array of records to create"""
  routes: [CreateRouteInput!]!
}

input UpdateManyRoutesInput {
  """Filter used to find fields to update"""
  filter: RouteUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: UpdateRouteInput!
}

input RouteUpdateFilter {
  and: [RouteUpdateFilter!]
  or: [RouteUpdateFilter!]
  id: IDFilterComparison
  parentId: IDFilterComparison
  name: StringFieldComparison
  path: StringFieldComparison
  component: StringFieldComparison
  icon: StringFieldComparison
  order: NumberFieldComparison
  type: StringFieldComparison
  status: StringFieldComparison
  isHidden: BooleanFieldComparison
  description: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input DeleteOneRouteInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyRoutesInput {
  """Filter to find records to delete"""
  filter: RouteDeleteFilter!
}

input RouteDeleteFilter {
  and: [RouteDeleteFilter!]
  or: [RouteDeleteFilter!]
  id: IDFilterComparison
  parentId: IDFilterComparison
  name: StringFieldComparison
  path: StringFieldComparison
  component: StringFieldComparison
  icon: StringFieldComparison
  order: NumberFieldComparison
  type: StringFieldComparison
  status: StringFieldComparison
  isHidden: BooleanFieldComparison
  description: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input CreateOneGroupInput {
  """The record to create"""
  group: CreateGroupInput!
}

input CreateGroupInput {
  name: String!
  belongTo: String
  contactInfo: String
  description: String
  memberCount: Float
  status: String!
  tenantId: String
  permissions: JSON
}

input CreateManyGroupsInput {
  """Array of records to create"""
  groups: [CreateGroupInput!]!
}

input UpdateOneGroupInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: CreateGroupInput!
}

input UpdateManyGroupsInput {
  """Filter used to find fields to update"""
  filter: GroupUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: CreateGroupInput!
}

input GroupUpdateFilter {
  and: [GroupUpdateFilter!]
  or: [GroupUpdateFilter!]
  id: IDFilterComparison
  name: StringFieldComparison
  belongTo: StringFieldComparison
  contactInfo: StringFieldComparison
  description: StringFieldComparison
  memberCount: IntFieldComparison
  permissions: JSONFilterComparison
  status: StringFieldComparison
  createTime: DateFieldComparison
  updateTime: DateFieldComparison
  tenantId: StringFieldComparison
}

input DeleteOneGroupInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyGroupsInput {
  """Filter to find records to delete"""
  filter: GroupDeleteFilter!
}

input GroupDeleteFilter {
  and: [GroupDeleteFilter!]
  or: [GroupDeleteFilter!]
  id: IDFilterComparison
  name: StringFieldComparison
  belongTo: StringFieldComparison
  contactInfo: StringFieldComparison
  description: StringFieldComparison
  memberCount: IntFieldComparison
  permissions: JSONFilterComparison
  status: StringFieldComparison
  createTime: DateFieldComparison
  updateTime: DateFieldComparison
  tenantId: StringFieldComparison
}

input CreateOneAntiBlockInput {
  """The record to create"""
  antiBlock: CreateAntiBlock!
}

input CreateAntiBlock {
  id: Int
  userId: String
  url: String
  status: String
  name: String
  relatedDomain: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

input UpdateOneAntiBlockInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: UpdateAntiBlock!
}

input UpdateAntiBlock {
  id: Int
  userId: String
  url: String
  status: String
  name: String
  relatedDomain: String
  tenantId: String
  createdAt: DateTime
  updatedAt: DateTime
}

input UpdateManyAntiBlocksInput {
  """Filter used to find fields to update"""
  filter: AntiBlockUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: UpdateAntiBlock!
}

input AntiBlockUpdateFilter {
  and: [AntiBlockUpdateFilter!]
  or: [AntiBlockUpdateFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  url: StringFieldComparison
  status: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input DeleteOneAntiBlockInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyAntiBlocksInput {
  """Filter to find records to delete"""
  filter: AntiBlockDeleteFilter!
}

input AntiBlockDeleteFilter {
  and: [AntiBlockDeleteFilter!]
  or: [AntiBlockDeleteFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  url: StringFieldComparison
  status: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input CreateOneEmployeeInput {
  """The record to create"""
  employee: CreateEmployee!
}

input CreateEmployee {
  id: ID
  firstName: String
  lastName: String
  name: String
  createdAt: DateTime
  updatedAt: DateTime
  isActive: Boolean!
  isArchived: Boolean!
}

input CreateManyEmployeesInput {
  """Array of records to create"""
  employees: [CreateEmployee!]!
}

input UpdateOneEmployeeInput {
  """The id of the record to update"""
  id: ID!

  """The update to apply."""
  update: UpdateEmployee!
}

input UpdateEmployee {
  id: ID
  firstName: String
  lastName: String
  name: String
  createdAt: DateTime
  updatedAt: DateTime
  isActive: Boolean
  isArchived: Boolean
}

input UpdateManyEmployeesInput {
  """Filter used to find fields to update"""
  filter: EmployeeUpdateFilter!

  """The update to apply to all records found using the filter"""
  update: UpdateEmployee!
}

input EmployeeUpdateFilter {
  and: [EmployeeUpdateFilter!]
  or: [EmployeeUpdateFilter!]
  id: IDFilterComparison
  firstName: StringFieldComparison
  lastName: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
  isActive: BooleanFieldComparison
  isArchived: BooleanFieldComparison
}

input DeleteOneEmployeeInput {
  """The id of the record to delete."""
  id: ID!
}

input DeleteManyEmployeesInput {
  """Filter to find records to delete"""
  filter: EmployeeDeleteFilter!
}

input EmployeeDeleteFilter {
  and: [EmployeeDeleteFilter!]
  or: [EmployeeDeleteFilter!]
  id: IDFilterComparison
  firstName: StringFieldComparison
  lastName: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
  isActive: BooleanFieldComparison
  isArchived: BooleanFieldComparison
}

type Subscription {
  createdAdDTO(input: CreateAdDTOSubscriptionFilterInput): AdDTO!
  updatedOneAdDTO(input: UpdateOneAdDTOSubscriptionFilterInput): AdDTO!
  updatedManyAdDTOS: UpdateManyResponse!
  deletedOneAdDTO(input: DeleteOneAdDTOSubscriptionFilterInput): AdDTODeleteResponse!
  deletedManyAdDTOS: DeleteManyResponse!
  createdAdCampaign(input: CreateAdCampaignSubscriptionFilterInput): AdCampaign!
  updatedOneAdCampaign(input: UpdateOneAdCampaignSubscriptionFilterInput): AdCampaign!
  updatedManyAdCampaigns: UpdateManyResponse!
  deletedOneAdCampaign(input: DeleteOneAdCampaignSubscriptionFilterInput): AdCampaignDeleteResponse!
  deletedManyAdCampaigns: DeleteManyResponse!
  createdAdCreative(input: CreateAdCreativeSubscriptionFilterInput): AdCreative!
  updatedOneAdCreative(input: UpdateOneAdCreativeSubscriptionFilterInput): AdCreative!
  updatedManyAdCreatives: UpdateManyResponse!
  deletedOneAdCreative(input: DeleteOneAdCreativeSubscriptionFilterInput): AdCreativeDeleteResponse!
  deletedManyAdCreatives: DeleteManyResponse!
  createdAdSet(input: CreateAdSetSubscriptionFilterInput): AdSet!
  updatedOneAdSet(input: UpdateOneAdSetSubscriptionFilterInput): AdSet!
  updatedManyAdSets: UpdateManyResponse!
  deletedOneAdSet(input: DeleteOneAdSetSubscriptionFilterInput): AdSetDeleteResponse!
  deletedManyAdSets: DeleteManyResponse!
  createdAudienceDTO(input: CreateAudienceDTOSubscriptionFilterInput): AudienceDTO!
  updatedOneAudienceDTO(input: UpdateOneAudienceDTOSubscriptionFilterInput): AudienceDTO!
  updatedManyAudienceDTOS: UpdateManyResponse!
  deletedOneAudienceDTO(input: DeleteOneAudienceDTOSubscriptionFilterInput): AudienceDTODeleteResponse!
  deletedManyAudienceDTOS: DeleteManyResponse!
  createdMaterial(input: CreateMaterialSubscriptionFilterInput): Material!
  updatedOneMaterial(input: UpdateOneMaterialSubscriptionFilterInput): Material!
  updatedManyMaterials: UpdateManyResponse!
  deletedOneMaterial(input: DeleteOneMaterialSubscriptionFilterInput): MaterialDeleteResponse!
  deletedManyMaterials: DeleteManyResponse!
  createdVcc(input: CreateVccSubscriptionFilterInput): Vcc!
  updatedOneVcc(input: UpdateOneVccSubscriptionFilterInput): Vcc!
  updatedManyVccs: UpdateManyResponse!
  deletedOneVcc(input: DeleteOneVccSubscriptionFilterInput): VccDeleteResponse!
  deletedManyVccs: DeleteManyResponse!
  createdMateriaCreate(input: CreateMateriaCreateSubscriptionFilterInput): materiaCreate!
  updatedOneMateriaCreate(input: UpdateOneMateriaCreateSubscriptionFilterInput): materiaCreate!
  updatedManyMateriaCreates: UpdateManyResponse!
  deletedOneMateriaCreate(input: DeleteOneMateriaCreateSubscriptionFilterInput): MateriaCreateDeleteResponse!
  deletedManyMateriaCreates: DeleteManyResponse!
  createdMaterialManagement(input: CreateMaterialManagementSubscriptionFilterInput): materialManagement!
  updatedOneMaterialManagement(input: UpdateOneMaterialManagementSubscriptionFilterInput): materialManagement!
  updatedManyMaterialManagements: UpdateManyResponse!
  deletedOneMaterialManagement(input: DeleteOneMaterialManagementSubscriptionFilterInput): MaterialManagementDeleteResponse!
  deletedManyMaterialManagements: DeleteManyResponse!
  createdIpRestriction(input: CreateIpRestrictionSubscriptionFilterInput): ipRestriction!
  updatedOneIpRestriction(input: UpdateOneIpRestrictionSubscriptionFilterInput): ipRestriction!
  updatedManyIpRestrictions: UpdateManyResponse!
  deletedOneIpRestriction(input: DeleteOneIpRestrictionSubscriptionFilterInput): IpRestrictionDeleteResponse!
  deletedManyIpRestrictions: DeleteManyResponse!
  createdGroup(input: CreateGroupSubscriptionFilterInput): Group!
  updatedOneGroup(input: UpdateOneGroupSubscriptionFilterInput): Group!
  updatedManyGroups: UpdateManyResponse!
  deletedOneGroup(input: DeleteOneGroupSubscriptionFilterInput): GroupDeleteResponse!
  deletedManyGroups: DeleteManyResponse!
  createdAntiBlock(input: CreateAntiBlockSubscriptionFilterInput): AntiBlock!
  updatedOneAntiBlock(input: UpdateOneAntiBlockSubscriptionFilterInput): AntiBlock!
  updatedManyAntiBlocks: UpdateManyResponse!
  deletedOneAntiBlock(input: DeleteOneAntiBlockSubscriptionFilterInput): AntiBlockDeleteResponse!
  deletedManyAntiBlocks: DeleteManyResponse!
  createdEmployee(input: CreateEmployeeSubscriptionFilterInput): Employee!
  updatedOneEmployee(input: UpdateOneEmployeeSubscriptionFilterInput): Employee!
  updatedManyEmployees: UpdateManyResponse!
  deletedOneEmployee(input: DeleteOneEmployeeSubscriptionFilterInput): EmployeeDeleteResponse!
  deletedManyEmployees: DeleteManyResponse!
}

input CreateAdDTOSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdDTOSubscriptionFilter!
}

input AdDTOSubscriptionFilter {
  and: [AdDTOSubscriptionFilter!]
  or: [AdDTOSubscriptionFilter!]
  raw: JSONFilterComparison
  insight: JSONFilterComparison
}

input UpdateOneAdDTOSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdDTOSubscriptionFilter!
}

input DeleteOneAdDTOSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdDTOSubscriptionFilter!
}

input CreateAdCampaignSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdCampaignSubscriptionFilter!
}

input AdCampaignSubscriptionFilter {
  and: [AdCampaignSubscriptionFilter!]
  or: [AdCampaignSubscriptionFilter!]
  id: IDFilterComparison
  platform: StringFieldComparison
  campaignId: StringFieldComparison
  name: StringFieldComparison
  raw: JSONFilterComparison
  status: StringFieldComparison
}

input UpdateOneAdCampaignSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdCampaignSubscriptionFilter!
}

input DeleteOneAdCampaignSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdCampaignSubscriptionFilter!
}

input CreateAdCreativeSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdCreativeSubscriptionFilter!
}

input AdCreativeSubscriptionFilter {
  and: [AdCreativeSubscriptionFilter!]
  or: [AdCreativeSubscriptionFilter!]
  id: IDFilterComparison
  platform: StringFieldComparison
  creativeId: StringFieldComparison
  name: StringFieldComparison
  raw: JSONFilterComparison
  status: StringFieldComparison
}

input UpdateOneAdCreativeSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdCreativeSubscriptionFilter!
}

input DeleteOneAdCreativeSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdCreativeSubscriptionFilter!
}

input CreateAdSetSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdSetSubscriptionFilter!
}

input AdSetSubscriptionFilter {
  and: [AdSetSubscriptionFilter!]
  or: [AdSetSubscriptionFilter!]
  id: IDFilterComparison
  platform: StringFieldComparison
  adsetId: StringFieldComparison
  name: StringFieldComparison
  raw: JSONFilterComparison
  status: StringFieldComparison
}

input UpdateOneAdSetSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdSetSubscriptionFilter!
}

input DeleteOneAdSetSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AdSetSubscriptionFilter!
}

input CreateAudienceDTOSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AudienceDTOSubscriptionFilter!
}

input AudienceDTOSubscriptionFilter {
  and: [AudienceDTOSubscriptionFilter!]
  or: [AudienceDTOSubscriptionFilter!]
  id: IDFilterComparison
  audienceId: StringFieldComparison
  name: StringFieldComparison
  notes: StringFieldComparison
  approximateCount: NumberFieldComparison
  tenantId: StringFieldComparison
  tenantName: StringFieldComparison
}

input UpdateOneAudienceDTOSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AudienceDTOSubscriptionFilter!
}

input DeleteOneAudienceDTOSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AudienceDTOSubscriptionFilter!
}

input CreateMaterialSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: MaterialSubscriptionFilter!
}

input MaterialSubscriptionFilter {
  and: [MaterialSubscriptionFilter!]
  or: [MaterialSubscriptionFilter!]
  id: IDFilterComparison
  type: StringFieldComparison
  name: StringFieldComparison
  url: StringFieldComparison
  content: StringFieldComparison
  status: StringFieldComparison
  raw: JSONFilterComparison
}

input UpdateOneMaterialSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: MaterialSubscriptionFilter!
}

input DeleteOneMaterialSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: MaterialSubscriptionFilter!
}

input CreateVccSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: VccSubscriptionFilter!
}

input VccSubscriptionFilter {
  and: [VccSubscriptionFilter!]
  or: [VccSubscriptionFilter!]
  id: IDFilterComparison
  cardNumber: StringFieldComparison
  cardHolder: StringFieldComparison
  expiry: StringFieldComparison
  cvv: StringFieldComparison
  bindAccount: StringFieldComparison
  status: StringFieldComparison
  raw: JSONFilterComparison
}

input UpdateOneVccSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: VccSubscriptionFilter!
}

input DeleteOneVccSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: VccSubscriptionFilter!
}

input CreateMateriaCreateSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: materiaCreateSubscriptionFilter!
}

input materiaCreateSubscriptionFilter {
  and: [materiaCreateSubscriptionFilter!]
  or: [materiaCreateSubscriptionFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  name: StringFieldComparison
  materials: NumberFieldComparison
  pageLading: StringFieldComparison
  createId: StringFieldComparison
  slogan: StringFieldComparison
  account: StringFieldComparison
  pageId: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input UpdateOneMateriaCreateSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: materiaCreateSubscriptionFilter!
}

input DeleteOneMateriaCreateSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: materiaCreateSubscriptionFilter!
}

input CreateMaterialManagementSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: materialManagementSubscriptionFilter!
}

input materialManagementSubscriptionFilter {
  and: [materialManagementSubscriptionFilter!]
  or: [materialManagementSubscriptionFilter!]
  id: IntFieldComparison
  fileName: StringFieldComparison
  tags: StringFieldComparison
  group: StringFieldComparison
  facebookImageId: StringFieldComparison
  facebookVedioId: StringFieldComparison
  adCount: IntFieldComparison
  activeAdCount: IntFieldComparison
  bannedCount: IntFieldComparison
  spend: IntFieldComparison
  conversion: IntFieldComparison
  commission: IntFieldComparison
  file: StringFieldComparison
  tenantId: StringFieldComparison
  notes: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input UpdateOneMaterialManagementSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: materialManagementSubscriptionFilter!
}

input DeleteOneMaterialManagementSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: materialManagementSubscriptionFilter!
}

input CreateIpRestrictionSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: ipRestrictionSubscriptionFilter!
}

input ipRestrictionSubscriptionFilter {
  and: [ipRestrictionSubscriptionFilter!]
  or: [ipRestrictionSubscriptionFilter!]
  id: IntFieldComparison
  ip: StringFieldComparison
  userId: StringFieldComparison
  status: StringFieldComparison
  tenantId: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input UpdateOneIpRestrictionSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: ipRestrictionSubscriptionFilter!
}

input DeleteOneIpRestrictionSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: ipRestrictionSubscriptionFilter!
}

input CreateGroupSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: GroupSubscriptionFilter!
}

input GroupSubscriptionFilter {
  and: [GroupSubscriptionFilter!]
  or: [GroupSubscriptionFilter!]
  id: IDFilterComparison
  name: StringFieldComparison
  belongTo: StringFieldComparison
  contactInfo: StringFieldComparison
  description: StringFieldComparison
  memberCount: IntFieldComparison
  permissions: JSONFilterComparison
  status: StringFieldComparison
  createTime: DateFieldComparison
  updateTime: DateFieldComparison
  tenantId: StringFieldComparison
}

input UpdateOneGroupSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: GroupSubscriptionFilter!
}

input DeleteOneGroupSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: GroupSubscriptionFilter!
}

input CreateAntiBlockSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AntiBlockSubscriptionFilter!
}

input AntiBlockSubscriptionFilter {
  and: [AntiBlockSubscriptionFilter!]
  or: [AntiBlockSubscriptionFilter!]
  id: IntFieldComparison
  userId: StringFieldComparison
  url: StringFieldComparison
  status: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
}

input UpdateOneAntiBlockSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AntiBlockSubscriptionFilter!
}

input DeleteOneAntiBlockSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: AntiBlockSubscriptionFilter!
}

input CreateEmployeeSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: EmployeeSubscriptionFilter!
}

input EmployeeSubscriptionFilter {
  and: [EmployeeSubscriptionFilter!]
  or: [EmployeeSubscriptionFilter!]
  id: IDFilterComparison
  firstName: StringFieldComparison
  lastName: StringFieldComparison
  createdAt: DateFieldComparison
  updatedAt: DateFieldComparison
  isActive: BooleanFieldComparison
  isArchived: BooleanFieldComparison
}

input UpdateOneEmployeeSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: EmployeeSubscriptionFilter!
}

input DeleteOneEmployeeSubscriptionFilterInput {
  """Specify to filter the records returned."""
  filter: EmployeeSubscriptionFilter!
}