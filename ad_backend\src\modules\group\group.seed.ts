import { Connection } from 'typeorm';
import { Group } from './group.entity';
import { GroupStatus } from './dto/group.dto';

export const createGroups = async (
    connection: Connection
): Promise<Group[]> => {
    const groups = new Array<Group>();

    const group1 = new Group();
    group1.name = '技术部';
    group1.description = '负责系统开发和维护';
    group1.belongTo = '张三';
    group1.contactInfo = '***********';
    group1.memberCount = 8;
    group1.status = GroupStatus.ACTIVE;
    group1.permissions = {
        systems: ['system:user', 'system:role', 'system:group'],
        ads: ['ad:account', 'ad:material']
    };
    groups.push(group1);

    const group2 = new Group();
    group2.name = '运营部';
    group2.description = '负责日常运营和推广';
    group2.belongTo = '李四';
    group2.contactInfo = '***********';
    group2.memberCount = 12;
    group2.status = GroupStatus.ACTIVE;
    group2.permissions = {
        systems: ['system:user'],
        ads: ['ad:account', 'ad:material', 'ad:campaign']
    };
    groups.push(group2);

    const group3 = new Group();
    group3.name = '内容部';
    group3.description = '负责内容创作和审核';
    group3.belongTo = '王五';
    group3.contactInfo = '***********';
    group3.memberCount = 6;
    group3.status = GroupStatus.DISABLED;
    group3.permissions = {
        systems: [],
        ads: ['ad:material']
    };
    groups.push(group3);

    return await insertGroups(connection, groups);
};

const insertGroups = async (
    connection: Connection,
    groups: Group[]
) => {
    const repo = connection.getRepository(Group);

    await Promise.all(
        groups.map(async (group) => {
            const existed = await repo.findOne({
                where: {
                    name: group.name,
                },
            });

            if (existed) {
                group.id = existed.id;
            }
        })
    );

    return await repo.save(groups);
}; 