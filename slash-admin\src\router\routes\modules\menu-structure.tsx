import { useTranslation } from 'react-i18next';
import { lazy, Suspense } from "react";
import { Icon } from "@/components/icon";
import { LineLoading } from "@/components/loading";
import { Outlet } from "react-router";
import type { AppRouteObject } from "#/router";

// 懒加载组件
const AutoOptimizationPage = lazy(() => import("@/pages/dashboard/auto-optimization"));

// 使用已有页面作为临时替代，直到实际页面创建完成
const MaterialManagementPage = lazy(() => import("@/pages/management/user/profile"));
const MaterialGroupPage = lazy(() => import("@/pages/management/user/profile"));
const MaterialAuditPage = lazy(() => import("@/pages/management/user/profile"));
const AntiBlockDomainPage = lazy(() => import("@/pages/management/domain/anti-block"));
const LandingPageManagementPage = lazy(() => import("@/pages/management/landing-page"));
const AdAccountPage = lazy(() => import("@/pages/management/ad-account"));
const AdCreativePage = lazy(() => import("@/pages/management/ad-account"));
const AdCampaignPage = lazy(() => import("@/pages/management/campaign"));
const AdGroupPage = lazy(() => import("@/pages/management/ad-account"));
const AdPage = lazy(() => import("@/pages/management/ad-account"));
const AudienceManagementPage = lazy(() => import("@/pages/management/facebook/audience"));
const CardManagementPage = lazy(() => import("@/pages/management/system/organization"));
const CardGroupPage = lazy(() => import("@/pages/management/system/organization"));
const IPRestrictionPage = lazy(() => import("@/pages/management/dofend/ip-restriction"));
const AccountManagementPage = lazy(() => import("@/pages/management/system/account"));
const RoleManagementPage = lazy(() => import("@/pages/management/system/role"));
const GroupManagementPage = lazy(() => import("@/pages/management/system/group"));
const RouteManagementPage = lazy(() => import("@/pages/management/system/route"));
const RiskControlManagement = lazy(() => import("@/pages/management/risk-control"));

function Wrapper({ children }: any) {
  const { t } = useTranslation();
  return <Suspense fallback={<LineLoading />}>{children}</Suspense>;
}

// 2. 自动化任务
const autoTaskMenu: AppRouteObject = {
  path: "auto-task",
  order: 2,
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: 'pages.key_1904',
    icon: <Icon icon="solar:settings-bold-duotone" className="ant-menu-item-icon" size="24" />,
    key: "/auto-task",
  },
  children: [
    {
      path: "promotion-task",
      element: (
        <Wrapper>
          <AdCampaignPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_1021',
        key: "/auto-task/promotion-task",
      },
    },
    {
      path: "auto-optimization",
      element: (
        <Wrapper>
          <AutoOptimizationPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_1902',
        key: "/auto-task/auto-optimization",
      },
    },
  ],
};

// 3. 物料管理
const materialMenu: AppRouteObject = {
  path: "material",
  order: 3,
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: 'pages.key_1597',
    icon: <Icon icon="solar:gallery-bold-duotone" className="ant-menu-item-icon" size="24" />,
    key: "/material",
  },
  children: [
    {
      path: "management",
      element: (
        <Wrapper>
          <MaterialManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_1787',
        key: "/material/management",
      },
    }
  ],
};

// 4. 域名管理
const domainMenu: AppRouteObject = {
  path: "domain",
  order: 4,
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: 'pages.key_554',
    icon: <Icon icon="solar:globe-bold-duotone" className="ant-menu-item-icon" size="24" />,
    key: "/domain",
  },
  children: [
    {
      path: "anti-block",
      element: (
        <Wrapper>
          <AntiBlockDomainPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_2515',
        key: "/domain/anti-block",
      },
    },
    {
      path: "landing-page",
      element: (
        <Wrapper>
          <LandingPageManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_2015',
        key: "/domain/landing-page",
      },
    },
  ],
};

// 5. Facebook管理
const facebookMenu: AppRouteObject = {
  path: "facebook",
  order: 5,
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: "Facebook管理",
    icon: <Icon icon="solar:share-bold-duotone" className="ant-menu-item-icon" size="24" />,
    key: "/facebook",
  },
  children: [
    {
      path: "ad-account",
      element: (
        <Wrapper>
          <AdAccountPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_819',
        key: "/facebook/ad-account",
      },
    },
    {
      path: "ad-campaign",
      element: (
        <Wrapper>
          <AdCampaignPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_806',
        key: "/facebook/ad-campaign",
      },
    },
    {
      path: "audience",
      element: (
        <Wrapper>
          <AudienceManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_419',
        key: "/facebook/audience",
      },
    },
  ],
};

// 6. 卡片管理
const cardMenu: AppRouteObject = {
  path: "card",
  order: 6,
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: 'pages.key_382',
    icon: <Icon icon="solar:card-bold-duotone" className="ant-menu-item-icon" size="24" />,
    key: "/card",
  },
  children: [
    {
      path: "management",
      element: (
        <Wrapper>
          <CardManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_382',
        key: "/card/management",
      },
    }
  ],
};

// 7. 斗篷系统
const dofendMenu: AppRouteObject = {
  path: "dofend",
  order: 7,
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: 'pages.key_1126',
    icon: <Icon icon="solar:shield-bold-duotone" className="ant-menu-item-icon" size="24" />,
    key: "/dofend",
  },
  children: [
    {
      path: "ip-restriction",
      element: (
        <Wrapper>
          <IPRestrictionPage />
        </Wrapper>
      ),
      meta: {
        label: "IP限制",
        key: "/dofend/ip-restriction",
      },
    },
  ],
};

// 8. 系统设置
const systemMenu: AppRouteObject = {
  path: "system",
  order: 8,
  element: (
    <Suspense fallback={<LineLoading />}>
      <Outlet />
    </Suspense>
  ),
  meta: {
    label: 'pages.key_1780',
    icon: <Icon icon="solar:settings-bold-duotone" className="ant-menu-item-icon" size="24" />,
    key: "/system",
  },
  children: [
    {
      path: "account",
      element: (
        <Wrapper>
          <AccountManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_2333',
        key: "/system/account",
      },
    },
    {
      path: "role",
      element: (
        <Wrapper>
          <RoleManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_2082',
        key: "/system/role",
      },
    },
    {
      path: "group",
      element: (
        <Wrapper>
          <GroupManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_1889',
        key: "/system/group",
      },
    },
    {
      path: "route",
      element: (
        <Wrapper>
          <RouteManagementPage />
        </Wrapper>
      ),
      meta: {
        label: 'pages.key_2349',
        key: "/system/route",
      },
    },
  ],
};

// 导出所有菜单
// 风险控制（一级菜单）
const riskControlMenu: AppRouteObject = {
  path: "risk-control",
  order: 3.5,
  element: (
    <Wrapper>
      <RiskControlManagement />
    </Wrapper>
  ),
  meta: {
    label: 'pages.key_2581',
    icon: <Icon icon="local:ic-shield" className="ant-menu-item-icon" size="24" />,
    key: "/risk-control",
  },
};

const menuStructure: AppRouteObject[] = [
  autoTaskMenu,
  materialMenu,
  riskControlMenu,
  domainMenu,
  facebookMenu,
  cardMenu,
  dofendMenu,
  systemMenu,
];

export default menuStructure;
