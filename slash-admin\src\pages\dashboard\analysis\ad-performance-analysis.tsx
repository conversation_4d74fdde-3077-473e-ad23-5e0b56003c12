import { useTranslation } from 'react-i18next';
import { Chart, useChart } from "@/components/chart";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Button } from "@/ui/button";
import { Separator } from "@/ui/separator";
import { Icon } from "@/components/icon";

// 数据类型定义
interface BarData {
  impressions: number[];
  clicks: number[];
  conversions: number[];
}

interface LineData {
  ctr: number[];
  cvr: number[];
}

interface AdPerformanceData {
  barData: BarData;
  lineData: LineData;
  timeLabels: string[];
}

interface AdPerformanceAnalysisProps {
  data: AdPerformanceData | null;
  timeRange: TimeRange;
  onTimeRangeChange: (timeRange: TimeRange) => void;
}

type TimeRange = "day" | "week" | "month";
type MetricType = "impressions" | "clicks" | "conversions" | "ctr" | "cvr";

const metricColors: Record<MetricType, string> = {
  impressions: "#4CAF50",
  clicks: "#2196F3",
  conversions: "#9C27B0",
  ctr: "#FF9800",
  cvr: "#F44336"
};

const AdPerformanceAnalysis = ({ data, timeRange, onTimeRangeChange }: AdPerformanceAnalysisProps) => {
  const { t } = useTranslation();
  const [selectedMetrics, setSelectedMetrics] = useState<MetricType[]>(["impressions", "clicks", "conversions", "ctr", "cvr"]);

  // metricLabels 必须在组件体内定义，才能使用 t
  const metricLabels: Record<MetricType, string> = {
    impressions: t('pages.key_725'),
    clicks: t('pages.key_1590'),
    conversions: t('pages.key_2362'),
    ctr: "点击率 (%)",
    cvr: "转化率 (%)"
  };

  const handleMetricToggle = (metric: MetricType) => {
    if (selectedMetrics.includes(metric)) {
      setSelectedMetrics(selectedMetrics.filter(m => m !== metric));
    } else {
      setSelectedMetrics([...selectedMetrics, metric]);
    }
  };

  // 如果没有数据，显示加载状态
  if (!data) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-base font-medium">{t('pages.key_792')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 准备图表数据系列
  const series = selectedMetrics.map(metric => {
    let seriesData: number[];
    let seriesType: string;
    let yAxisIndex = 0;

    if (metric === "impressions") {
      seriesData = data.barData.impressions;
      seriesType = "column";
      yAxisIndex = 0;
    } else if (metric === "clicks") {
      seriesData = data.barData.clicks;
      seriesType = "column";
      yAxisIndex = 0;
    } else if (metric === "conversions") {
      seriesData = data.barData.conversions;
      seriesType = "column";
      yAxisIndex = 0;
    } else if (metric === "ctr") {
      seriesData = data.lineData.ctr;
      seriesType = "line";
      yAxisIndex = 1;
    } else if (metric === "cvr") {
      seriesData = data.lineData.cvr;
      seriesType = "line";
      yAxisIndex = 1;
    } else {
      seriesData = [];
      seriesType = "line";
    }

    return {
      name: metricLabels[metric],
      data: seriesData,
      type: seriesType,
      yAxisIndex,
    };
  });

  // 使用项目中的useChart hook
  const chartOptions = useChart({
    chart: {
      height: 300,
      type: 'line',
      stacked: false,
    },
    stroke: {
      width: [0, 0, 0, 4, 4],
      curve: 'smooth'
    },
    plotOptions: {
      bar: {
        columnWidth: '50%'
      }
    },
    fill: {
      opacity: [0.85, 0.85, 0.85, 1, 1],
    },
    labels: data.timeLabels,
    markers: {
      size: [0, 0, 0, 4, 4]
    },
    xaxis: {
      type: 'category',
      categories: data.timeLabels,
    },
    yaxis: [
      {
        title: {
          text: t('pages.key_1116'),
        },
        labels: {
          formatter: (value: number) => Math.round(value).toString(),
        },
      },
      {
        opposite: true,
        title: {
          text: '百分比 (%)',
        },
        min: 0,
        max: Math.max(...data.lineData.ctr, ...data.lineData.cvr) * 1.1,
        labels: {
          formatter: (value: number) => value.toFixed(1) + '%',
        },
      },
    ],
    colors: selectedMetrics.map(metric => metricColors[metric]),
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: function (y: number, { seriesIndex }: { seriesIndex: number }) {
          const metric = selectedMetrics[seriesIndex];
          if (["ctr", "cvr"].includes(metric)) {
            return y.toFixed(2) + "%";
          }
          return y.toLocaleString();
        }
      }
    },
    legend: {
      show: false,
      horizontalAlign: 'left',
      offsetX: 40
    }
  });

  // 计算统计数据
  const totalImpressions = data.barData.impressions.reduce((a: number, b: number) => a + b, 0);
  const totalClicks = data.barData.clicks.reduce((a: number, b: number) => a + b, 0);
  const totalConversions = data.barData.conversions.reduce((a: number, b: number) => a + b, 0);

  // 平均点击率 = 总点击量 / 总展示量 × 100%
  const avgCtr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

  // 平均转化率 = 总转化量 / 总点击量 × 100%
  const avgCvr = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-base font-medium">{t('pages.key_792')}</CardTitle>
        <div className="flex space-x-1">
          <button
            className={`px-3 py-1 text-xs rounded-md ${timeRange === 'day' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
            onClick={() => onTimeRangeChange('day')}
          >
            日
          </button>
          <button
            className={`px-3 py-1 text-xs rounded-md ${timeRange === 'week' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
            onClick={() => onTimeRangeChange('week')}
          >
            周
          </button>
          <button
            className={`px-3 py-1 text-xs rounded-md ${timeRange === 'month' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
            onClick={() => onTimeRangeChange('month')}
          >
            月
          </button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <Chart
            type="line"
            series={series}
            options={chartOptions}
            height={300}
          />
        </div>

        <Separator className="my-4" />

        <div className="flex flex-wrap gap-2 justify-center">
          {(Object.keys(metricLabels) as MetricType[]).map(metric => (
            <button
              key={metric}
              className={`flex items-center gap-1.5 px-3 py-1.5 text-xs rounded-full border ${selectedMetrics.includes(metric)
                ? 'text-white border-transparent'
                : 'border-gray-300'
                }`}
              style={{
                backgroundColor: selectedMetrics.includes(metric) ? metricColors[metric] : 'transparent',
                color: selectedMetrics.includes(metric) ? 'white' : 'inherit'
              }}
              onClick={() => handleMetricToggle(metric)}
            >
              {selectedMetrics.includes(metric) && (
                <Icon icon="solar:check-circle-bold" size={14} />
              )}
              {metricLabels[metric]}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="space-y-1 text-center">
            <p className="text-sm text-muted-foreground">{t('pages.key_766')}</p>
            <p className="text-2xl font-bold text-blue-600">
              {avgCtr.toFixed(2)}%
            </p>
          </div>
          <div className="space-y-1 text-center">
            <p className="text-sm text-muted-foreground">{t('pages.key_767')}</p>
            <p className="text-2xl font-bold text-purple-600">
              {avgCvr.toFixed(2)}%
            </p>
          </div>
          <div className="space-y-1 text-center">
            <p className="text-sm text-muted-foreground">{t('pages.key_926')}</p>
            <p className="text-2xl font-bold text-green-600">
              {totalConversions.toLocaleString()}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdPerformanceAnalysis;
