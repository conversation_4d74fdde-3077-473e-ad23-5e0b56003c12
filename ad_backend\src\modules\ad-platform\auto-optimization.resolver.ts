import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AutoOptimizationDTO } from './entities/auto-optimization.dto';
import { AdSet } from './entities/adset.entity';
import { Audience } from './entities/audience.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../../guards/tenant.guard';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => AutoOptimizationDTO)
export class AutoOptimizationResolver {
    constructor(
        @InjectRepository(AdSet)
        private readonly adSetRepo: Repository<AdSet>,
        @InjectRepository(Audience)
        private readonly audienceRepo: Repository<Audience>,
    ) { }

    @Query(() => AutoOptimizationDTO)
    async autoOptimization(
        @Args('type') type: string,
        @Context() ctx: any,
    ): Promise<AutoOptimizationDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const adSetWhere = isSuperAdmin ? {} : { tenantId };
        const audienceWhere = isSuperAdmin ? {} : { tenant: { id: tenantId } };
        if (type === 'budget') {
            // 预算自动优化：统计预算使用率、建议调整的广告组数、潜在节省
            const adSets = await this.adSetRepo.find({ where: adSetWhere });
            const totalBudget = adSets.reduce((sum, a) => sum + (a.dailyBudget || 0), 0);
            const totalSpent = adSets.reduce((sum, a) => sum + ((a.raw?.spend) || 0), 0);
            const progress = totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0;
            const recommendations = adSets.filter(a => a.status === 'active' && a.dailyBudget > 0 && ((a.raw?.spend || 0) / a.dailyBudget) > 0.8).length;
            const potentialSavings = totalBudget > 0 ? `¥${Math.round((totalBudget - totalSpent) * 0.2)}` : '¥0';
            return {
                status: 'active',
                progress,
                lastOptimized: new Date().toISOString().slice(0, 10),
                recommendations,
                potentialSavings,
            };
        }
        if (type === 'bidding') {
            // 出价自动优化：统计高出价广告组、建议调整数、潜在提升
            const adSets = await this.adSetRepo.find({ where: adSetWhere });
            const recommendations = adSets.filter(a => a.status === 'active' && (a.raw?.bid_amount || 0) > 100).length;
            const progress = Math.min(100, recommendations * 10);
            const potentialImprovement = `+${(progress / 2).toFixed(1)}%`;
            return {
                status: 'active',
                progress,
                lastOptimized: new Date().toISOString().slice(0, 10),
                recommendations,
                potentialImprovement,
            };
        }
        // targeting
        // 定向自动优化：统计受众数量、建议调整数、潜在触达
        const audiences = await this.audienceRepo.find({ where: audienceWhere });
        const recommendations = audiences.filter(a => (a.age_min || 0) < 18 || (a.age_max || 100) > 60).length;
        const progress = Math.min(100, recommendations * 5);
        const potentialReach = `+${recommendations * 10000}人`;
        return {
            status: recommendations > 0 ? 'active' : 'inactive',
            progress,
            lastOptimized: new Date().toISOString().slice(0, 10),
            recommendations,
            potentialReach,
        };
    }
} 