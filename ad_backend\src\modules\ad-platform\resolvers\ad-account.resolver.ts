import { Resolver, Query, Args, Context, Mutation } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdAccountDTO } from '../entities/ad-account.dto';
import { AdAccount } from '../entities/ad-account.entity';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../../modules/auth/jwt-auth.guard';
import { plainToInstance } from 'class-transformer';
import { AdAccountFilterInput } from '../entities/ad-account.input';
import { InputType, Field, ObjectType, ID } from '@nestjs/graphql';
import { AdAccountUploadResult, AdAccountInput } from '../entities/ad-account.dto';
import { Tenant } from '../../../entity/tenant.entity';
import { TenantGuard } from '../../../guards/tenant.guard';
const mode = process.env.NODE_ENV
const rediectUrl = mode === 'production' ? 'https://ad-api.yeeu.net' : 'https://799380fy71ow.vicp.fun';
@InputType()
class TestInput {
    @Field()
    name: string;
    @Field()
    platform: string;
}

@ObjectType()
class TestOutput {
    @Field()
    name: string;
    @Field()
    platform: string;
}

@Resolver(() => AdAccountDTO)
@UseGuards(JwtAuthGuard, TenantGuard)
export class AdAccountResolver {
    constructor(
        @InjectRepository(AdAccount)
        private readonly adAccountRepo: Repository<AdAccount>,
    ) { }

    @Query(() => [AdAccountDTO])
    async adAccounts(
        @Args('filter', { type: () => AdAccountFilterInput, nullable: true }) filter: AdAccountFilterInput,
        @Context() ctx,
    ): Promise<AdAccountDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { status: 'active', ...(filter || {}) } : { tenant: { id: tenantId }, status: 'active', ...(filter || {}) };
        const accounts = await this.adAccountRepo.find({ where });
        return plainToInstance(AdAccountDTO, accounts);
    }

    @Mutation(() => TestOutput)
    async testEcho(@Args('input') input: TestInput): Promise<TestOutput> {
        return input as TestOutput;
    }

    @Mutation(() => AdAccountDTO)
    async deleteOneAdAccount(
        @Args('accountId', { type: () => String }) accountId: string,
        @Context() ctx,
    ): Promise<AdAccountDTO> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { accountId } : { accountId, tenant: { id: tenantId } };
        const account = await this.adAccountRepo.findOne({ where });
        if (!account) throw new Error('广告账户不存在');
        // 先保存要返回的数据
        const result = { ...account };
        await this.adAccountRepo.remove(account);
        return plainToInstance(AdAccountDTO, result);
    }

    @Mutation(() => [AdAccountUploadResult])
    async batchUploadAdAccounts(
        @Args({ name: 'accounts', type: () => [AdAccountInput] }) accounts: AdAccountInput[],
        @Args('platform', { type: () => String }) platform: string,
        @Context() ctx,
    ): Promise<AdAccountUploadResult[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const tenantRepo = this.adAccountRepo.manager.getRepository('Tenant');
        const tenant = await tenantRepo.findOne({ where: { id: tenantId } }) as Tenant;
        if (!tenant) throw new Error('租户不存在');
        const results: AdAccountUploadResult[] = [];
        for (const input of accounts) {
            let acc = isSuperAdmin
                ? await this.adAccountRepo.findOne({ where: { accountId: input.accountId } })
                : await this.adAccountRepo.findOne({ where: { accountId: input.accountId, tenant: { id: tenantId } } });
            if (!acc) {
                acc = this.adAccountRepo.create({
                    accountId: input.accountId,
                    account: input.account,
                    password: input.password,
                    tag: input.tag,
                    channel: input.channel,
                    status: 'pending',
                    platform,
                    tenant,
                });
                await this.adAccountRepo.save(acc);
            } else {
                acc.password = input.password;
                acc.tag = input.tag;
                acc.channel = input.channel;
                acc.platform = platform;
                acc.account = input.account;
                acc.tenant = tenant;
                await this.adAccountRepo.save(acc);
            }
            const authorized = !!acc.accessToken;
            results.push({ account: input.account, status: authorized ? '已授权' : '未授权' });
        }
        return results;
    }

    @Query(() => [AdAccountDTO])
    async unauthorizedAdAccounts(@Context() ctx): Promise<AdAccountDTO[]> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { status: 'pending' } : { tenant: { id: tenantId }, status: 'pending' };
        const accounts = await this.adAccountRepo.find({ where });
        return accounts.map(acc => plainToInstance(AdAccountDTO, acc));
    }

    @Query(() => String)
    getAdAccountOAuth2Url(@Args('account', { type: () => String }) account: string, @Context() ctx: any): string {
        const { tenantId, isSuperAdmin } = ctx;
        if (!tenantId && !isSuperAdmin) throw new Error('未获取到租户ID');
        // state 拼 tenantId|accountId
        const state = encodeURIComponent(`${tenantId}|${account}`);
        const redirectUri = encodeURIComponent(`${rediectUrl}/ad-platform/ad-account/oauth2-callback`);
        const clientId = process.env.FACEBOOK_APPID;
        const scope = [
            'ads_management',
            'ads_read',
            'business_management',
            'pages_show_list',
            'pages_read_engagement',
            'pages_manage_ads',
            'pages_manage_metadata',
            'pages_read_user_content',
            'public_profile',
            'email',
        ].join(',');
        return `https://www.facebook.com/v18.0/dialog/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=${scope}`;
    }

    @Mutation(() => Boolean)
    async saveAdAccountOAuthToken(
        @Args('account', { type: () => String }) account: string,
        @Args('accessToken', { type: () => String }) accessToken: string,
        @Context() ctx: any,
    ): Promise<boolean> {
        const { tenantId, isSuperAdmin } = ctx;
        const where = isSuperAdmin ? { accountId: account } : { accountId: account, tenant: { id: tenantId } };
        const acc = await this.adAccountRepo.findOne({ where });
        if (acc) {
            acc.accessToken = accessToken;
            acc.status = 'active';
            await this.adAccountRepo.save(acc);
            return true;
        }
        return false;
    }
} 