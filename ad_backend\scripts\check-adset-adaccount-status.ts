// 检查所有ad_set.adAccountId对应的ad_account记录是否存在且status为'active'
const pg = require('pg');

const pgClient = new pg.Client({
    host: 'pgm-t4n50ng2p45atkun7o.rwlb.singapore.rds.aliyuncs.com',
    port: 5432,
    user: 'yeeu',
    password: 'Yy&********',
    database: 'ad_auto',
});

async function main() {
    await pgClient.connect();
    const { rows } = await pgClient.query(`
        SELECT a."id" as adset_id, a."name" as adset_name, a."adAccountId", b."accountId", b."status"
        FROM "ad_set" a
        LEFT JOIN "ad_account" b ON a."adAccountId" = b."accountId"
        WHERE b."accountId" IS NULL OR b."status" != 'active'
    `);
    if (rows.length === 0) {
        console.log('所有 ad_set.adAccountId 均存在且 status=active');
    } else {
        console.log(`共发现 ${rows.length} 条无效或未激活的 ad_set 记录：`);
        rows.forEach(row => {
            console.log(`ad_set.id=${row.adset_id}, name=${row.adset_name}, adAccountId=${row.adAccountId}, ad_account.status=${row.status}, ad_account.accountId=${row.accountId}`);
        });
    }
    await pgClient.end();
}

main().catch(e => {
    console.error(e);
    process.exit(1);
}); 