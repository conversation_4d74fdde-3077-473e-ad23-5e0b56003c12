import { InputType, Field, ID } from '@nestjs/graphql';
import { IsEmail, IsOptional, IsString, MinLength, IsUUID, IsArray } from 'class-validator';
import { UserStatus } from '../user.entity';

@InputType()
export class UpdateUserInput {
    @Field(() => ID)
    @IsUUID()
    id: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    username?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsEmail()
    email?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MinLength(6)
    password?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    avatar?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    fullName?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    address?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    registerIp?: string;

    @Field(() => UserStatus, { nullable: true })
    @IsOptional()
    status?: UserStatus;

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID("4", { each: true })
    roleIds?: string[];

    @Field(() => [ID], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsUUID("4", { each: true })
    groupIds?: string[];

    @Field(() => String, { nullable: true })
    @IsOptional()
    tenantId?: string;
} 