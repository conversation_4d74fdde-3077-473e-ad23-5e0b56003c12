import { MigrationInterface, QueryRunner } from "typeorm";

export class CleanAdSetUuidColumns20240612010000 implements MigrationInterface {
    name = 'CleanAdSetUuidColumns20240612010000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 删除 tenantId 非 uuid 格式的数据（uuid 字段需先转为 text）
        await queryRunner.query(`DELETE FROM "ad_set" WHERE "tenantId" IS NOT NULL AND ("tenantId"::text !~* '^[0-9a-fA-F-]{36}$')`);
        // 删除 id 非 uuid 格式的数据
        await queryRunner.query(`DELETE FROM "ad_set" WHERE "id" IS NOT NULL AND ("id"::text !~* '^[0-9a-fA-F-]{36}$')`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 清理操作不可逆，down 为空
    }
} 