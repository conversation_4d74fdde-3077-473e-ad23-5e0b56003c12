import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCountryToAudience20250525112021 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 添加字段，允许 NULL
        await queryRunner.query(`ALTER TABLE "audience" ADD COLUMN "country" text[]`);
        // 2. 填充旧数据为空数组
        await queryRunner.query(`UPDATE "audience" SET "country" = '{}' WHERE "country" IS NULL`);
        // 3. 设置 NOT NULL 约束
        await queryRunner.query(`ALTER TABLE "audience" ALTER COLUMN "country" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "audience" DROP COLUMN IF EXISTS "country"`);
    }
} 