import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAdSetIdTenantIdType20240611200000 implements MigrationInterface {
    name = 'FixAdSetIdTenantIdType20240611200000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // id 字段改为 uuid
        await queryRunner.query(`ALTER TABLE "ad_set" ALTER COLUMN "id" TYPE uuid USING id::uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 回滚为 varchar(64)
        await queryRunner.query(`ALTER TABLE "ad_set" ALTER COLUMN "id" TYPE varchar(64) USING id::varchar`);
    }
} 