// 角色状态枚举
export enum RoleStatus {
    ENABLED = 'enabled',
    DISABLED = 'disabled'
}

// 角色项
export interface RoleItem {
    id: string;
    name: string;
    description?: string;
    status: RoleStatus;
    order: number;
    routeIds?: string[];
    createdAt: string;
    updatedAt: string;
}

// 分页信息
export interface PageInfo {
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
}

// 角色列表响应
export interface RoleListResponse {
    items: RoleItem[];
    pageInfo: PageInfo;
    totalCount: number;
}

// 创建角色参数
export interface CreateRoleParams {
    name: string;
    description?: string;
    status?: RoleStatus;
    order?: number;
    routeIds?: string[];
}

// 更新角色参数
export interface UpdateRoleParams {
    id: string;
    name?: string;
    description?: string;
    status?: RoleStatus;
    order?: number;
    routeIds?: string[];
}

// 角色查询参数
export interface RoleQueryParams {
    page?: number;
    limit?: number;
    search?: string;
    status?: RoleStatus;
} 