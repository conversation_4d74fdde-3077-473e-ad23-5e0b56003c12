import { Resolver, Args, Context, Mutation } from '@nestjs/graphql';
import { AntiBlockDTO } from '../dto/antiBlock.dto';
import { AntiBlockService } from '../antiBlock.service';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { TenantGuard } from '../../../guards/tenant.guard';
import { UseGuards } from '@nestjs/common';

@UseGuards(JwtAuthGuard, TenantGuard)
@Resolver(() => AntiBlockDTO)
export class AntiBlockResolver {
	constructor(private readonly antiBlockService: AntiBlockService) { }

	@Mutation(() => [AntiBlockDTO])
	async createManyAntiBlocks(
		@Args('antiBlocks', { type: () => [AntiBlockDTO] }) antiBlocks: AntiBlockDTO[],
		@Context() ctx
	) {
		const req = ctx.req || (ctx as any).req;
		const user = req?.user || ctx.user;
		const tenantId = ctx.tenantId ?? req?.tenantId;
		const isSuperAdmin = ctx.isSuperAdmin ?? req?.isSuperAdmin;
		if (!user) throw new Error('未登录');
		const patchedInputs = isSuperAdmin
			? antiBlocks
			: antiBlocks.map(item => ({ ...(item as any), tenantId }));
		console.log('createManyAntiBlocks patchedInputs:', patchedInputs);
		return Promise.all(
			patchedInputs.map(item =>
				this.antiBlockService.createWithTenant(item, item.tenantId || tenantId)
			)
		);
	}
}
