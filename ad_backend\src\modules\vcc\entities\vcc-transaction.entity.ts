import {
    Entity,
    Column,
    PrimaryGeneratedColumn,
    ManyToOne,
    CreateDateColumn,
    UpdateDateColumn,
    Index,
    JoinColumn
} from 'typeorm';
import { ObjectType, Field, ID, Float, registerEnumType } from '@nestjs/graphql';
import { VccCard } from './vcc-card.entity';
import { Tenant } from '../../../entity/tenant.entity';

// 交易状态枚举 - 与前端完全一致
export enum TransactionStatus {
    SUCCESS = 'success',        // 成功
    PENDING = 'pending',        // 处理中
    FAILED = 'failed'           // 失败
}

// 交易类型枚举 - 与前端完全一致
export enum TransactionType {
    PAYMENT = 'payment',        // 支付（Facebook广告消费）
    DEPOSIT = 'deposit',        // 充值
    REFUND = 'refund'           // 退款
}

// 注册GraphQL枚举
registerEnumType(TransactionStatus, {
    name: 'TransactionStatus',
    description: '交易状态'
});

registerEnumType(TransactionType, {
    name: 'TransactionType',
    description: '交易类型'
});

@ObjectType('VccTransaction')
@Entity('vcc_transactions')
@Index(['cardId', 'transactionTime'])
@Index(['tenantId', 'transactionTime'])
@Index(['status', 'type'])
@Index(['merchant'])
export class VccTransaction {
    @Field(() => ID, { description: '交易ID' })
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Field(() => ID, { description: 'VCC卡片ID' })
    @Column({ type: 'uuid', comment: 'VCC卡片ID' })
    cardId: string;

    @Field(() => VccCard, { description: 'VCC卡片', nullable: true })
    @ManyToOne(() => VccCard, vccCard => vccCard.id, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'cardId' })
    vccCard?: VccCard;

    @Field({ description: '卡号（脱敏显示）' })
    @Column({ type: 'varchar', length: 50, comment: '卡号（脱敏显示）' })
    cardNo: string;

    @Field(() => Float, { description: '交易金额' })
    @Column({ type: 'decimal', precision: 12, scale: 2, comment: '交易金额' })
    amount: number;

    @Field({ description: '商户名称' })
    @Column({ type: 'varchar', length: 100, comment: '商户名称' })
    merchant: string;

    @Field({ description: '交易时间' })
    @Column({ type: 'timestamp', comment: '交易时间' })
    transactionTime: Date;

    @Field(() => TransactionStatus, { description: '交易状态' })
    @Column({
        type: 'enum',
        enum: TransactionStatus,
        default: TransactionStatus.SUCCESS,
        comment: '交易状态'
    })
    status: TransactionStatus;

    @Field(() => TransactionType, { description: '交易类型' })
    @Column({
        type: 'enum',
        enum: TransactionType,
        comment: '交易类型'
    })
    type: TransactionType;

    @Field({ nullable: true, description: 'Facebook广告账户ID' })
    @Column({ type: 'varchar', length: 50, nullable: true, comment: 'Facebook广告账户ID' })
    facebookAccountId?: string;

    @Field({ nullable: true, description: 'Facebook交易ID' })
    @Column({ type: 'varchar', length: 100, nullable: true, comment: 'Facebook交易ID' })
    facebookTransactionId?: string;

    @Field({ nullable: true, description: '广告活动名称' })
    @Column({ type: 'varchar', length: 200, nullable: true, comment: '广告活动名称' })
    campaignName?: string;

    @Field({ nullable: true, description: '交易描述' })
    @Column({ type: 'text', nullable: true, comment: '交易描述' })
    description?: string;

    @Field(() => ID, { description: '租户ID' })
    @Column({ type: 'uuid', comment: '租户ID' })
    tenantId: string;

    @Field(() => Tenant, { description: '租户', nullable: true })
    @ManyToOne(() => Tenant, tenant => tenant.id)
    @JoinColumn({ name: 'tenantId' })
    tenant?: Tenant;

    @Field({ description: '创建时间' })
    @CreateDateColumn({ comment: '创建时间' })
    createdAt: Date;

    @Field({ description: '更新时间' })
    @UpdateDateColumn({ comment: '更新时间' })
    updatedAt: Date;
} 