import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Tenant } from '../../../entity/tenant.entity';
import { AdAccount } from './ad-account.entity';
import { Campaign } from './campaign.entity';
import { Audience } from './audience.entity';

@Entity('ad_set')
export class AdSet {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '平台类型（facebook/google/tiktok/adjust/af等）' })
  @Column({ length: 32 })
  platform: string;

  @ApiProperty({ description: '广告平台AdSet ID' })
  @Index()
  @Column({ length: 64 })
  adsetId: string;

  @ApiProperty({ description: '广告组名称' })
  @Column({ length: 128 })
  name: string;

  @ApiProperty({ description: '所属广告账户' })
  @ManyToOne(() => AdAccount, { nullable: false })
  @JoinColumn({ name: 'adAccountId', referencedColumnName: 'accountId' })
  adAccount: AdAccount;

  @Column({ name: 'adAccountId', type: 'varchar', length: 64 })
  adAccountId: string;

  @ApiProperty({ description: '所属广告系列' })
  @ManyToOne(() => Campaign, { nullable: false })
  adCampaign: Campaign;

  @Column({ name: 'tenantId', type: 'uuid' })
  tenantId: string;

  @ApiProperty({ description: '租户ID' })
  @Index()
  @ManyToOne(() => Tenant, { nullable: false })
  @JoinColumn({ name: 'tenantId' })
  tenant: Tenant;

  @ApiProperty({ description: '原始adset数据（JSON）' })
  @Column({ type: 'jsonb', nullable: true })
  raw?: any;

  @ApiProperty({ description: '广告组状态' })
  @Column({ length: 32, default: 'active' })
  status: string;

  @ManyToOne(() => Audience, { nullable: false })
  @JoinColumn({ name: 'audienceId' })
  audience: Audience;

  @Column({ name: 'audienceId', type: 'uuid' })
  audienceId: string;

  @ApiProperty({ description: '日预算' })
  @Column({ type: 'float', nullable: false, default: 0 })
  dailyBudget: number;

  @ApiProperty({ description: '开始时间' })
  @Column({ name: 'start_time', type: 'timestamp', nullable: false })
  startTime: Date;

  @ApiProperty({ description: '结束时间' })
  @Column({ name: 'end_time', type: 'timestamp', nullable: true })
  endTime?: Date;

  @ApiProperty({ description: '优化目标' })
  @Column({ name: 'optimization_goal', type: 'varchar', length: 64, nullable: false, default: 'CONVERSIONS' })
  optimizationGoal: string;

  @ApiProperty({ description: '计费方式' })
  @Column({ name: 'billing_event', type: 'varchar', length: 64, nullable: false, default: 'IMPRESSIONS' })
  billingEvent: string;
}
