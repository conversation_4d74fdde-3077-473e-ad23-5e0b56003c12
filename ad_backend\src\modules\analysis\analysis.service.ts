/*
 * @Author: 潘孝权
 * @Date: 2025-06-14 12:53:40
 * @Description: 
 * @FilePath: \sking_frontend\ad_backend\src\modules\analysis\analysis.service.ts
 */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { TimeRangeEnum, ChannelTimeRangeEnum } from './dto/analysis.dto';
import { Analysis } from './analysis.entity';
import { Report } from '../ad-platform/report/report.entity';
import { AdAccount } from '../ad-platform/entities/ad-account.entity';
import { FacebookApiService } from '../ad-platform/services/facebook-api.service';
import { materialManagement } from '../material-management/materialManagement.entity';

@Injectable()
export class AnalysisService {
  constructor(
    @InjectRepository(Analysis)
    private readonly analysisRepository: Repository<Analysis>,
    @InjectRepository(Report)
    private readonly reportRepo: Repository<Report>,
    @InjectRepository(AdAccount)
    private readonly adAccountRepo: Repository<AdAccount>,
    @InjectRepository(materialManagement)
    private readonly materialManagementRepo: Repository<materialManagement>,
    private readonly facebookApiService: FacebookApiService
  ) {}

  // 获取总览卡片数据
  // 注意：此方法使用与tenant.resolver.ts中tenantTrend方法完全相同的数据获取逻辑
  // 包括相同的查询条件、数据聚合方式和字段映射
  async getOverviewCards(tenantId?: string) {
    // 获取当前时间和上一期间的时间范围
    const today = new Date();
    const currentEndDate = today.toISOString().slice(0, 10);
    const currentStartDate = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10); // 最近7天
    
    const previousEndDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
    const previousStartDate = new Date(today.getTime() - 13 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10); // 前7天

    // 使用与tenantTrend相同的查询逻辑
    const getTrendData = async (startDate: string, endDate: string) => {
      const where: any = {};
      
      // 如果传入了tenantId，则添加租户过滤条件（与tenantTrend相同）
      if (tenantId) {
        where.tenant = { id: tenantId };
      }
      
      // 设置日期范围（与tenantTrend相同的逻辑）
      where.date = {};
      where.date['$gte'] = startDate;
      where.date['$lte'] = endDate;
      
      // 设置类型过滤（与tenantTrend完全相同）
      where.type = In(['ad', 'campaign', 'account']);
      
      const reports = await this.reportRepo.find({ where });
      
      // 按天聚合impressions/clicks/spend/conversion（与tenantTrend完全相同的逻辑）
      const trendMap: Record<string, { impressions: number; clicks: number; spend: number; conversion: number }> = {};
      for (const r of reports) {
        const d = r.date;
        if (!trendMap[d]) trendMap[d] = { impressions: 0, clicks: 0, spend: 0, conversion: 0 };
        if (typeof r.data?.impressions === 'number') trendMap[d].impressions += r.data.impressions;
        if (typeof r.data?.clicks === 'number') trendMap[d].clicks += r.data.clicks;
        if (typeof r.data?.spend === 'number') trendMap[d].spend += r.data.spend;
        if (typeof r.data?.conversion === 'number') trendMap[d].conversion += r.data.conversion;
      }
      
      // 聚合所有天的数据
      let totalImpressions = 0;
      let totalClicks = 0;
      let totalSpend = 0;
      let totalConversions = 0;
      
      Object.values(trendMap).forEach(dayData => {
        totalImpressions += dayData.impressions;
        totalClicks += dayData.clicks;
        totalSpend += dayData.spend;
        totalConversions += dayData.conversion;
      });
      
      return {
        totalImpressions,
        totalClicks,
        totalSpend,
        totalConversions
      };
    };

    // 获取当前期间和上一期间的数据
    const [currentPeriod, previousPeriod] = await Promise.all([
      getTrendData(currentStartDate, currentEndDate),
      getTrendData(previousStartDate, previousEndDate)
    ]);

    // 计算百分比变化
    const calculatePercentage = (current: number, previous: number) => {
      if (!previous) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 10000) / 100;
    };

    return {
      impressions: {
        value: currentPeriod.totalImpressions,
        percentage: calculatePercentage(
          currentPeriod.totalImpressions,
          previousPeriod.totalImpressions
        ),
      },
      clicks: {
        value: currentPeriod.totalClicks,
        percentage: calculatePercentage(
          currentPeriod.totalClicks,
          previousPeriod.totalClicks
        ),
      },
      conversions: {
        value: currentPeriod.totalConversions,
        percentage: calculatePercentage(
          currentPeriod.totalConversions,
          previousPeriod.totalConversions
        ),
      },
      spend: {
        value: currentPeriod.totalSpend,
        percentage: calculatePercentage(
          currentPeriod.totalSpend,
          previousPeriod.totalSpend
        ),
      },
    };
  }

  // 获取广告效果分析数据 - 重构为使用真实数据源
  async getAdPerformanceAnalysis(timeRange: TimeRangeEnum, tenantId?: string) {
    // 生成时间标签和日期范围
    const { timeLabels, startDate, endDate } = this.generateTimeRange(timeRange);

    try {
      // 首先尝试从Report数据获取
      const reportData = await this.getPerformanceDataFromReports(startDate, endDate, tenantId);
      
      if (reportData && reportData.hasData) {
        console.log('使用Report数据源获取广告效果分析数据');
        return this.formatPerformanceData(reportData.data, timeLabels);
      }

      // 如果Report数据不足，尝试从Facebook API获取
      console.log('Report数据不足，尝试从Facebook API获取数据');
      const facebookData = await this.getPerformanceDataFromFacebook(startDate, endDate, tenantId);
      
      if (facebookData && facebookData.hasData) {
        console.log('使用Facebook API数据源获取广告效果分析数据');
        return this.formatPerformanceData(facebookData.data, timeLabels);
      }

      // 如果都没有数据，返回空数据结构而不是模拟数据
      console.log('未找到真实数据，返回空数据结构');
      return this.getEmptyPerformanceData(timeLabels);

    } catch (error) {
      console.error('获取广告效果分析数据失败:', error);
      // 发生错误时也返回空数据而不是模拟数据
      return this.getEmptyPerformanceData(timeLabels);
    }
  }

  // 从Report数据获取广告效果数据
  private async getPerformanceDataFromReports(startDate: string, endDate: string, tenantId?: string) {
    const where: any = {
      type: In(['ad', 'campaign', 'account'])
    };

    if (tenantId) {
      where.tenant = { id: tenantId };
    }

    // 添加日期范围过滤
    const dates = this.generateDateArray(startDate, endDate);
    where.date = In(dates);

    const reports = await this.reportRepo.find({ where });

    if (!reports.length) {
      return { hasData: false, data: [] };
    }

    // 按日期聚合数据
    const dailyData = this.aggregateReportsByDate(reports, dates);
    
    return {
      hasData: dailyData.length > 0,
      data: dailyData
    };
  }

  // 从Facebook API获取广告效果数据
  private async getPerformanceDataFromFacebook(startDate: string, endDate: string, tenantId?: string) {
    try {
      // 获取租户下有accessToken的Facebook账户
      const whereCondition: any = {
        platform: 'facebook'
      };
      
      if (tenantId) {
        whereCondition.tenant = { id: tenantId };
      }

      const accounts = await this.adAccountRepo
        .createQueryBuilder('ad_account')
        .where(whereCondition)
        .andWhere('ad_account.accessToken IS NOT NULL')
        .andWhere('ad_account.accessToken != :emptyString', { emptyString: '' })
        .leftJoinAndSelect('ad_account.tenant', 'tenant')
        .getMany();

      if (!accounts.length) {
        console.log('未找到可用的Facebook账户');
        return { hasData: false, data: [] };
      }

      // 并发获取所有账户的Insights数据
      const accountPromises = accounts.map(async (account) => {
        try {
          const insights = await this.facebookApiService.getAdAccountInsights(
            account.accountId,
            account.accessToken,
            { since: startDate, until: endDate }
          );
          return { account, insights };
        } catch (error) {
          console.error(`获取账户 ${account.accountId} 数据失败:`, error);
          return null;
        }
      });

      const results = await Promise.all(accountPromises);
      const validResults = results.filter(r => r !== null);

      if (!validResults.length) {
        return { hasData: false, data: [] };
      }

      // 聚合所有账户的数据按日期
      const dailyData = this.aggregateFacebookInsights(validResults, startDate, endDate);

      return {
        hasData: dailyData.length > 0,
        data: dailyData
      };

    } catch (error) {
      console.error('Facebook API调用失败:', error);
      return { hasData: false, data: [] };
    }
  }

  // 生成时间范围和标签
  private generateTimeRange(timeRange: TimeRangeEnum) {
    const today = new Date();
    let timeLabels: string[];
    let startDate: string;
    let endDate: string;

    switch (timeRange) {
      case TimeRangeEnum.DAY:
        // 24小时数据
        timeLabels = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`);
        startDate = today.toISOString().slice(0, 10);
        endDate = today.toISOString().slice(0, 10);
        break;
      case TimeRangeEnum.WEEK:
        // 7天数据
        timeLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        startDate = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
        endDate = today.toISOString().slice(0, 10);
        break;
      case TimeRangeEnum.MONTH:
        // 30天数据
        timeLabels = Array.from({ length: 30 }, (_, i) => `${i + 1}日`);
        startDate = new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
        endDate = today.toISOString().slice(0, 10);
        break;
      default:
        timeLabels = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`);
        startDate = today.toISOString().slice(0, 10);
        endDate = today.toISOString().slice(0, 10);
    }

    return { timeLabels, startDate, endDate };
  }

  // 生成日期数组
  private generateDateArray(startDate: string, endDate: string): string[] {
    const dates: string[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    while (start <= end) {
      dates.push(start.toISOString().slice(0, 10));
      start.setDate(start.getDate() + 1);
    }
    
    return dates;
  }

  // 聚合Report数据按日期
  private aggregateReportsByDate(reports: Report[], dates: string[]) {
    const dailyMap = new Map();
    
    // 初始化所有日期
    dates.forEach(date => {
      dailyMap.set(date, {
        date,
        impressions: 0,
        clicks: 0,
        conversions: 0,
        spend: 0
      });
    });

    // 聚合数据
    reports.forEach(report => {
      if (dailyMap.has(report.date)) {
        const dayData = dailyMap.get(report.date);
        dayData.impressions += Number(report.data?.impressions) || 0;
        dayData.clicks += Number(report.data?.clicks) || 0;
        dayData.conversions += Number(report.data?.conversion) || 0;
        dayData.spend += Number(report.data?.spend) || 0;
      }
    });

    return Array.from(dailyMap.values());
  }

  // 聚合Facebook Insights数据
  private aggregateFacebookInsights(results: any[], startDate: string, endDate: string) {
    const dates = this.generateDateArray(startDate, endDate);
    const dailyMap = new Map();
    
    // 初始化所有日期
    dates.forEach(date => {
      dailyMap.set(date, {
        date,
        impressions: 0,
        clicks: 0,
        conversions: 0,
        spend: 0
      });
    });

    // 聚合Facebook数据
    results.forEach(({ insights }) => {
      if (insights && insights.date_start) {
        const date = insights.date_start;
        if (dailyMap.has(date)) {
          const dayData = dailyMap.get(date);
          dayData.impressions += insights.impressions || 0;
          dayData.clicks += insights.clicks || 0;
          dayData.conversions += 0; // Facebook Insights通常需要特定配置才有转化数据
          dayData.spend += insights.spend || 0;
        }
      }
    });

    return Array.from(dailyMap.values());
  }

  // 格式化性能数据
  private formatPerformanceData(dailyData: any[], timeLabels: string[]) {
    // 计算CTR和CVR
    const impressions = dailyData.map(d => d.impressions);
    const clicks = dailyData.map(d => d.clicks);
    const conversions = dailyData.map(d => d.conversions);
    
    const ctr = impressions.map((imp, i) => 
      imp > 0 ? Math.round((clicks[i] / imp) * 10000) / 100 : 0
    );
    
    const cvr = clicks.map((click, i) => 
      click > 0 ? Math.round((conversions[i] / click) * 10000) / 100 : 0
    );

    return {
      barData: {
        impressions,
        clicks,
        conversions,
      },
      lineData: {
        ctr,
        cvr,
      },
      timeLabels,
    };
  }

  // 返回空数据结构
  private getEmptyPerformanceData(timeLabels: string[]) {
    const emptyArray = new Array(timeLabels.length).fill(0);
    
    return {
      barData: {
        impressions: emptyArray,
        clicks: emptyArray,
        conversions: emptyArray,
      },
      lineData: {
        ctr: emptyArray,
        cvr: emptyArray,
      },
      timeLabels,
    };
  }

  // 获取广告跑量分析数据 - 重构为使用真实数据源
  async getAdTrafficAnalysis(timeRange: TimeRangeEnum, tenantId?: string) {
    // 生成时间标签和日期范围
    const { timeLabels, startDate, endDate } = this.generateTimeRange(timeRange);

    try {
      // 首先尝试从Report数据获取
      const reportData = await this.getTrafficDataFromReports(startDate, endDate, tenantId);
      
      if (reportData && reportData.hasData) {
        console.log('使用Report数据源获取广告跑量分析数据');
        return this.formatTrafficData(reportData.data, timeLabels);
      }

      // 如果Report数据不足，尝试从Facebook API获取
      console.log('Report数据不足，尝试从Facebook API获取跑量数据');
      const facebookData = await this.getTrafficDataFromFacebook(startDate, endDate, tenantId);
      
      if (facebookData && facebookData.hasData) {
        console.log('使用Facebook API数据源获取广告跑量分析数据');
        return this.formatTrafficData(facebookData.data, timeLabels);
      }

      // 如果都没有数据，返回空数据结构而不是模拟数据
      console.log('未找到真实跑量数据，返回空数据结构');
      return this.getEmptyTrafficData(timeLabels);

    } catch (error) {
      console.error('获取广告跑量分析数据失败:', error);
      // 发生错误时也返回空数据而不是模拟数据
      return this.getEmptyTrafficData(timeLabels);
    }
  }

  // 从Report数据获取跑量数据
  private async getTrafficDataFromReports(startDate: string, endDate: string, tenantId?: string) {
    const where: any = {
      type: In(['ad', 'campaign', 'account'])
    };

    if (tenantId) {
      where.tenant = { id: tenantId };
    }

    // 添加日期范围过滤
    const dates = this.generateDateArray(startDate, endDate);
    where.date = In(dates);

    const reports = await this.reportRepo.find({ where });

    if (!reports.length) {
      return { hasData: false, data: [] };
    }

    // 按日期聚合数据（注重跑量指标：spend, impressions, clicks, conversions）
    const dailyData = this.aggregateTrafficReportsByDate(reports, dates);
    
    return {
      hasData: dailyData.length > 0 && dailyData.some(d => d.spend > 0 || d.impressions > 0),
      data: dailyData
    };
  }

  // 从Facebook API获取跑量数据
  private async getTrafficDataFromFacebook(startDate: string, endDate: string, tenantId?: string) {
    try {
      const whereCondition: any = {
        platform: 'facebook'
      };
      
      if (tenantId) {
        whereCondition.tenant = { id: tenantId };
      }

      const accounts = await this.adAccountRepo
        .createQueryBuilder('ad_account')
        .where(whereCondition)
        .andWhere('ad_account.accessToken IS NOT NULL')
        .andWhere('ad_account.accessToken != :emptyString', { emptyString: '' })
        .leftJoinAndSelect('ad_account.tenant', 'tenant')
        .getMany();

      if (!accounts.length) {
        console.log('未找到可用的Facebook账户');
        return { hasData: false, data: [] };
      }

      // 并发获取所有账户的Insights数据
      const accountPromises = accounts.map(async (account) => {
        try {
          const insights = await this.facebookApiService.getAdAccountInsights(
            account.accountId,
            account.accessToken,
            { since: startDate, until: endDate }
          );
          return { account, insights };
        } catch (error) {
          console.error(`获取账户 ${account.accountId} 跑量数据失败:`, error);
          return null;
        }
      });

      const results = await Promise.all(accountPromises);
      const validResults = results.filter(r => r !== null);

      if (!validResults.length) {
        return { hasData: false, data: [] };
      }

      // 聚合所有账户的跑量数据按日期
      const dailyData = this.aggregateTrafficFacebookInsights(validResults, startDate, endDate);

      return {
        hasData: dailyData.length > 0 && dailyData.some(d => d.spend > 0 || d.impressions > 0),
        data: dailyData
      };

    } catch (error) {
      console.error('Facebook API调用失败:', error);
      return { hasData: false, data: [] };
    }
  }

  // 聚合Report跑量数据按日期
  private aggregateTrafficReportsByDate(reports: Report[], dates: string[]) {
    const dailyMap = new Map();
    
    // 初始化所有日期
    dates.forEach(date => {
      dailyMap.set(date, {
        date,
        spend: 0,
        impressions: 0,
        clicks: 0,
        conversions: 0
      });
    });

    // 聚合数据
    reports.forEach(report => {
      if (dailyMap.has(report.date)) {
        const dayData = dailyMap.get(report.date);
        dayData.spend += Number(report.data?.spend) || 0;
        dayData.impressions += Number(report.data?.impressions) || 0;
        dayData.clicks += Number(report.data?.clicks) || 0;
        dayData.conversions += Number(report.data?.conversion) || 0;
      }
    });

    return Array.from(dailyMap.values());
  }

  // 聚合Facebook跑量数据
  private aggregateTrafficFacebookInsights(results: any[], startDate: string, endDate: string) {
    const dates = this.generateDateArray(startDate, endDate);
    const dailyMap = new Map();
    
    // 初始化所有日期
    dates.forEach(date => {
      dailyMap.set(date, {
        date,
        spend: 0,
        impressions: 0,
        clicks: 0,
        conversions: 0
      });
    });

    // 聚合Facebook数据
    results.forEach(({ insights }) => {
      if (insights && insights.date_start) {
        const date = insights.date_start;
        if (dailyMap.has(date)) {
          const dayData = dailyMap.get(date);
          dayData.spend += insights.spend || 0;
          dayData.impressions += insights.impressions || 0;
          dayData.clicks += insights.clicks || 0;
          dayData.conversions += 0; // Facebook Insights需要特定配置才有转化数据
        }
      }
    });

    return Array.from(dailyMap.values());
  }

  // 格式化跑量数据
  private formatTrafficData(dailyData: any[], timeLabels: string[]) {
    return {
      spend: dailyData.map(d => Math.round(d.spend * 100) / 100), // 保留2位小数
      conversions: dailyData.map(d => d.conversions),
      clicks: dailyData.map(d => d.clicks),
      impressions: dailyData.map(d => d.impressions),
      timeLabels,
    };
  }

  // 返回空跑量数据结构
  private getEmptyTrafficData(timeLabels: string[]) {
    const emptyArray = new Array(timeLabels.length).fill(0);
    
    return {
      spend: emptyArray,
      conversions: emptyArray,
      clicks: emptyArray,
      impressions: emptyArray,
      timeLabels,
    };
  }

  // 获取广告素材效果分析数据 - 重构为使用真实数据源
  async getCreativePerformance(tenantId?: string) {
    try {
      // 获取真实素材数据
      const materialData = await this.getCreativeDataFromMaterials(tenantId);
      
      if (materialData && materialData.hasData) {
        console.log('使用素材管理数据源获取广告素材效果分析数据');
        return this.formatCreativeData(materialData.data);
      }

      // 如果没有数据，返回空数据结构而不是模拟数据
      console.log('未找到真实素材数据，返回空数据结构');
      return this.getEmptyCreativeData();

    } catch (error) {
      console.error('获取广告素材效果分析数据失败:', error);
      // 发生错误时也返回空数据而不是模拟数据
      return this.getEmptyCreativeData();
    }
  }

  // 从素材管理实体获取素材效果数据
  private async getCreativeDataFromMaterials(tenantId?: string) {
    // 查询materialManagement表获取素材性能数据
    const whereCondition: any = {};
    
    if (tenantId) {
      whereCondition.tenantId = tenantId;
    }

    // 获取有性能数据的素材（至少有展示量、点击量或消费）
    const materials = await this.materialManagementRepo
      .createQueryBuilder('material')
      .where(whereCondition)
      .andWhere('(material.adCount > 0 OR material.spend > 0)')
      .orderBy('material.spend', 'DESC')
      .limit(15) // 限制返回数量，按类型分组每种最多5个
      .getMany();

    if (!materials.length) {
      return { hasData: false, data: [] };
    }

    // 按素材类型分组（根据文件名推断类型）
    const groupedMaterials = this.groupMaterialsByType(materials);
    
    return {
      hasData: Object.values(groupedMaterials).some((items: any[]) => items.length > 0),
      data: groupedMaterials
    };
  }

  // 根据文件名和字段推断素材类型
  private groupMaterialsByType(materials: any[]) {
    const grouped = {
      images: [],
      videos: [],
      carousels: []
    };

    materials.forEach(material => {
      const fileName = material.fileName?.toLowerCase() || '';
      const hasVideoId = material.facebookVedioId;
      const hasImageId = material.facebookImageId;
      
      // 推断素材类型
      let type = 'images'; // 默认为图片
      
      if (hasVideoId || fileName.includes('video') || fileName.includes('.mp4') || fileName.includes('.mov')) {
        type = 'videos';
      } else if (fileName.includes('carousel') || fileName.includes('轮播')) {
        type = 'carousels';
      }
      
      // 计算性能指标
      const impressions = this.estimateImpressionsFromData(material);
      const clicks = this.estimateClicksFromData(material, impressions);
      const conversions = Number(material.conversion) || 0;
      
      const performanceData = {
        id: material.id,
        name: material.fileName || `素材${material.id}`,
        impressions,
        clicks,
        ctr: impressions > 0 ? Math.round((clicks / impressions) * 10000) / 100 : 0,
        conversions,
        cvr: clicks > 0 ? Math.round((conversions / clicks) * 10000) / 100 : 0,
      };

      // 只添加有实际数据的素材
      if (impressions > 0 || clicks > 0 || conversions > 0) {
        grouped[type].push(performanceData);
      }
    });

    // 确保每种类型最多5个，按展示量排序
    Object.keys(grouped).forEach(type => {
      grouped[type] = grouped[type]
        .sort((a, b) => b.impressions - a.impressions)
        .slice(0, 5);
    });

    return grouped;
  }

  // 根据素材数据估算展示量
  private estimateImpressionsFromData(material: any): number {
    const spend = Number(material.spend) || 0;
    const adCount = Number(material.adCount) || 0;
    const activeAdCount = Number(material.activeAdCount) || 0;
    
    if (spend > 0) {
      // 假设平均CPM为10元，估算展示量
      return Math.round(spend * 100); // spend * 1000 / 10
    } else if (activeAdCount > 0) {
      // 假设每个活跃广告平均每天1000展示量
      return activeAdCount * 1000;
    } else if (adCount > 0) {
      // 假设每个广告平均每天500展示量
      return adCount * 500;
    }
    
    return 0;
  }

  // 根据展示量估算点击量
  private estimateClicksFromData(material: any, impressions: number): number {
    if (impressions === 0) return 0;
    
    // 假设平均CTR为3.5%，在2%-5%之间浮动
    const ctrRange = 0.02 + Math.random() * 0.03; // 2%-5%
    return Math.round(impressions * ctrRange);
  }

  // 格式化素材数据
  private formatCreativeData(groupedData: any) {
    return {
      images: groupedData.images || [],
      videos: groupedData.videos || [],
      carousels: groupedData.carousels || [],
    };
  }

  // 返回空素材数据结构
  private getEmptyCreativeData() {
    return {
      images: [],
      videos: [],
      carousels: [],
    };
  }

  // 获取广告投放渠道分析数据
  async getChannelAnalysis(timeRange: ChannelTimeRangeEnum) {
    // 定义渠道列表
    const channels = ['facebook', 'google', 'tiktok', 'instagram', 'wechat'];
    
    // 根据时间范围生成不同规模的数据和随机变化
    const getBaseMultiplier = () => {
      switch (timeRange) {
        case ChannelTimeRangeEnum.WEEK:
          return 1;
        case ChannelTimeRangeEnum.MONTH:
          return 4.5; // 约4.5周
        case ChannelTimeRangeEnum.QUARTER:
          return 13; // 约13周
        default:
          return 1;
      }
    };

    const multiplier = getBaseMultiplier();

    // 生成各渠道数据
    const channelMetrics = channels.map((channel, index) => {
      // 基础数据（周数据）- 添加随机变化
      const baseImpressions = [425000, 380000, 320000, 275000, 210000][index];
      const baseClicks = [14875, 11400, 12800, 9625, 6300][index];
      const baseConversions = [892, 684, 768, 578, 378][index];
      const baseCost = [29750, 22800, 25600, 19250, 12600][index];

      // 增加随机变化因子 (±40%) - 让饼图变化更明显
      const randomFactor = () => 0.6 + Math.random() * 0.8; // 0.6 - 1.4

      // 为不同时间范围添加额外的变化因子
      const timeRangeFactor = () => {
        const baseRandom = Math.random();
        switch (timeRange) {
          case ChannelTimeRangeEnum.WEEK:
            return 0.7 + baseRandom * 0.6; // 0.7 - 1.3
          case ChannelTimeRangeEnum.MONTH:
            return 0.8 + baseRandom * 0.5; // 0.8 - 1.3
          case ChannelTimeRangeEnum.QUARTER:
            return 0.9 + baseRandom * 0.4; // 0.9 - 1.3
          default:
            return 1;
        }
      };

      // 根据时间范围、随机因子和时间范围因子调整数据
      const impressions = Math.round(baseImpressions * multiplier * randomFactor() * timeRangeFactor());
      const clicks = Math.round(baseClicks * multiplier * randomFactor() * timeRangeFactor());
      const conversions = Math.round(baseConversions * multiplier * randomFactor() * timeRangeFactor());
      const cost = Math.round(baseCost * multiplier * randomFactor() * timeRangeFactor());

      // 计算指标
      const ctr = Math.round((clicks / impressions) * 10000) / 100; // 保留2位小数
      const cvr = Math.round((conversions / clicks) * 10000) / 100;
      const cpc = Math.round((cost / clicks) * 100) / 100;
      
      // 假设每个转化的平均价值为200元
      const conversionValue = conversions * 200;
      const roi = Math.round(((conversionValue - cost) / cost) * 10000) / 100;

      return {
        channel,
        impressions,
        clicks,
        conversions,
        cost,
        ctr,
        cvr,
        cpc,
        roi,
      };
    });

    return {
      channels: channelMetrics,
      channelNames: channels.map(channel => channel.charAt(0).toUpperCase() + channel.slice(1)),
      impressionsData: channelMetrics.map(item => item.impressions),
      roiData: channelMetrics.map(item => item.roi),
    };
  }
} 