import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateVccCardsTable1748156179911 implements MigrationInterface {
    name = 'CreateVccCardsTable1748156179911'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "vcc_cards" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "channel" character varying(50) NOT NULL, "country" character varying(50) NOT NULL, "cardHolder" character varying(100) NOT NULL, "countryCode" character varying(10) NOT NULL, "cardNumber" character varying(20) NOT NULL, "balance" numeric(10,2) NOT NULL DEFAULT '0', "consumption" numeric(10,2) NOT NULL DEFAULT '0', "boundAdAccount" character varying(200), "group" character varying(100), "transactionCount" integer NOT NULL DEFAULT '0', "adAccountStatus" integer NOT NULL DEFAULT '0', "expiryMonth" character varying(2) NOT NULL, "expiryYear" character varying(4) NOT NULL, "cvv" character varying(4) NOT NULL, "zipCode" character varying(20) NOT NULL, "usedCount" integer NOT NULL DEFAULT '0', "bindCount" integer NOT NULL DEFAULT '0', "totalAdAccounts" integer NOT NULL DEFAULT '0', "limitCount" integer NOT NULL DEFAULT '10', "status" character varying(20) NOT NULL DEFAULT '未使用', "remark" character varying(500), "tenantId" uuid NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_6ab3337909b5dcd7b325dae49bd" UNIQUE ("cardNumber"), CONSTRAINT "PK_3f3ac2c4e14b123ec887872bc5d" PRIMARY KEY ("id")); COMMENT ON COLUMN "vcc_cards"."channel" IS '渠道'; COMMENT ON COLUMN "vcc_cards"."country" IS '国家'; COMMENT ON COLUMN "vcc_cards"."cardHolder" IS '持卡人'; COMMENT ON COLUMN "vcc_cards"."countryCode" IS '国家代码'; COMMENT ON COLUMN "vcc_cards"."cardNumber" IS '卡号'; COMMENT ON COLUMN "vcc_cards"."balance" IS '余额'; COMMENT ON COLUMN "vcc_cards"."consumption" IS '消费'; COMMENT ON COLUMN "vcc_cards"."boundAdAccount" IS '已绑定广告号'; COMMENT ON COLUMN "vcc_cards"."group" IS '所属群组'; COMMENT ON COLUMN "vcc_cards"."transactionCount" IS '交易数'; COMMENT ON COLUMN "vcc_cards"."adAccountStatus" IS '广告号存活数量'; COMMENT ON COLUMN "vcc_cards"."expiryMonth" IS '过期月'; COMMENT ON COLUMN "vcc_cards"."expiryYear" IS '过期年'; COMMENT ON COLUMN "vcc_cards"."cvv" IS 'CVV'; COMMENT ON COLUMN "vcc_cards"."zipCode" IS '邮编'; COMMENT ON COLUMN "vcc_cards"."usedCount" IS '已使用次数'; COMMENT ON COLUMN "vcc_cards"."bindCount" IS '绑定次数'; COMMENT ON COLUMN "vcc_cards"."totalAdAccounts" IS '绑定广告号总数'; COMMENT ON COLUMN "vcc_cards"."limitCount" IS '限制次数'; COMMENT ON COLUMN "vcc_cards"."status" IS '状态: 未使用/已使用/已封禁'; COMMENT ON COLUMN "vcc_cards"."remark" IS '备注'; COMMENT ON COLUMN "vcc_cards"."tenantId" IS '租户ID'; COMMENT ON COLUMN "vcc_cards"."createdAt" IS '创建时间'; COMMENT ON COLUMN "vcc_cards"."updatedAt" IS '更新时间'`);
        await queryRunner.query(`CREATE INDEX "idx_vcc_card_number" ON "vcc_cards" ("cardNumber") `);
        await queryRunner.query(`ALTER TABLE "vcc_cards" ADD CONSTRAINT "FK_5117808e82d89e750e86d2f9c7a" FOREIGN KEY ("tenantId") REFERENCES "tenant"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "vcc_cards" DROP CONSTRAINT "FK_5117808e82d89e750e86d2f9c7a"`);
        await queryRunner.query(`DROP INDEX "public"."idx_vcc_card_number"`);
        await queryRunner.query(`DROP TABLE "vcc_cards"`);
    }

}
