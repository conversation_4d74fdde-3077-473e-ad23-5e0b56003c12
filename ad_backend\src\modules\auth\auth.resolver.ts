import { Resolver, Mutation, Args, Query, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import {
    LoginInput,
    RegisterInput,
    LoginResponse,
    RegisterResponse,
    AuthUserResponse
} from './dto/auth.dto';
import { JwtAuthGuard } from './jwt-auth.guard';
import { Public } from '../../common/decorators/public.decorator';

@Resolver()
export class AuthResolver {
    constructor(private readonly authService: AuthService) { }

    @Public()
    @Mutation(() => LoginResponse)
    async login(@Args('input') loginInput: LoginInput): Promise<LoginResponse> {
        return this.authService.login(loginInput);
    }

    @Public()
    @Mutation(() => RegisterResponse)
    async register(@Args('input') registerInput: RegisterInput): Promise<RegisterResponse> {
        return this.authService.register(registerInput);
    }

    @Query(() => AuthUserResponse)
    @UseGuards(JwtAuthGuard)
    async me(@Context() context: any): Promise<AuthUserResponse> {
        // 从JWT中获取用户ID
        const userId = context.req.user.userId;
        return this.authService.getAuthUser(userId);
    }
} 