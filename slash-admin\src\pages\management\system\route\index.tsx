import { useTranslation } from 'react-i18next';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { Card, Table, Button, Space, Modal, message, Switch, App, notification } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, MinusSquareOutlined, PlusSquareOutlined } from '@ant-design/icons';
import { useApolloClient } from '@apollo/client';
import type { RouteItem, RouteTreeItem } from './types';
import RouteForm from './components/RouteForm';
import { Icon } from '@/components/icon';
import type { TablePaginationConfig } from 'antd/es/table';
import { RouteService } from '@/api/services/route.service';

function RouteManagement() {
  const { t } = useTranslation();
    const [routes, setRoutes] = useState<RouteItem[]>([]);
    const [selectedRoute, setSelectedRoute] = useState<RouteItem | null>(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [modalType, setModalType] = useState<'add' | 'edit'>('add');
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    const [actionInProgress, setActionInProgress] = useState(false);

    const client = useApolloClient();
    const routeService = useMemo(() => new RouteService(client), [client]);
    const { modal } = App.useApp();

    // 加载路由数据
    const loadRoutes = useCallback(async () => {
        if (loading || actionInProgress) return;

        try {
            setLoading(true);
            const routes = await routeService.getRoutes();
            setRoutes(routes);
        } catch (error) {
            notification.error({ message: t('pages.key_347'), description: (error as Error).message || t('pages.key_1312') });
            console.error('Failed to load routes:', error);
        } finally {
            setLoading(false);
        }
    }, [routeService, loading, actionInProgress]);

    useEffect(() => {
        loadRoutes();
    }, []);

    // 转换为树形结构
    const treeData = useMemo(() => {
        // 先按 order 排序，确保同级节点按正确顺序显示
        const sortedRoutes = [...routes].sort((a, b) => {
            // 如果是同一层级（都是根节点或有相同的父节点），按 order 排序
            if ((!a.parentId && !b.parentId) || (a.parentId === b.parentId)) {
                return a.order - b.order;
            }
            return 0;
        });

        const buildTree = (items: RouteItem[]): RouteTreeItem[] => {
            const map = new Map<string, RouteTreeItem>();
            const tree: RouteTreeItem[] = [];

            // 第一步：创建所有节点的映射
            items.forEach(item => {
                map.set(item.id, {
                    ...item,
                    key: item.id,
                    title: item.name,
                    children: []
                });
            });

            // 第二步：构建树形结构
            items.forEach(item => {
                const node = map.get(item.id)!;
                if (!item.parentId) {
                    // 根节点直接加入树中
                    tree.push(node);
                } else {
                    // 子节点加入到父节点的 children 中
                    const parent = map.get(item.parentId);
                    if (parent) {
                        if (!parent.children) {
                            parent.children = [];
                        }
                        parent.children.push(node);
                    }
                }
            });

            // 第三步：递归排序所有层级
            const sortTreeNodes = (nodes: RouteTreeItem[]) => {
                nodes.sort((a, b) => a.order - b.order);
                nodes.forEach(node => {
                    if (node.children && node.children.length > 0) {
                        sortTreeNodes(node.children);
                    }
                });
            };
            sortTreeNodes(tree);

            return tree;
        };

        return buildTree(sortedRoutes);
    }, [routes]);

    const handleAdd = () => {
        setModalType('add');
        setSelectedRoute(null);
        setIsModalVisible(true);
    };

    const handleEdit = (record: RouteItem) => {
        setModalType('edit');
        setSelectedRoute(record);
        setIsModalVisible(true);
    };

    const handleDelete = async (record: RouteItem) => {
        try {
            await modal.confirm({
                title: t('pages.key_1706'),
                content: `确定要删除路由 "${record.name}" 吗？`,
                onOk: async () => {
                    if (actionInProgress) return;
                    try {
                        setActionInProgress(true);
                        await routeService.deleteRoute(record.id);
                        notification.success({ message: t('pages.key_294') });
                        loadRoutes();
                    } catch (error) {
                        notification.error({ message: t('pages.key_293'), description: (error as Error).message || t('pages.key_1312') });
                        console.error('Failed to delete route:', error);
                    } finally {
                        setActionInProgress(false);
                    }
                }
            });
        } catch (error) {
            // User canceled
        }
    };

    const handleSave = async (values: any) => {
        if (actionInProgress) return;

        try {
            setActionInProgress(true);
            if (modalType === 'add') {
                await routeService.createRoute(values);
                notification.success({ message: t('pages.key_246') });
            } else {
                await routeService.updateRoute(selectedRoute!.id, values);
                notification.success({ message: t('pages.key_1252') });
            }
            setIsModalVisible(false);
            loadRoutes();
        } catch (error) {
            notification.error({ message: modalType === 'add' ? t('pages.key_245') : t('pages.key_1251'), description: (error as Error).message || t('pages.key_1312') });
            console.error('Failed to save route:', error);
        } finally {
            setActionInProgress(false);
        }
    };

    const handleStatusChange = async (checked: boolean, record: RouteItem) => {
        if (actionInProgress) return;

        try {
            setActionInProgress(true);
            await routeService.toggleRouteStatus(
                record.id,
                checked ? 'enabled' : 'disabled'
            );
            notification.success({ message: t('pages.key_1607') });
            loadRoutes();
        } catch (error) {
            notification.error({ message: t('pages.key_1606'), description: (error as Error).message || t('pages.key_1312') });
            console.error('Failed to update status:', error);
        } finally {
            setActionInProgress(false);
        }
    };

    const columns = [
        {
            title: t('pages.key_2346'),
            dataIndex: 'name',
            key: 'name',
            render: (text: string, record: RouteTreeItem) => (
                <Space>
                    {record.type === 'menu' && record.icon && <Icon icon={record.icon} size={20} />}
                    <span>{text}</span>
                </Space>
            )
        },
        {
            title: t('pages.key_2351'),
            dataIndex: 'path',
            key: 'path'
        },
        {
            title: t('pages.key_1804'),
            dataIndex: 'component',
            key: 'component'
        },
        {
            title: t('pages.key_1765'),
            dataIndex: 'type',
            key: 'type',
            render: (type: string) => type === 'menu' ? t('pages.key_2006') : t('pages.key_2540')
        },
        {
            title: t('pages.key_1014'),
            dataIndex: 'order',
            key: 'order'
        },
        {
            title: t('pages.key_1602'),
            dataIndex: 'status',
            key: 'status',
            render: (status: string, record: RouteItem) => (
                <Switch
                    checked={status === 'enabled'}
                    onChange={(checked) => handleStatusChange(checked, record)}
                />
            )
        },
        {
            title: t('pages.key_1058'),
            key: 'action',
            render: (_: any, record: RouteItem) => (
                <Space size="middle">
                    <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    >{t('pages.key_1853')}</Button>
                    <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDelete(record)}
                    >{t('pages.key_287')}</Button>
                </Space>
            )
        }
    ];

    return (
        <Card>
            <div style={{ marginBottom: 16 }}>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>{t('pages.key_1543')}</Button>
            </div>
            <Table
                columns={columns}
                dataSource={treeData}
                loading={loading}
                pagination={false}
                expandable={{
                    expandedRowKeys: expandedKeys,
                    onExpandedRowsChange: (keys) => setExpandedKeys(keys as string[]),
                    rowExpandable: (record: RouteTreeItem) => record.type === 'menu',
                    expandIcon: ({ expanded, onExpand, record }) =>
                        record.type === 'menu' ? (
                            <Button
                                type="text"
                                icon={expanded ? <MinusSquareOutlined /> : <PlusSquareOutlined />}
                                onClick={e => onExpand(record, e)}
                            />
                        ) : null
                }}
            />
            <RouteForm
                open={isModalVisible}
                type={modalType}
                initialValues={selectedRoute}
                onCancel={() => setIsModalVisible(false)}
                onOk={handleSave}
                routes={routes}
            />
        </Card>
    );
}

// 包装组件以使用 App context
export default function RouteManagementWithApp() {
    return (
        <App>
            <RouteManagement />
        </App>
    );
} 