import React, { createContext, useContext, useState, useCallback } from 'react';
import { ApolloClient } from '@apollo/client';

interface TenantContextValue {
  tenantId: string;
  setTenantId: (id: string) => void;
}

const TenantContext = createContext<TenantContextValue | undefined>(undefined);

export function useTenantContext() {
  return useContext(TenantContext);
}

export function TenantProvider({ children, apolloClient }: { children: React.ReactNode; apolloClient: ApolloClient<any> }) {
  const [tenantId, setTenantIdState] = useState<string>(() => {
    // 默认从 localStorage 读取
    return localStorage.getItem('current_tenant_id') || '';
  });

  const setTenantId = useCallback((id: string) => {
    setTenantIdState(id);
    localStorage.setItem('current_tenant_id', id);
    // 清理 Apollo 缓存
    if (apolloClient) apolloClient.clearStore();
    // 切换 token
    const allTokens = JSON.parse(localStorage.getItem('tenant_tokens') || '{}');
    const token = allTokens[id] || '';
    localStorage.setItem('auth_token', token);
    // 可扩展：切换 userInfo/settings 等缓存
    // ...
  }, [apolloClient]);

  return (
    <TenantContext.Provider value={{ tenantId, setTenantId }}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const ctx = useContext(TenantContext);
  return ctx?.tenantId || null;
}
